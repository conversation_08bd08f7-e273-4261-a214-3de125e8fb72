﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasDeviceClassAggregate;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class BasDeviceClassConfig : AbstractEntityTypeConfiguration<BasDeviceClass>
    {

        public override void Configure(EntityTypeBuilder<BasDeviceClass> builder)
        {
            base.Configure(builder);
            builder.<PERSON>Key(x => x.Id);
            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.DevCode).HasColumnName("devcode");
            builder.Property(x => x.ClassCode).HasColumnName("classcode");
            builder.Property(x => x.WorkingDate).HasColumnName("workingdate");
            builder.Property(x => x.CreateBy).HasColumnName("createby");
            builder.Property(x => x.CreateTime).HasColumnName("createtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
        }
    }
}
