﻿using Adnc.Infra.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Aggregates.BasDevice
{
    public class BasDevice : EfFullAuditEntity
    {
        /// <summary>
        /// ID
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int? Status { get; set; }

        /// <summary>
        /// 设备编码
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        public string? Name { get; set; }

        ///// <summary>
        ///// 所属工位
        ///// </summary>
        //public long Stationid { get; set; }

        ///// <summary>
        ///// 所属工位名称
        ///// </summary>
        //public string? StationName { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 创建人名称
        /// </summary>
        public string? CreatedName { get; set; }

        /// <summary>
        /// 修改人名称
        /// </summary>
        public string? ModifyName { get; set; }

        /// <summary>
        /// 产能
        /// </summary>
        public int? Capacity { get; set; }


        /// <summary>
        /// 设备类型
        /// </summary>
        public string? Dtype { get; set; }

        /// <summary>
        /// 设备新度
        /// </summary>
        public decimal? Newness { get; set; }
    }
}
