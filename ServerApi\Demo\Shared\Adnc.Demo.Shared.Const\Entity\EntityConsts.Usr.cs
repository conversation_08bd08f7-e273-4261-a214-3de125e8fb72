﻿namespace Adnc.Demo.Shared.Const.Entity.Usr;

public static class UserConsts
{
    public const int Account_MaxLength = 16;
    public const int Avatar_MaxLength = 64;
    public const int Email_Maxlength = 32;
    public const int Name_Maxlength = 16;
    public const int Password_Maxlength = 32;
    public const int Phone_Maxlength = 11;
    public const int RoleIds_Maxlength = 72;
    public const int Salt_Maxlength = 6;
}

public static class RoleConsts
{
    public const int Name_MaxLength = 32;
    public const int Tips_MaxLength = 64;
}

public static class DeptConsts
{
    public const int FullName_MaxLength = 32;
    public const int SimpleName_MaxLength = 16;
    public const int Tips_MaxLength = 64;
    public const int Pids_MaxLength = 80;
}

public static class MenuConsts
{
    public const int Code_MaxLength = 16;
    public const int PCode_MaxLength = 16;
    public const int PCodes_MaxLength = 128;
    public const int Component_MaxLength = 64;
    public const int Icon_MaxLength = 50;
    public const int Name_MaxLength = 100;
    public const int Tips_MaxLength = 32;
    public const int Url_MaxLength = 64;
}