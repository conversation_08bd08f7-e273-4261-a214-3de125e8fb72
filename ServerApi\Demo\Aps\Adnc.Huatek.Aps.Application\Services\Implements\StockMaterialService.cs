﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.BasBomAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasLineProductRelationAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasMaterial;
using Adnc.Huatek.Aps.Domain.Aggregates.StockMaterial;
using Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.SysUserAggregate;
using Adnc.Huatek.Aps.Domain.Services;
using Adnc.Infra.IdGenerater.Yitter;
using Adnc.Shared;
using Adnc.Shared.Application.Contracts.ResultModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services.Implements
{
    
    public class StockMaterialService : AbstractAppService, IStockMaterialService
    {
        private readonly StockMaterialManagement _stockMaterialMgr;
        private readonly IEfBasicRepository<StockMaterial> _stockMaterialRepo;
        private readonly IEfBasicRepository<SysUser> _sysUserRepo;
        private readonly IEfBasicRepository<BasMaterial> _basMaterialRepo;

        public StockMaterialService(
            StockMaterialManagement stockMaterialMgr,
            IEfBasicRepository<StockMaterial> stockMaterialRepo,
            IEfBasicRepository<SysUser> sysUserRepo,
            IEfBasicRepository<BasMaterial> basMaterialRepo
            )
        {
            _stockMaterialMgr = stockMaterialMgr;
            _stockMaterialRepo = stockMaterialRepo;
            _sysUserRepo = sysUserRepo;
            _basMaterialRepo = basMaterialRepo;
        }

        /// <summary>
        /// 新增物料
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<ResultJson> ImportAsync(List<StockMaterialDto> dtos)
        {
            var entities = Mapper.Map<List<StockMaterial>>(dtos);

            var resultObj = await _stockMaterialMgr.ImportAsync(entities);

            if (resultObj.insertEntities.Any())
                await _stockMaterialRepo.InsertRangeAsync(resultObj.insertEntities);

            if (resultObj.updateEntities.Any())
                await _stockMaterialRepo.UpdateRangeAsync(resultObj.updateEntities);

            return new ResultJson(resultObj.Msg);
        }

        /// <summary>
        /// 获取列表分页
        /// </summary>
        /// <param name="search"></param>
        /// <returns></returns>
        public async Task<PageModelDto<StockMaterialDto>> GetPagedAsync(StockMaterialPagedDto search)
        {
            search.TrimStringFields();
            var stocks = _stockMaterialRepo.Where(x=>!x.IsDeleted);//获取库存基本信息
            List<long> userIds = stocks.Select(x => x.ModifyBy ?? 0).ToList();//更新用户id
            userIds.AddRange(stocks.Select(x => x.CreateBy!).ToList());//创建用户id
            var users = _sysUserRepo.Where(x => userIds.Contains(x.Id));//用户信息

            var pWhere = ExpressionCreator
                                                .New<BasMaterial>()
                                                .And(x => !x.IsDeleted)
                                                .AndIf(search.Mcode.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Code!, $"%{search.Mcode}%"))
                                                .AndIf(search.Mname.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Name!, $"%{search.Mname}%"));
            var materials = _basMaterialRepo.Where(pWhere);//获取符合查询条件的物料信息


            var entities = (from p in stocks
                            join u in users on p.CreateBy equals u.Id
                            join m in users on p.ModifyBy equals m.Id
                            join a in materials on p.Mcode equals a.Code
                            select new StockMaterialDto
                            {
                                Id = p.Id,
                                Mcode = p.Mcode,
                                Mname = a.Name,
                                Qty = p.Qty,
                                unit = a.Unit,
                                unit_dis = a.Unit,
                                PreLockqty = p.Prelockqty,
                                ModifyBy = p.ModifyBy,
                                ModifyName = m.Name,
                                ModifyTime = p.ModifyTime,
                                CreateBy = p.CreateBy,
                                CreateTime = p.CreateTime,
                                CreatName = u.Name
                            }
                        );//处理列表返回对象
            

            var total = entities.Count();//总行数
            var results = await entities.OrderByDescending(x => x.CreateTime)
                                            .Skip(search.SkipRows())
                                            .Take(search.PageSize).ToListAsync();
            return new PageModelDto<StockMaterialDto>(search, results, total);
        }
        /// <summary>
        /// 获取所有数据
        /// </summary>
        /// <returns></returns>

        public async Task<ResultJson> GetAllAsync()
        {
            var sMaterials = await _stockMaterialRepo.Where(x => !x.IsDeleted).ToListAsync();

            var sMaterialDtos = Mapper.Map<List<StockMaterialDto>>(sMaterials);

            return new ResultJson("查询成功", sMaterialDtos);
        }


        /// <summary>
        /// 创建物料库存
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult> CreateAsync(StockMaterialDto input)
        {
            return new AppSrvResult();
        }



        /// <summary>
        /// 更新产线
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult> UpdateAsync(StockMaterialDto input)
        {
            bool isUpdete = await _stockMaterialMgr.UpdateAsync(input.Mcode!, input.Id ?? 0);
            if (isUpdete)
            {
                var stock = await _stockMaterialRepo.GetAsync(input.Id ?? 0);
                if (stock != null)
                {
                    stock.Qty = input.Qty;
                    await _stockMaterialRepo.UpdateAsync(stock);
                }
            }
            return AppSrvResult();
        }


        /// <summary>
        /// 根据ID获取库存数量
        /// </summary>
        /// <returns></returns>

        public async Task<StockMaterialDto> GetByIdAsync(long id)
        {
            var stock = await _stockMaterialRepo.GetAsync(id);
            var stockDto = Mapper.Map<StockMaterialDto>(stock);
            var material = await _basMaterialRepo.Where(x => !x.IsDeleted && x.Code == stock.Mcode).FirstOrDefaultAsync();
            stockDto.Mname = material.Name;
            return stockDto;
        }

    }

}
