﻿using Adnc.Shared;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace Adnc.Huatek.Aps.Application.Dtos
{
    /// <summary>
    /// 客户订单
    /// </summary>
    public class BasOrderDto : Dto
    {
        /// <summary>
        /// ID
        /// </summary>
        public long? Id { get; set; }


        /// <summary>
        /// ID
        /// </summary>
        public long? IdDetail { get; set; }

        /// <summary>
        /// 优先级
        /// </summary>
        public int Priority { get; set; }
        /// <summary>
        /// 生产状态
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 订单编号
        /// </summary>
        public string OrderNumber { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 交付日期
        /// </summary>
        [JsonConverter(typeof(DateTimeJsonConverter))]
        public DateTime? DeliveryDate { get; set; }

        /// <summary>
        ///  产品名称
        /// </summary>
        public string? ProductName { get; set; }

        /// <summary>
        ///  产品编码
        /// </summary>
        public string? ProductCode { get; set; }

        /// <summary>
        /// 产品数量
        /// </summary>
        public decimal Quantity { get; set; }
        /// <summary>
        /// 销售单号
        /// </summary>
        public string? SaleNumber { get; set; }
        /// <summary>
        /// 待排产产品数量
        /// </summary>
        public decimal PendingNum { get; set; }

    }

    public class BasOrderPagedDto : SearchPagedDto
    {
        /// <summary>
        /// 优先级
        /// </summary>
        public int Priority { get; set; }
        /// <summary>
        /// 生产状态
        /// </summary>
        public int Status { get; set; }
        /// <summary>
        /// 销售单号
        /// </summary>
        public string? SaleNumber { get; set; }
        /// <summary>
        /// 订单编号
        /// </summary>
        public string? OrderNumber { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? UserName { get; set; }
        /// <summary>
        ///  产品编码
        /// </summary>
        public string? ProductCode { get; set; }
        /// <summary>
        ///  产品名称
        /// </summary>
        public string? ProductName { get; set; }
    }
    /// <summary>
    /// 批量更新订单状态为完成
    /// </summary>
    public class FinishedOrders
    {
        public List<BasOrderDto> Orders { get; set; }
    }
    /// <summary>
    /// 导入订单
    /// </summary>
    public class ImportOrderDto
    {
        [Display(Name = "销售单号")]
        public string SaleNumber { get; set; }
        [Display(Name = "产品名称")]
        public string ProductName { get; set; }
        [Display(Name = "产品数量")]
        public int ProductQty { get; set; }
        [Display(Name = "客户名称")]
        public string UserName { get; set; }
        [Display(Name = "优先级")]
        public string Priority { get; set; }
        [Display(Name = "交付日期")]
        public DateTime DeliveryDate { get; set; }
    }
}
