﻿using Adnc.Infra.Entities;

namespace Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanStepAggregate
{
    public class PartSchedulePlanStep : EfFullAuditEntity
    {

        /// <summary>
        /// 
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 排产计划Code
        /// </summary>
        public string? ScheduleCode { get; set; }

        /// <summary>
        /// 前置工序
        /// </summary>
        public string? PreCode { get; set; }

        /// <summary>
        /// 工序编码
        /// </summary>
        public string? StepCode { get; set; }

        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime BeginDate { get; set; }

        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// 消耗工时;用产能模型中考虑产能的资源的单元工作时长计算消耗时长
        /// </summary>
        public decimal? Tat { get; set; }

        /// <summary>
        /// 来源
        /// </summary>
        public int ScheduleType { get; set; }

        /// <summary>
        /// 报工结果
        /// </summary>
        public decimal? ReportResult { get; set; }


        /// <summary>
        /// 报工时间
        /// </summary>
        public DateTime? ReportDate { get; set; }
    }

   
}
