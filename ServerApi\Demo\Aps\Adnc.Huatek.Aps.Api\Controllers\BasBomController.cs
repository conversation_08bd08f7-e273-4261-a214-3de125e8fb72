﻿using Adnc.Demo.Shared.Const;
using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Application.Services;
using Adnc.Shared.Application.Contracts.ResultModels;
using Adnc.Shared.WebApi.Authorization;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion.Internal;

namespace Adnc.Huatek.Aps.Api.Controllers
{
    [Route($"{RouteConsts.ApsRoot}/bom")]
    [ApiController]
    [AllowAnonymous]
    public class BasBomController : AdncControllerBase
    {
        private readonly IBasBomService _bomSrv;

        public BasBomController(IBasBomService bomSrv) => _bomSrv = bomSrv;

        /// <summary>
        /// 获取Bom分页信息
        /// </summary>
        /// <returns></returns>
        [HttpPost("page")]
        public async Task<ActionResult<PageModelDto<BasBomDto>>> GetPagedAsync([FromBody] BomPagedDto search) => await _bomSrv.GetPagedAsync(search);

        /// <summary>
        /// 新增Bom
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("add")]
        public async Task<ActionResult<string>> CreateAsync([FromBody] BasBomDto input)
           => CreatedResult(await _bomSrv.CreateAsync(input));

        /// <summary>
        /// 更新Bom
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("edit")]
        public async Task<ActionResult> UpdateAsync([FromBody] BasBomDto input)
           => Result(await _bomSrv.UpdateAsync(input));


        /// <summary>
        /// 删除Bom
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpDelete("delete/{productCode}")]
        public async Task<ActionResult> DeleteAsync([FromRoute] string productCode)
          => Result(await _bomSrv.DeleteAsync(productCode));

        /// <summary>
        /// 根据ID获取Bom
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getbyproductcode/{productcode}")]
        public async Task<ActionResult<BasBomDto>> GetByIdAsync([FromRoute] string productcode)
            => await _bomSrv.GetByIdAsync(productcode);

        /// <summary>
        /// 根据产品code获取bom中物料列表
        /// </summary>
        /// <param name="productCode"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getmaterialbyproductcode/{productCode}")]
        public async Task<ActionResult<List<BasBomListDto>>> GetMaterialByProductCodeAsync([FromRoute] string productCode)
            => await _bomSrv.GetMaterialByProductCodeAsync(productCode);

    }
}
