﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Dtos
{
    public class BasHolidayDto : Dto
    {

        public long? Id { get; set; }
        /// <summary>
        /// 日期
        /// </summary>
        public DateTime? CurrentDate { get; set; }
        /// <summary>
        /// 是否工作日
        /// </summary>
        public int IsHoliday { get; set; }
        /// <summary>
        /// 是否排产
        /// </summary>

        public int IsPlaned { get; set; }
        /// <summary>
        /// 星期名称
        /// </summary>
        public string WeekName { get; set; }
    }

    public class HolidayPagedDto : SearchPagedDto
    {
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? Start { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? End { get; set; }
        /// <summary>
        /// 是否工作日
        /// </summary>
        public int? IsHoliday { get; set; }
        /// <summary>
        /// 是否排产
        /// </summary>

        public int? IsPlaned { get; set; }

    }

    public class ImportHolidayDto
    {
        [Display(Name = "日期")]
        public DateTime CurrentDate { get; set; }
        [Display(Name = "是否工作日")]
        public string IsHoliday { get; set; }
        [Display(Name = "是否排产")]
        public string IsPlaned { get; set; }
        [Display(Name = "星期名称")]
        public string WeekName { get; set; }
       
    }
}
