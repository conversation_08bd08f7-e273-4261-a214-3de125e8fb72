﻿using Adnc.Huatek.Aps.Domain.Aggregates.PlanSchedulStepSolutionAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.SysUserAggregate;
using Adnc.Huatek.Aps.Domain.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services.Implements
{
    public class PlanStepCapacityService: AbstractAppService, IPlanStepCapacityService
    {
        private readonly IEfBasicRepository<PlanSchedulStepSolution> _planSchedulStepSolutionRepo;
        private readonly IEfBasicRepository<SysUser> _sysUserRepo;

        public PlanStepCapacityService(
           IEfBasicRepository<PlanSchedulStepSolution> planSchedulStepSolutionRepo,
          IEfBasicRepository<SysUser> sysUserRepo)
        {
            _planSchedulStepSolutionRepo = planSchedulStepSolutionRepo;
            _sysUserRepo = sysUserRepo;
        }
    }
}
