﻿using Adnc.Infra.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Aggregates.PartOrderAggregate
{
    public class PartOrder : EfFullAuditEntity
    {
        /// <summary>
        /// ID
        /// </summary>
        public long? Id { get; set; }
        public int? Level { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        public string? OrderCode { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public int? Status { get; set; }


        public long? IdProduct { get; set; }

        public long? IdTec { get; set; }

        public decimal? Qty { get; set; }

        public long? IdCustorm { get; set; }

        public DateTime? DeliveryTime { get; set; }
    }
}
