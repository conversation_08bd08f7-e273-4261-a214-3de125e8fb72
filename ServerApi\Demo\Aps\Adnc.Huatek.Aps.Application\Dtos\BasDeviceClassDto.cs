﻿using System.ComponentModel.DataAnnotations;

namespace Adnc.Huatek.Aps.Application.Dtos
{
    public class BasDeviceClassDto : IDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool Isdeleted { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public long? CreateBy { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 更新人
        /// </summary>
        public long? ModifyBy { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 创建人名称
        /// </summary>
        public string? CreatedName { get; set; }

        /// <summary>
        /// 修改人名称
        /// </summary>
        public string? ModifyName { get; set; }

        /// <summary>
        /// 设备编号
        /// </summary>
        public string? DevCode { get; set; }
        /// <summary>
        /// 设备名称
        /// </summary>

        public string? DevName { get; set; }
        /// <summary>
        /// 班次编码
        /// </summary>

        public string? ClassCode { get; set; }
        /// <summary>
        /// 班次名称
        /// </summary>
        public string? ClassName { get; set; }

        /// <summary>
        /// 班次颜色
        /// </summary>
        public string? ClassColor { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public string? BeginTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public string? EndTime { get; set; }
        /// <summary>
        /// 工作时间
        /// </summary>

        public DateTime WorkingDate { get; set; }
    }


    public class DeviceWorkingDateDto:BasDeviceDto
    {
        private List<BasDeviceClassDto> _shifts;

        public List<BasDeviceClassDto> Shifts
        {
            get
            {
                if (_shifts == null)
                    _shifts = new List<BasDeviceClassDto>();
                return _shifts;
            }
            set
            {
                _shifts = value;
            }
        } 

    }


    public class BasDeviceClassPagedDto : SearchPagedDto
    {

        /// <summary>
        /// 设备编码
        /// </summary>
        public string DevCode { get; set; }
        /// <summary>
        /// 设备类型
        /// </summary>
        public string Dtype { get; set; }

        /// <summary>
        /// 班次
        /// </summary>
        public string ClassCode { get; set; }


        /// <summary>
        /// 当前月
        /// </summary>
        public DateTime CurrentMonth { get; set; }

    }
}
