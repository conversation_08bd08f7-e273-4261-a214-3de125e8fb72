﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Dtos
{
    public class BasTechnologyDto : IDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 工序名称
        /// </summary>
        public string? Tname { get; set; }

        /// <summary>
        /// 工序编码
        /// </summary>
        public string? Tcode { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public int? Status { get; set; }
        /// <summary>
        /// 创建名
        /// </summary>
        public string? CreatName { get; set; }
        /// <summary>
        /// 更新人
        /// </summary>
        public string? ModifyName { get; set; }

        public long CreateBy { get; set; }

        /// <summary>
        /// 创建时间/注册时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 最后更新人
        /// </summary>
        public long? ModifyBy { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        public List<BasTechnologyStepRelationDto> Items { get; set; }


    }

    public class TechnologyPagedDto : SearchPagedDto
    {
        /// <summary>
        /// 工序名称
        /// </summary>
        public string? Tname { get; set; }

        /// <summary>
        /// 工序编码
        /// </summary>
        public string? Tcode { get; set; }


        /// <summary>
        /// 状态
        /// </summary>
        public int? Status { get; set; }

    }
}
