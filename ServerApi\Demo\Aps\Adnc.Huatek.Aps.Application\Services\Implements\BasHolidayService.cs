﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Services;
using Adnc.Infra.IdGenerater.Yitter;
using Adnc.Shared.Application.Contracts.ResultModels;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Adnc.Huatek.Aps.Domain.Aggregates.BasMaterial;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasLineAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasWorksjop;
using Adnc.Huatek.Aps.Domain.Aggregates.BasLineProductRelationAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasBomAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasBomListAggregate;
using Adnc.Shared;
using Adnc.Huatek.Aps.Domain.Aggregates.BasHolidayAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.SysUserAggregate;

namespace Adnc.Huatek.Aps.Application.Services.Implements
{
    public class BasHolidayService : AbstractAppService, IBasHolidayService
    {
        private readonly BasHolidayManagement _basHolidayMgr;
        private readonly IEfBasicRepository<BasHoliday> _basHolidayRepo;
        private readonly IEfBasicRepository<SysUser> _userRepo;

        public BasHolidayService(
            BasHolidayManagement basHolidayMgr,
            IEfBasicRepository<BasHoliday> basHolidayRepo,
            IEfBasicRepository<SysUser> userRepo)
        {
            _basHolidayMgr = basHolidayMgr;
            _basHolidayRepo = basHolidayRepo;
            _userRepo = userRepo;
        }
        /// <summary>
        /// 获取分页列表
        /// </summary>
        /// <param name="search"></param>
        /// <returns></returns>
        public async Task<PageModelDto<BasHolidayDto>> GetPagedAsync(HolidayPagedDto search)
        {
            search.TrimStringFields();
            var whereExpression = ExpressionCreator
                                                 .New<BasHoliday>()
                                                 .And(x => !x.IsDeleted)
                                                 .AndIf(search.Start.HasValue, x => x.CurrentDate >= search.Start)
                                                 .AndIf(search.End.HasValue, x => x.CurrentDate >= search.End)
                                                 .AndIf(search.IsHoliday.HasValue, x => x.IsHoliday == search.IsHoliday)
                                                 .AndIf(search.IsPlaned.HasValue, x => x.IsPlaned == search.IsPlaned);

            var total = await _basHolidayRepo.CountAsync(whereExpression);
            if (total == 0)
                return new PageModelDto<BasHolidayDto>(search);
            var holidays = _basHolidayRepo.Where(whereExpression);
            var users = _userRepo.Where(x => true);
            var results = await (from h in holidays
                                 join u in users on h.CreateBy equals u.Id
                                 select new BasHolidayDto
                                 {
                                     Id = h.Id,
                                     CurrentDate = h.CurrentDate,
                                     WeekName = h.WeekName,
                                     IsHoliday = h.IsHoliday,
                                     IsPlaned = h.IsPlaned,
                                     CreateBy = h.CreateBy,
                                     CreatName = u.Name,
                                     CreateTime = h.CreateTime
                                 }
                           ).Skip(search.SkipRows())
                           .Take(search.PageSize)
                           .ToListAsync();
            return new PageModelDto<BasHolidayDto>(search, results, total);
        }


        /// <summary>
        /// 根据ID获取数据
        /// </summary>
        /// <returns></returns>

        public async Task<BasHolidayDto> GetByIdAsync(long id)
        {
            var h = await _basHolidayRepo.GetAsync(id);

            var dto = new BasHolidayDto()
            {
                Id = h.Id,
                CurrentDate = h.CurrentDate,
                WeekName = h.WeekName,
                IsHoliday = h.IsHoliday,
                IsPlaned = h.IsPlaned,
                CreateBy = h.CreateBy,
                CreateTime = h.CreateTime
            };
            return dto;
        }



        /// <summary>
        /// 创建
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult<long>> CreateAsync(BasHolidayDto input)
        {
            input.TrimStringFields();
            var info = await _basHolidayMgr.CreateAsync(input.CurrentDate.Value);

            info.CurrentDate = input.CurrentDate.Value;
            info.WeekName = GetWeekName(input.CurrentDate.Value);
            info.IsPlaned = input.IsPlaned;
            info.IsHoliday = input.IsHoliday;

            await _basHolidayRepo.InsertAsync(info);
            return info.Id;
        }



        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult> UpdateAsync(BasHolidayDto input)
        {
            bool isUpdete = await _basHolidayMgr.UpdateAsync(input.CurrentDate.Value, input.Id ?? 0);
            if (isUpdete)
            {
                var info = await _basHolidayRepo.GetAsync(input.Id ?? 0);

                if (info != null)
                {
                    info.CurrentDate = input.CurrentDate.Value;
                    info.WeekName = GetWeekName(input.CurrentDate.Value);
                    info.IsPlaned = input.IsPlaned;
                    info.IsHoliday = input.IsHoliday;
                    await _basHolidayRepo.UpdateAsync(info);
                }
            }
            return AppSrvResult();
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult> DeleteAsync(long id)
        {
            var info = await _basHolidayRepo.GetAsync(id);
            if (info != null)
            {
                info.IsDeleted = true;
                await _basHolidayRepo.UpdateAsync(info);
            }
            return AppSrvResult();


        }

        /// <summary>
        /// 获取所有数据
        /// </summary>
        /// <returns></returns>

        public async Task<List<BasHolidayDto>> GetAllAsync()
        {
            var holidays = _basHolidayRepo.Where(x => !x.IsDeleted);
            var users = _userRepo.Where(x => true);
            var results = await (from h in holidays
                                 join u in users on h.CreateBy equals u.Id
                                 select new BasHolidayDto
                                 {
                                     Id = h.Id,
                                     CurrentDate = h.CurrentDate,
                                     WeekName = h.WeekName,
                                     IsHoliday = h.IsHoliday,
                                     IsPlaned = h.IsPlaned,
                                     CreateBy = h.CreateBy,
                                     CreatName = u.Name,
                                     CreateTime = h.CreateTime
                                 }
                           ).ToListAsync();

            return results;
        }

        public async Task<List<string>> GetNonScheduleDateAsync(HolidayPagedDto search)
        {
            search.TrimStringFields();
            var whereExpression = ExpressionCreator
                                                 .New<BasHoliday>()
                                                 .And(x => !x.IsDeleted)
                                                 .And(p => p.IsPlaned == 0)
                                                 .AndIf(search.Start.HasValue, x => x.CurrentDate.Value.Date >= search.Start.Value.Date)
                                                 .AndIf(search.End.HasValue, x => x.CurrentDate.Value.Date <= search.End.Value.Date);

            var total = await _basHolidayRepo.CountAsync(whereExpression);
            if (total == 0)
                return new List<string>();
            var holidays = _basHolidayRepo.Where(whereExpression);
            var results = await holidays.Select(x => x.CurrentDate.Value.ToString("yyyy-MM-dd")).ToListAsync();
            return results;
        }

        public async Task<ResultJson> ImportAsync(List<ImportHolidayDto> infos)
        {
            infos.TrimStringFields();
            foreach (var item in infos)
            {
                var iscreate = await _basHolidayMgr.CreateAsync(item.CurrentDate);
                var o = new BasHoliday()
                {
                    Id = IdGenerater.GetNextId(),
                    CurrentDate = item.CurrentDate,
                    IsHoliday = item.IsHoliday == "是" ? 1 : 0,
                    IsPlaned = item.IsPlaned == "是" ? 1 : 0,
                    IsDeleted = false,
                    WeekName = GetWeekName(item.CurrentDate)
                };
                await _basHolidayRepo.InsertAsync(o);
            }
            return new ResultJson("导入成功");
        }

        public string GetWeekName(DateTime day)
        {
            string weekName = day.GetWeekNameOfDay();

            return weekName;
        }

        /// <summary>
        /// 删除全部
        /// </summary>
        /// <returns></returns>
        public async Task<AppSrvResult> DeleteAllAsync()
        {
            var infos = _basHolidayRepo.Where(x => true, noTracking: true);
            var info = await _basHolidayRepo.RemoveRangeAsync(infos);
            return AppSrvResult();


        }
    }
}
