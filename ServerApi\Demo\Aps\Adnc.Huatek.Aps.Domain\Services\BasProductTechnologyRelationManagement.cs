﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasProductTechnologyRelationAggregate;
using Adnc.Infra.Helper;
using Adnc.Shared.Rpc.Event;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using static StackExchange.Redis.Role;

namespace Adnc.Huatek.Aps.Domain.Services
{
    public class BasProductTechnologyRelationManagement : IDomainService
    {
        private readonly IEfBasicRepository<BasProductTechnologyRelation> _basProductTecRepo;

        public BasProductTechnologyRelationManagement(IEfBasicRepository<BasProductTechnologyRelation> basProductTecRepo) 
        {
            _basProductTecRepo = _basProductTecRepo;
        }
    }
}
