﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.SysTestAggregate;
using Adnc.Huatek.Aps.Domain.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services.Implements
{
    public class TestAppService : AbstractAppService, ITestAppService
    {
        private readonly  SysTestManager _sysTestMgr;
        private readonly IEfBasicRepository<SysTest> _sysTestRepo;

        public TestAppService(
            IEfBasicRepository<SysTest> sysTestRepo,
            SysTestManager sysTestMgr) 
        {
            _sysTestRepo = sysTestRepo;
            _sysTestMgr = sysTestMgr;
        }

        public async Task<List<SysTestDto>> GetAllAsync() 
        {
            var whereCondition = ExpressionCreator
                                           .New<SysTest>();
            var sysTests = await _sysTestRepo.Where(whereCondition).ToListAsync();

            var sysTestsDto = Mapper.Map<List<SysTestDto>>(sysTests);

            return sysTestsDto;
        }
    }
}
