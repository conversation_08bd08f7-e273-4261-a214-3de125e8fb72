﻿
using Adnc.Huatek.Aps.Domain.Aggregates.PartOrderAggregate;
using Adnc.Infra.Helper;
using Adnc.Shared.Rpc.Event;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using static StackExchange.Redis.Role;

namespace Adnc.Huatek.Aps.Domain.Services
{
    public class PartOrderManagement : IDomainService
    {
        private readonly IEfBasicRepository<PartOrder> _partOrderRepo;

        public PartOrderManagement(IEfBasicRepository<PartOrder> partOrderRepo) 
        {
            _partOrderRepo = partOrderRepo;
        }


        public virtual async Task<PartOrder> CreateAsync(string? OrderCode, string? ProductName,long? IdProduct,long? IdCustorm,decimal? Qty, long? IdTec, int? Status, DateTime? DeliveryTime, string? Remark,int? Level)
        {
            var exists = await _partOrderRepo.AnyAsync(x => x.OrderCode == OrderCode && x.IdProduct == IdProduct  && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"订单:{OrderCode}中的产品{ProductName}已创建生产订单，不可重复操作！");

            return new PartOrder()
            {
                Id= IdGenerater.GetNextId(),
                OrderCode = OrderCode,
                IdProduct = IdProduct,
                IdTec= IdTec,
                Qty = Qty,
                IdCustorm = IdCustorm,
                Status = Status,
                DeliveryTime= DeliveryTime,
                Remark= Remark,
                Level = Level
            };
        }

        public virtual async Task<bool> UpdateAsync(string? OrderCode, string? ProductName, long? IdProduct, long? id)
        {
            var exists = await _partOrderRepo.AnyAsync(x => x.OrderCode == OrderCode && x.IdProduct == IdProduct  && x.Id != id && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"订单:{OrderCode}中的产品{ProductName}已创建生产订单，不可重复操作！");

            exists = await _partOrderRepo.AnyAsync(x => x.Status != 1 && x.Id == id && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"订单:{OrderCode}中的产品{ProductName}的生产订单状态为已计划或已完成，不可操作修改！");

            return true;
        }
    }
}
