﻿using Adnc.Demo.Shared.Const;
using Adnc.Huatek.Aps.Api.ApsAlgorithmHelper;
using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Application.Services;
using Adnc.Huatek.Aps.Domain.Aggregates.BasNumberManagementAggregate;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Adnc.Huatek.Aps.Api.Controllers
{
    /// <summary>
    /// 编码管理
    /// </summary>
    [Route($"{RouteConsts.ApsRoot}/number")]
    [ApiController]
    public class BasNumberManagementController : AdncControllerBase
    {
        private readonly IBasNumberManagementService _basNumberManagementSrv;

        public BasNumberManagementController(IBasNumberManagementService basNumberManagementSrv)
        {
            _basNumberManagementSrv = basNumberManagementSrv;
        }

        /// <summary>
        /// 获取编码分页数据
        /// </summary>
        /// <param name="search"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("page")]
        public async Task<ActionResult<PageModelDto<BasNumberManagementDto>>> GetPagedAsync([FromBody] BasNumberManagementPagedDto search) => await _basNumberManagementSrv.GetPagedAsync(search);

        /// <summary>
        /// 新增
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("add")]
        public async Task<ActionResult<long>> CreateAsync([FromBody] BasNumberManagementDto input)
        => CreatedResult(await _basNumberManagementSrv.CreateAsync(input));


        /// <summary>
        /// 通过id 获取单个编码信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getbyid/{id}")]
        public async Task<ActionResult<BasNumberManagement>> GetByIdAsync([FromRoute] long id)
        => await _basNumberManagementSrv.GetByIdAsync(id);


        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("edit")]
        public async Task<ActionResult> UpdateAsync([FromBody] BasNumberManagementDto input)
        => Result(await _basNumberManagementSrv.UpdateAsync(input));


        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpDelete("delete/{id}")]
        public async Task<ActionResult> DeleteAsync([FromRoute] long id)
          => Result(await _basNumberManagementSrv.DeleteAsync(id));

        /// <summary>
        /// 通过 名称简写 获取最新编码
        /// </summary>
        /// <param name="simplename"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getNumberBySimpleName/{simplename}")]
        public async Task<string> GetNumberBySimpleName([FromRoute] string simplename) => await _basNumberManagementSrv.GetNumberBySimpleName(simplename);


    }
}
