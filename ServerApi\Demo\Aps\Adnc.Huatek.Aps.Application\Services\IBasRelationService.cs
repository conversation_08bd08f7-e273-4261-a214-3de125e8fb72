﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Infra.Redis.Caching.Core.Interceptor;
using Adnc.Shared.Application.Contracts.ResultModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services
{
   
    public interface IBasRelationService : IAppService
    {
        /// <summary>
        /// 获取单个资源关系搭建信息
        /// </summary>
        /// <returns></returns>
        [OperateLog(LogName = "获取指定资源关系搭建信息")]
        Task<BasRelationDto> GetByIdAsync(long id);

        /// <summary>
        /// 新增资源关系搭建
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [OperateLog(LogName = "新增资源关系搭建")]
        [UnitOfWork]
        Task<AppSrvResult<long>> CreateAsync(BasRelationDto input);

        /// <summary>
        /// 修改资源关系搭建
        /// </summary>
        /// <param name="id"></param>
        /// <param name="input"></param>
        /// <returns></returns>
        [OperateLog(LogName = "修改资源关系搭建")]
        [UnitOfWork]
        Task<AppSrvResult> UpdateAsync(BasRelationDto input);


        /// <summary>
        /// 删除资源关系搭建
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [OperateLog(LogName = "删除资源关系搭建")]
        [UnitOfWork]
        Task<AppSrvResult> DeleteAsync(long id);

        /// <summary>
        /// 获取用户列表
        /// </summary>
        /// <param name="search"></param>
        /// <returns></returns>
        Task<PageModelDto<BasRelationDto>> GetPagedAsync(RelationPagedDto search);

    }
}
