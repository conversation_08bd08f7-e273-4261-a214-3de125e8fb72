﻿using Adnc.Infra.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Aggregates.BasMaterial
{
     public class BasMaterial : EfFullAuditEntity
    {
        /// <summary>
        /// 物料类型
        /// </summary>
        public string? Mtype { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string? Unit { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 创建人名称
        /// </summary>
        public string? CreatedName { get; set; }

        /// <summary>
        /// 修改人名称
        /// </summary>
        public string? ModifyName { get; set; }
    }
}
