﻿using Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanRuleAggregate;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class PartSchedulePlanRuleConfig : AbstractEntityTypeConfiguration<PartSchedulePlanRule>
    {
        public override void Configure(EntityTypeBuilder<PartSchedulePlanRule> builder)
        {
            base.Configure(builder);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.ScheduleCode).HasColumnName("schedulecode");
            builder.Property(x => x.RuleType).HasColumnName("ruletype");
            builder.Property(x => x.RuleValue).HasColumnName("rulevalue");
            
            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.CreateBy).HasColumnName("createby");
            builder.Property(x => x.CreateTime).HasColumnName("createtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
           
        }
    }
}
