﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasWorksjop;

namespace Adnc.Huatek.Aps.Domain.Services
{

    public class BasWorksjopManager : IDomainService
    {
        private readonly IEfBasicRepository<BasWorksjop> _stationRepo;

        public BasWorksjopManager(IEfBasicRepository<BasWorksjop> stationRepo)
        {
            _stationRepo = stationRepo;
        }


        public virtual async Task<BasWorksjop> CreateAsync(string Code, string Name, string Remark)
        {
            var exists = await _stationRepo.AnyAsync(x => x.Code == Code && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"车间编码:{Code}已存在，不可重复添加！");

            exists = await _stationRepo.AnyAsync(x => x.Name == Name && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"车间名称:{Name}已存在，不可重复添加！");

            return new BasWorksjop()
            {
                Id = IdGenerater.GetNextId(),
                Name = Name,
                Code = Code,
                Remark = Remark
            };
        }

        public virtual async Task<bool> UpdateAsync(string Code, string Name, long id)
        {
            var exists = await _stationRepo.AnyAsync(x => x.Code == Code && x.Id != id && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"车间编码:{Code}已存在，不可重复添加！");

            exists = await _stationRepo.AnyAsync(x => x.Name == Name && x.Id != id && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"车间名称:{Name}已存在，不可重复添加！");

            return true;
        }
    }
}
