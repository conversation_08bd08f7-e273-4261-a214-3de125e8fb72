﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasClassesAggregate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class BasClassesConfig : AbstractEntityTypeConfiguration<BasClasses>
    {
        public override void Configure(EntityTypeBuilder<BasClasses> builder)
        {
            base.Configure(builder);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.Ccode).HasColumnName("ccode");
            builder.Property(x => x.Cname).HasColumnName("cname");
            builder.Property(x => x.ShortName).HasColumnName("shortname");
            builder.Property(x => x.Color).HasColumnName("color");
            builder.Property(x => x.Status).HasColumnName("status");
            builder.Property(x => x.BeginTime ).HasColumnName("begintime");
            builder.Property(x => x.EndTime).HasColumnName("endtime");
            builder.Property(x => x.Remark).HasColumnName("remark");
            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.CreateBy).HasColumnName("createby");
            builder.Property(x => x.CreateTime).HasColumnName("createtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
        }
    }
}
