﻿using Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulStepDetailsAggregate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class PlanProductionSchedulStepDetailsConfig : AbstractEntityTypeConfiguration<PlanProductionSchedulStepDetails>
    {
        public override void Configure(EntityTypeBuilder<PlanProductionSchedulStepDetails> builder)
        {
            base.Configure(builder);

            builder.HasKey(x => x.Id);
            builder.Property(x => x.PlanCode).HasColumnName("plancode");
            builder.Property(x => x.TaskCode).HasColumnName("taskcode");
            builder.Property(x => x.StepCode).HasColumnName("stepcode");
            builder.Property(x => x.SchedulCode).HasColumnName("schedulcode");
            builder.Property(x => x.SchedulDate).HasColumnName("scheduldate");
            builder.Property(x => x.Remark).HasColumnName("remark");
            builder.Property(x => x.Exenum).HasColumnName("exenum");

            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.CreateBy).HasColumnName("createby");
            builder.Property(x => x.CreateTime).HasColumnName("createtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
            builder.Property(x => x.RemainingTime).HasColumnName("remainingtime");
        }
    }
}
