﻿using Adnc.Demo.Shared.Const;
using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Application.Services;
using Microsoft.AspNetCore.Authorization;
using Npoi.Mapper;

namespace Adnc.Huatek.Aps.Api.Controllers
{
    [Route($"{RouteConsts.ApsRoot}/material")]
    [ApiController]
    public class BasMaterialController : AdncControllerBase
    {
        private readonly IBasMaterialService _stationSrv;

        public BasMaterialController(IBasMaterialService stationSrv) => _stationSrv = stationSrv;

        /// <summary>
        /// 获取所有信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getallasync")]
        public async Task<ActionResult<List<BasMaterialDto>>> GetAllAsync() => await _stationSrv.GetAllAsync();



        /// <summary>
        /// 新增物料
        /// </summary>
        /// <param name="input">物料信息</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("createasync")]
        public async Task<ActionResult<long>> CreateAsync([FromBody] BasMaterialDto input)
            => CreatedResult(await _stationSrv.CreateAsync(input));

        /// <summary>
        /// 删除物料
        /// </summary>
        /// <param name="id">物料ID</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpDelete("deleteasync/{id}")]
        public async Task<ActionResult> DeleteAsync([FromRoute] long id)
            => Result(await _stationSrv.DeleteAsync(id));


        /// <summary>
        /// 修改物料
        /// </summary>
        /// <param name="id">id</param>
        /// <param name="input">物料信息</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPut("updateasync")]
        public async Task<ActionResult> UpdateAsync([FromBody] BasMaterialDto input)
            => Result(await _stationSrv.UpdateAsync(input));



        /// <summary>
        /// 变更物料状态
        /// </summary>
        /// <param name="id">物料ID</param>
        /// <param name="status">状态</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPut("batch/changestatus/{id}")]
        public async Task<ActionResult> ChangeStatus([FromRoute] long id, [FromBody] SimpleDto<int> status)
            => Result(await _stationSrv.ChangeStatusAsync(id, status.Value));

        /// <summary>
        /// 批量变更物料状态
        /// </summary>
        /// <param name="input">物料Ids与状态</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPut("changestatus")]
        public async Task<ActionResult> ChangeStatus([FromBody] UpdateStatusDto input)
            => Result(await _stationSrv.ChangeStatusAsync(input));

        /// <summary>
        /// 获取物料列表分页
        /// </summary>
        /// <param name="search">查询条件</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("page")]
        public async Task<ActionResult<PageModelDto<BasMaterialDto>>> GetPagedAsync([FromBody] BasMaterialPagedDto search)
            => await _stationSrv.GetPagedAsync(search);

        /// <summary>
        /// 获取单个物料详情信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("current/{id}")]
        public async Task<ActionResult<ResultJson>> GetCurrentUserInfoAsync(long id) => await _stationSrv.GetAppointAsync(id);


        /// <summary>
        /// 批量启用
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPut("changestatus/{status}")]
        public async Task<ActionResult> MakeEnableAsync([FromRoute] int status, [FromBody] List<long> data)
          => Result(await _stationSrv.MakeEnableAsync(status, data));




        /// <summary>
        /// 新增物料
        /// </summary>
        /// <param name="input">物料信息</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("importasync")]
        public async Task<ResultJson> ImportAsync(IFormFile formFile)
        {
            //通过上传文件流初始化Mapper
            var mapper = new Mapper(formFile.OpenReadStream());
            //读取sheet1的数据
            var devices = mapper.Take<BasMaterialDto>("Sheet1").Select(i => i.Value).ToList();
            return await _stationSrv.ImportAsync(devices);
        }

    }
}
