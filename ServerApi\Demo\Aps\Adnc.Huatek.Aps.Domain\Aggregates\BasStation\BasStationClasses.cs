﻿using Adnc.Infra.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Aggregates.BasStation
{
    /// <summary>
    /// 工位与班次关系表
    /// </summary>
    public class BasStationClasses: EfEntity
    {
        public long Id { get; set; }
        /// <summary>
        /// 工位id
        /// </summary>
        public long StationId { get; set; }
        /// <summary>
        /// 工位编码
        /// </summary>
        public string? StationCode { get; set; }
        /// <summary>
        /// 班次id
        /// </summary>
        public long ClassesId { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public int Status { get; set; }
        /// <summary>
        /// 工作时长(h)
        /// </summary>
        public int? Duration { get; set; }
        /// <summary>
        /// 人员数量
        /// </summary>
        public int? Qty { get; set; }
    }
}
