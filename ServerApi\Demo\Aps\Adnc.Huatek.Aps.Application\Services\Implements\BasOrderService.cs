﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.BasBomAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasMaterial;
using Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.SysUserAggregate;
using Adnc.Huatek.Aps.Domain.Services;
using Adnc.Infra.IdGenerater.Yitter;
using Adnc.Shared;
using Adnc.Shared.Application.Contracts.ResultModels;
using Microsoft.AspNetCore.Mvc;
using OfficeOpenXml;
using LicenseContext = OfficeOpenXml.LicenseContext;

namespace Adnc.Huatek.Aps.Application.Services.Implements
{
    public class BasOrderService : AbstractAppService, IBasOrderService
    {
        private readonly BasOrderManagement _basOrderMgr;
        private readonly IEfBasicRepository<BasOrder> _basOrderRepo;
        private readonly IEfBasicRepository<BasOrderProduct> _basOrderProductRepo;
        private readonly IEfBasicRepository<BasProduct> _basProductRepo;
        private readonly IEfBasicRepository<SysUser> _sysUserRepo;
        private readonly BasNumberManagementService _basNumberManagementService;
        private readonly IEfBasicRepository<BasBom> _basbomRepo;
        private readonly IEfBasicRepository<BasMaterial> _basMaterialRepo;
        private readonly IEfBasicRepository<PartPlanPending> _pengdingPlanRepo;
        private readonly IEfBasicRepository<PlanProductionSchedule> _planRepo;

        public BasOrderService(
            IEfBasicRepository<BasOrder> basOrderRepo,
            IEfBasicRepository<BasOrderProduct> basOrderProductRepo,
            IEfBasicRepository<BasProduct> basProductRepo,
            IEfBasicRepository<SysUser> sysUserRepo,
            BasOrderManagement basOrderMgr,
            BasNumberManagementService basNumberManagementService,
            IEfBasicRepository<BasBom> basbomRepo,
            IEfBasicRepository<BasMaterial> basMaterialRepo,
            IEfBasicRepository<PartPlanPending> pengdingPlanRepo,
            IEfBasicRepository<PlanProductionSchedule> planRepo)
        {
            _basOrderMgr = basOrderMgr;
            _basOrderRepo = basOrderRepo;
            _basOrderProductRepo = basOrderProductRepo;
            _basProductRepo = basProductRepo;
            _sysUserRepo = sysUserRepo;
            _basNumberManagementService = basNumberManagementService;
            _basbomRepo = basbomRepo;
            _basMaterialRepo = basMaterialRepo;
            _planRepo = planRepo;
            _pengdingPlanRepo = pengdingPlanRepo;
        }

        /// <summary>
        /// 获取分页列表
        /// </summary>
        /// <param name="search"></param>
        /// <returns></returns>
        public async Task<PageModelDto<BasOrderDto>> GetPagedAsync(BasOrderPagedDto search)
        {
            search.TrimStringFields();
            var whereExpression = ExpressionCreator
                                                .New<BasOrder>()
                                                .And(x => !x.IsDeleted)
                                                 .AndIf(search.SaleNumber.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.SaleNumber!, $"%{search.SaleNumber}%"))
                                                .AndIf(search.OrderNumber.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.OrderNumber!, $"%{search.OrderNumber}%"))
                                                .AndIf(search.UserName.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.UserName!, $"%{search.UserName}%"));

            var orders = _basOrderRepo.Where(whereExpression)
                                            .OrderByDescending(x => x.CreateTime);
            var pwhere = ExpressionCreator.New<BasOrderProduct>()
                                                .And(x => !x.IsDeleted)
                                                .AndIf(search.Status > -1, x => x.Status == search.Status);
            var products = _basOrderProductRepo.Where(pwhere);
            var proWhere = ExpressionCreator.New<BasProduct>()
                                                .And(x => !x.IsDeleted)
                                                 .AndIf(search.ProductCode.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Procode!, $"%{search.ProductCode}%"))
                                                .AndIf(search.ProductName.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Proname!, $"%{search.ProductName}%"));
            var baseProducts = _basProductRepo.Where(proWhere);
            var users = _sysUserRepo.Where(x => true);
            var entities = (from order in orders
                            join product in products on order.Id equals product.OrderId
                            join baseproduct in baseProducts on product.ProductCode equals baseproduct.Procode
                            join u in users on order.CreateBy equals u.Id
                            select new BasOrderDto
                            {
                                Id = order.Id,
                                IdDetail = product.Id,
                                Priority = order.Priority,
                                SaleNumber = order.SaleNumber,
                                OrderNumber = order.OrderNumber,
                                UserName = order.UserName,
                                ProductName = baseproduct.Proname,
                                ProductCode = product.ProductCode,
                                Status = product.Status,
                                Quantity = product.Quantity,
                                PendingNum = product.PendingNum,
                                DeliveryDate = order.DeliveryDate,
                                CreatName = u.Name,
                                CreateTime = order.CreateTime
                            }
                       );
            var totalCount = entities.Count();
            var result = entities.OrderByDescending(x => x.CreateTime).ThenBy(x => x.DeliveryDate).Skip(search.SkipRows()).Take(search.PageSize).ToList();

            return new PageModelDto<BasOrderDto>(search, result, totalCount);
        }

        /// <summary>
        /// 获取所有数据
        /// </summary>
        /// <returns></returns>

        public async Task<ResultJson> GetAllAsync(BasOrderPagedDto search)
        {
            search.PageIndex = 1;
            search.PageSize = int.MaxValue;
            search.TrimStringFields();
            var whereExpression = ExpressionCreator
                                                .New<BasOrder>()
                                                .And(x => !x.IsDeleted)
                                                 .AndIf(search.SaleNumber.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.SaleNumber!, $"%{search.SaleNumber}%"))
                                                .AndIf(search.OrderNumber.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.OrderNumber!, $"%{search.OrderNumber}%"))
                                                .AndIf(search.UserName.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.UserName!, $"%{search.UserName}%"));

            var orders = _basOrderRepo.Where(whereExpression)
                                            .OrderByDescending(x => x.CreateTime);
            var pwhere = ExpressionCreator.New<BasOrderProduct>()
                                                 .And(x => !x.IsDeleted)
                                                 .AndIf(search.Status > -1, x => x.Status == search.Status);
            var products = _basOrderProductRepo.Where(pwhere);
            var proWhere = ExpressionCreator.New<BasProduct>()
                                               .And(x => !x.IsDeleted)
                                                .AndIf(search.ProductCode.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Procode!, $"%{search.ProductCode}%"))
                                               .AndIf(search.ProductName.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Proname!, $"%{search.ProductName}%"));
            var baseProducts = _basProductRepo.Where(proWhere);

            var users = _sysUserRepo.Where(x => true);
            var entities = (from order in orders
                            join product in products on order.Id equals product.OrderId
                            join pros in baseProducts on product.ProductId equals pros.Id
                            join u in users on order.CreateBy equals u.Id
                            select new BasOrderDto
                            {
                                Id = order.Id,
                                Priority = order.Priority,
                                SaleNumber = order.SaleNumber,
                                OrderNumber = order.OrderNumber,
                                UserName = order.UserName,
                                ProductCode = pros.Procode,
                                ProductName = pros.Proname,
                                Status = product.Status,
                                Quantity = product.Quantity,
                                PendingNum = product.PendingNum,
                                DeliveryDate = order.DeliveryDate,
                                CreatName = u.Name,
                                CreateTime = order.CreateTime
                            }

                       ).ToList();

            return new ResultJson("查询成功", entities);
        }

        /// <summary>
        /// 创建订单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult<long>> CreateAsync(BasOrderDto input)
        {
            return 0;
            //input.TrimStringFields();

            //var step = await _basOrderMgr.CreateAsync(input);

            //await _basOrderRepo.InsertAsync(step);

            //return step.Id;
        }

        /// <summary>
        /// 更新订单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult> UpdateAsync(BasOrderDto input)
        {
            bool isUpdete = await _basOrderMgr.UpdateAsync(input.Status, input.Priority, input.Id ?? 0);
            if (isUpdete)
            {
                var order = await _basOrderRepo.GetAsync(input.Id ?? 0);
                if (order != null)
                {
                    order.Status = input.Status;
                    order.Priority = input.Priority;
                    await _basOrderRepo.UpdateAsync(order);

                    var products = _basOrderProductRepo.Where(x => x.OrderId == order.Id, noTracking: false);
                    foreach (var item in products)
                    {
                        item.Status = 2;
                    }
                    await _basOrderProductRepo.UpdateRangeAsync(products);
                }
            }
            return AppSrvResult();
        }

        /// <summary>
        /// 删除订单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult> DeleteAsync(long id)
        {
            var step = await _basOrderRepo.GetAsync(id);
            if (step != null)
            {
                step.IsDeleted = true;
                await _basOrderRepo.UpdateAsync(step);
            }
            return AppSrvResult();
        }

        /// <summary>
        /// 根据ID获取订单数据
        /// </summary>
        /// <returns></returns>

        public async Task<ResultJson> GetByIdAsync(long id)
        {
            var step = await _basOrderRepo.GetAsync(id);

            var stepDto = Mapper.Map<BasOrderDto>(step);

            return new ResultJson("查询成功", stepDto);
        }

        /// <summary>
        /// 初始化新增生产计划页面
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<PartPlanPendingDto> GetDetailsByIdAsync(long id)
        {
            var order = await _basOrderRepo.GetAsync(id);
            if (order == null)
            {
                return null;
            }
            var orderProducts = _basOrderProductRepo.Where(x => !x.IsDeleted && x.OrderId == id).ToList();
            var basProducts = _basProductRepo.Where(x => true);

            var results = (from op in orderProducts
                           join p in basProducts on op.ProductId equals p.Id
                           select new BasOrderProductDto()
                           {
                               ProductCode = op.ProductCode,
                               ProductName = p.Proname ?? String.Empty,
                               Qty = op.PendingNum
                           }).ToList();
            var entityDto = new PartPlanPendingDto()
            {
                Priority = order.Priority,
                OrderId = order.Id,
                OrderNumber = order.OrderNumber,
                DeliveryDate = order.DeliveryDate,
                UserName = order.UserName
            };
            if (results.Count() == 1)
            {
                entityDto.Qty = results.Select(x => x.Qty).FirstOrDefault();
            }
            entityDto.ProductList = results;

            return entityDto;
        }

        /// <summary>
        /// 批量更新订单为已完成
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult> FinishedAsync(FinishedOrders input)
        {
            var orderIds = input.Orders.Select(x => x.Id ?? 0).ToList();
            bool isUpdete = await _basOrderMgr.FinishedAsync(orderIds);
            if (isUpdete)
            {
                var orders = _basOrderRepo.Where(x => orderIds.Contains(x.Id), noTracking: false);
                if (orders.Any())
                {
                    foreach (var o in orders)
                    {
                        o.Status = 2;
                        await _basOrderRepo.UpdateAsync(o);
                    }
                }
            }
            return AppSrvResult();
        }

        /// <summary>
        /// 导入订单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<ResultJson> ImportOrdersAsync(List<ImportOrderDto> orders)
        {
            orders.TrimStringFields();
            var groupOrders = orders.GroupBy(x => x.SaleNumber);
            var pnames = orders.Select(x => x.ProductName).ToList();
            var saleNumbers = orders.Select(x => x.SaleNumber).ToList();
            var basProducts = await _basOrderMgr.CreateAsync(pnames, saleNumbers);
            long orderId = 0;
            try
            {
                foreach (var order in groupOrders)
                {
                    var autoCode = _basNumberManagementService.GetNumberBySimpleName(CommonConst.ORDERNUMBER).Result;
                    var firstProduct = order.FirstOrDefault();
                    var o = new BasOrder()
                    {
                        Id = IdGenerater.GetNextId(),
                        OrderNumber = autoCode,
                        SaleNumber = order.Key,
                        DeliveryDate = order.Max(x => x.DeliveryDate),
                        UserName = firstProduct.UserName,
                        Priority = GetPriority(firstProduct.Priority),
                        Status = 0
                    };
                    orderId = o.Id;
                    await _basOrderRepo.InsertAsync(o);
                    List<BasOrderProduct> pList = new List<BasOrderProduct>();
                    foreach (var item in order)
                    {
                        var pro = basProducts.Where(x => x.Proname == item.ProductName).FirstOrDefault();
                        var product = new BasOrderProduct()
                        {
                            Id = IdGenerater.GetNextId(),
                            OrderId = o.Id,
                            ProductId = pro.Id,
                            ProductCode = pro.Procode,
                            Quantity = item.ProductQty,
                            PendingNum = item.ProductQty
                        };
                        pList.Add(product);
                    }
                    await _basOrderProductRepo.InsertRangeAsync(pList);
                }
            }
            catch (Exception e)
            {
                var order = GetByIdAsync(orderId);
                if (order != null)
                {
                    await DeleteAsync(orderId);
                    var orderProducts = _basOrderProductRepo.Where(x => x.OrderId == orderId).ToList();
                    _basOrderProductRepo.RemoveRangeAsync(orderProducts);
                }
            }
            return new ResultJson("导入成功");
        }

        public int GetPriority(string p)
        {
            int priority = 0;
            switch (p)
            {
                case "一般": priority = PriorityConst.Normal; break;
                case "紧急": priority = PriorityConst.Urgent; break;
                case "非常紧急": priority = PriorityConst.ExtremelyUrgent; break;
                default:
                    priority = 0;
                    break;
            }
            return priority;
        }

        public string GetPriorityString(int p)
        {
            string priority = string.Empty;
            switch (p)
            {
                case PriorityConst.Normal: priority = "一般"; break;
                case PriorityConst.Urgent: priority = "紧急"; break;
                case PriorityConst.ExtremelyUrgent: priority = "非常紧急"; break;
                default:
                    priority = "一般";
                    break;
            }
            return priority;
        }

        public string GetStatusString(int p)
        {
            string status = string.Empty;
            switch (p)
            {
                case 0: status = "待计划"; break;
                case 1: status = "计划中"; break;
                case 2: status = "已完成"; break;
                default:
                    status = "待计划";
                    break;
            }
            return status;
        }

        /// <summary>
        /// 导出列表到excel文件
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="data">需要导出的列表数据</param>
        /// <param name="headers">需要自定义的字段和表头值</param>
        /// <returns></returns>
        public MemoryStream ExportOrders(BasOrderPagedDto search)
        {
            search.PageIndex = 1;
            search.PageSize = int.MaxValue;
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            var orders = GetPagedAsync(search).Result.Data;
            using (var package = new ExcelPackage())
            {
                var workSheet = package.Workbook.Worksheets.Add("orders");
                //workSheet.Cells.LoadFromCollection(orders, true);
                var headerList = new List<string>
                {
                    "优先级",
                    "生产状态",
                    "销售单号",
                    "订单号",
                    "交付日期",
                    "产品名称",
                    "数量",
                    "客户",
                    "创建人",
                    "创建时间"
                };
                for (var i = 0; i < headerList.Count; i++)
                {
                    workSheet.Cells[1, i + 1].Value = headerList[i];
                }

                int row = 2;
                foreach (var item in orders)
                {
                    int col = 1;
                    workSheet.Cells[row, col++].Value = GetPriorityString(item.Priority);
                    workSheet.Cells[row, col++].Value = GetStatusString(item.Status);
                    workSheet.Cells[row, col++].Value = item.SaleNumber;
                    workSheet.Cells[row, col++].Value = item.OrderNumber;
                    workSheet.Cells[row, col++].Value = item.DeliveryDate;
                    workSheet.Cells[row, col++].Value = item.ProductName;
                    workSheet.Cells[row, col++].Value = item.Quantity;
                    workSheet.Cells[row, col++].Value = item.UserName;
                    workSheet.Cells[row, col++].Value = item.CreatName;
                    workSheet.Cells[row, col++].Value = item.CreateTime.ToString("yyyy-MM-dd HH:mm:ss");
                    row++;
                }

                return new MemoryStream(package.GetAsByteArray());
            }
        }

        /// <summary>
        /// 初始化订单详情页面
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ActionResult<OrderDetailDto>> GetOrderDetailAsync(long id)
        {
            var entityDto = new OrderDetailDto();
            var orderEntity = await _basOrderRepo.Where(x => !x.IsDeleted && x.Id == id).FirstOrDefaultAsync();
            if (orderEntity != null)
            {
                entityDto.OrderNumber = orderEntity.OrderNumber;
                entityDto.DeliveryDate = orderEntity.DeliveryDate;
                entityDto.UserName = orderEntity.UserName;
                entityDto.Priority = orderEntity.Priority;
            }
            var pengdingPlans = await _pengdingPlanRepo.Where(x => true).ToListAsync();
            var plans = await _planRepo.Where(x => true).ToListAsync();
            var orders = await _basOrderRepo.Where(x => !x.IsDeleted).ToListAsync();

            var entity = orders
                // 1. 左连接 PendingPlan（优化为显式左连接）
                .GroupJoin(
                    pengdingPlans.Where(p => p.PlanCode != null),  // 提前过滤无效 PlanCode
                    order => order.Id,
                    pending => pending.OrderId,
                    (order, pendingGroup) => new { order, pendingGroup }
                )
                // 2. 展开左连接结果
                .SelectMany(
                    temp => temp.pendingGroup.DefaultIfEmpty(),
                    (temp, pending) => new { temp.order, pending }
                )
                // 3. 左连接 Plan（直接关联 PlanCode）
                .GroupJoin(
                    plans,
                    temp => temp.pending?.PlanCode,  // 处理 pending 为 null 的情况
                    plan => plan.PlanCode,
                    (temp, planGroup) => new { temp.order, temp.pending, planGroup }
                )
                // 4. 展开左连接结果
                .SelectMany(
                    temp => temp.planGroup.DefaultIfEmpty(),
                    (temp, plan) => new { temp.order, plan }
                )
                // 5. 映射 DTO（空值保护）
                .Select(temp => new OrderDetailDto
                {
                    Status = temp.plan?.Status ?? 0,              // 默认值处理
                    OrderNumber = temp.order.OrderNumber,
                    DeliveryDate = temp.order.DeliveryDate,
                    UserName = temp.order.UserName,
                    Priority = temp.order.Priority,
                    PlanStartDate = temp.plan?.PlanStart,         // 使用 null 安全访问
                    PlanEndDate = temp.plan?.PlanEnd
                })
                // 6. 优化 FirstOrDefault 逻辑
                .FirstOrDefault();

            //var entity = (from order in orders
            //              join pengding in pengdingPlans on order.Id equals pengding.OrderId into pengdingPlan
            //              from pp in pengdingPlan.DefaultIfEmpty()
            //              where pp.PlanCode != null
            //              join plan in plans on pp.PlanCode equals plan.PlanCode into ps
            //              from p in ps.DefaultIfEmpty()
            //              select new OrderDetailDto()
            //              {
            //                  Status = p.Status,
            //                  OrderNumber = order.OrderNumber,
            //                  DeliveryDate = order.DeliveryDate,
            //                  UserName = order.UserName,
            //                  Priority = order.Priority,
            //                  PlanStartDate = p.PlanStart,
            //                  PlanEndDate = p.PlanEnd
            //              }).FirstOrDefault();

            entityDto.Status = entity?.Status ?? default;
            entityDto.PlanStartDate = entity?.PlanStartDate;
            entityDto.PlanEndDate = entity?.PlanEndDate;

            entityDto.PriorityString = GetPriorityString(entityDto.Priority);
            entityDto.StatusString = GetStatusString(entityDto.Status ?? 0);

            var orderProducts = _basOrderProductRepo.Where(x => !x.IsDeleted && x.OrderId == id).ToList();
            var basProducts = _basProductRepo.Where(x => true);
            var basboms = _basbomRepo.Where(x => true);
            var materials = _basMaterialRepo.Where(x => true);
            var results = (from op in orderProducts
                           join p in basProducts on op.ProductId equals p.Id
                           join bom in basboms on p.Procode equals bom.ProductCode
                           join m in materials on bom.MaterialCode equals m.Code
                           select new
                           {
                               ProductName = p.Proname ?? String.Empty,
                               MaterialName = m.Name,
                               Qty = bom.Qty
                           }).ToList();
            var mlsit = new List<BasProductMaterialDto>();
            foreach (var item in results.GroupBy(x => x.ProductName))
            {
                var mdto = new BasProductMaterialDto()
                {
                    ProductName = item.Key
                };
                foreach (var m in item)
                {
                    var mertial = new MaterialDto()
                    {
                        MaterialName = m.MaterialName,
                        Qty = m.Qty ?? 0
                    };
                    mdto.Materials.Add(mertial);
                }
                mlsit.Add(mdto);
            }
            entityDto.MaterialList.AddRange(mlsit);
            return entityDto;
        }
    }
}