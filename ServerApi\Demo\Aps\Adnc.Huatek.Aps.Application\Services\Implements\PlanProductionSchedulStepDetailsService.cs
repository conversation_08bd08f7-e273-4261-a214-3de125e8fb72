﻿using Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulStepDetailsAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.SysUserAggregate;
using Adnc.Huatek.Aps.Domain.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services.Implements
{
    public class PlanProductionSchedulStepDetailsService : AbstractAppService, IPlanProductionSchedulStepDetailsService
    {
        private readonly PlanProductionSchedulStepDetailsManagement   _planProductionSchedulStepDetailsMgr;
        private readonly IEfBasicRepository<PlanProductionSchedulStepDetails> _planProductionSchedulStepDetailsRepo;
        private readonly IEfBasicRepository<SysUser> _sysUserRepo;

        public PlanProductionSchedulStepDetailsService(PlanProductionSchedulStepDetailsManagement planProductionSchedulStepDetailsMgr,
           IEfBasicRepository<PlanProductionSchedulStepDetails> planProductionSchedulStepDetailsRepo,
           IEfBasicRepository<SysUser> sysUserRepo) 
        {
            _planProductionSchedulStepDetailsMgr = planProductionSchedulStepDetailsMgr;
            _planProductionSchedulStepDetailsRepo = planProductionSchedulStepDetailsRepo;
            _sysUserRepo = sysUserRepo;
        }
    }
}
