﻿using Adnc.Demo.Shared.Const;
using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Application.Services;
using Adnc.Shared.Application.Contracts.ResultModels;
using Adnc.Shared.WebApi.Authorization;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Adnc.Huatek.Aps.Api.Controllers
{
    [Route($"{RouteConsts.ApsRoot}/order")]
    [ApiController]
    public class PartOrderController : AdncControllerBase
    {
        private readonly IPartOrderService _orderSrv;

        public PartOrderController(IPartOrderService orderSrv) => _orderSrv = orderSrv;

        /// <summary>
        /// 获取订单分页信息
        /// </summary>
        /// <returns></returns>
        [HttpPost("page")]
        public async Task<ActionResult<PageModelDto<PartOrderDto>>> GetPagedAsync([FromBody] PartOrderPagedDto search) => await _orderSrv.GetPagedAsync(search);

        /// <summary>
        /// 新增订单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("add")]
        public async Task<ActionResult<long>> CreateAsync([FromBody] PartOrderDto input)
           => CreatedResult(await _orderSrv.CreateAsync(input));

        /// <summary>
        /// 更新订单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("edit")]
        public async Task<ActionResult> UpdateAsync([FromBody] PartOrderDto input)
           => Result(await _orderSrv.UpdateAsync(input));


        /// <summary>
        /// 删除订单
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpDelete("delete/{id}")]
        public async Task<ActionResult> DeleteAsync([FromRoute] long id)
          => Result(await _orderSrv.DeleteAsync(id));

        /// <summary>
        /// 根据ID获取订单
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getbyid/{id}")]
        public async Task<ActionResult<PartOrderDto>> GetByIdAsync([FromRoute] long id)
            => await _orderSrv.GetByIdAsync(id);

    }
}
