﻿using Adnc.Infra.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Aggregates.BasHolidayAggregate
{
    /// <summary>
    /// 假期时间管理
    /// </summary>
    public class BasHoliday : EfFullAuditEntity
    {
        public long? Id { get; set; }
        /// <summary>
        /// 日期
        /// </summary>
        public DateTime? CurrentDate { get; set; }
        /// <summary>
        /// 是否工作日
        /// </summary>
        public int IsHoliday { get; set; }
        /// <summary>
        /// 是否排产
        /// </summary>

        public int IsPlaned { get; set; }
        /// <summary>
        /// 星期名称
        /// </summary>
        public string WeekName { get; set; }
      


    }
}
