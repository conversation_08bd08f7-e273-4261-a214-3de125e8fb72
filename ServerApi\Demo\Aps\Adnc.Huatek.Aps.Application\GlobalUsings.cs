﻿global using Adnc.Infra.IRepositories;
global using Adnc.Infra.Redis.Caching;
global using Adnc.Shared.Application.Caching;
global using Adnc.Shared.Application.Contracts.Attributes;
global using Adnc.Shared.Application.Contracts.Dtos;
global using Adnc.Shared.Application.Contracts.Interfaces;
global using Adnc.Shared.Application.Services;
global using Adnc.Shared.Application.Services.Trackers;
global using Adnc.Demo.Shared.Const;
global using Adnc.Shared.Domain;
global using Adnc.Shared.Rpc.Event;
global using Adnc.Demo.Shared.Rpc.Http.Services;
global using AutoMapper;
global using DotNetCore.CAP;
global using Microsoft.EntityFrameworkCore;
global using Microsoft.Extensions.DependencyInjection;
global using Microsoft.Extensions.Logging;
global using MongoDB.Driver;
global using System.Linq.Expressions;
global using System.Reflection;
