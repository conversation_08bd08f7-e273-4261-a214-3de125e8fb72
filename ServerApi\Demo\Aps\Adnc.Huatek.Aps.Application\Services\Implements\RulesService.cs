﻿using Adnc.Huatek.Aps.Domain.Comm.Rules;
using NRules;
using NRules.Fluent;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services.Implements
{
    public class RulesService
    {
        public void Test()
        {
            var repository = new RuleRepository();
            repository.Load(x => x.From(typeof(ProductionRule).Assembly));

            var factory = repository.Compile();
            var session = factory.CreateSession();

            // 添加要匹配的订单
            var order = new ProductionOrder { OrderId = 1, Status = OrderStatusNew.Pending };
            session.Insert(order);

            // 触发规则引擎执行
            session.Fire();
        }
        /// <summary>
        /// 算法思路
        /// 不考虑物料
        /// 核心限制因素 工序：结批量、消耗时间周期
        /// </summary>
        public void Test2()
        {
            // 获取工艺路线=> 获取工序>
            var tList=new List<dynamic>();// 所有工序
        }
    }
}
