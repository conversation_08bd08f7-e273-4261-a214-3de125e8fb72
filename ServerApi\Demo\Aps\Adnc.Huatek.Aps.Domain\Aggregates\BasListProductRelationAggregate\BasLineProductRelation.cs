﻿using Adnc.Infra.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Aggregates.BasLineProductRelationAggregate
{
    public class BasLineProductRelation : EfFullAuditEntity
    {
        /// <summary>
        /// ID
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 产线ID
        /// </summary>
        public string? LineCode { get; set; }

        /// <summary>
        /// 产品ID
        /// </summary>
        public string? ProductCode { get; set; }

        /// <summary>
        /// 默认产能
        /// </summary>
        public decimal? Capacity { get; set; }

        /// <summary>
        /// 产能单位
        /// </summary>
        public string? Unit { get; set; }







    }


}
