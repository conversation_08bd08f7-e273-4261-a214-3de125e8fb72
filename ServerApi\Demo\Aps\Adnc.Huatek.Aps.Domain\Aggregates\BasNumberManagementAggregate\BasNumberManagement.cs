﻿using Adnc.Infra.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Aggregates.BasNumberManagementAggregate
{
    public class BasNumberManagement : EfFullAuditEntity
    {
        public long Id { get; set; }

        public string Name { get; set; }
        public string? SimpleName { get; set; }
        public string? CurrentNumber { get; set; }
        public string? StartNumber { get; set; }
        public string? IdentityNumber { get; set; }
        public string? IsContainerDate { get; set; }

        public string? TimeFormat { get; set; }
        public string? Description { get; set; }
        public long NumberFormat { get; set; }
        public string? StartTarget { get; set; }
        public string? IsRound { get; set; }

    }
}
