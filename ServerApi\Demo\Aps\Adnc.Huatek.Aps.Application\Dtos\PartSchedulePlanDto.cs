﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanMaterialAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanSourceAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanStepAggregate;

namespace Adnc.Huatek.Aps.Application.Dtos
{
    /// <summary>
    /// 生产计划
    /// </summary>
    public class PartSchedulePlanDto : Dto
    {

        /// <summary>
        /// 排产计划编码
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 排产计划编码
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 生产计划编码
        /// </summary>
        public string? PlanCode { get; set; }

        /// <summary>
        /// 产品
        /// </summary>
        public string? ProductId { get; set; }

        /// <summary>
        /// 产品
        /// </summary>
        public string? Tcode { get; set; }


        /// <summary>
        /// 产品
        /// </summary>
        public string? Tname { get; set; }

        /// <summary>
        /// 产品
        /// </summary>
        public string? ProCode { get; set; }

        /// <summary>
        /// 产品
        /// </summary>
        public string? ProName { get; set; }

        /// <summary>
        /// 产品
        /// </summary>
        public string? UserName { get; set; }
        /// <summary>
        /// 产品
        /// </summary>
        public string? OrderNum { get; set; }

        public string? ScheduleResult { get; set; }

        /// <summary>
        /// 资源冲突结果
        /// </summary>
        public string? SourceConflict { get; set; }

        /// <summary>
        /// 生产数量
        /// </summary>
        public decimal Qty { get; set; }

        /// <summary>
        /// 开始排产时间
        /// </summary>
        public DateTime BeginDate { get; set; }


        /// <summary>
        /// delivery time
        /// </summary>
        public DateTime DtDelivery { get; set; }


        /// <summary>
        /// 排产计划编码
        /// </summary>
        public DateTime PlanBeginDate { get; set; }

        /// <summary>
        /// 生产计划编码
        /// </summary>
        public DateTime PlanEndDate { get; set; }

        /// <summary>
        /// 计划状态
        /// </summary>
        public int? State { get; set; }

        /// <summary>
        /// 计划状态
        /// </summary>
        public String? StateDis { get; set; }

        public int? Priority { get; set; }

        public string? PriorityDis { get; set; }

        /// <summary>
        /// 来源
        /// </summary>
        public int? ScheduleType { get; set; }
        /// <summary>
        /// 来源
        /// </summary>
        public string? ScheduleTypeDis { get; set; }
        /// <summary>
        /// 排产规则
        /// </summary>

        public List<PartSchedulePlanRuleDto>? Rules { get; set; }
        public List<PartSchedulePlanStepDto>? Steps { get; set; }

        public BasProductDto? ProductModule { get; set; }
    }

    

    public class ScheduleSourceWarningDto
    {
        public string StepCode { get; set; }
        public string StepName { get; set; }
        public string SourceCode { get; set; }
        public string SourceName { get; set; }
        public string WarningPlanCode { get; set; }
        public string WarningScheduleCode { get; set; }
        public string WarningStepCode { get; set; }
        public string WarningStepName { get; set; }
        public DateTime WarningBeginDate { get; set; }
        public DateTime WarningEndDate { get; set; }
        public Double WarningDuration { get; set; }

    }


    public class PartSchedulPlanPageDto : SearchPagedDto
    {
        public int State { get; set; }

        public string? SourceConflict { get; set; }

        public int Priority { get; set; }
        /// <summary>
        /// 订单编号
        /// </summary>
        public string? OrderNumber { get; set; }

        /// <summary>
        /// 计划编码
        /// </summary>
        public string? PlanCode { get; set; }

        /// <summary>
        /// 排产编码
        /// </summary>
        public string? ScheduleCode { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string? ProductName { get; set; }
    }

    public class QuerySourceConflict
    {
        /// <summary>
        /// 排产编码
        /// </summary>
        public string ScheduleCode { get; set; }

        /// <summary>
        /// 计划编码
        /// </summary>
        public string PlanCode { get; set; }
    }
    public class StepTreeDto
    { 
        public string? StepCode { get; set; }
        public string? PreCode { get; set; } 

        private List<StepTreeDto>? _childNodes;

        public List<StepTreeDto> ChildNodes
        {
            get
            {
                if (_childNodes == null)
                    _childNodes = new List<StepTreeDto>();
                return _childNodes;
            }
            set
            {
                _childNodes = value;
            }
        }

        public bool IsLeaf => !ChildNodes.Any();
        
    }


    public class ScheduleRule
    { 
        public bool ForWardSchedule { get; set; }
        public bool BatchOperation { get; set; }
        public bool KittingSchedule { get; set; }
        public bool SourceRange { get; set; } 
    }

    public class QuerycheduleStepDto
    {
        public StepTreeDto Nodes { get; set; }
        public BasProductDto ProModule { get; set; }
        public DateTime BeginDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal Qty { get; set; }
        public string ScheduleCode { get; set; }
        public ScheduleRule Rule { get; set; }
         
    }

    public class SchedulePlanResultDto
    {

        private PartSchedulePlan? _schedulePlan;

        public PartSchedulePlan SchedulePlan
        {
            get
            {
                if (_schedulePlan == null)
                    _schedulePlan = new PartSchedulePlan();
                return _schedulePlan;
            }
            set
            {
                _schedulePlan = value;
            }
        }

        private List<PartSchedulePlanStep>? _stepLists;

        public List<PartSchedulePlanStep> StepLists
        {
            get
            {
                if (_stepLists == null)
                    _stepLists = new List<PartSchedulePlanStep>();
                return _stepLists;
            }
            set
            {
                _stepLists = value;
            }
        }

        private List<PartSchedulePlanSource>? _sourcesLists;

        public List<PartSchedulePlanSource> SourcesLists
        {
            get
            {
                if (_sourcesLists == null)
                    _sourcesLists = new List<PartSchedulePlanSource>();
                return _sourcesLists;
            }
            set
            {
                _sourcesLists = value;
            }
        }

        private List<PartSchedulePlanMaterial>? _materialLists;

        public List<PartSchedulePlanMaterial> MaterialLists
        {
            get
            {
                if (_materialLists == null)
                    _materialLists = new List<PartSchedulePlanMaterial>();
                return _materialLists;
            }
            set
            {
                _materialLists = value;
            }
        } 
    }


    public class TimeInterval
    {
        public DateTime Start { get;  }
        public DateTime End { get;  }
        public decimal Tat { get;  }

        //public int State { get; }

        public TimeInterval(DateTime start, DateTime end/*, int state*/)
        {
            if (start >= end) throw new ArgumentException("End time must be after start time");
            Start = start;
            End = end;
            Tat = Convert.ToDecimal(Math.Round((End - Start).TotalHours, 2));
            //State = state;
        }

    }


}

