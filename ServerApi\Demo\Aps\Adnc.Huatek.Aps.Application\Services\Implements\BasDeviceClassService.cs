﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.BasDevice;
using Adnc.Huatek.Aps.Domain.Aggregates.BasDevicePreserveAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PartDeviceStateAggregates;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductStandardCapacityAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStation;
using Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.SourceSchedulDeviceStationAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.SysUserAggregate;
using Adnc.Huatek.Aps.Domain.Services;
using Adnc.Infra.IdGenerater.Yitter;
using Adnc.Shared;
using Adnc.Shared.Application.Contracts.ResultModels;
using Microsoft.AspNetCore.Mvc;
using System.Net;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.ViewComponents;
using Adnc.Infra.Core.Exceptions;
using Adnc.Huatek.Aps.Domain.Aggregates.BasDeviceClassAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasClassesAggregate;

namespace Adnc.Huatek.Aps.Application.Services.Implements
{

    public class BasDeviceClassService : AbstractAppService, IBasDeviceClassService
    {
        private readonly BasDeviceClassManager _devClassMgr;
        private readonly IEfBasicRepository<BasDevice> _devRepo;
        private readonly IEfBasicRepository<SysUser> _sysUserRepo;
        private readonly IEfBasicRepository<BasDeviceClass> _devClassRepo;
        private readonly IEfBasicRepository<BasClasses> _classRepo;
        public IMaintRestClient _maintRestClient;

        public BasDeviceClassService(
           BasDeviceClassManager devClassMgr, 
           IEfBasicRepository<BasDevice> devRepo, 
           IEfBasicRepository<SysUser> sysUserRepo, 
           IEfBasicRepository<BasDeviceClass> devClassRepo,
           IEfBasicRepository<BasClasses> classRepo,
            IMaintRestClient maintRestClient)
        {
            _devClassMgr = devClassMgr;
            _devRepo = devRepo;
            _classRepo = classRepo;
            _sysUserRepo = sysUserRepo;
            _devClassRepo = devClassRepo;
            _maintRestClient = maintRestClient;
        }

        /// <summary>
        /// 新增工位
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<AppSrvResult<long>> CreateAsync(BasDeviceClassDto input)
        {
            var menu = Mapper.Map<BasDeviceClass>(input);
            var deviceClass = await _devClassMgr.CreateAsync(menu.DevCode, menu.ClassCode, menu.WorkingDate);
            deviceClass.Id = IdGenerater.GetNextId();
            await _devClassRepo.InsertAsync(deviceClass);
            return deviceClass.Id;
        }

        /// <summary>
        /// 删除工位
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<AppSrvResult> DeleteAsync(long id)
        {
            var userProfile = await _devClassRepo.GetAsync(id);
            if (userProfile != null)
            {
                //var o = await _basStationRepo.Where(x => !x.IsDeleted && x.DeviceCode == userProfile.Code).ToListAsync();
                //if (o.Any())
                //{
                //    return Problem(HttpStatusCode.BadRequest, "该设备所属工位有效，不可以删除！");
                //}
                var menu = Mapper.Map<BasDeviceClass>(userProfile);
                menu.IsDeleted = true;
                await _devClassRepo.UpdateAsync(menu);
            }
            return AppSrvResult();
        }

        /// <summary>
        /// 获取所有工位
        /// </summary>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<List<BasDeviceClassDto>> GetAllAsync()
        {
            var sysTests = await _devClassRepo.Where(x => !x.IsDeleted).ToListAsync();
            var sysTestsDto = Mapper.Map<List<BasDeviceClassDto>>(sysTests);
            return sysTestsDto;
        }


        /// <summary>
        /// 修改工位
        /// </summary>
        /// <param name="id"></param>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<AppSrvResult> UpdateAsync(BasDeviceClassDto input)
        {
            bool isUpdete = await _devClassMgr.UpdateAsync(input.DevCode,input.ClassCode,input.WorkingDate, input.Id ?? 0);
            if (isUpdete)
            {
                input.TrimStringFields();
                var station = Mapper.Map<BasDeviceClass>(input);
                var userProfile = await _devClassRepo.GetAsync(input.Id ?? 0);
                if (userProfile != null)
                { 
                    userProfile.DevCode = station.DevCode;
                    userProfile.ClassCode = station.ClassCode;
                    userProfile.WorkingDate = station.WorkingDate;
                    userProfile.ModifyTime = station.ModifyTime;
                    userProfile.ModifyBy = station.ModifyBy;
                    await _devClassRepo.UpdateAsync(userProfile);
                }
            }
            return AppSrvResult();
        }


        public async Task<PageModelDto<DeviceWorkingDateDto>> GetPagedAsync(BasDeviceClassPagedDto search)
        {
            var result = new List<DeviceWorkingDateDto>();
            search.TrimStringFields();
            var whereExpression = ExpressionCreator
                                                .New<BasDeviceClass>()
                                                .And(x => !x.IsDeleted)
                                                .AndIf(search.CurrentMonth !=null,x=>x.WorkingDate>=new DateTime(search.CurrentMonth.Year,search.CurrentMonth.Month,1) && x.WorkingDate<= search.CurrentMonth.AddMonths(1).AddDays(-1));

            var deviceWhereExpression = ExpressionCreator
                                                .New<BasDevice>()
                                                .AndIf(search.DevCode.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Code!, $"%{search.DevCode}%"))
                                                .AndIf((!string.IsNullOrWhiteSpace(search.Dtype) && search.Dtype != "0"), x => x.Dtype == search.Dtype)
                                                .And(x => !x.IsDeleted)
                                                .And(x=>x.Status == 1);

            var total =  _devRepo.Where(deviceWhereExpression).Count();

            var deviceEntity = await _devRepo.Where(deviceWhereExpression)
                                             .Skip(search.SkipRows())
                                             .Take(search.PageSize).ToListAsync();
            if (deviceEntity.Any())
            {
                //var deviceDtos = Mapper.Map<List<BasDeviceDto>>(deviceEntity);
                var sysUsers = _sysUserRepo.Where(o => true);
                var entities = _devClassRepo.Where(whereExpression);
                var deviceClassDtos = Mapper.Map<List<BasDeviceClassDto>>(entities);
                var classes = await _classRepo.Where(x => !x.IsDeleted).ToListAsync();
                deviceClassDtos.ForEach(item =>
                {
                    var classDto = classes.FirstOrDefault(x=>x.Ccode == item.ClassCode);
                    item.CreatedName = sysUsers?.FirstOrDefault(x => x.Id == item.CreateBy)?.Name ?? "";
                    item.ModifyName = sysUsers.FirstOrDefault(x => x.Id == item.ModifyBy)?.Name ?? "";
                    item.ClassName = classDto.ShortName;
                    item.ClassColor = classDto.Color;
                });

                result = deviceEntity.Select(a=> new DeviceWorkingDateDto
                              {
                                  Code = a.Code,
                                  Name = a.Name,
                                  Shifts = deviceClassDtos.Where(p => p.DevCode == a.Code).ToList()
                              }).ToList();
            }
            return new PageModelDto<DeviceWorkingDateDto>(search, result, total);
        }


        /// <summary>
        /// 获取指定工位
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<ResultJson> GetByIdAsync(long id)
        {

            var userProfile = await _devClassRepo.GetAsync(id);
            if (userProfile == null)
                return new ResultJson("查询成功！无数据", null);
            var menu = Mapper.Map<BasDeviceDto>(userProfile);

            return new ResultJson("查询成功", menu, 0);
        }

    }
}
