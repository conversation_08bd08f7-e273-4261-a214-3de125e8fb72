﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasBomAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasClassesAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasDevice;
using Adnc.Huatek.Aps.Domain.Aggregates.BasDeviceClassAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasDevicePreserveAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasHolidayAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasLineAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasLineProductRelationAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasMaterial;
using Adnc.Huatek.Aps.Domain.Aggregates.BasNumberManagementAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductStandardCapacityAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductStepRelationAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductStepRelationDeviceAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductTechnologyRelationAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasRelation;
using Adnc.Huatek.Aps.Domain.Aggregates.BasRelationDeviceAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStation;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStepAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStepMaterialRelationAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasTechnologyAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasTechnologyStepRelationAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasWorksjop;
using Adnc.Huatek.Aps.Domain.Aggregates.PartDeviceStateAggregates;
using Adnc.Huatek.Aps.Domain.Aggregates.PartOrderAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PartOrderStepAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanMaterialAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanRuleAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanSourceAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanStepAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulStepDetailsAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PlanSchedulStepSolutionAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.SourceSchedulDeviceStationAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.StockMaterial;
using Adnc.Huatek.Aps.Domain.Aggregates.SysTestAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.SysUserAggregate;
using Adnc.Huatek.Aps.Domain.AggregatesPartOrderMaterialAggregate;
using Adnc.Shared;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class EntityInfo : AbstractDomainEntityInfo
    {
        public EntityInfo(UserContext userContext) : base(userContext)
        {
        }
        protected override Assembly GetCurrentAssembly() => GetType().Assembly;

        protected override void SetTableName(dynamic modelBuilder)
        {
            if (modelBuilder is not ModelBuilder builder)
                throw new ArgumentNullException(nameof(modelBuilder));
            builder.Entity<SysTest>().ToTable("sys_test");
            builder.Entity<BasTechnology>().ToTable("bas_technology");
            builder.Entity<BasStep>().ToTable("bas_step");
            builder.Entity<BasStation>().ToTable("bas_station");
            builder.Entity<BasDevice>().ToTable("bas_device");
            builder.Entity<BasWorksjop>().ToTable("bas_worksjop");
            builder.Entity<BasMaterial>().ToTable("bas_material");
            builder.Entity<BasTechnologyStepRelation>().ToTable("bas_technology_step_relation");
            builder.Entity<BasProductStepRelation>().ToTable("bas_product_step_relation");
            builder.Entity<BasProductStepRelationMaterial>().ToTable("bas_product_step_relation_material");
            builder.Entity<BasProductStandardCapacity>().ToTable("bas_product_standardcapacity");
            builder.Entity<BasStepMaterialRelation>().ToTable("bas_step_material_relation");
            builder.Entity<BasRelation>().ToTable("bas_relation");
            builder.Entity<BasProduct>().ToTable("bas_product");
            builder.Entity<BasProductTechnologyRelation>().ToTable("bas_product_technology_relation");
            builder.Entity<BasBom>().ToTable("bas_product_bom");
            builder.Entity<BasProductStepRelationSource>().ToTable("bas_product_step_relation_source");
            builder.Entity<BasLine>().ToTable("bas_line");
            builder.Entity<BasLineProductRelation>().ToTable("bas_line_product_relation");
            builder.Entity<BasClasses>().ToTable("bas_classes");
            builder.Entity<PartOrder>().ToTable("part_order");
            builder.Entity<PartOrderMaterial>().ToTable("part_order_material");
            builder.Entity<PartOrderStep>().ToTable("part_order_step");
            builder.Entity<BasRelationDevice>().ToTable("bas_relation_device");
            builder.Entity<BasOrder>().ToTable("bas_order");
            builder.Entity<BasOrderProduct>().ToTable("bas_order_product");
            builder.Entity<SysUser>().ToTable("sys_user");
            builder.Entity<BasNumberManagement>().ToTable("bas_number_management");
            builder.Entity<PartPlanPending>().ToTable("part_plan_pending");
            builder.Entity<PlanProductionSchedule>().ToTable("plan_production_schedule");
            builder.Entity<BasStationClasses>().ToTable("bas_station_classes");
            builder.Entity<SourceSchedulDeviceStation>().ToTable("source_schedul_device_station");
            builder.Entity<BasDevicePreserve>().ToTable("bas_device_preserve");
            builder.Entity<PlanProductionSchedulStepDetails>().ToTable("plan_production_schedul_step_details");
            builder.Entity<BasHoliday>().ToTable("bas_holiday");
            builder.Entity<PlanStepCapacity>().ToTable("plan_step_capacity");
            builder.Entity<PlanSchedulStepSolution>().ToTable("plan_schedul_step_solution");
            builder.Entity<StockMaterial>().ToTable("stock_material");
            builder.Entity<PartDeviceState>().ToTable("part_device_state");
            builder.Entity<PartSchedulePlan>().ToTable("part_schedule_plan");
            builder.Entity<PartSchedulePlanRule>().ToTable("part_schedule_plan_rule");
            builder.Entity<PartSchedulePlanSource>().ToTable("part_schedule_plan_source");
            builder.Entity<PartSchedulePlanStep>().ToTable("part_schedule_plan_step");
            builder.Entity<PartSchedulePlanMaterial>().ToTable("part_schedule_plan_material");
            builder.Entity<PartSchedulePlanInfo>().ToTable("part_schedule_plan_info");
            builder.Entity<BasDeviceClass>().ToTable("bas_device_class");


        }
    }
}
