﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.BasBomAggregate;
using Adnc.Shared.Application.Contracts.ResultModels;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services
{
    public interface IBasBomService: IAppService
    {
        [OperateLog(LogName = "获取Bom列表信息")]
        Task<PageModelDto<BasBomDto>> GetPagedAsync(BomPagedDto search);
  
        [OperateLog(LogName = "创建Bom")]
        [UnitOfWork]
        Task<AppSrvResult<string>> CreateAsync(BasBomDto input);
        [OperateLog(LogName = "修改Bom")]
        [UnitOfWork]
        Task<AppSrvResult> UpdateAsync(BasBomDto input);

        [OperateLog(LogName = "删除Bom")]
        [UnitOfWork]
        Task<AppSrvResult> DeleteAsync(string productCode);


        [OperateLog(LogName = "根据ID获取Bom数据")]
        Task<BasBomDto> GetByIdAsync(string productCode);


        [OperateLog(LogName = "根据产品ID获取物料数据")]

        Task<List<BasBomListDto>> GetMaterialByProductCodeAsync(string productCode);
    }
}
