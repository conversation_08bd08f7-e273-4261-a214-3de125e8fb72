﻿using Adnc.Demo.Shared.Rpc.Http.Services;
using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.BasDevice;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStation;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStepAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasTechnologyAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasTechnologyStepRelationAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.SysUserAggregate;
using Adnc.Huatek.Aps.Domain.Services;
using Adnc.Infra.Core.Exceptions;
using Adnc.Shared;
using Adnc.Shared.Application.Contracts.ResultModels;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services.Implements
{
    public class BasStepService : AbstractAppService, IBasStepService
    {
        private readonly BasStepManagement _basStepMgr;
        private readonly IEfBasicRepository<BasStep> _basStepRepo;
        private readonly IEfBasicRepository<SysUser> _sysUserRepo;
        private readonly BasNumberManagementService _basNumberManagementService;

        public BasStepService(
            IEfBasicRepository<BasStep> basStepRepo,
            IEfBasicRepository<SysUser> sysUserRepo,
            BasStepManagement basStepMgr,
            BasNumberManagementService basNumberManagementService
            )
        {
            _basStepMgr = basStepMgr;
            _basStepRepo = basStepRepo;
            _sysUserRepo = sysUserRepo;
            _basNumberManagementService = basNumberManagementService;
        }
        /// <summary>
        /// 获取分页列表
        /// </summary>
        /// <param name="search"></param>
        /// <returns></returns>
        public async Task<PageModelDto<BasStepDto>> GetPagedAsync(StepPagedDto search)
        {
            search.TrimStringFields();
            var whereExpression = ExpressionCreator
                                                .New<BasStep>()
                                                .And(x => !x.IsDeleted)
                                                .AndIf(search.Code.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Code!, $"%{search.Code}%"))
                                                .AndIf(search.Name.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Name!, $"%{search.Name}%"))
                                                .AndIf(search.Status > -1, x => x.Status == search.Status);

            var total = await _basStepRepo.CountAsync(whereExpression);
            if (total == 0)
                return new PageModelDto<BasStepDto>(search);

            var entities = _basStepRepo.Where(whereExpression)
                                            .OrderByDescending(x => x.CreateTime)
                                            .Skip(search.SkipRows())
                                            .Take(search.PageSize);

            var cusers = _sysUserRepo.Where(x => !x.IsDeleted);
            var musers = _sysUserRepo.Where(x => !x.IsDeleted);
            var results = (from s in entities
                           join cu in cusers on s.CreateBy equals cu.Id
                           join mu in musers on s.ModifyBy equals mu.Id
                           select new BasStepDto()
                           {
                               Id = s.Id,
                               Code = s.Code,
                               Name = s.Name,
                               Status = s.Status,
                               Remark = s.Remark,
                               CreatName = cu.Name,
                               CreateTime = s.CreateTime,
                               ModifyName = mu.Name,
                               ModifyTime = s.ModifyTime
                           }).ToList();

            //var stepDtos = Mapper.Map<List<BasStepDto>>(entities);
            return new PageModelDto<BasStepDto>(search, results, total);
        }
        /// <summary>
        /// 获取所有数据
        /// </summary>
        /// <returns></returns>

        public async Task<ResultJson> GetAllAsync()
        {
            var basSteps = await _basStepRepo.Where(x => !x.IsDeleted && x.Status == 1).ToListAsync();

            var basStepsDto = Mapper.Map<List<BasStepDto>>(basSteps);

            return new ResultJson("查询成功", basStepsDto);
        }

        /// <summary>
        /// 创建工序
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult<long>> CreateAsync(BasStepDto input)
        {
            input.TrimStringFields();

            var autoCode = _basNumberManagementService.GetNumberBySimpleName(CommonConst.STEPNUMBER).Result;

            var step = await _basStepMgr.CreateAsync(autoCode, input.Name, input.Status, input.StepTime, input.Remark,input.Tat);

            step.Code = autoCode;
            await _basStepRepo.InsertAsync(step);

            return step.Id;
        }


        /// <summary>
        /// 更新工序
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult> UpdateAsync(BasStepDto input)
        {
            bool isUpdete = await _basStepMgr.UpdateAsync(input.Code, input.Name, input.Id ?? 0);
            if (isUpdete)
            {
                var step = await _basStepRepo.GetAsync(input.Id ?? 0);
                if (step != null)
                {
                    step.Status = input.Status;
                    step.StepTime = input.StepTime;
                    step.Name = input.Name;
                    step.Code = input.Code;
                    step.Tat = input.Tat;
                    step.Remark = input.Remark;
                    await _basStepRepo.UpdateAsync(step);
                }
            }
            return AppSrvResult();
        }

        /// <summary>
        /// 删除工序
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult> DeleteAsync(long id)
        {
            var step = await _basStepRepo.GetAsync(id);
            if (step != null)
            {
                //如果工序和工艺绑定，不可删除
                var isExist = await _basStepMgr.DeleteAsync(step.Code);
                if (isExist)
                {
                    step.IsDeleted = true;
                    await _basStepRepo.UpdateAsync(step);
                }
            }
            return AppSrvResult();


        }


        /// <summary>
        /// 根据ID获取BOM数据
        /// </summary>
        /// <returns></returns>

        public async Task<ResultJson> GetByIdAsync(long id)
        {
            var step = await _basStepRepo.GetAsync(id);

            var stepDto = Mapper.Map<BasStepDto>(step);

            return new ResultJson("查询成功", stepDto);

        }

        /// <summary>
        /// 更新状态
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult> ChangeStatusAsync(ChangeStatusDto input)
        {
            if (input.CodeList.Count == 0)
                return Problem(HttpStatusCode.BadRequest, "编码列表不能为空！");

            var steps = await _basStepRepo.Where(x => input.CodeList.Contains(x.Code ?? "-1"), false, false).ToListAsync();
            if (input.Status == 0)
            {
                steps.ForEach(x => { x.Status = 0; });
            }
            else
            {
                steps.ForEach(x => { x.Status = 1; });
            }

            await _basStepRepo.UpdateRangeAsync(steps);

            return AppSrvResult();
        }


        public async Task<ResultJson> ImportAsync(List<BasStepDto> dtos)
        {
            dtos.ForEach(x =>
            {
                x.Status = x.StatusDis == "启用" ? 1 : 0;
            });

            var entities = Mapper.Map<List<BasStep>>(dtos);

            var resultObj = await _basStepMgr.ImportAsync(entities);

            if (resultObj.insertEntities.Any())
                await _basStepRepo.InsertRangeAsync(resultObj.insertEntities);

            if (resultObj.updateEntities.Any())
                await _basStepRepo.UpdateRangeAsync(resultObj.updateEntities);

            return new ResultJson(resultObj.Msg);
        }
    }
}
