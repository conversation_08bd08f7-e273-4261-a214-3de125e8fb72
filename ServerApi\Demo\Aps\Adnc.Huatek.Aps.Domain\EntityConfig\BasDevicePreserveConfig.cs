﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasDevice;
using Adnc.Huatek.Aps.Domain.Aggregates.BasDevicePreserveAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class BasDevicePreserveConfig : AbstractEntityTypeConfiguration<BasDevicePreserve>
    {

        public override void Configure(EntityTypeBuilder<BasDevicePreserve> builder)
        {
            base.Configure(builder);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.Status).HasColumnName("status");
            builder.Property(x => x.DevCode).HasColumnName("devcode");
            builder.Property(x => x.DevName).HasColumnName("devname");
            builder.Property(x => x.Remark).HasColumnName("remark");
            builder.Property(x => x.CreateBy).HasColumnName("createdby");
            builder.Property(x => x.CreateTime).HasColumnName("createdtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
            builder.Property(x => x.ModifyName).HasColumnName("modifyname");
            builder.Property(x => x.CreatedName).HasColumnName("createdname");

            builder.Property(x => x.BeginTime).HasColumnName("begintime");
            builder.Property(x => x.EndTime).HasColumnName("endtime");
        }
    }
}
