﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.BasDevice;
using Adnc.Huatek.Aps.Domain.Aggregates.BasRelation;
using Adnc.Huatek.Aps.Domain.Aggregates.BasRelationDeviceAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStepAggregate;
using Adnc.Huatek.Aps.Domain.Services;
using Adnc.Infra.IdGenerater.Yitter;
using Adnc.Shared.Application.Contracts.ResultModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services.Implements
{
    
    public class BasRelationService : AbstractAppService, IBasRelationService
    {
        private readonly BasRelationManager _relationMgr;
        private readonly IEfBasicRepository<BasRelation> _relationRepo;
        private readonly IEfBasicRepository<BasRelationDevice> _relDevRepo;
        private readonly IEfBasicRepository<BasDevice> _devRepo;
        private readonly IEfBasicRepository<BasStep> _stepRepo;

        public BasRelationService(
            IEfBasicRepository<BasRelation> relationRepo,
            IEfBasicRepository<BasRelationDevice> relDevRepo,
            IEfBasicRepository<BasStep> stepRepo,
            IEfBasicRepository<BasDevice> devRepo,
        BasRelationManager relationMgr)
        {
            _relationRepo = relationRepo;
            _relDevRepo = relDevRepo;
            _devRepo = devRepo;
            _stepRepo = stepRepo;
            _relationMgr = relationMgr;
        }

        /// <summary>
        /// 获取指定资源关系搭建
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<BasRelationDto> GetByIdAsync(long id)
        {
            var relation = await _relationRepo.GetAsync(id);

            var relationDto = Mapper.Map<BasRelationDto>(relation);

            var devices = await _relDevRepo.Where(x => !x.IsDeleted && x.IdRelation == id).ToListAsync();
            relationDto.Devices = Mapper.Map<List<BasRelationDeviceDto>>(devices);

            var deviceList = await _devRepo.Where(x => !x.IsDeleted && devices.Select(p => p.IdDevice).Contains(x.Id)).ToListAsync();

            relationDto.Devices.ForEach(x => {
                x.DeviceCode = deviceList.FirstOrDefault(p => p.Id == x.IdDevice).Code ?? "";
                x.DeviceName = deviceList.FirstOrDefault(p => p.Id == x.IdDevice).Name?? "";
            });
            return relationDto;

        }

        /// <summary>
        /// 新增资源关系搭建
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<AppSrvResult<long>> CreateAsync(BasRelationDto input)
        {
            input.TrimStringFields();
            var relation = await _relationMgr.CreateAsync(input.IdStep, input.StepName, input.Remark);
            var relationDevs = Mapper.Map<List<BasRelationDevice>>(input.Devices);
            relationDevs.ForEach(o =>
            {
                o.Id = IdGenerater.GetNextId();
                o.IdRelation = relation.Id;
            });

            await _relationRepo.InsertAsync(relation);
            await _relDevRepo.InsertRangeAsync(relationDevs);
            return relation.Id;
        }

        /// <summary>
        /// 删除资源关系搭建
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<AppSrvResult> DeleteAsync(long id)
        {
            var relation = await _relationRepo.GetAsync(id);
            if (relation != null)
            {
                relation.IsDeleted = true;
                await _relationRepo.UpdateAsync(relation);
                var devices = await _relDevRepo.Where(x => x.IdRelation == id, false, false).ToListAsync();
                devices.ForEach(o =>
                {
                    o.IsDeleted = true;
                });
                await _relDevRepo.UpdateRangeAsync(devices);
            }
            return AppSrvResult();
        }


        /// <summary>
        /// 修改资源关系搭建
        /// </summary>
        /// <param name="id"></param>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<AppSrvResult> UpdateAsync(BasRelationDto input)
        {
            bool isUpdete = await _relationMgr.UpdateAsync(input.IdStep, input.StepName, input.Id);
            if (isUpdete)
            {
                var relation = await _relationRepo.GetAsync(input.Id);
                if (relation != null)
                {
                    relation.IdStep = relation.IdStep;
                    relation.Remark = input.Remark;
                    await _relationRepo.UpdateAsync(relation);

                    var devices = await _relDevRepo.Where(x => x.IdRelation == input.Id, false, false).ToListAsync();
                    await _relDevRepo.RemoveRangeAsync(devices);

                    var deviceList = Mapper.Map<List<BasRelationDevice>>(input.Devices);
                    deviceList.ForEach(o =>
                    {
                        o.Id = IdGenerater.GetNextId();
                        o.IdRelation = relation.Id;
                    });
                    await _relDevRepo.InsertRangeAsync(deviceList);

                }
            }
            return AppSrvResult();
        }

       
        /// <summary>
        /// 获取分页
        /// </summary>
        /// <param name="search"></param>
        /// <returns></returns>
        public async Task<PageModelDto<BasRelationDto>> GetPagedAsync(RelationPagedDto search)
        {
            search.TrimStringFields();
            var whereExpression = ExpressionCreator
                                                .New<BasRelation>()
                                                .And(x => !x.IsDeleted)
                                                .AndIf(search.IdStep != null , x => x.IdStep == search.IdStep);

            var total = await _relationRepo.CountAsync(whereExpression);
            if (total == 0)
                return new PageModelDto<BasRelationDto>(search);

            var entities = await _relationRepo
                                            .Where(whereExpression)
                                            .OrderByDescending(x => x.Id)
                                            .Skip(search.SkipRows())
                                            .Take(search.PageSize)
                                            .ToListAsync();

            var relationDtos = Mapper.Map<List<BasRelationDto>>(entities);

            var stepList = await _stepRepo.Where(x=> relationDtos.Select(x=>x.IdStep).Contains(x.Id)).ToListAsync();

            relationDtos.ForEach(async o =>
            {
                o.StepCode = stepList.FirstOrDefault(x => x.Id == o.IdStep).Code;
                o.StepName = stepList.FirstOrDefault(x => x.Id == o.IdStep).Name;
            });

            return new PageModelDto<BasRelationDto>(search, relationDtos, total);
        }

    }
}
