﻿using Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanAggregate;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class PartSchedulePlanInfoConfig : AbstractEntityTypeConfiguration<PartSchedulePlanInfo>
    {
        public override void Configure(EntityTypeBuilder<PartSchedulePlanInfo> builder)
        {
            base.Configure(builder);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.PlanCode).HasColumnName("plancode");
            builder.Property(x => x.ScheduleCode).HasColumnName("schedulecode");
            builder.Property(x => x.ProductModule).HasColumnName("productmodule");
            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.CreateBy).HasColumnName("createby");
            builder.Property(x => x.CreateTime).HasColumnName("createtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
           
        }
    }
}
