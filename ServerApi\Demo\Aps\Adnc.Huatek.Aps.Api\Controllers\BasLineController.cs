﻿using Adnc.Demo.Shared.Const;
using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Application.Services;
using Adnc.Shared;
using Adnc.Shared.Application.Contracts.ResultModels;
using Adnc.Shared.WebApi.Authorization;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Adnc.Huatek.Aps.Api.Controllers
{
    [Route($"{RouteConsts.ApsRoot}/line")]
    [ApiController]
    [AllowAnonymous]
    public class BasLineController : AdncControllerBase
    {
        private readonly IBasLineService _lineSrv;

        public BasLineController(IBasLineService lineSrv) => _lineSrv = lineSrv;

        /// <summary>
        /// 获取产线分页信息
        /// </summary>
        /// <returns></returns>
        [HttpPost("page")]
        public async Task<ActionResult<PageModelDto<BasLineDto>>> GetPagedAsync([FromBody] LinePagedDto search) => await _lineSrv.GetPagedAsync(search);

        /// <summary>
        /// 获取所有信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getallasync")]
        public async Task<ActionResult<List<BasLineDto>>> GetAllAsync() => await _lineSrv.GetAllAsync();

        /// <summary>
        /// 新增产线
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("add")]
        public async Task<ActionResult<long>> CreateAsync([FromBody] BasLineDto input)
         => CreatedResult(await _lineSrv.CreateAsync(input));

        /// <summary>
        /// 更新产线
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("edit")]
        public async Task<ActionResult> UpdateAsync([FromBody] BasLineDto input)
           => Result(await _lineSrv.UpdateAsync(input));


        /// <summary>
        /// 批量启用
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPut("changestatus/{status}")]
        public async Task<ActionResult> MakeEnableAsync([FromRoute] int status,[FromBody] List<long> data)
          => Result(await _lineSrv.MakeEnableAsync(status, data));


        /// <summary>
        /// 删除产线
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpDelete("delete/{id}")]
        public async Task<ActionResult> DeleteAsync([FromRoute] long id)
          => Result(await _lineSrv.DeleteAsync(id));

        /// <summary>
        /// 根据ID获取产线
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getbyid/{id}")]
        public async Task<ActionResult<BasLineDto>> GetByIdAsync([FromRoute] long id)
            => await _lineSrv.GetByIdAsync(id);

        /// <summary>
        /// 根据产品code获取产线列表
        /// </summary>
        /// <param name="productCode"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getlinebyproductcode/{productCode}")]
        public async Task<ActionResult<List<ProductLineDto>>> GetMaterialByProductCodeAsync([FromRoute] string productCode)
            => await _lineSrv.GetLinebyProductcode(productCode);
    }
}
