﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasStation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class BasStationConfig : AbstractEntityTypeConfiguration<BasStation>
    {
         public override void Configure(EntityTypeBuilder<BasStation> builder)
        {
            base.Configure(builder);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.Status).HasColumnName("status");
            builder.Property(x => x.Code).HasColumnName("code");
            builder.Property(x => x.Name).HasColumnName("name");
           // builder.Property(x => x.Worksjopid).HasColumnName("worksjopid");
            builder.Property(x => x.Remark).HasColumnName("Remark");
            builder.Property(x => x.CreateBy).HasColumnName("createdby");
            builder.Property(x => x.CreateTime).HasColumnName("createdtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
            builder.Property(x => x.ModifyName).HasColumnName("modifyname");
            builder.Property(x => x.CreatedName).HasColumnName("createdname");
            //builder.Property(x => x.WorksjopName).HasColumnName("worksjopname");
            builder.Property(x => x.DeviceCode).HasColumnName("devicecode");
            builder.Property(x => x.LineCode).HasColumnName("linecode");
            builder.Property(x => x.Duration).HasColumnName("duration");

        }
    }
}
