<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Adnc.Huatek.Aps.Api</name>
    </assembly>
    <members>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasBomController.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.BomPagedDto)">
            <summary>
            获取Bom分页信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasBomController.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.BasBomDto)">
            <summary>
            新增Bom
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasBomController.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.BasBomDto)">
            <summary>
            更新Bom
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasBomController.DeleteAsync(System.String)">
            <summary>
            删除Bom
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasBomController.GetByIdAsync(System.String)">
            <summary>
            根据ID获取Bom
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasBomController.GetMaterialByProductCodeAsync(System.String)">
            <summary>
            根据产品code获取bom中物料列表
            </summary>
            <param name="productCode"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasClassesController.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.ClassesPagedDto)">
            <summary>
            获取班次分页信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasClassesController.GetAllAsync">
            <summary>
            获取班次信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasClassesController.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.BasClassesDto)">
            <summary>
            新增班次
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasClassesController.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.BasClassesDto)">
            <summary>
            更新班次
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasClassesController.DeleteAsync(System.Int64)">
            <summary>
            删除班次
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasClassesController.GetByIdAsync(System.Int64)">
            <summary>
            根据ID获取班次
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasClassesController.GetAllClassesAsync">
            <summary>
            获取班次信息 工位模块弹框中选择班次
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasClassesController.MakeEnableAsync(System.Int32,System.Collections.Generic.List{System.Int64})">
            <summary>
            批量启用
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasDeviceClassController.GetAllAsync">
            <summary>
            获取所有设备班次信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasDeviceClassController.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.BasDeviceClassDto)">
            <summary>
            新增设备班次
            </summary>
            <param name="input">用户信息</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasDeviceClassController.DeleteAsync(System.Int64)">
            <summary>
            删除设备班次
            </summary>
            <param name="id">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasDeviceClassController.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.BasDeviceClassDto)">
            <summary>
            修改设备班次
            </summary>
            <param name="id">id</param>
            <param name="input">用户信息</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasDeviceClassController.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.BasDeviceClassPagedDto)">
            <summary>
            获取设备班次列表分页
            </summary>
            <param name="search">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasDeviceClassController.GetCurrentUserInfoAsync(System.Int64)">
            <summary>
            获取单个设备班次详情信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasDeviceController.GetAllAsync">
            <summary>
            获取所有信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasDeviceController.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.BasDeviceDto)">
            <summary>
            新增设备
            </summary>
            <param name="input">用户信息</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasDeviceController.DeleteAsync(System.Int64)">
            <summary>
            删除设备
            </summary>
            <param name="id">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasDeviceController.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.BasDeviceDto)">
            <summary>
            修改设备
            </summary>
            <param name="id">id</param>
            <param name="input">用户信息</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasDeviceController.ChangeStatus(System.Int64,Adnc.Shared.Application.Contracts.Dtos.SimpleDto{System.Int32})">
            <summary>
            变更设备状态
            </summary>
            <param name="id">设备ID</param>
            <param name="status">状态</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasDeviceController.ChangeStatus(Adnc.Huatek.Aps.Application.Dtos.UpdateStatusDto)">
            <summary>
            批量变更设备状态
            </summary>
            <param name="input">设备Ids与状态</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasDeviceController.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.BasDevicePagedDto)">
            <summary>
            获取设备列表分页
            </summary>
            <param name="search">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasDeviceController.GetCurrentUserInfoAsync(System.Int64)">
            <summary>
            获取单个设备详情信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasDeviceController.GetCalendarAsync(Adnc.Huatek.Aps.Application.Dtos.BasDeviceCalendarDto)">
            <summary>
            设备日历-获取单个设备日历
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasDeviceController.UpdateCalendarAsync(Adnc.Huatek.Aps.Application.Dtos.BasDeviceCalendarDto)">
            <summary>
            设备日历-设置设备保养状态
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasDeviceController.DelCalendarAsync(Adnc.Huatek.Aps.Application.Dtos.BasDeviceCalendarDto)">
            <summary>
            设备日历-删除设备保养状态
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasDeviceController.ChangeStatus(Adnc.Huatek.Aps.Application.Dtos.ChangeStatusDto)">
            <summary>
            更新启用/禁用状态
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasDeviceController.GetDevicePreserveAsync(System.Int64)">
            <summary>
            获取设备保养列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasDeviceController.ImportAsync(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            新增物料
            </summary>
            <param name="input">物料信息</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasDeviceController.GetTypeDeviceAsync(Adnc.Huatek.Aps.Application.Dtos.BasDevicePagedDto)">
            <summary>
            获取设备列表分页
            </summary>
            <param name="search">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasDeviceController.GetDeviceStateAsync(Adnc.Huatek.Aps.Application.Dtos.QueryDevStateDto)">
            <summary>
            获取设备列表分页
            </summary>
            <param name="search">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasDeviceController.GetCurrentDeviceStateInfoAsync(System.Int64)">
            <summary>
            获取单个设备状态详情信息
            </summary>
            <returns></returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Adnc.Huatek.Aps.Api.Controllers.BasDeviceController.UpdateDeviceStateAsync(Adnc.Huatek.Aps.Application.Dtos.PartDeviceStateDto)" -->
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasDeviceController.DeleteDeviceStateAsync(System.Int64)">
            <summary>
            删除设备
            </summary>
            <param name="id">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasDeviceController.ImportDeviceStateAsync(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            新增物料
            </summary>
            <param name="input">物料信息</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasHolidayController.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.HolidayPagedDto)">
            <summary>
            获取分页信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasHolidayController.GetAllAsync">
            <summary>
            获取所有信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasHolidayController.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.BasHolidayDto)">
            <summary>
            新增
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasHolidayController.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.BasHolidayDto)">
            <summary>
            更新
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasHolidayController.DeleteAsync(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasHolidayController.GetByIdAsync(System.Int64)">
            <summary>
            根据ID获取
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasHolidayController.ImportOrders(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            导入
            </summary>
            <param name="formFile"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasHolidayController.DeleteAllAsync">
            <summary>
            删除全部假期
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasHolidayController.GetNonScheduleDateAsync(Adnc.Huatek.Aps.Application.Dtos.HolidayPagedDto)">
            <summary>
            获取时间段内的非排产时间
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasLineController.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.LinePagedDto)">
            <summary>
            获取产线分页信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasLineController.GetAllAsync">
            <summary>
            获取所有信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasLineController.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.BasLineDto)">
            <summary>
            新增产线
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasLineController.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.BasLineDto)">
            <summary>
            更新产线
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasLineController.MakeEnableAsync(System.Int32,System.Collections.Generic.List{System.Int64})">
            <summary>
            批量启用
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasLineController.DeleteAsync(System.Int64)">
            <summary>
            删除产线
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasLineController.GetByIdAsync(System.Int64)">
            <summary>
            根据ID获取产线
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasLineController.GetMaterialByProductCodeAsync(System.String)">
            <summary>
            根据产品code获取产线列表
            </summary>
            <param name="productCode"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasMaterialController.GetAllAsync">
            <summary>
            获取所有信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasMaterialController.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.BasMaterialDto)">
            <summary>
            新增物料
            </summary>
            <param name="input">物料信息</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasMaterialController.DeleteAsync(System.Int64)">
            <summary>
            删除物料
            </summary>
            <param name="id">物料ID</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasMaterialController.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.BasMaterialDto)">
            <summary>
            修改物料
            </summary>
            <param name="id">id</param>
            <param name="input">物料信息</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasMaterialController.ChangeStatus(System.Int64,Adnc.Shared.Application.Contracts.Dtos.SimpleDto{System.Int32})">
            <summary>
            变更物料状态
            </summary>
            <param name="id">物料ID</param>
            <param name="status">状态</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasMaterialController.ChangeStatus(Adnc.Huatek.Aps.Application.Dtos.UpdateStatusDto)">
            <summary>
            批量变更物料状态
            </summary>
            <param name="input">物料Ids与状态</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasMaterialController.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.BasMaterialPagedDto)">
            <summary>
            获取物料列表分页
            </summary>
            <param name="search">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasMaterialController.GetCurrentUserInfoAsync(System.Int64)">
            <summary>
            获取单个物料详情信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasMaterialController.MakeEnableAsync(System.Int32,System.Collections.Generic.List{System.Int64})">
            <summary>
            批量启用
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasMaterialController.ImportAsync(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            新增物料
            </summary>
            <param name="input">物料信息</param>
            <returns></returns>
        </member>
        <member name="T:Adnc.Huatek.Aps.Api.Controllers.BasNumberManagementController">
            <summary>
            编码管理
            </summary>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasNumberManagementController.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.BasNumberManagementPagedDto)">
            <summary>
            获取编码分页数据
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasNumberManagementController.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.BasNumberManagementDto)">
            <summary>
            新增
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasNumberManagementController.GetByIdAsync(System.Int64)">
            <summary>
            通过id 获取单个编码信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasNumberManagementController.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.BasNumberManagementDto)">
            <summary>
            修改
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasNumberManagementController.DeleteAsync(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasNumberManagementController.GetNumberBySimpleName(System.String)">
            <summary>
            通过 名称简写 获取最新编码
            </summary>
            <param name="simplename"></param>
            <returns></returns>
        </member>
        <member name="T:Adnc.Huatek.Aps.Api.Controllers.BasOrderController">
            <summary>
            客户订单
            </summary>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasOrderController.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.BasOrderPagedDto)">
            <summary>
            获取订单分页信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasOrderController.GetAllAsync(Adnc.Huatek.Aps.Application.Dtos.BasOrderPagedDto)">
            <summary>
            获取订单分页信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasOrderController.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.BasOrderDto)">
            <summary>
            新增订单
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasOrderController.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.BasOrderDto)">
            <summary>
            更新订单
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasOrderController.DeleteAsync(System.Int64)">
            <summary>
            删除订单
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasOrderController.GetByIdAsync(System.Int64)">
            <summary>
            根据ID获取订单
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasOrderController.GetByOrderIdAsync(System.Int64)">
            <summary>
            根据订单Id初始化新增生产计划页面
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasOrderController.FinishedAsync(Adnc.Huatek.Aps.Application.Dtos.FinishedOrders)">
            <summary>
            更新订单为完成
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasOrderController.ExportOrdersAsync(Adnc.Huatek.Aps.Application.Dtos.BasOrderPagedDto)">
            <summary>
            导出订单
            </summary>
            <param name="queryParamDto"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasOrderController.Download">
            <summary>
            下载导入模板
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasOrderController.ImportOrders(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            导入订单
            </summary>
            <param name="formFile"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasOrderController.GetOrderDetailAsync(System.Int64)">
            <summary>
            根据订单Id初始化订单详情页面
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasProductController.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.ProductPagedDto)">
            <summary>
            获取产品分页信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasProductController.GetAllAsync">
            <summary>
            获取所有信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasProductController.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.BasProductBomDto)">
            <summary>
            新增产品
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasProductController.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.BasProductBomDto)">
            <summary>
            更新产品
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasProductController.UpdateStepMaterialAsync(Adnc.Huatek.Aps.Application.Dtos.BasProductDto)">
            <summary>
            更新产品工序物料
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasProductController.DeleteAsync(System.Int64)">
            <summary>
            删除产品
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasProductController.GetByIdAsync(System.Int64)">
            <summary>
            获取产品详细信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasProductController.UpdateProductStdCapAsync(Adnc.Huatek.Aps.Application.Dtos.BasProductDto)">
            <summary>
            更新产品产能
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasProductController.ChangeStatus(Adnc.Huatek.Aps.Application.Dtos.ChangeStatusDto)">
            <summary>
            更新启用/禁用状态
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasProductController.GetModuleByIdAsync(System.Int64)">
            <summary>
            获取产品详细信息
            </summary>
            <returns></returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Adnc.Huatek.Aps.Api.Controllers.BasProductController.ImportAsync(Microsoft.AspNetCore.Http.IFormFile)" -->
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasRelationController.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.BasRelationDto)">
            <summary>
            新增资源关系搭建
            </summary>
            <param name="input">资源关系搭建信息</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasRelationController.DeleteAsync(System.Int64)">
            <summary>
            删除资源关系搭建
            </summary>
            <param name="id">资源关系搭建ID</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasRelationController.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.BasRelationDto)">
            <summary>
            修改资源关系搭建
            </summary>
            <param name="id">id</param>
            <param name="input">资源关系搭建信息</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasRelationController.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.RelationPagedDto)">
            <summary>
            获取资源关系搭建列表分页
            </summary>
            <param name="search">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasRelationController.GetCurrentUserInfoAsync(System.Int64)">
            <summary>
            获取单个资源关系搭建详情信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasStationController.GetAllAsync">
            <summary>
            获取所有信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasStationController.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.BasStationDto)">
            <summary>
            新增工位
            </summary>
            <param name="input">用户信息</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasStationController.DeleteAsync(System.Int64)">
            <summary>
            删除工位
            </summary>
            <param name="id">工位ID</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasStationController.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.BasStationDto)">
            <summary>
            修改工位
            </summary>
            <param name="id">id</param>
            <param name="input">用户信息</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasStationController.ChangeStatus(System.Int64,Adnc.Shared.Application.Contracts.Dtos.SimpleDto{System.Int32})">
            <summary>
            变更工位状态
            </summary>
            <param name="id">工位ID</param>
            <param name="status">状态</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasStationController.ChangeStatus(Adnc.Huatek.Aps.Application.Dtos.UpdateStatusDto)">
            <summary>
            批量变更工位状态
            </summary>
            <param name="input">工位Ids与状态</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasStationController.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.BasStationPagedDto)">
            <summary>
            获取工位列表分页
            </summary>
            <param name="search">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasStationController.GetListAsync(Adnc.Huatek.Aps.Application.Dtos.BasStationSearchDto)">
            <summary>
            按产线编码获取工位列表
            </summary>
            <param name="search">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasStationController.GetCurrentUserInfoAsync(System.Int64)">
            <summary>
            获取单个工位详情信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasStationController.GetClassesInfoAsync(System.Int64)">
            <summary>
            获取工位中班次信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasStationController.MakeEnableAsync(System.Int32,System.Collections.Generic.List{System.Int64})">
            <summary>
            批量启用
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasStepController.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.StepPagedDto)">
            <summary>
            获取工序分页信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasStepController.GetAllAsync">
            <summary>
            获取工序分页信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasStepController.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.BasStepDto)">
            <summary>
            新增工序
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasStepController.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.BasStepDto)">
            <summary>
            更新工序
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasStepController.DeleteAsync(System.Int64)">
            <summary>
            删除工序
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasStepController.GetByIdAsync(System.Int64)">
            <summary>
            根据ID获取班次
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasStepController.ChangeStatus(Adnc.Huatek.Aps.Application.Dtos.ChangeStatusDto)">
            <summary>
            更新启用/禁用状态
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasStepController.ImportAsync(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            新增物料
            </summary>
            <param name="input">物料信息</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasTechnologyController.GetAllAsync">
            <summary>
            获取工艺信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasTechnologyController.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.TechnologyPagedDto)">
            <summary>
            获取工序分页信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasTechnologyController.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.BasTechnologyDto)">
            <summary>
            新增工艺
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasTechnologyController.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.BasTechnologyDto)">
            <summary>
            更新工序
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasTechnologyController.DeleteAsync(System.Int64)">
            <summary>
            删除工序
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasTechnologyController.GetByIdAsync(System.Int64)">
            <summary>
            获取工序详细信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasTechnologyController.GetByTCodeAsync(System.String)">
            <summary>
            通过工艺编码获取工序详细信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasTechnologyController.ChangeStatus(Adnc.Huatek.Aps.Application.Dtos.ChangeStatusDto)">
            <summary>
            更新启用/禁用状态
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasWorksjopController.GetAllAsync">
            <summary>
            获取所有信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasWorksjopController.GetWorkShopAsync">
            <summary>
            获取所有信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasWorksjopController.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.BasWorksjopDto)">
            <summary>
            新增车间
            </summary>
            <param name="input">车间信息</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasWorksjopController.DeleteAsync(System.Int64)">
            <summary>
            删除车间
            </summary>
            <param name="id">车间ID</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasWorksjopController.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.BasWorksjopDto)">
            <summary>
            修改车间
            </summary>
            <param name="id">id</param>
            <param name="input">车间信息</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasWorksjopController.ChangeStatus(System.Int64,Adnc.Shared.Application.Contracts.Dtos.SimpleDto{System.Int32})">
            <summary>
            变更车间状态
            </summary>
            <param name="id">车间ID</param>
            <param name="status">状态</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasWorksjopController.ChangeStatus(Adnc.Huatek.Aps.Application.Dtos.UpdateStatusDto)">
            <summary>
            批量变更车间状态
            </summary>
            <param name="input">车间Ids与状态</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasWorksjopController.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.BasStationPagedDto)">
            <summary>
            获取车间列表分页
            </summary>
            <param name="search">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.BasWorksjopController.GetCurrentUserInfoAsync(System.Int64)">
            <summary>
            获取单个车间详情信息
            </summary>
            <returns></returns>
        </member>
        <member name="T:Adnc.Huatek.Aps.Api.Controllers.HomePageController">
            <summary>
            首页
            </summary>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.HomePageController.GetOrderNumAsync(System.Int32)">
            <summary>
            获取订单数量
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.HomePageController.GetIdleDevicesCount">
            <summary>
            获取首页空闲设备数量
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.HomePageController.GetDevicesStatusCount">
            <summary>
            获取首页设备数量饼型图统计
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.HomePageController.GetOrderNumByDateAsync">
            <summary>
            获取首页订单数量曲线图数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.HomePageController.GetPlanByStatusAsync">
            <summary>
            首页计划数量统计
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.PartOrderController.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.PartOrderPagedDto)">
            <summary>
            获取订单分页信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.PartOrderController.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.PartOrderDto)">
            <summary>
            新增订单
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.PartOrderController.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.PartOrderDto)">
            <summary>
            更新订单
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.PartOrderController.DeleteAsync(System.Int64)">
            <summary>
            删除订单
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.PartOrderController.GetByIdAsync(System.Int64)">
            <summary>
            根据ID获取订单
            </summary>
            <returns></returns>
        </member>
        <member name="T:Adnc.Huatek.Aps.Api.Controllers.PartPlanPendingController">
            <summary>
            待排产计划
            </summary>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.PartPlanPendingController.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingPagedDto)">
            <summary>
            获取待排产计划分页信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.PartPlanPendingController.GetAllAsync">
            <summary>
            获取待排产计划分页信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.PartPlanPendingController.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingDto)">
            <summary>
            新增待排产计划
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.PartPlanPendingController.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingDto)">
            <summary>
            更新待排产计划
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.PartPlanPendingController.DeleteAsync(System.Int64)">
            <summary>
            删除待排产计划
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.PartPlanPendingController.GetByIdAsync(System.Int64)">
            <summary>
            根据ID获取待排产计划
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.PartPlanPendingController.GetPlanPendingByIdAsync(System.Int64)">
            <summary>
            根据ID获取待排产计划
            </summary>
            <returns></returns>
        </member>
        <member name="T:Adnc.Huatek.Aps.Api.Controllers.PartSchedulePlanController">
            <summary>
            排产计划
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Adnc.Huatek.Aps.Api.Controllers.PartSchedulePlanController.CreateSchedulePlanAsync(Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanDto)" -->
        <!-- Badly formed XML comment ignored for member "M:Adnc.Huatek.Aps.Api.Controllers.PartSchedulePlanController.TrialSchedulePlanAsync(Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanDto)" -->
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.PartSchedulePlanController.ChangeScheduleStatusAsync(Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanDto)">
            <summary>
            拖拉拽后保存接口
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.PartSchedulePlanController.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.PartSchedulPlanPageDto)">
            <summary>
            列表分页
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.PartSchedulePlanController.GetDeviceGanttPLanAsync(Adnc.Huatek.Aps.Application.Dtos.PartSchedulPlanSourcePageDto)">
            <summary>
            设备甘特图
            设备甘特图
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.PartSchedulePlanController.ChangeStepProgressAsync(Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanStepReportDto)">
            <summary>
            报工保存接口
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.PartSchedulePlanController.CheckSourceConflictAsync(Adnc.Huatek.Aps.Application.Dtos.QuerySourceConflict)">
            <summary>
            分析资源冲突列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:Adnc.Huatek.Aps.Api.Controllers.PlanProductionSchedulController">
            <summary>
            排产计划
            </summary>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.PlanProductionSchedulController.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulPageDto)">
            <summary>
            获取生产计划分页信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.PlanProductionSchedulController.Getdetails(System.Int64)">
            <summary>
            获取生产计划详情信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.PlanProductionSchedulController.GetDeviceGantt(System.String)">
            <summary>
            按排产编码获取设备甘特图
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.PlanProductionSchedulController.GetGanttByProduct(System.Int64)">
            <summary>
            获取产品排产计划甘特图
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.PlanProductionSchedulController.GetGanttByOrderNumber(Adnc.Huatek.Aps.Application.Dtos.ProductionSchedulGanttDto)">
            <summary>
            获取产品订单排产计划甘特图
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.PlanProductionSchedulController.GetGanttByOrderProductNumber(Adnc.Huatek.Aps.Application.Dtos.OrderProdcutGanttDto)">
            <summary>
            获取订单产品已排产计划甘特图
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.PlanProductionSchedulController.GetGanttByPlanCodeNumber(System.String)">
            <summary>
            按计划编码获取生产计划甘特图
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.PlanProductionSchedulController.GetMaterials(Adnc.Huatek.Aps.Application.Dtos.PlanMaterialDto)">
            <summary>
            获取物料计划信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.PlanProductionSchedulController.GetPersonnels(Adnc.Huatek.Aps.Application.Dtos.PersonnelDto)">
            <summary>
            获取人员计划信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.PlanProductionSchedulController.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDto)">
            <summary>
            新增排产计划
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.PlanProductionSchedulController.DeleteAsync(System.Collections.Generic.List{System.String})">
            <summary>
            批量删除排产计划
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.PlanProductionSchedulController.RealoadSchedulAsync(Adnc.Huatek.Aps.Application.Dtos.ReloadBasProcuct)">
            <summary>
            重排接口
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.PlanProductionSchedulController.setScheduleStatus(Adnc.Huatek.Aps.Application.Dtos.SchedulStatus)">
            <summary>
            修改排产状态
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.PlanProductionSchedulController.GetStepSolution(System.String)">
            <summary>
            查询排产工序方案
            </summary>
            <param name="code">排产编码</param>
            <returns></returns>
        </member>
        <member name="T:Adnc.Huatek.Aps.Api.Controllers.PlanProductionSchedulStepDetailsController">
            <summary>
            排产详情
            </summary>
        </member>
        <member name="T:Adnc.Huatek.Aps.Api.Controllers.PlanSchedulStepSolutionController">
            <summary>
            计划工序排产方案
            </summary>
        </member>
        <member name="T:Adnc.Huatek.Aps.Api.Controllers.SourceSchedulDeviceStationController">
            <summary>
            工位设备资源占用表
            </summary>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.StockMaterialController.GetAllAsync">
            <summary>
            获取所有信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.StockMaterialController.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.StockMaterialPagedDto)">
            <summary>
            获取物料列表分页
            </summary>
            <param name="search">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.StockMaterialController.GetByIdAsync(System.Int64)">
            <summary>
            根据ID获取数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.StockMaterialController.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.StockMaterialDto)">
            <summary>
            新增产线
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.StockMaterialController.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.StockMaterialDto)">
            <summary>
            更新产线
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.StockMaterialController.ImportAsync(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            新增物料
            </summary>
            <param name="input">物料信息</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Controllers.TestController.GetAllAsync">
            <summary>
            获取所有信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Util.DateUtils.TimestampToDate(System.String)">
            <summary>
            判断是否是时间戳
            </summary>
            <param name="timestamp"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Api.Util.DateUtils.CurrentTimeMillis">
            <summary>
            获取时间戳
            </summary>
            <returns></returns>
        </member>
    </members>
</doc>
