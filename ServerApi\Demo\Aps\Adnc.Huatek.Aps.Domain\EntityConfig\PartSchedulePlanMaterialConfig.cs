﻿using Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanMaterialAggregate;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class PartSchedulePlanMaterialConfig : AbstractEntityTypeConfiguration<PartSchedulePlanMaterial>
    {
        public override void Configure(EntityTypeBuilder<PartSchedulePlanMaterial> builder)
        {
            base.Configure(builder);
            builder.<PERSON><PERSON>ey(x => x.Id);
            builder.Property(x => x.ScheduleCode).HasColumnName("schedulecode");
            builder.Property(x => x.Mcode).HasColumnName("mcode");
            builder.Property(x => x.NeedDate).HasColumnName("needdate");
            builder.Property(x => x.Qty).HasColumnName("qty");
            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.CreateBy).HasColumnName("createby");
            builder.Property(x => x.CreateTime).HasColumnName("createtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
           
        }
    }
}
