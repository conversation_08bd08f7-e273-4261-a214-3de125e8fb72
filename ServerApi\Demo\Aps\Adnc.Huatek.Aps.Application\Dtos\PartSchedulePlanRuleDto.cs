﻿using Adnc.Demo.Shared.Rpc.Http.Rtos;
using Consul.Filtering;

namespace Adnc.Huatek.Aps.Application.Dtos
{
    /// <summary>
    /// 生产计划
    /// </summary>
    public class PartSchedulePlanRuleDto : Dto
    {
        /// <summary>
        /// ID
        /// </summary>
        public long Id { get; set; }
        /// <summary>
        /// 规则类型
        /// </summary>
        public string? RuleType { get; set; }

        /// <summary>
        /// 规则类型
        /// </summary>
        public string? RuleTypeName { get; set; }

        /// <summary>
        /// 规则值
        /// </summary>
        public string? RuleValue { get; set; }
        /// <summary>
        /// 规则值
        /// </summary>
        public string? RuleValueName { get; set; }

        /// <summary>
        /// 排产计划编码
        /// </summary>
        public string? ScheduleCode { get; set; }

        public List<DictEnumTreeRto> Nodes { get; set; }

    }

}
