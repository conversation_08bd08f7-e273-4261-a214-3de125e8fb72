﻿using Adnc.Demo.Shared.Const;
using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Application.Services;
using Adnc.Shared.Application.Contracts.ResultModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Adnc.Huatek.Aps.Api.Controllers
{
    /// <summary>
    /// 排产计划
    /// </summary>
    [Route($"{RouteConsts.ApsRoot}/Schedul")]
    [ApiController]
    [AllowAnonymous]
    public class PlanProductionSchedulController : AdncControllerBase
    {
        private readonly IPlanProductionSchedulService _planSrv;

        public PlanProductionSchedulController(IPlanProductionSchedulService planSrv) => _planSrv = planSrv;

        /// <summary>
        /// 获取生产计划分页信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("page")]
        public async Task<ActionResult<PageModelDto<PlanProductionSchedulResultDto>>> GetPagedAsync([FromBody] PlanProductionSchedulPageDto search)
            => await _planSrv.GetPagedAsync(search);

        /// <summary>
        /// 获取生产计划详情信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getdetails/{id}")]
        public async Task<AppSrvResult<PartSchedulePlanDto>> Getdetails([FromRoute] long  id)
            => await _planSrv.GetDetailsNewAsync(id);

        /// <summary>
        /// 获取排产计划设备甘特图
        /// </summary>
        /// <returns></returns>
        //[AllowAnonymous]
        //[HttpPost("gantt/device")]
        //public async Task<ProductionSchedulDeviceGanttResultDto> GetDeviceGanttForDevice([FromBody] ProductionSchedulGanttDto search)
        //    => await _planSrv.GetGanttForDeviceAsync(search);

        /// <summary>
        /// 按排产编码获取设备甘特图
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("gantt/device/{scheduleCode}")]
        public async Task<List<ScheduleDeviceGanttItem>> GetDeviceGantt([FromRoute] string scheduleCode)
            => await _planSrv.GetDeviceGanttAsync(scheduleCode);

        /// <summary>
        /// 获取产品排产计划甘特图
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("gantt/productplan/{id}")]
        public async Task<ActionResult<PlanGanttDto>> GetGanttByProduct([FromRoute] long id)
            => await _planSrv.GetGanttByProductNewAsync(id);

        /// <summary>
        /// 获取订单生产计划甘特图
        /// </summary>
        /// <returns></returns>
        //[AllowAnonymous]
        //[HttpPost("gantt/order/produceplan")]
        //public async Task<ActionResult<List<PlanProductionSchedulGanttNewResultDto>>> GetGanttForOrderProducePlan([FromBody] ProductionSchedulGanttDto search)
        //    => await _planSrv.GetGanttForOrderProducePlanAsync(search);

        /// <summary>
        /// 获取产品订单排产计划甘特图
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("gantt/orderplan")]
        public async Task<ActionResult<List<PlanProductionSchedulGanttForOrderDto>>> GetGanttByOrderNumber([FromBody] ProductionSchedulGanttDto search)
            => await _planSrv.GetGanttByOrderNewAsync(search);

        /// <summary>
        /// 获取订单产品已排产计划甘特图
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("gantt/orderproductplan")]
        public async Task<ActionResult<List<PlanProductionSchedulGanttNewResultDto>>> GetGanttByOrderProductNumber([FromBody] OrderProdcutGanttDto search)
            => await _planSrv.GetGanttByOrderProductAsync(search);

        /// <summary>
        /// 按计划编码获取生产计划甘特图
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("gantt/plan")]
        public async Task<ActionResult<List<PlanProductionSchedulGanttNewResultForPlanDto>>> GetGanttByPlanCodeNumber([FromQuery] string planCode)
            => await _planSrv.GetGanttByPlanCodeAsync(planCode);


        /// <summary>
        /// 获取物料计划信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("getmaterials")]
        public async Task<ActionResult<PlanMaterialListDto>> GetMaterials([FromBody] PlanMaterialDto search)
            => await _planSrv.GetMaterialsNew(search);

        /// <summary>
        /// 获取人员计划信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("getpersonnels")]
        public async Task<ActionResult<List<PersonnelResultDto>>> GetPersonnels([FromBody] PersonnelDto search)
            => await _planSrv.GetPersonnels(search);


        /// <summary>
        /// 新增排产计划
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("add")]
        public async Task<ActionResult<long>> CreateAsync([FromBody] PlanProductionSchedulDto input)
           => CreatedResult(await _planSrv.CreateAsync(input));

        /// <summary>
        /// 批量删除排产计划
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("batchDel")]
        public async Task<ActionResult<long>> DeleteAsync([FromBody] List<string> codes)
           => CreatedResult(await _planSrv.BatchDelAsync(codes));

        /// <summary>
        /// 重排接口
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("reschedulingproduction")]
        public async Task<ActionResult<List<dynamic>>> RealoadSchedulAsync([FromBody] ReloadBasProcuct input) 
            => CreatedResult(await _planSrv.RealoadSchedulAsync(input));


        [AllowAnonymous]
        [HttpPost("saveSchedulingProduction")]
        public async Task<ActionResult<long>> SaveSchedulingProduction([FromBody] ReloadBasProcuct input)
           => CreatedResult(await _planSrv.SaveSchedulingProduction(input));
        

        /// <summary>
        /// 修改排产状态
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("setScheduleStatus")]
        public async Task<ActionResult> setScheduleStatus([FromBody] SchedulStatus input)
            => Result(await _planSrv.setScheduleStatusAsync(input));

        /// <summary>
        /// 查询排产工序方案
        /// </summary>
        /// <param name="code">排产编码</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("stepsolution/{code}")]
        public async Task<List<SchduleStepSolutionItem>> GetStepSolution([FromRoute] string code)
            => await _planSrv.GetStepSolution(code);


        [AllowAnonymous]
        [HttpPost("material")]
        public async Task<ActionResult<PageModelDto<MaterialWarningReturnDto>>> GetPagedAsync([FromBody] MaterialWarningPagedDto search)
    => await _planSrv.ComputeMaterialWarningNew(search);

        [AllowAnonymous]
        [HttpPost("materialchart")]
        public async Task<MaterialWarningChartDto> GetChartAsync([FromBody] string Mcode)
    =>await _planSrv.GetMaterialWarningChart(Mcode);


    }

}
