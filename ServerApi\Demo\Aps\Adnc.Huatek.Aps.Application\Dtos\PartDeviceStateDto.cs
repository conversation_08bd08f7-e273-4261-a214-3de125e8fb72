﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;

namespace Adnc.Huatek.Aps.Application.Dtos
{
    public class PartDeviceStateDto : IDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool Isdeleted { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public long CreateBy { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 更新人
        /// </summary>
        public long? ModifyBy { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int? Status { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        /// 
        [Display(Name = "运行状态")]
        public string? StatusDis { get; set; }

        /// <summary>
        /// 设备类型
        /// </summary>
        public string? Dtype { get; set; }


        /// <summary>
        /// 设备类型编码
        /// </summary>
        public string? DtypeDis { get; set; }

        /// <summary>
        /// 设备编码
        /// </summary>
        /// 
        [Display(Name = "设备编码")]
        public string? DevCode { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        /// 
        [Display(Name = "设备名称")]
        public string? DevName { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        [Display(Name = "开始时间")]
        public DateTime? BeginTime { get; set; }


        /// <summary>
        /// 结束时间
        /// </summary>
        [Display(Name = "结束时间")]
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 创建人名称
        /// </summary>
        public string? CreatedName { get; set; }

        /// <summary>
        /// 修改人名称
        /// </summary>
        public string? ModifyName { get; set; }

        public decimal? Tat { get; set; }
    }


    public class DeviceStateDto : IDto
    {
        public DateTime targetDate { get; set; }

        public string? status { get; set; }
    }

    public class QueryDevStateDto : IDto
    {
        public string devCode { get; set; }

        public string targetDate { get; set; }
    }

}
