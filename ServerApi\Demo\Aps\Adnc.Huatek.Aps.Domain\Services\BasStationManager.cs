﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasDevice;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Services
{
    public class BasStationManager : IDomainService
    {
        private readonly IEfBasicRepository<BasStation> _stationRepo;

        public BasStationManager(IEfBasicRepository<BasStation> stationRepo)
        {
            _stationRepo = stationRepo;
        }


        public virtual async Task<BasStation> CreateAsync(string Code, string Name,int dur, int totaldur, string? devCode)
        {
            var exists = await _stationRepo.AnyAsync(x => x.Code == Code && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"工位编码:{Code}已存在，不可重复添加！");

            exists = await _stationRepo.AnyAsync(x => x.Name == Name && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"工位名称:{Name}已存在，不可重复添加！");

            if (!string.IsNullOrWhiteSpace(devCode))
            {
                var s = await _stationRepo.Where(x => x.DeviceCode == devCode && !x.IsDeleted).FirstOrDefaultAsync();
                if (s != null)
                    throw new BusinessException($"该设备已被工位 {s.Name} 占用，不可重复设置！");
            }

            if (dur != totaldur)
            {
                throw new BusinessException($"标准工作时长必须等于班次总时长");
            }
            return new BasStation()
            {
                Id = IdGenerater.GetNextId(),
                Name = Name,
                Code = Code
            };
        }

        public virtual async Task<bool> UpdateAsync(string Code, string Name, long id, int dur, int totaldur)
        {
            var exists = await _stationRepo.AnyAsync(x => x.Code == Code && x.Id != id && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"工位编码:{Code}已存在，不可重复添加！");

            exists = await _stationRepo.AnyAsync(x => x.Name == Name && x.Id != id && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"工位名称:{Name}已存在，不可重复添加！");
            if (dur != totaldur)
            {
                throw new BusinessException($"标准工作时长必须等于班次总时长");
            }
            return true;
        }
    }
}
