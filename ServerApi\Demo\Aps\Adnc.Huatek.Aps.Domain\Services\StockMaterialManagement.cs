﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasDevice;
using Adnc.Huatek.Aps.Domain.Aggregates.BasLineAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasMaterial;
using Adnc.Huatek.Aps.Domain.Aggregates.StockMaterial;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace Adnc.Huatek.Aps.Domain.Services
{
    
    public class StockMaterialManagement : IDomainService
    {
        private readonly IEfBasicRepository<StockMaterial> _stockMaterialRepo;
        private readonly IEfBasicRepository<BasMaterial> _basMaterialRepo;

        public StockMaterialManagement(IEfBasicRepository<StockMaterial> stockMaterialRepo, IEfBasicRepository<BasMaterial> basMaterialRepo)
        {
            _stockMaterialRepo = stockMaterialRepo;
            _basMaterialRepo = basMaterialRepo;
        }


        public virtual async Task<(List<StockMaterial> insertEntities, List<StockMaterial> updateEntities, string Msg)> ImportAsync(List<StockMaterial> entities)
        {
            List<StockMaterial> insertEntities = new List<StockMaterial>();
            List<StockMaterial> updateEntities = new List<StockMaterial>();
            StringBuilder sb = new StringBuilder();
            if (entities.Any())
            {
                foreach (var item in entities)
                {
                    var exists = await _basMaterialRepo.AnyAsync(x => x.Code == item.Mcode && !x.IsDeleted);
                    if (!exists)
                    {
                        sb.Append($"物料编码:{item.Mcode}不存在，请前往基础数据-物料管理维护该物料信息后再试");
                    }
                    else
                    {
                        var oldObj = _stockMaterialRepo.Where(x => x.Mcode == item.Mcode && !x.IsDeleted, noTracking: false).FirstOrDefault();
                        if (oldObj == null)
                        {
                            var obj = new StockMaterial();
                            obj.Id = IdGenerater.GetNextId();
                            obj.Mcode = item.Mcode;
                            obj.Qty = item.Qty;
                            obj.Prelockqty = 0;
                            insertEntities.Add(obj);
                        }
                        else
                        {
                            oldObj.Qty = item.Qty;
                            updateEntities.Add(oldObj);
                        }
                    }


                }
            }
            return (insertEntities, updateEntities,sb.ToString());
        }

        public virtual async Task<StockMaterial> CreateAsync(string Mcode,decimal qty)
        {
            var exists = await _basMaterialRepo.AnyAsync(x => x.Code == Mcode && !x.IsDeleted);
            if (!exists)
                throw new BusinessException($"物料编码:{Mcode}不存在，请前往基础数据-物料管理维护该物料信息后再试！");

                exists = await _stockMaterialRepo.AnyAsync(x => x.Mcode == Mcode && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"物料编码:{Mcode}已存在，不可重复操作！");

            return new StockMaterial()
            {
                Id = IdGenerater.GetNextId(),
                Mcode = Mcode,
                Qty = qty,
                Prelockqty = 0
            };
        }

        public virtual async Task<bool> UpdateAsync(string Mcode, long id)
        {
            var exists = await _stockMaterialRepo.AnyAsync(x => x.Mcode == Mcode && x.Id != id && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"物料编码:{Mcode}库存已维护，不可重复添加！");

            return true;
        }

    }
}
