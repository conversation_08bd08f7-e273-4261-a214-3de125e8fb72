﻿using Adnc.Infra.Entities;

namespace Adnc.Demo.Maint.Application.Services.Implements;

public class DictAppService : AbstractAppService, IDictAppService
{
    private readonly IEfRepository<Dict> _dictRepository;
    private readonly BloomFilterFactory _bloomFilterFactory;
    private readonly CacheService _cacheService;

    public DictAppService(
        IEfRepository<Dict> dictRepository,
        BloomFilterFactory bloomFilterFactory,
        CacheService cacheService)
    {
        _dictRepository = dictRepository;
        _bloomFilterFactory = bloomFilterFactory;
        _cacheService = cacheService;
    }

    public async Task<AppSrvResult<long>> CreateAsync(DictCreationDto input)
    {
        input.TrimStringFields();
        var exists = await _dictRepository.AnyAsync(x => x.Name.Equals(input.Name));
        if (exists)
            return Problem(HttpStatusCode.BadRequest, "字典名字已经存在");

        var dists = new List<Dict>();
        long id = IdGenerater.GetNextId();
        var dict = new Dict { Id = id, Name = input.Name, Value = input.Value, status = input.Status, Pid = 0 };

        dists.Add(dict);
        input.Children?.ForEach(x =>
        {
            if (!(string.IsNullOrWhiteSpace(x.Name) && string.IsNullOrWhiteSpace(x.Value)))
            {
                dists.Add(new Dict
                {
                    Id = IdGenerater.GetNextId(),
                    Pid = id,
                    Name = x.Name,
                    Value = x.Value,
                    status = x.Status
                });
            }
           
        });

        var cacheKey = _cacheService.ConcatCacheKey(CachingConsts.DictSingleKeyPrefix, id);
        var cahceBf = _bloomFilterFactory.Create(CachingConsts.BloomfilterOfCacheKey);
        var addedStatus = await cahceBf.AddAsync(cacheKey);
        if (!addedStatus)
            return Problem(HttpStatusCode.BadRequest, "添加到布隆过滤器失败!");
        else
            await _dictRepository.InsertRangeAsync(dists);
        return id;
    }

    public async Task<AppSrvResult> UpdateAsync(long id, DictUpdationDto input)
    {
        input.TrimStringFields();
        var exists = await _dictRepository.AnyAsync(x => x.Name.Equals(input.Name) && x.Id != input.Id);
        if (exists)
            return Problem(HttpStatusCode.BadRequest, "字典名字已经存在");

        var dict = new Dict { Name = input.Name, Value = input.Value, Id = input.Id, Pid = 0, status = input.Status };

        var subDicts = new List<Dict>();
        input.Children?.ForEach(x =>
        {
            if (!(string.IsNullOrWhiteSpace(x.Name) && string.IsNullOrWhiteSpace(x.Value)))
            {
                subDicts.Add(new Dict
                {
                    Id = IdGenerater.GetNextId(),
                    Pid = input.Id,
                    Name = x.Name,
                    Value = x.Value,
                    status = x.Status
                });
            }
        });

        await _dictRepository.UpdateAsync(dict, UpdatingProps<Dict>(d => d.Name, d => d.Value, d => d.status));

        if (subDicts.IsNotNullOrEmpty())
        {
            await _dictRepository.DeleteRangeAsync(d => d.Pid == dict.Id);
            await _dictRepository.InsertRangeAsync(subDicts);
        }

        return AppSrvResult();
    }

    public async Task<AppSrvResult> DeleteAsync(long id)
    {
        await _dictRepository.DeleteRangeAsync(d => d.Id == id || d.Pid == id);
        return AppSrvResult();
    }

    public async Task<DictDto> GetAsync(long id)
    {
        var dictEntity = await _dictRepository.FindAsync(id);
        if (dictEntity is null)
            return default;

        var dictDto = Mapper.Map<DictDto>(dictEntity);
        var subDictEnties = await _dictRepository.Where(x => x.Pid == id).ToListAsync();
        if (subDictEnties is not null)
        {
            var subDictDtos = Mapper.Map<List<DictDto>>(subDictEnties);
            dictDto.Children = subDictDtos;
        }
        return dictDto;
    }


    public async Task<List<EnumDto>> GetByCodeAsync(string name)
    {

        //var whereCondition = ExpressionCreator
        //                                    .New<Dict>(x => x.Pid == 0)
        //                                    .And(x => !x.IsDeleted && x.status)
        //                                    .And(x => x.Name == name);

        var dictEntity = await _dictRepository.Where(x=> !x.IsDeleted && x.status && x.Name == name).FirstOrDefaultAsync();
        if (dictEntity is null)
            return default;

        var dictDto = Mapper.Map<DictDto>(dictEntity);
        var subDictEnties = await _dictRepository.Where(x => x.Pid == dictEntity.Id).ToListAsync();
        List<EnumDto> enums = subDictEnties.Select(x => new EnumDto { label = x.Name, value = x.Value }).ToList();
        return enums;
    }

    public async Task<DictDto> GetDetail(DictSearchDetailDto input)
    {
        var dictEntity = await _dictRepository.FindAsync(x => x.Name == input.Name || x.Value == input.Value);
        if (dictEntity is null)
            return default;

        var dictDto = Mapper.Map<DictDto>(dictEntity);

        return dictDto;
    }

    public async Task<PageModelDto<DictDto>> GetListAsync(DictSearchDto search)
    {
        search.TrimStringFields();
        List<long> ids = new List<long>();
        var result = new List<DictDto>();
        var pwhere = ExpressionCreator
            .New<Dict>(x => !x.IsDeleted)
             .AndIf(search.Name.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Name!, $"%{search.Name}%"));

        var dics = _dictRepository.Where(pwhere).ToList();
        ids.AddRange(dics.Where(x => x.Pid == 0).Select(x => x.Id));
        ids.AddRange(dics.Where(x => x.Pid != 0).Select(x => x.Pid));

        var whereCondition = ExpressionCreator
            .New<Dict>(x => x.Pid == 0)
            .And(x=>!x.IsDeleted)
            .And(x => ids.Contains(x.Id));

        var total = await _dictRepository.CountAsync(whereCondition);
        if (total == 0)
            return new PageModelDto<DictDto>(search);

        var dictEntities = await _dictRepository
            .Where(whereCondition)
            .Skip(search.SkipRows())
            .Take(search.PageSize)
            .OrderBy(d => d.status)
            .ToListAsync();

        if (dictEntities is null)
            return new PageModelDto<DictDto>();

        var subPids = dictEntities.Select(x => x.Id);
        var allSubDictEntities = await _dictRepository.Where(x => subPids.Contains(x.Pid)).ToListAsync();

        var dictDtos = Mapper.Map<List<DictDto>>(dictEntities);
        var allSubDictDtos = Mapper.Map<List<DictDto>>(allSubDictEntities);
        foreach (var dto in dictDtos)
        {
            var subDtos = allSubDictDtos?.Where(x => x.Pid == dto.Id).ToList();
            if (subDtos != null)
            {
                if (subDtos.IsNotNullOrEmpty())
                {
                    dto.Children = subDtos;
                }
            }
        }

        return new PageModelDto<DictDto>(search, dictDtos, total);
    }

    public async Task<EnumTreeDto> GetChildByCodeAsync(string name)
    {
        var results = new EnumTreeDto();
        //var whereCondition = ExpressionCreator
        //                                    .New<Dict>(x => x.Pid == 0)
        //                                    .And(x => !x.IsDeleted && x.status)
        //                                    .And(x => x.Name == name);

        var dictEntity = await _dictRepository.Where(x => !x.IsDeleted && x.status && x.Name == name).FirstOrDefaultAsync();
        if (dictEntity is null)
            return default;

        results.value = dictEntity.Value;
        results.label = dictEntity.Name;
        

        var subDictEnties = await _dictRepository.Where(x => x.Pid == dictEntity.Id).ToListAsync();
        List<EnumTreeDto> enums = subDictEnties.Select(x => new EnumTreeDto { label = x.Name, value = x.Value }).ToList();

        foreach(var item in enums)
        {
            var mainEntity = await _dictRepository.Where(x => !x.IsDeleted && x.status && x.Name == (name+"-"+ item.label)).FirstOrDefaultAsync();
            var subDictobjs = await _dictRepository.Where(x => x.Pid == mainEntity.Id).ToListAsync();
           item.childs = subDictobjs?.Select(x => new EnumTreeDto { label = x.Name, value = x.Value })?.ToList()??new List<EnumTreeDto>();

        }
        results.childs = enums;

        return results;
    }
}