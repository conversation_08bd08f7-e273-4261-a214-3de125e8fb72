﻿using Adnc.Huatek.Aps.Application.Dtos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services
{
    public interface IHomePageService : IAppService
    {
        [OperateLog(LogName = "获取对应订单状态数")]
        Task<int> GetOrderNumByStatusAsync(int orderStatus);

        [OperateLog(LogName = "获取首页空闲设备数量")]
        Task<int> GetIdleDevicesCount();
        [OperateLog(LogName = "获取首页设备数量饼型图统计")]
        Task<List<HomeDeviceNumDto>> GetDevicesStatusCount();

        [OperateLog(LogName = "首页订单数量统计")]
        Task<List<HomeOrderNumByDateDto>> GetOrderNumByDateAsync();
        [OperateLog(LogName = "首页计划数量统计")]
        Task<List<PlanNumByDateDto>> GetPlanByStatusAsync();
    }
}
