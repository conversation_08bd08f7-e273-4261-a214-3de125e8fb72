﻿using Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanAggregate;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class PartSchedulePlanConfig : AbstractEntityTypeConfiguration<PartSchedulePlan>
    {
        public override void Configure(EntityTypeBuilder<PartSchedulePlan> builder)
        {
            base.Configure(builder);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.Code).HasColumnName("code");
            builder.Property(x => x.PlanCode).HasColumnName("plancode");
            builder.Property(x => x.BeginDate).HasColumnName("begindate");
            builder.Property(x => x.PlanBeginDate).HasColumnName("planbegindate");
            builder.Property(x => x.PlanEndDate).HasColumnName("planenddate");
            builder.Property(x => x.ScheduleType).HasColumnName("scheduletype");
            builder.Property(x => x.State).HasColumnName("state");
            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.CreateBy).HasColumnName("createby");
            builder.Property(x => x.CreateTime).HasColumnName("createtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
           
        }
    }
}
