﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.BasDevice;
using Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.SysUserAggregate;
using Adnc.Huatek.Aps.Domain.Services;
using Adnc.Shared;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services.Implements
{
    public class HomePageService : AbstractAppService, IHomePageService
    {
        private readonly IEfBasicRepository<BasOrder> _basOrderRepo;
        private readonly IEfRepository<BasDevice> _deviceRepo;
        private readonly IEfBasicRepository<PlanProductionSchedule> _planRepo;
        private readonly IEfBasicRepository<PartPlanPending> _pendingplanRepo;

        public HomePageService(
            IEfBasicRepository<BasOrder> basOrderRepo,
            IEfRepository<BasDevice> deviceRepo,
            IEfBasicRepository<PlanProductionSchedule> planRepo,
            IEfBasicRepository<PartPlanPending> pendingplanRepo
            )
        {
            _basOrderRepo = basOrderRepo;
            _deviceRepo = deviceRepo;
            _planRepo = planRepo;
            _pendingplanRepo = pendingplanRepo;

        }
        public async Task<int> GetOrderNumByStatusAsync(int orderStatus)
        {
            var whereExpression = ExpressionCreator
                                                 .New<BasOrder>()
                                                 .And(x => !x.IsDeleted)
                                                 .AndIf(orderStatus > -1, x => x.Status == orderStatus);
            var total = await _basOrderRepo.CountAsync(whereExpression);
            return total;
        }

        /// <summary>
        /// 获取首页空闲设备数量
        /// </summary>
        /// <returns></returns>
        public async Task<int> GetIdleDevicesCount()
        {
            var num = 0;
            var sql = new StringBuilder();

            sql = sql.Append(@"select count(1) from bas_device bd 
                where bd.isdeleted =false and bd.status =1
                and not exists (
                select 1 from source_schedul_device_station ss where DATE_FORMAT(ss.usedate, '%Y-%m-%d') = CURDATE() 
                and ss.divicecode =bd.code 
                )
                 and not exists (
                     select 1 from part_device_state bdp
                    where CURDATE()>=bdp.beginTime and CURDATE()<=bdp.endTime  and bdp.devcode =bd.code 
                    )
");

            var result = _deviceRepo.AdoQuerier.QueryAsync<int>(sql.ToString());
            if (result.Result != null)
            {
                num = result.Result.FirstOrDefault();
            }
            return num;
        }

        /// <summary>
        /// 获取首页设备数量饼型图统计
        /// </summary>
        /// <returns></returns>
        public async Task<List<HomeDeviceNumDto>> GetDevicesStatusCount()
        {
            var sql = new StringBuilder();

            sql = sql.Append(@"select '占用' as Status, count(1) num from source_schedul_device_station 
                    where DATE_FORMAT(usedate, '%Y-%m-%d') = CURDATE() and status =1
                    union all
                    select '保养' as Status, count(1) num from part_device_state bdp
                    where CURDATE()>=bdp.beginTime and CURDATE()<=bdp.endTime  and bdp.status = '2'
                    union all
                    select '空闲' as Status, count(1) num from bas_device bd 
                    where bd.isdeleted =false and bd.status =1
                    and not exists (
                    select 1 from source_schedul_device_station ss where DATE_FORMAT(ss.usedate, '%Y-%m-%d') = CURDATE() and status =1
                    and ss.divicecode =bd.code 
                    )
                    and not exists (
                     select 1 from part_device_state bdp
                    where CURDATE()>=bdp.beginTime and CURDATE()<=bdp.endTime  and bdp.devcode =bd.code 
                    )
                ");

            var result = _deviceRepo.AdoQuerier.QueryAsync<HomeDeviceNumDto>(sql.ToString());
            if (result.Result != null)
            {
                var deviceList = result.Result.ToList();
                var total = deviceList.Sum(x => x.Num);
                if (total > 0)
                {
                    deviceList.ForEach(x =>
                    {
                        x.Num = Math.Round(((x.Num / total) * 100).Value, 2);
                    });
                }

                return deviceList;
            }
            return null;
        }
        /// <summary>
        /// 首页订单数量统计
        /// </summary>
        /// <returns></returns>
        public async Task<List<HomeOrderNumByDateDto>> GetOrderNumByDateAsync()
        {
            var start = DateTime.Now.AddYears(-1);
            var whereExpression = ExpressionCreator
                                                 .New<BasOrder>()
                                                 .And(x => !x.IsDeleted)
                                                 .And(x => x.CreateTime >= start);

            var orderlist = await _basOrderRepo.Where(whereExpression).ToListAsync();
            var results = orderlist.GroupBy(x => x.CreateTime.ToString("yyyy-MM"));
            var result = new List<HomeOrderNumByDateDto>();
            foreach (var item in results)
            {
                var o = new HomeOrderNumByDateDto()
                {
                    Orderdate = item.Key.ToString(),
                    Num = item.Count()
                };
                result.Add(o);
            }
            for (int i = 0; i < 12; i++)
            {
                var currentMonth = start.AddMonths(i).ToString("yyyy-MM");
                if (!result.Any(x => x.Orderdate == currentMonth))
                {
                    var o = new HomeOrderNumByDateDto()
                    {
                        Orderdate = currentMonth,
                        Num = 0
                    };
                    result.Add(o);
                }
            }
            return result.OrderBy(x => x.Orderdate).ToList();
        }

        /// <summary>
        /// 统计首页计划曲线图
        /// </summary>
        /// <returns></returns>
        public async Task<List<PlanNumByDateDto>> GetPlanByStatusAsync()
        {
            var whereExpression = ExpressionCreator
                                                 .New<PartPlanPending>()
                                                 .And(x => !x.IsDeleted)
                                                  .And(x => x.CreateTime >= DateTime.Now.AddYears(-1));

            var plans = await _pendingplanRepo.Where(whereExpression).Where(x => x.Status == PendingPlanStatusConst.Waiting).ToListAsync();

            //获取已排产的订单
            var schedulingPlans = await _pendingplanRepo.Where(whereExpression).Where(x => x.Status == PendingPlanStatusConst.Finished).ToListAsync();

            List<string> dateList = new List<string>();
            var planList = new List<PlanNumByDateDto>();
            var gplans = plans.GroupBy(x => x.CreateTime.ToString("yyyy-MM-dd"));
            var gs = schedulingPlans.GroupBy(x => x.CreateTime.ToString("yyyy-MM-dd"));
            dateList.AddRange(gplans.Select(x=>x.Key));
            dateList.AddRange(gs.Select(x => x.Key));
            foreach (var item in dateList)
            {
                var pendings = gplans.Where(x => x.Key == item);
                var finished = gs.Where(x => x.Key == item);
                var p = new PlanNumByDateDto()
                {
                    PlanDate = item,
                    PlanNum = pendings?.FirstOrDefault()?.Count() ?? 0,
                    SchedulingNum = finished?.FirstOrDefault()?.Count() ?? 0
                };
                planList.Add(p);
            }
            return planList;
        }
    }
}
