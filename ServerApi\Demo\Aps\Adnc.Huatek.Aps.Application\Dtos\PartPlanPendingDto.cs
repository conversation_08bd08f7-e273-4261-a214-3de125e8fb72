﻿using Adnc.Demo.Shared.Rpc.Http.Rtos;
using Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate;
using System;
using System.Collections.Generic;
using System.Drawing.Printing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Dtos
{
    /// <summary>
    /// 待排产计划
    /// </summary>
    public class PartPlanPendingDto : Dto
    {
        /// <summary>
        /// ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// ID
        /// </summary>
        public long IdDetail { get; set; }

        /// <summary>
        /// 计划编码
        /// </summary>
        public string? PlanCode { get; set; }
        /// <summary>
        /// 优先级
        /// </summary>
        public int Priority { get; set; }

        /// <summary>
        /// 优先级
        /// </summary>
        public string? PriorityDis { get; set; }
        /// <summary>
        /// 生产状态
        /// </summary>
        public int? Status { get; set; }

        /// <summary>
        /// 订单id
        /// </summary>
        public long? OrderId { get; set; }
        /// <summary>
        /// 订单编号
        /// </summary>
        public string OrderNumber { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// 交付日期
        /// </summary>
        public DateTime? DeliveryDate { get; set; }

        /// <summary>
        /// 计划排产数量
        /// </summary>
        public decimal Qty { get; set; }

        /// <summary>
        /// 产品实际数量
        /// </summary>
        public decimal? ProductQty { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string? ProductName { get; set; }
        /// <summary>
        /// 产品编码
        /// </summary>
        public string? ProductCode { get; set; }
        /// <summary>
        /// 工艺名称
        /// </summary>
        public string? TName { get; set; }
        /// <summary>
        /// 产品列表
        /// </summary>
        public List<BasOrderProductDto>? ProductList { get; set; }

        public List<PartSchedulePlanRuleDto>? Rules { get; set; }

    }

    public class PartPlanPendingResultDto : Dto
    {
        /// <summary>
        /// ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 计划编码
        /// </summary>
        public string? PlanCode { get; set; }
        /// <summary>
        /// 优先级
        /// </summary>
        public string? Priority { get; set; }

        /// <summary>
        /// 生产状态
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// 生产状态
        /// </summary>
        public string? StatusDis { get; set; }

        /// <summary>
        /// 订单id
        /// </summary>
        public long? OrderId { get; set; }
        /// <summary>
        /// 订单编号
        /// </summary>
        public string OrderNumber { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// 交付日期
        /// </summary>
        public string? DeliveryDate { get; set; }

        /// <summary>
        /// 计划排产数量
        /// </summary>
        public decimal Qty { get; set; }

        /// <summary>
        /// 产品实际数量
        /// </summary>
        public decimal? ProductQty { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string? ProductName { get; set; }
        /// <summary>
        /// 产品编码
        /// </summary>
        public string? ProductCode { get; set; }
        /// <summary>
        /// 工艺名称
        /// </summary>
        public string? TName { get; set; }
        /// <summary>
        /// 产品列表
        /// </summary>
        public List<BasOrderProductDto>? ProductList { get; set; }

    }

    public class BasOrderProductDto
    {
        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }
        /// <summary>
        /// 产品编码
        /// </summary>
        public string ProductCode { get; set; }
        /// <summary>
        /// 订单产品待排产数量
        /// </summary>
        public decimal Qty { get; set; }
    }

    public class PartPlanPendingPagedDto : SearchPagedDto
    {
        /// <summary>
        /// 优先级
        /// </summary>
        public string? Priority { get; set; }
        /// <summary>
        /// 生产状态
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// 订单编号
        /// </summary>
        public string? OrderNumber { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? UserName { get; set; }
        /// <summary>
        ///  产品编码
        /// </summary>
        public string? ProductCode { get; set; }
        /// <summary>
        ///  产品名称
        /// </summary>
        public string? ProductName { get; set; }
        
    }

    public class OrderDetailDto
    {

        /// <summary>
        /// 生产状态
        /// </summary>
        public int? Status { get; set; }
        public string StatusString { get; set; }

        /// <summary>
        /// 订单编号
        /// </summary>
        public string? OrderNumber { get; set; }
        /// <summary>
        /// 交付日期
        /// </summary>
        public DateTime? DeliveryDate { get; set; }
        /// <summary>
        /// 客户
        /// </summary>
        public string? UserName { get; set; }
        /// <summary>
        /// 优先级
        /// </summary>
        public int Priority { get; set; }
        public string PriorityString { get; set; }
        /// <summary>
        /// 计划开始日期
        /// </summary>
        public DateTime? PlanStartDate { get; set; }
        /// <summary>
        /// 计划结束日期
        /// </summary>
        public DateTime? PlanEndDate { get; set; }

        /// <summary>
        /// 物料列表
        /// </summary>
        public List<BasProductMaterialDto>? MaterialList { get; set; } = new List<BasProductMaterialDto>();
    }
    /// <summary>
    /// 订单物料BOM
    /// </summary>
    public class BasProductMaterialDto
    {
        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }
        /// <summary>
        /// 物料BOM
        /// </summary>
        public List<MaterialDto> Materials { get; set; } = new List<MaterialDto>();
    }

    public class MaterialDto
    {
        /// <summary>
        /// 物料编码
        /// </summary>
        public string MaterialName { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        public decimal Qty { get; set; }
    }
}
