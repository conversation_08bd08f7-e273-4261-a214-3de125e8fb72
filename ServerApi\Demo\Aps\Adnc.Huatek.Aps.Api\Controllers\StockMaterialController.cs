﻿using Adnc.Demo.Shared.Const;
using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Application.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Npoi.Mapper;

namespace Adnc.Huatek.Aps.Api.Controllers
{
    [Route($"{RouteConsts.ApsRoot}/stockmaterial")]
    [ApiController]
    public class StockMaterialController : AdncControllerBase
    {
        private readonly IStockMaterialService _stockMaterialSrv;

        public StockMaterialController(IStockMaterialService stockMaterialSrv) => _stockMaterialSrv = stockMaterialSrv;

        /// <summary>
        /// 获取所有信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getallasync")]
        public async Task<ResultJson> GetAllAsync() => await _stockMaterialSrv.GetAllAsync();

        /// <summary>
        /// 获取物料列表分页
        /// </summary>
        /// <param name="search">查询条件</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("page")]
        public async Task<ActionResult<PageModelDto<StockMaterialDto>>> GetPagedAsync([FromBody] StockMaterialPagedDto search)
            => await _stockMaterialSrv.GetPagedAsync(search);


        /// <summary>
        /// 根据ID获取数据
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getbyid/{id}")]
        public async Task<ActionResult<StockMaterialDto>> GetByIdAsync([FromRoute] long id)
            => await _stockMaterialSrv.GetByIdAsync(id);
        /// <summary>
        /// 新增产线
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("add")]
        public async Task<ActionResult> CreateAsync([FromBody] StockMaterialDto input)
         => Result(await _stockMaterialSrv.CreateAsync(input));

        /// <summary>
        /// 更新产线
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("edit")]
        public async Task<ActionResult> UpdateAsync([FromBody] StockMaterialDto input)
           => Result(await _stockMaterialSrv.UpdateAsync(input));

        /// <summary>
        /// 新增物料
        /// </summary>
        /// <param name="input">物料信息</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("importasync")]
        public async Task<ResultJson> ImportAsync(IFormFile formFile)
        {
            //通过上传文件流初始化Mapper
            var mapper = new Mapper(formFile.OpenReadStream());
            //读取sheet1的数据
            var stocks = mapper.Take<StockMaterialDto>("Sheet1").Select(i => i.Value).ToList();
            return await _stockMaterialSrv.ImportAsync(stocks);
        }

    }
}
