﻿using Adnc.Infra.Entities;

namespace Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate
{
    /// <summary>
    /// 待排产计划
    /// </summary>
    public class PartPlanPending : EfFullAuditEntity
    {
        /// <summary>
        /// ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 计划编码
        /// </summary>
        public string PlanCode { get; set; }

        /// <summary>
        /// 优先级
        /// </summary>
        public int Priority { get; set; }

        /// <summary>
        /// 生产状态
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 订单id
        /// </summary>
        public long OrderId { get; set; }

        /// <summary>
        /// 订单明细id
        /// </summary>
        public long IdDetail { get; set; }

        /// <summary>
        /// 订单编号
        /// </summary>
        public string OrderNumber { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 交付日期
        /// </summary>
        public DateTime DeliveryDate { get; set; }

        /// <summary>
        /// 计划排产数量
        /// </summary>
        public decimal Qty { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 产品编码
        /// </summary>
        public string ProductCode { get; set; }
    }
}