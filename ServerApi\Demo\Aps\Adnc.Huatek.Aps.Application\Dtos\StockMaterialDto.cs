﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;

namespace Adnc.Huatek.Aps.Application.Dtos
{
     public class StockMaterialDto : Dto
    {

        public long? Id { get; set; }
        /// <summary>
        /// 物料编码
        /// </summary>
        [Display(Name = "物料编码")]
        public string? Mcode { get; set; }
        /// <summary>
        /// 单位枚举
        /// </summary>
        public string? unit { get; set; }
        /// <summary>
        /// 单位显示值
        /// </summary>
        public string? unit_dis { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        [Display(Name = "物料名称")]
        public string? Mname { get; set; }

        /// <summary>
        /// 物料库存
        /// </summary>
        [Display(Name = "库存数量(可用)")]
        public decimal? Qty { get; set; }
        /// <summary>
        /// 预锁库存
        /// </summary>
        public decimal? PreLockqty { get; set; }

    }


    public class StockMaterialPagedDto : SearchPagedDto
    {
        /// <summary>
        ///  物料编码
        /// </summary>
        public string? Mcode { get; set; }
        /// <summary>
        ///  物料名称
        /// </summary>
        public string? Mname { get; set; }

    }
}
