﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Dtos
{
    public class BasStationDto : Dto
    {
        /// <summary>
        /// ID
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool Isdeleted { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 工位编码
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 工位名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 所属车间
        /// </summary>
        public long? Worksjopid { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 车间名称
        /// </summary>
        public string? WorksjopName { get; set; }

        /// <summary>
        /// 设备编码
        /// </summary>
        public string? DeviceCode { get; set; }

        /// <summary>
        /// 产线code
        /// </summary>
        public string? Linecode { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        public string? DeviceName { get; set; }
        /// <summary>
        /// 产线名称
        /// </summary>
        public string? LineName { get; set; }
        /// <summary>
        /// 标准工作时长(h)
        /// </summary>
        public int? Duration { get; set; }
        /// <summary>
        /// 工位中设置的班次列表
        /// </summary>
        public List<ClassesDto>? Classes { get; set; }

        public bool? isEdit { get; set; }
    }
}
