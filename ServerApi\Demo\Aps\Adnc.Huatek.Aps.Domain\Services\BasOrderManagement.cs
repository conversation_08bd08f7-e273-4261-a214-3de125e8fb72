﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductAggregate;
using System.Net;

namespace Adnc.Huatek.Aps.Domain.Services
{
    /// <summary>
    /// 客户订单
    /// </summary>
    public class BasOrderManagement : IDomainService
    {
        private readonly IEfBasicRepository<BasOrder> _basOrderRepo;
        private readonly IEfBasicRepository<BasProduct> _basProductRepo;

        public BasOrderManagement(IEfBasicRepository<BasOrder> basOrderRepo,
            IEfBasicRepository<BasProduct> basProductRepo) 
        {
            _basOrderRepo = basOrderRepo;
            _basProductRepo = basProductRepo;
        }


        public virtual async Task<List<BasProduct>> CreateAsync(List<string> pNames, List<string> salesNumber)
        {
            var exists =await _basOrderRepo.AnyAsync(x=> salesNumber.Contains(x.SaleNumber));
            if (exists) {
                var nums =await _basOrderRepo.Where(x => salesNumber.Contains(x.SaleNumber)).Select(x => x.SaleNumber).ToListAsync();
                var name = string.Join(";",nums);
                throw new BusinessException(HttpStatusCode.OK, $"销售单号:{name}已存在，不可重复导入！");
            }
            var pros = new List<BasProduct>();
            var proNames = await _basProductRepo.Where(x => pNames.Contains(x.Proname) && !x.IsDeleted).ToListAsync();
            if (proNames.Any())
            {
                pros.AddRange(proNames);
                foreach (var item in pNames)
                {
                    if (!pros.Any(x => x.Proname == item))
                    {
                        throw new BusinessException(HttpStatusCode.OK, $"产品名称{item}不存在，不可导入！");
                    }
                }
            }
            else {
                throw new BusinessException(HttpStatusCode.OK, $"产品名称不存在，不可导入！");
            }

            return pros;
        }

        public virtual async Task<bool> UpdateAsync(int Status, int Priority, long id)
        {
            
            return true;
        }
        /// <summary>
        /// 校验订单是否可以更新为已完成
        /// </summary>
        /// <param name="Status"></param>
        /// <param name="Priority"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public virtual async Task<bool> FinishedAsync(List<long> orderIds)
        {

            return true;
        }
    }
}
