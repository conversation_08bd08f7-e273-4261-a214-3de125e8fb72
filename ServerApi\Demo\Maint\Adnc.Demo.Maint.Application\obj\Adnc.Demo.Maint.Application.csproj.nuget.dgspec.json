{"format": 1, "restore": {"C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Demo\\Maint\\Adnc.Demo.Maint.Application\\Adnc.Demo.Maint.Application.csproj": {}}, "projects": {"C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Demo\\Maint\\Adnc.Demo.Maint.Application\\Adnc.Demo.Maint.Application.csproj": {"version": "0.9.9.4", "restore": {"projectUniqueName": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Demo\\Maint\\Adnc.Demo.Maint.Application\\Adnc.Demo.Maint.Application.csproj", "projectName": "Adnc.Demo.Maint.Application", "projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Demo\\Maint\\Adnc.Demo.Maint.Application\\Adnc.Demo.Maint.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Demo\\Maint\\Adnc.Demo.Maint.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Demo\\Maint\\Adnc.Demo.Maint.Repository\\Adnc.Demo.Maint.Repository.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Demo\\Maint\\Adnc.Demo.Maint.Repository\\Adnc.Demo.Maint.Repository.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Demo\\Shared\\Adnc.Demo.Shared.Rpc.Grpc\\Adnc.Demo.Shared.Rpc.Grpc.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Demo\\Shared\\Adnc.Demo.Shared.Rpc.Grpc\\Adnc.Demo.Shared.Rpc.Grpc.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Demo\\Shared\\Adnc.Demo.Shared.Rpc.Http\\Adnc.Demo.Shared.Rpc.Http.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Demo\\Shared\\Adnc.Demo.Shared.Rpc.Http\\Adnc.Demo.Shared.Rpc.Http.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared.Application\\Adnc.Shared.Application.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared.Application\\Adnc.Shared.Application.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Demo\\Maint\\Adnc.Demo.Maint.Repository\\Adnc.Demo.Maint.Repository.csproj": {"version": "0.9.9.4", "restore": {"projectUniqueName": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Demo\\Maint\\Adnc.Demo.Maint.Repository\\Adnc.Demo.Maint.Repository.csproj", "projectName": "Adnc.Demo.Maint.Repository", "projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Demo\\Maint\\Adnc.Demo.Maint.Repository\\Adnc.Demo.Maint.Repository.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Demo\\Maint\\Adnc.Demo.Maint.Repository\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Demo\\Shared\\Adnc.Demo.Shared.Const\\Adnc.Demo.Shared.Const.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Demo\\Shared\\Adnc.Demo.Shared.Const\\Adnc.Demo.Shared.Const.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.EfCore.MySQL\\Adnc.Infra.Repository.EfCore.MySql.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.EfCore.MySQL\\Adnc.Infra.Repository.EfCore.MySql.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Repository\\Adnc.Infra.Repository.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Repository\\Adnc.Infra.Repository.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared.Repository\\Adnc.Shared.Repository.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared.Repository\\Adnc.Shared.Repository.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared\\Adnc.Shared.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared\\Adnc.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Demo\\Shared\\Adnc.Demo.Shared.Const\\Adnc.Demo.Shared.Const.csproj": {"version": "0.9.9.4", "restore": {"projectUniqueName": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Demo\\Shared\\Adnc.Demo.Shared.Const\\Adnc.Demo.Shared.Const.csproj", "projectName": "Adnc.Demo.Shared.Const", "projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Demo\\Shared\\Adnc.Demo.Shared.Const\\Adnc.Demo.Shared.Const.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Demo\\Shared\\Adnc.Demo.Shared.Const\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Demo\\Shared\\Adnc.Demo.Shared.Rpc.Grpc\\Adnc.Demo.Shared.Rpc.Grpc.csproj": {"version": "0.9.9.4", "restore": {"projectUniqueName": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Demo\\Shared\\Adnc.Demo.Shared.Rpc.Grpc\\Adnc.Demo.Shared.Rpc.Grpc.csproj", "projectName": "Adnc.Demo.Shared.Rpc.Grpc", "projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Demo\\Shared\\Adnc.Demo.Shared.Rpc.Grpc\\Adnc.Demo.Shared.Rpc.Grpc.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Demo\\Shared\\Adnc.Demo.Shared.Rpc.Grpc\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Helper\\Adnc.Infra.Helper.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Helper\\Adnc.Infra.Helper.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared.Rpc\\Adnc.Shared.Rpc.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared.Rpc\\Adnc.Shared.Rpc.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared\\Adnc.Shared.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared\\Adnc.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Google.Protobuf": {"target": "Package", "version": "[3.21.2, )"}, "Grpc.Net.ClientFactory": {"target": "Package", "version": "[2.47.0, )"}, "Grpc.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[2.47.0, )"}, "Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[6.0.1, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[6.0.1, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[6.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Demo\\Shared\\Adnc.Demo.Shared.Rpc.Http\\Adnc.Demo.Shared.Rpc.Http.csproj": {"version": "0.9.9.4", "restore": {"projectUniqueName": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Demo\\Shared\\Adnc.Demo.Shared.Rpc.Http\\Adnc.Demo.Shared.Rpc.Http.csproj", "projectName": "Adnc.Demo.Shared.Rpc.Http", "projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Demo\\Shared\\Adnc.Demo.Shared.Rpc.Http\\Adnc.Demo.Shared.Rpc.Http.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Demo\\Shared\\Adnc.Demo.Shared.Rpc.Http\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Helper\\Adnc.Infra.Helper.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Helper\\Adnc.Infra.Helper.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared.Rpc\\Adnc.Shared.Rpc.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared.Rpc\\Adnc.Shared.Rpc.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared\\Adnc.Shared.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared\\Adnc.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[6.0.1, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[6.0.1, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[6.0.0, )"}, "Refit.HttpClientFactory": {"target": "Package", "version": "[6.3.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Consul\\Adnc.Infra.Consul.csproj": {"version": "*******", "restore": {"projectUniqueName": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Consul\\Adnc.Infra.Consul.csproj", "projectName": "Adnc.Infra.Consul", "projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Consul\\Adnc.Infra.Consul.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Consul\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Consul": {"target": "Package", "version": "[********, )"}, "Grpc.Net.Client": {"target": "Package", "version": "[2.47.0, )"}, "Microsoft.AspNetCore.Hosting": {"target": "Package", "version": "[2.2.7, )"}, "Microsoft.Extensions.Caching.Abstractions": {"target": "Package", "version": "[6.0.0, )"}, "Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[6.0.1, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[6.0.1, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[6.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj": {"version": "*******", "restore": {"projectUniqueName": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj", "projectName": "Adnc.Infra.Core", "projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[6.0.0, )"}, "Microsoft.Extensions.Hosting.Abstractions": {"target": "Package", "version": "[6.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[6.0.1, )"}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[6.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Dapper\\Adnc.Infra.Repository.Dapper.csproj": {"version": "*******", "restore": {"projectUniqueName": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Dapper\\Adnc.Infra.Repository.Dapper.csproj", "projectName": "Adnc.Infra.Repository.Dapper", "projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Dapper\\Adnc.Infra.Repository.Dapper.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Dapper\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Repository\\Adnc.Infra.Repository.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Repository\\Adnc.Infra.Repository.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Dapper": {"target": "Package", "version": "[2.0.123, )"}, "MySqlConnector": {"target": "Package", "version": "[2.1.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.EfCore.MySql\\Adnc.Infra.Repository.EfCore.MySql.csproj": {"version": "*******", "restore": {"projectUniqueName": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.EfCore.MySql\\Adnc.Infra.Repository.EfCore.MySql.csproj", "projectName": "Adnc.Infra.Repository.EfCore.MySql", "projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.EfCore.MySql\\Adnc.Infra.Repository.EfCore.MySql.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.EfCore.MySql\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.EfCore\\Adnc.Infra.Repository.EfCore.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.EfCore\\Adnc.Infra.Repository.EfCore.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Repository\\Adnc.Infra.Repository.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Repository\\Adnc.Infra.Repository.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"DotNetCore.CAP.MySql": {"target": "Package", "version": "[6.1.0, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[6.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.EfCore\\Adnc.Infra.Repository.EfCore.csproj": {"version": "*******", "restore": {"projectUniqueName": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.EfCore\\Adnc.Infra.Repository.EfCore.csproj", "projectName": "Adnc.Infra.Repository.EfCore", "projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.EfCore\\Adnc.Infra.Repository.EfCore.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.EfCore\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Repository\\Adnc.Infra.Repository.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Repository\\Adnc.Infra.Repository.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Z.EntityFramework.Plus.EFCore": {"target": "Package", "version": "[6.14.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.EventBus\\Adnc.Infra.EventBus.csproj": {"version": "*******", "restore": {"projectUniqueName": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.EventBus\\Adnc.Infra.EventBus.csproj", "projectName": "Adnc.Infra.EventBus", "projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.EventBus\\Adnc.Infra.EventBus.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.EventBus\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"DotNetCore.CAP": {"target": "Package", "version": "[6.1.0, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[6.0.0, )"}, "Polly": {"target": "Package", "version": "[7.2.3, )"}, "RabbitMQ.Client": {"target": "Package", "version": "[6.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Helper\\Adnc.Infra.Helper.csproj": {"version": "*******", "restore": {"projectUniqueName": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Helper\\Adnc.Infra.Helper.csproj", "projectName": "Adnc.Infra.Helper", "projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Helper\\Adnc.Infra.Helper.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Helper\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.AspNetCore.Http": {"target": "Package", "version": "[2.2.2, )"}, "System.Text.Encoding.CodePages": {"target": "Package", "version": "[7.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.IdGenerater\\Adnc.Infra.IdGenerater.csproj": {"version": "*******", "restore": {"projectUniqueName": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.IdGenerater\\Adnc.Infra.IdGenerater.csproj", "projectName": "Adnc.Infra.IdGenerater", "projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.IdGenerater\\Adnc.Infra.IdGenerater.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.IdGenerater\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Redis\\Adnc.Infra.Redis.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Redis\\Adnc.Infra.Redis.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Yitter.IdGenerator": {"target": "Package", "version": "[1.0.14, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Mapper\\Adnc.Infra.Mapper.csproj": {"version": "*******", "restore": {"projectUniqueName": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Mapper\\Adnc.Infra.Mapper.csproj", "projectName": "Adnc.Infra.Mapper", "projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Mapper\\Adnc.Infra.Mapper.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Mapper\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[11.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Mongo\\Adnc.Infra.Repository.Mongo.csproj": {"version": "*******", "restore": {"projectUniqueName": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Mongo\\Adnc.Infra.Repository.Mongo.csproj", "projectName": "Adnc.Infra.Repository.Mongo", "projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Mongo\\Adnc.Infra.Repository.Mongo.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Mongo\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Repository\\Adnc.Infra.Repository.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Repository\\Adnc.Infra.Repository.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Humanizer.Core": {"target": "Package", "version": "[2.14.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Redis.Caching\\Adnc.Infra.Redis.Caching.csproj": {"version": "*******", "restore": {"projectUniqueName": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Redis.Caching\\Adnc.Infra.Redis.Caching.csproj", "projectName": "Adnc.Infra.Redis.Caching", "projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Redis.Caching\\Adnc.Infra.Redis.Caching.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Redis.Caching\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Redis\\Adnc.Infra.Redis.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Redis\\Adnc.Infra.Redis.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Castle.Core.AsyncInterceptor": {"target": "Package", "version": "[2.1.0, )"}, "Polly": {"target": "Package", "version": "[7.2.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Redis\\Adnc.Infra.Redis.csproj": {"version": "*******", "restore": {"projectUniqueName": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Redis\\Adnc.Infra.Redis.csproj", "projectName": "Adnc.Infra.Redis", "projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Redis\\Adnc.Infra.Redis.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Redis\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[6.0.0, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.6.48, )"}, "protobuf-net": {"target": "Package", "version": "[3.1.17, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Repository\\Adnc.Infra.Repository.csproj": {"version": "*******", "restore": {"projectUniqueName": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Repository\\Adnc.Infra.Repository.csproj", "projectName": "Adnc.Infra.Repository", "projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Repository\\Adnc.Infra.Repository.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Repository\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"MongoDB.Driver": {"target": "Package", "version": "[2.16.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared.Application.Contracts\\Adnc.Shared.Application.Contracts.csproj": {"version": "0.9.9.4", "restore": {"projectUniqueName": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared.Application.Contracts\\Adnc.Shared.Application.Contracts.csproj", "projectName": "Adnc.Shared.Application.Contracts", "projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared.Application.Contracts\\Adnc.Shared.Application.Contracts.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared.Application.Contracts\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"FluentValidation": {"target": "Package", "version": "[11.1.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared.Application\\Adnc.Shared.Application.csproj": {"version": "0.9.9.4", "restore": {"projectUniqueName": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared.Application\\Adnc.Shared.Application.csproj", "projectName": "Adnc.Shared.Application", "projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared.Application\\Adnc.Shared.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Consul\\Adnc.Infra.Consul.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Consul\\Adnc.Infra.Consul.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Dapper\\Adnc.Infra.Repository.Dapper.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Dapper\\Adnc.Infra.Repository.Dapper.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.EfCore.MySql\\Adnc.Infra.Repository.EfCore.MySql.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.EfCore.MySql\\Adnc.Infra.Repository.EfCore.MySql.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.EventBus\\Adnc.Infra.EventBus.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.EventBus\\Adnc.Infra.EventBus.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Helper\\Adnc.Infra.Helper.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Helper\\Adnc.Infra.Helper.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.IdGenerater\\Adnc.Infra.IdGenerater.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.IdGenerater\\Adnc.Infra.IdGenerater.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Mapper\\Adnc.Infra.Mapper.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Mapper\\Adnc.Infra.Mapper.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Mongo\\Adnc.Infra.Repository.Mongo.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Mongo\\Adnc.Infra.Repository.Mongo.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Redis.Caching\\Adnc.Infra.Redis.Caching.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Redis.Caching\\Adnc.Infra.Redis.Caching.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared.Application.Contracts\\Adnc.Shared.Application.Contracts.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared.Application.Contracts\\Adnc.Shared.Application.Contracts.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared.Repository\\Adnc.Shared.Repository.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared.Repository\\Adnc.Shared.Repository.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared.Rpc\\Adnc.Shared.Rpc.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared.Rpc\\Adnc.Shared.Rpc.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared\\Adnc.Shared.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared\\Adnc.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"DotNetCore.CAP.Dashboard": {"target": "Package", "version": "[6.1.0, )"}, "DotNetCore.CAP.RabbitMQ": {"target": "Package", "version": "[6.1.0, )"}, "EFCore.NamingConventions": {"target": "Package", "version": "[6.0.0, )"}, "FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[11.1.0, )"}, "Grpc.Net.ClientFactory": {"target": "Package", "version": "[2.47.0, )"}, "Microsoft.Extensions.Http.Polly": {"target": "Package", "version": "[6.0.6, )"}, "SkyAPM.Diagnostics.CAP": {"target": "Package", "version": "[1.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared.Repository\\Adnc.Shared.Repository.csproj": {"version": "0.9.9.4", "restore": {"projectUniqueName": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared.Repository\\Adnc.Shared.Repository.csproj", "projectName": "Adnc.Shared.Repository", "projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared.Repository\\Adnc.Shared.Repository.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared.Repository\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Repository\\Adnc.Infra.Repository.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Repository\\Adnc.Infra.Repository.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared\\Adnc.Shared.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared\\Adnc.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[6.0.6, )"}, "Microsoft.EntityFrameworkCore.Relational": {"target": "Package", "version": "[6.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared.Rpc\\Adnc.Shared.Rpc.csproj": {"version": "0.9.9.4", "restore": {"projectUniqueName": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared.Rpc\\Adnc.Shared.Rpc.csproj", "projectName": "Adnc.Shared.Rpc", "projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared.Rpc\\Adnc.Shared.Rpc.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared.Rpc\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Core\\Adnc.Infra.Core.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Helper\\Adnc.Infra.Helper.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\Infrastructures\\Adnc.Infra.Helper\\Adnc.Infra.Helper.csproj"}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared\\Adnc.Shared.csproj": {"projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared\\Adnc.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[6.0.1, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[6.0.1, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[6.0.0, )"}, "Refit.HttpClientFactory": {"target": "Package", "version": "[6.3.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared\\Adnc.Shared.csproj": {"version": "0.9.9.4", "restore": {"projectUniqueName": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared\\Adnc.Shared.csproj", "projectName": "Adnc.Shared", "projectPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared\\Adnc.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\C3S\\compass\\JunXin2025Code\\ServerApi\\ServiceShared\\Adnc.Shared\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}}}