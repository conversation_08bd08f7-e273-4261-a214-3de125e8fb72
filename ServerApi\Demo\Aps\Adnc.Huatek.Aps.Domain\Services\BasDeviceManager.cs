﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasBomAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasDevice;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStation;
using Adnc.Huatek.Aps.Domain.Aggregates.PartDeviceStateAggregates;
using Adnc.Huatek.Aps.Domain.Aggregates.StockMaterial;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Services
{
    public class BasDeviceManager : IDomainService
    {
        private readonly IEfBasicRepository<BasDevice> _stationRepo;
        private readonly IEfBasicRepository<PartDeviceState> _partDevRepo;

        public BasDeviceManager(IEfBasicRepository<BasDevice> stationRepo, IEfBasicRepository<PartDeviceState> partDevRepo)
        {
            _stationRepo = stationRepo;
            _partDevRepo = partDevRepo;
        }

        public virtual async Task<BasDevice> CreateAsync(string Code, string Name,  string Remark, int Capacity, int Status,string Dtype,decimal Newness)
        {
            var exists = await _stationRepo.AnyAsync(x => x.Code == Code && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"设备编码:{Code}已存在，不可重复添加！");
            return new BasDevice()
            {
                Id = IdGenerater.GetNextId(),
                Name = Name,
                Code = Code,
                Capacity = Capacity,
                Remark = Remark,
                Status= Status,
                Dtype = Dtype,
                Newness= Newness
            };
        }

        public virtual async Task<bool> UpdateAsync(string Code, long id)
        {
            var exists = await _stationRepo.AnyAsync(x => x.Code == Code && x.Id != id && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"设备编码:{Code}已存在，不可重复添加！");

            return true;
        }


        public virtual async Task<bool> UpdateDeviceStateAsync(string Code,DateTime? dtBegin,DateTime? dtEnd,int? state,long id)
        {
            var oldData = await _partDevRepo.Where(x => !x.IsDeleted && x.Id != id && x.DevCode == Code && !(x.BeginTime>=dtEnd || x.EndTime <= dtBegin)).ToListAsync();
            if (oldData.Any())
            {

                throw new BusinessException($"设备编码:{Code}在时间段{dtBegin}~{dtEnd}已存在其他状态的数据，不可重复添加！");
            }

            return true;
        }




        public virtual async Task<(List<BasDevice> insertEntities, List<BasDevice> updateEntities, string Msg)> ImportAsync(List<BasDevice> entities)
        {
            List<BasDevice> insertEntities = new List<BasDevice>();
            List<BasDevice> updateEntities = new List<BasDevice>();
            StringBuilder sb = new StringBuilder();
            if (entities.Any())
            {
                foreach (var item in entities)
                {
                    var oldObj = _stationRepo.Where(x => x.Code == item.Code && !x.IsDeleted, noTracking: false).FirstOrDefault();
                    if (oldObj == null)
                    {
                        item.Id = IdGenerater.GetNextId();
                        insertEntities.Add(item);
                    }
                    else
                    {
                        oldObj.Name = item.Name;
                        oldObj.Status = item.Status;
                        oldObj.Dtype = item.Dtype;
                        oldObj.Newness = item.Newness;
                        oldObj.Remark = item.Remark;
                        updateEntities.Add(oldObj);
                    }
                }
            }
            return (insertEntities, updateEntities, sb.ToString());
        }



        public virtual async Task<List<PartDeviceState>> ImportDeviceStateAsync(List<PartDeviceState> entities)
        {
            List<PartDeviceState> insertEntities = new List<PartDeviceState>();
            StringBuilder sb = new StringBuilder();
            if (entities.Any())
            {
                foreach (var item in entities)
                {
                    var oldObj = _partDevRepo.Where(x => x.DevCode == item.DevCode && !x.IsDeleted && !(x.BeginTime >= item.EndTime || x.EndTime <= item.BeginTime), noTracking: false);
                    if (!oldObj.Any())
                    {
                        var objs = insertEntities.Where(x => x.DevCode == item.DevCode && !(x.BeginTime >= item.EndTime || x.EndTime <= item.BeginTime)).Any();
                        if (objs)
                        {
                            throw new BusinessException(@$"您所上传的excel文件中，设备{item.DevCode}在时间段{item.BeginTime}~{item.EndTime} 存在重复状态 请调整后再试");
                        }
                        item.Id = IdGenerater.GetNextId();
                        insertEntities.Add(item);
                    }
                    else
                    {
                        throw new BusinessException($"设备编码:{item.DevCode}在时间段{item.BeginTime}~{item.EndTime}已存在其他状态的数据，不可重复添加！");
                    }
                    
                }
            }
            return insertEntities;
        }
    }
}
