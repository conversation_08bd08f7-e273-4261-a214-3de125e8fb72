﻿using System;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Adnc.Huatek.Aps.Migrations.Migrations
{
    public partial class Update20231009 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterDatabase()
                .Annotation("MySql:CharSet", "utf8mb4 ");

            migrationBuilder.CreateTable(
                name: "eventtracker",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false, comment: ""),
                    eventid = table.Column<long>(type: "bigint", nullable: false, comment: ""),
                    trackername = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false, comment: "")
                        .Annotation("MySql:CharSet", "utf8mb4 "),
                    createby = table.Column<long>(type: "bigint", nullable: false, comment: "创建人"),
                    createtime = table.Column<DateTime>(type: "datetime(6)", nullable: false, comment: "创建时间/注册时间")
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_eventtracker", x => x.id);
                },
                comment: "事件跟踪/处理信息")
                .Annotation("MySql:CharSet", "utf8mb4 ");

            migrationBuilder.CreateTable(
                name: "sys_test",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false, comment: ""),
                    ht_name = table.Column<string>(type: "longtext", nullable: false, comment: "")
                        .Annotation("MySql:CharSet", "utf8mb4 "),
                    htdescription = table.Column<string>(type: "longtext", nullable: false, comment: "")
                        .Annotation("MySql:CharSet", "utf8mb4 "),
                    rowversion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: false, comment: "")
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn),
                    createby = table.Column<long>(type: "bigint", nullable: false, comment: ""),
                    createtime = table.Column<DateTime>(type: "datetime(6)", nullable: false, comment: "")
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_sys_test", x => x.id);
                },
                comment: "")
                .Annotation("MySql:CharSet", "utf8mb4 ");

            migrationBuilder.CreateIndex(
                name: "ix_eventtracker_eventid_trackername",
                table: "eventtracker",
                columns: new[] { "eventid", "trackername" },
                unique: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "eventtracker");

            migrationBuilder.DropTable(
                name: "sys_test");
        }
    }
}
