﻿using Adnc.Infra.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanAggregate
{
    public class PartSchedulePlan : EfFullAuditEntity
    {

        /// <summary>
        /// 排产计划编码
        /// </summary>
        public long Id { get; set; }
        /// <summary>
        /// 排产计划编码
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 生产计划编码
        /// </summary>
        public string PlanCode { get; set; }

        /// <summary>
        /// 排产计划编码
        /// </summary>
        public DateTime  PlanBeginDate { get; set; }

        /// <summary>
        /// 生产计划编码
        /// </summary>
        public DateTime PlanEndDate { get; set; }

        /// <summary>
        /// 生产计划编码
        /// </summary>
        public DateTime BeginDate { get; set; }

        /// <summary>
        /// 来源
        /// </summary>
        public int ScheduleType { get; set; }

        public int State { get; set; }
    }

   
}
