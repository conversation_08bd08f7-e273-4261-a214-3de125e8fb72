﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasDevice;
using Adnc.Huatek.Aps.Domain.Aggregates.BasMaterial;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Services
{
    
    public class BasMaterialManager : IDomainService
    {
        private readonly IEfBasicRepository<BasMaterial> _stationRepo;

        public BasMaterialManager(IEfBasicRepository<BasMaterial> stationRepo)
        {
            _stationRepo = stationRepo;
        }


        public virtual async Task<BasMaterial> CreateAsync(string Code, string Name,string unit,string Mtype, string Remark)
        {
            var exists = await _stationRepo.AnyAsync(x => x.Code == Code && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"物料编码:{Code}已存在，不可重复添加！");

            exists = await _stationRepo.AnyAsync(x => x.Name == Name && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"物料名称:{Name}已存在，不可重复添加！");

            return new BasMaterial()
            {
                Id = IdGenerater.GetNextId(),
                Name = Name,
                Code = Code,
                Unit = unit,
                Mtype = Mtype,
                Remark = Remark
            };
        }

        public virtual async Task<bool> UpdateAsync(string Code, string Name, long id)
        {
            var exists = await _stationRepo.AnyAsync(x => x.Code == Code && x.Id != id && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"物料编码:{Code}已存在，不可重复添加！");

            exists = await _stationRepo.AnyAsync(x => x.Name == Name && x.Id != id && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"物料名称:{Name}已存在，不可重复添加！");

            return true;
        }



        public virtual async Task<(List<BasMaterial> insertEntities, List<BasMaterial> updateEntities, string Msg)> ImportAsync(List<BasMaterial> entities)
        {
            List<BasMaterial> insertEntities = new List<BasMaterial>();
            List<BasMaterial> updateEntities = new List<BasMaterial>();
            StringBuilder sb = new StringBuilder();
            if (entities.Any())
            {
                foreach (var item in entities)
                {
                    var oldObj = _stationRepo.Where(x => x.Code == item.Code && !x.IsDeleted, noTracking: false).FirstOrDefault();
                    if (oldObj == null)
                    {
                        item.Id = IdGenerater.GetNextId();
                        insertEntities.Add(item);
                    }
                    else
                    {
                        oldObj.Name = item.Name;
                        oldObj.Status = item.Status;
                        oldObj.Mtype = item.Mtype;
                        oldObj.Unit = item.Unit;
                        oldObj.Remark = item.Remark;
                        updateEntities.Add(oldObj);
                    }
                }
            }
            return (insertEntities, updateEntities, sb.ToString());
        }
    }
}
