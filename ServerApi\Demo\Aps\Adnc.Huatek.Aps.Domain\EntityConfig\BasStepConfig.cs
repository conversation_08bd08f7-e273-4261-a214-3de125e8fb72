﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasStepAggregate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class BasStepConfig : AbstractEntityTypeConfiguration<BasStep>
    {
        public override void Configure(EntityTypeBuilder<BasStep> builder)
        {
            base.Configure(builder);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.Code).HasColumnName("code");
            builder.Property(x => x.Name).HasColumnName("name");
            builder.Property(x => x.Status).HasColumnName("status");
            builder.Property(x => x.StepTime ).HasColumnName("steptime");
            builder.Property(x => x.Remark).HasColumnName("remark");
            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.CreateBy).HasColumnName("createby");
            builder.Property(x => x.CreateTime).HasColumnName("createtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
            builder.Property(x => x.Tat).HasColumnName("tat");
        }
    }
}
