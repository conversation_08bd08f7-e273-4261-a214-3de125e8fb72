﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasMaterial;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{

    public class BasMaterialConfig : AbstractEntityTypeConfiguration<BasMaterial>
    {

        public override void Configure(EntityTypeBuilder<BasMaterial> builder)
        {
            base.Configure(builder);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.Status).HasColumnName("status");
            builder.Property(x => x.Code).HasColumnName("code");
            builder.Property(x => x.Name).HasColumnName("name");
            builder.Property(x => x.Mtype).HasColumnName("mtype");
            builder.Property(x => x.Unit).HasColumnName("unit");
            builder.Property(x => x.Remark).HasColumnName("Remark");
            builder.Property(x => x.CreateBy).HasColumnName("createdby");
            builder.Property(x => x.CreateTime).HasColumnName("createdtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
            builder.Property(x => x.ModifyName).HasColumnName("modifyname");
            builder.Property(x => x.CreatedName).HasColumnName("createdname");
        }
    }
}
