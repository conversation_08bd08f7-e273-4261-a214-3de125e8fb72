﻿using Adnc.Huatek.Aps.Domain.Aggregates.PlanSchedulStepSolutionAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.SourceSchedulDeviceStationAggregate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class SourceSchedulDeviceStationConfig : AbstractEntityTypeConfiguration<SourceSchedulDeviceStation>
    {
        public override void Configure(EntityTypeBuilder<SourceSchedulDeviceStation> builder)
        {
            base.Configure(builder);

            builder.HasKey(x => x.Id);
            builder.Property(x => x.OrderNumber).HasColumnName("ordernumber");
            builder.Property(x => x.StationCode).HasColumnName("stationcode");
            builder.Property(x => x.DiviceCode).HasColumnName("divicecode");
            builder.Property(x => x.UseDate).HasColumnName("usedate");
            builder.Property(x => x.Status).HasColumnName("status");
            builder.Property(x => x.Remark).HasColumnName("remark");

            builder.Property(x => x.SchedulCode).HasColumnName("schedulcode");
            builder.Property(x => x.StepCode).HasColumnName("stepcode");
            builder.Property(x => x.UseendDate).HasColumnName("useenddate");
            builder.Property(x => x.StepName).HasColumnName("stepname");

            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.CreateBy).HasColumnName("createby");
            builder.Property(x => x.CreateTime).HasColumnName("createtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
        }
    }
}
