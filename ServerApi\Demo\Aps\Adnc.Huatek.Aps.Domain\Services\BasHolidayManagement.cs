﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasHolidayAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasLineAggregate;
using Adnc.Infra.Helper;
using Adnc.Shared.Rpc.Event;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using static StackExchange.Redis.Role;

namespace Adnc.Huatek.Aps.Domain.Services
{
    public class BasHolidayManagement : IDomainService
    {
        private readonly IEfBasicRepository<BasHoliday> _basHolidayRepo;

        public BasHolidayManagement(IEfBasicRepository<BasHoliday> basHolidayRepo) 
        {
            _basHolidayRepo = basHolidayRepo;
        }


        public virtual async Task<BasHoliday> CreateAsync(DateTime date)
        {
            var exists = await _basHolidayRepo.AnyAsync(x => x.CurrentDate == date && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"日期:{date}已存在，不可重复添加！");
            return new BasHoliday()
            {
                Id= IdGenerater.GetNextId()
            };
        }

        public virtual async Task<bool> UpdateAsync(DateTime date, long id)
        {
            var exists = await _basHolidayRepo.AnyAsync(x => x.CurrentDate == date && x.Id != id && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"日期:{date}已存在，不可重复添加！");


            return true;
        }
    }
}
