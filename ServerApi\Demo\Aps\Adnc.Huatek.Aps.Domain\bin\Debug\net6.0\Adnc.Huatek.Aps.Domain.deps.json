{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"Adnc.Huatek.Aps.Domain/*******": {"dependencies": {"Adnc.Demo.Shared.Const": "*******", "Adnc.Demo.Shared.Rpc.Event": "*******", "Adnc.Infra.EventBus": "*******", "Adnc.Infra.Helper": "*******", "Adnc.Infra.IdGenerater": "*******", "Adnc.Infra.Repository": "*******", "Adnc.Shared": "*******", "Adnc.Shared.Domain": "*******", "NRules": "0.9.4"}, "runtime": {"Adnc.Huatek.Aps.Domain.dll": {}}}, "DnsClient/1.6.1": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0"}, "runtime": {"lib/net5.0/DnsClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "DotNetCore.CAP/6.1.0": {"dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.1", "Microsoft.Extensions.Options": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.0", "System.Text.Json": "6.0.0", "System.Threading.Channels": "7.0.0"}, "runtime": {"lib/netstandard2.1/DotNetCore.CAP.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.AspNetCore.Http/2.2.2": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Net.Http.Headers": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.2.19024"}}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"dependencies": {"Microsoft.Net.Http.Headers": "2.2.0", "System.Text.Encodings.Web": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.EntityFrameworkCore/6.0.6": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "6.0.6", "Microsoft.EntityFrameworkCore.Analyzers": "6.0.6", "Microsoft.Extensions.Caching.Memory": "6.0.1", "Microsoft.Extensions.DependencyInjection": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "System.Collections.Immutable": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "6.0.6.0", "fileVersion": "6.0.622.26602"}}}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.6": {"runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "6.0.6.0", "fileVersion": "6.0.622.26602"}}}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.6": {}, "Microsoft.EntityFrameworkCore.Relational/6.0.6": {"dependencies": {"Microsoft.EntityFrameworkCore": "6.0.6", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "6.0.6.0", "fileVersion": "6.0.622.26602"}}}, "Microsoft.Extensions.Caching.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Caching.Memory/6.0.1": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.1", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.222.6406"}}}, "Microsoft.Extensions.Configuration/6.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.322.12309"}}}, "Microsoft.Extensions.Configuration.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Configuration.Binder/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.DependencyInjection/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.FileProviders.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Hosting.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0"}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Http/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.1", "Microsoft.Extensions.Options": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Logging/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.1", "Microsoft.Extensions.Options": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.0"}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Logging.Abstractions/6.0.1": {"runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.322.12309"}}}, "Microsoft.Extensions.ObjectPool/2.2.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18315"}}}, "Microsoft.Extensions.Options/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.Binder": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Primitives/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Net.Http.Headers/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0", "System.Buffers": "4.5.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "Microsoft.Win32.SystemEvents/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "MongoDB.Bson/2.16.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/netstandard2.1/MongoDB.Bson.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MongoDB.Driver/2.16.1": {"dependencies": {"MongoDB.Bson": "2.16.1", "MongoDB.Driver.Core": "2.16.1", "MongoDB.Libmongocrypt": "1.5.3"}, "runtime": {"lib/netstandard2.1/MongoDB.Driver.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MongoDB.Driver.Core/2.16.1": {"dependencies": {"DnsClient": "1.6.1", "MongoDB.Bson": "2.16.1", "MongoDB.Libmongocrypt": "1.5.3", "SharpCompress": "0.30.1", "System.Buffers": "4.5.1"}, "runtime": {"lib/netstandard2.1/MongoDB.Driver.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "runtimeTargets": {"runtimes/linux/native/libsnappy64.so": {"rid": "linux", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux/native/libzstd.so": {"rid": "linux", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx/native/libsnappy64.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx/native/libzstd.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win/native/libzstd.dll": {"rid": "win", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win/native/snappy32.dll": {"rid": "win", "assetType": "native", "fileVersion": "1.1.1.7"}, "runtimes/win/native/snappy64.dll": {"rid": "win", "assetType": "native", "fileVersion": "1.1.1.7"}}}, "MongoDB.Libmongocrypt/1.5.3": {"runtime": {"lib/netstandard2.1/MongoDB.Libmongocrypt.dll": {"assemblyVersion": "1.5.3.0", "fileVersion": "1.5.3.0"}}, "runtimeTargets": {"runtimes/linux/native/libmongocrypt.so": {"rid": "linux", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx/native/libmongocrypt.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win/native/mongocrypt.dll": {"rid": "win", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "NRules/0.9.4": {"dependencies": {"NRules.Fluent": "0.9.4", "NRules.Runtime": "0.9.4"}}, "NRules.Fluent/0.9.4": {"dependencies": {"NRules.RuleModel": "0.9.4"}, "runtime": {"lib/netstandard2.1/NRules.Fluent.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NRules.RuleModel/0.9.4": {"runtime": {"lib/netstandard2.1/NRules.RuleModel.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NRules.Runtime/0.9.4": {"dependencies": {"NRules.RuleModel": "0.9.4"}, "runtime": {"lib/netstandard2.1/NRules.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Pipelines.Sockets.Unofficial/2.2.2": {"dependencies": {"System.IO.Pipelines": "5.0.1"}, "runtime": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.2.34088"}}}, "Polly/7.2.3": {"runtime": {"lib/netstandard2.0/Polly.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "protobuf-net/3.1.17": {"dependencies": {"protobuf-net.Core": "3.1.17"}, "runtime": {"lib/net5.0/protobuf-net.dll": {"assemblyVersion": "*******", "fileVersion": "3.1.17.6991"}}}, "protobuf-net.Core/3.1.17": {"runtime": {"lib/net5.0/protobuf-net.Core.dll": {"assemblyVersion": "*******", "fileVersion": "3.1.17.6991"}}}, "RabbitMQ.Client/6.5.0": {"dependencies": {"System.Memory": "4.5.5", "System.Threading.Channels": "7.0.0"}, "runtime": {"lib/netstandard2.0/RabbitMQ.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Refit/6.3.2": {"dependencies": {"System.Net.Http.Json": "6.0.0"}, "runtime": {"lib/net6.0/Refit.dll": {"assemblyVersion": "*******", "fileVersion": "6.3.2.28196"}}}, "Refit.HttpClientFactory/6.3.2": {"dependencies": {"Microsoft.Extensions.Http": "6.0.0", "Refit": "6.3.2"}, "runtime": {"lib/net6.0/Refit.HttpClientFactory.dll": {"assemblyVersion": "*******", "fileVersion": "6.3.2.28196"}}}, "SharpCompress/0.30.1": {"runtime": {"lib/net5.0/SharpCompress.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "StackExchange.Redis/2.6.48": {"dependencies": {"Pipelines.Sockets.Unofficial": "2.2.2", "System.Diagnostics.PerformanceCounter": "5.0.0"}, "runtime": {"lib/net5.0/StackExchange.Redis.dll": {"assemblyVersion": "*******", "fileVersion": "2.6.48.48654"}}}, "System.Buffers/4.5.1": {}, "System.Collections.Immutable/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Configuration.ConfigurationManager/5.0.0": {"dependencies": {"System.Security.Cryptography.ProtectedData": "5.0.0", "System.Security.Permissions": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Diagnostics.DiagnosticSource/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Diagnostics.PerformanceCounter/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.Win32.Registry": "5.0.0", "System.Configuration.ConfigurationManager": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Diagnostics.PerformanceCounter.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Drawing.Common/5.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "5.0.0"}, "runtime": {"lib/netcoreapp3.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp3.0/System.Drawing.Common.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}, "runtimes/win/lib/netcoreapp3.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.IO.Pipelines/5.0.1": {"runtime": {"lib/netcoreapp3.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.120.57516"}}}, "System.Memory/4.5.5": {}, "System.Net.Http.Json/6.0.0": {"dependencies": {"System.Text.Json": "6.0.0"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Security.AccessControl/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Security.Cryptography.ProtectedData/5.0.0": {"runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Security.Permissions/5.0.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Windows.Extensions": "5.0.0"}, "runtime": {"lib/net5.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encoding.CodePages/7.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Text.Encodings.Web/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Json/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "6.0.0"}}, "System.Threading.Channels/7.0.0": {"runtime": {"lib/net6.0/System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Windows.Extensions/5.0.0": {"dependencies": {"System.Drawing.Common": "5.0.0"}, "runtime": {"lib/netcoreapp3.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Yitter.IdGenerator/1.0.14": {"runtime": {"lib/netstandard2.0/Yitter.IdGenerator.dll": {"assemblyVersion": "1.0.14.0", "fileVersion": "1.0.14.0"}}}, "Adnc.Demo.Shared.Const/*******": {"runtime": {"Adnc.Demo.Shared.Const.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Demo.Shared.Rpc.Event/*******": {"dependencies": {"Adnc.Infra.Core": "*******", "Adnc.Shared": "*******", "Adnc.Shared.Rpc": "*******"}, "runtime": {"Adnc.Demo.Shared.Rpc.Event.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Infra.Core/*******": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Hosting.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.1", "Microsoft.Extensions.Options": "6.0.0"}, "runtime": {"Adnc.Infra.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Infra.EventBus/*******": {"dependencies": {"Adnc.Infra.Core": "*******", "DotNetCore.CAP": "6.1.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "6.0.0", "Polly": "7.2.3", "RabbitMQ.Client": "6.5.0"}, "runtime": {"Adnc.Infra.EventBus.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Infra.Helper/*******": {"dependencies": {"Adnc.Infra.Core": "*******", "Microsoft.AspNetCore.Http": "2.2.2", "System.Text.Encoding.CodePages": "7.0.0"}, "runtime": {"Adnc.Infra.Helper.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Infra.IdGenerater/*******": {"dependencies": {"Adnc.Infra.Core": "*******", "Adnc.Infra.Redis": "*******", "Yitter.IdGenerator": "1.0.14"}, "runtime": {"Adnc.Infra.IdGenerater.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Infra.Redis/*******": {"dependencies": {"Adnc.Infra.Core": "*******", "Microsoft.Extensions.Options.ConfigurationExtensions": "6.0.0", "StackExchange.Redis": "2.6.48", "protobuf-net": "3.1.17"}, "runtime": {"Adnc.Infra.Redis.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Infra.Repository/*******": {"dependencies": {"MongoDB.Driver": "2.16.1"}, "runtime": {"Adnc.Infra.Repository.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Shared/*******": {"runtime": {"Adnc.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Shared.Domain/*******": {"dependencies": {"Adnc.Infra.EventBus": "*******", "Adnc.Infra.Helper": "*******", "Adnc.Infra.Repository": "*******", "Adnc.Shared.Repository": "*******"}, "runtime": {"Adnc.Shared.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Shared.Repository/*******": {"dependencies": {"Adnc.Infra.Core": "*******", "Adnc.Infra.Repository": "*******", "Adnc.Shared": "*******", "Microsoft.EntityFrameworkCore": "6.0.6", "Microsoft.EntityFrameworkCore.Relational": "6.0.6"}, "runtime": {"Adnc.Shared.Repository.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Shared.Rpc/*******": {"dependencies": {"Adnc.Infra.Core": "*******", "Adnc.Infra.Helper": "*******", "Adnc.Shared": "*******", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Caching.Memory": "6.0.1", "Microsoft.Extensions.Configuration": "6.0.1", "Microsoft.Extensions.Options.ConfigurationExtensions": "6.0.0", "Refit.HttpClientFactory": "6.3.2"}, "runtime": {"Adnc.Shared.Rpc.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"Adnc.Huatek.Aps.Domain/*******": {"type": "project", "serviceable": false, "sha512": ""}, "DnsClient/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-4H/f2uYJOZ+YObZjpY9ABrKZI+JNw3uizp6oMzTXwDw6F+2qIPhpRl/1t68O/6e98+vqNiYGu+lswmwdYUy3gg==", "path": "dnsclient/1.6.1", "hashPath": "dnsclient.1.6.1.nupkg.sha512"}, "DotNetCore.CAP/6.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-KypVROHMh05rLYvAyHhRJpEpAfHJPG4lPs6nAzRznJp4c0SQ6PC2ADgB1B4ssAJfZubgYd1nyt7S5YcsXxp7ng==", "path": "dotnetcore.cap/6.1.0", "hashPath": "dotnetcore.cap.6.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http/2.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-BAibpoItxI5puk7YJbIGj95arZueM8B8M5xT1fXBn3hb3L2G3ucrZcYXv1gXdaroLbntUs8qeV8iuBrpjQsrKw==", "path": "microsoft.aspnetcore.http/2.2.2", "hashPath": "microsoft.aspnetcore.http.2.2.2.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "path": "microsoft.aspnetcore.http.features/2.2.0", "hashPath": "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ErxAAKaDzxXASB/b5uLEkLgUWv1QbeVxyJYEHQwMaxXOeFFVkQxiq8RyfVcifLU7NR0QY0p3acqx4ZpYfhHDg==", "path": "microsoft.aspnetcore.webutilities/2.2.0", "hashPath": "microsoft.aspnetcore.webutilities.2.2.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/6.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-icy5aMdp9R984RGmbgZvcjEX9HYcwqF+6HXLFydL7PJBlc9eVVPRdBSFS9mCFwXyFl24x7xUORhZx/cSLvwH7Q==", "path": "microsoft.entityframeworkcore/6.0.6", "hashPath": "microsoft.entityframeworkcore.6.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Z4Ep2LDUSSNjriin0wKH4jF4vsjQ2ICwC9/5ntDVShQqy1C8AmmE5oK25jfthEVSIosDhJoWCescV3xKa9kcpg==", "path": "microsoft.entityframeworkcore.abstractions/6.0.6", "hashPath": "microsoft.entityframeworkcore.abstractions.6.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-EqBhW1bJnDw42CIGf/Dh1TbYXFUh53pghb5KGMTrxcNU6Ntfd8UEHs7LntZrMQrECrkhW7zBvfGvv9SbxOj5VQ==", "path": "microsoft.entityframeworkcore.analyzers/6.0.6", "hashPath": "microsoft.entityframeworkcore.analyzers.6.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/6.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-sMFh4InretduD2ppxRJ9aArwDgg1EqUMp8iXaMoXn64eGW+8hyvuZGD3VdwJF6qQJWd4B6ns1/zkASR7MHOF2g==", "path": "microsoft.entityframeworkcore.relational/6.0.6", "hashPath": "microsoft.entityframeworkcore.relational.6.0.6.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bcz5sSFJbganH0+YrfvIjJDIcKNW7TL07C4d1eTmXy/wOt52iz4LVogJb6pazs7W0+74j0YpXFErvp++Aq5Bsw==", "path": "microsoft.extensions.caching.abstractions/6.0.0", "hashPath": "microsoft.extensions.caching.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-B4y+Cev05eMcjf1na0v9gza6GUtahXbtY1JCypIgx3B4Ea/KAgsWyXEmW4q6zMbmTMtKzmPVk09rvFJirvMwTg==", "path": "microsoft.extensions.caching.memory/6.0.1", "hashPath": "microsoft.extensions.caching.memory.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-BUyFU9t+HzlSE7ri4B+AQN2BgTgHv/uM82s5ZkgU1BApyzWzIl48nDsG5wR1t0pniNuuyTBzG3qCW8152/NtSw==", "path": "microsoft.extensions.configuration/6.0.1", "hashPath": "microsoft.extensions.configuration.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qWzV9o+ZRWq+pGm+1dF+R7qTgTYoXvbyowRoBxQJGfqTpqDun2eteerjRQhq5PQ/14S+lqto3Ft4gYaRyl4rdQ==", "path": "microsoft.extensions.configuration.abstractions/6.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-b3<PERSON>rKzND8LIC7o08QAVlKfaEIYEvLJbtmVbFZVBRXeu9YkKfSSzLZfR1SUfQPBIy9mKLhEtJgGYImkcMNaKE0A==", "path": "microsoft.extensions.configuration.binder/6.0.0", "hashPath": "microsoft.extensions.configuration.binder.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-k6PWQMuoBDGGHOQTtyois2u4AwyVcIwL2LaSLlTZQm2CYcJ1pxbt6jfAnpWmzENA/wfrYRI/X9DTLoUkE4AsLw==", "path": "microsoft.extensions.dependencyinjection/6.0.0", "hashPath": "microsoft.extensions.dependencyinjection.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xlzi2IYREJH3/m6+lUrQlujzX8wDitm4QGnUu6kUXTQAWPuZY8i+ticFJbzfqaetLA6KR/rO6Ew/HuYD+bxifg==", "path": "microsoft.extensions.dependencyinjection.abstractions/6.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0pd4/fho0gC12rQswaGQxbU34jOS1TPS8lZPpkFCH68ppQjHNHYle9iRuHeev1LhrJ94YPvzcRd8UmIuFk23Qw==", "path": "microsoft.extensions.fileproviders.abstractions/6.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GcT5l2CYXL6Sa27KCSh0TixsRfADUgth+ojQSD5EkzisZxmGFh7CwzkcYuGwvmXLjr27uWRNrJ2vuuEjMhU05Q==", "path": "microsoft.extensions.hosting.abstractions/6.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-15+pa2G0bAMHbHewaQIdr/y6ag2H3yh4rd9hTXavtWDzQBkvpe2RMqFg8BxDpcQWssmjmBApGPcw93QRz6YcMg==", "path": "microsoft.extensions.http/6.0.0", "hashPath": "microsoft.extensions.http.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eIbyj40QDg1NDz0HBW0S5f3wrLVnKWnDJ/JtZ+yJDFnDj90VoPuoPmFkeaXrtu+0cKm5GRAwoDf+dBWXK0TUdg==", "path": "microsoft.extensions.logging/6.0.0", "hashPath": "microsoft.extensions.logging.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-dzB2Cgg+JmrouhjkcQGzSFjjvpwlq353i8oBQO2GWNjCXSzhbtBRUf28HSauWe7eib3wYOdb3tItdjRwAdwCSg==", "path": "microsoft.extensions.logging.abstractions/6.0.1", "hashPath": "microsoft.extensions.logging.abstractions.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-gA8H7uQOnM5gb+L0uTNjViHYr+hRDqCdfugheGo/MxQnuHzmhhzCBTIPm19qL1z1Xe0NEMabfcOBGv9QghlZ8g==", "path": "microsoft.extensions.objectpool/2.2.0", "hashPath": "microsoft.extensions.objectpool.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Options/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dzXN0+V1AyjOe2xcJ86Qbo233KHuLEY0njf/P2Kw8SfJU+d45HNS2ctJdnEnrWbM9Ye2eFgaC5Mj9otRMU6IsQ==", "path": "microsoft.extensions.options/6.0.0", "hashPath": "microsoft.extensions.options.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXWINbTn0vC0FYc9GaQTISbxhQLAMrvtbuvD9N6JelEaIS/Pr62wUCinrq5bf1WRBGczt1v4wDhxFtVFNcMdUQ==", "path": "microsoft.extensions.options.configurationextensions/6.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9+PnzmQFfEFNR9J2aDTfJGGupShHjOuGw4VUv+JB044biSHrnmCIMD+mJHmb2H7YryrfBEXDurxQ47gJZdCKNQ==", "path": "microsoft.extensions.primitives/6.0.0", "hashPath": "microsoft.extensions.primitives.6.0.0.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-iZNkjYqlo8sIOI0bQfpsSoMTmB/kyvmV2h225ihyZT33aTp48ZpF6qYnXxzSXmHt8DpBAwBTX+1s1UFLbYfZKg==", "path": "microsoft.net.http.headers/2.2.0", "hashPath": "microsoft.net.http.headers.2.2.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Bh6blKG8VAKvXiLe2L+sEsn62nc1Ij34MrNxepD2OCrS5cpCwQa9MeLyhVQPQ/R4Wlzwuy6wMK8hLb11QPDRsQ==", "path": "microsoft.win32.systemevents/5.0.0", "hashPath": "microsoft.win32.systemevents.5.0.0.nupkg.sha512"}, "MongoDB.Bson/2.16.1": {"type": "package", "serviceable": true, "sha512": "sha512-d647KTPQ7hWqJmvCg80iiVdm239llaUwMyIeRF/0zKpN+ddRWh9jT/g6R6WQrmi4qd7yodvv/Jn0rpEqh4QN8g==", "path": "mongodb.bson/2.16.1", "hashPath": "mongodb.bson.2.16.1.nupkg.sha512"}, "MongoDB.Driver/2.16.1": {"type": "package", "serviceable": true, "sha512": "sha512-LqDWfRG7gM1tZKcG9Q/vuQGYF5GsBuxUF5KcOgvWrE6P7pSkOSL9xGZ7ABZ6XRVECGEQs+N6GyU1E4ViI4lh+g==", "path": "mongodb.driver/2.16.1", "hashPath": "mongodb.driver.2.16.1.nupkg.sha512"}, "MongoDB.Driver.Core/2.16.1": {"type": "package", "serviceable": true, "sha512": "sha512-oJZnXHnAyTd/haHYdbfKMRmPEZ1fFrndv8xhorHeTkUDeGgsPA89vpGMHcjppUyHMYBpLgHbn6GoMkleHKsTHg==", "path": "mongodb.driver.core/2.16.1", "hashPath": "mongodb.driver.core.2.16.1.nupkg.sha512"}, "MongoDB.Libmongocrypt/1.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-OWWxuyuxbjAmOLPoaoUEYnIW4F7qexS6XYOdu6absxyGAqLBWEY+M4WY2Y0km2UUG1+QOPdebpb/7cg5BIEbdw==", "path": "mongodb.libmongocrypt/1.5.3", "hashPath": "mongodb.libmongocrypt.1.5.3.nupkg.sha512"}, "NRules/0.9.4": {"type": "package", "serviceable": true, "sha512": "sha512-KLQbXIOMz7eDL9+ZFp3hFLibCgO11NHuJRjTQP8BkTyPpVsYRX3Kjbs6LkKAkcXo9Wip4nbbjU0YLkNhQnpqQQ==", "path": "nrules/0.9.4", "hashPath": "nrules.0.9.4.nupkg.sha512"}, "NRules.Fluent/0.9.4": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON>nc7zzijfWzGSqh2nrf3yJodvPr6KC4/J4jeGh7VlnKz4qInw+40/906upfGbT0hRFUvIcnCcnuOaK2GJ4V0GQ==", "path": "nrules.fluent/0.9.4", "hashPath": "nrules.fluent.0.9.4.nupkg.sha512"}, "NRules.RuleModel/0.9.4": {"type": "package", "serviceable": true, "sha512": "sha512-/Rons42/UiTihUVTysI6CWRExFDsrpxqm7KfUvlz7+mizAizBqkANQ9AouElqCkQwqzfR6/eSdDXTh8eSfru6A==", "path": "nrules.rulemodel/0.9.4", "hashPath": "nrules.rulemodel.0.9.4.nupkg.sha512"}, "NRules.Runtime/0.9.4": {"type": "package", "serviceable": true, "sha512": "sha512-xRtOPDVbpaBAnqB+5HT8juxmyoiPmc0L9zROc8idJdpHhBUiJyDDfZiXyUk592sR5pOTEVupMPA3GPfRWkgP0g==", "path": "nrules.runtime/0.9.4", "hashPath": "nrules.runtime.0.9.4.nupkg.sha512"}, "Pipelines.Sockets.Unofficial/2.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-Bhk0FWxH1paI+18zr1g5cTL+ebeuDcBCR+rRFO+fKEhretgjs7MF2Mc1P64FGLecWp4zKCUOPzngBNrqVyY7Zg==", "path": "pipelines.sockets.unofficial/2.2.2", "hashPath": "pipelines.sockets.unofficial.2.2.2.nupkg.sha512"}, "Polly/7.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-DeCY0OFbNdNxsjntr1gTXHJ5pKUwYzp04Er2LLeN3g6pWhffsGuKVfMBLe1lw7x76HrPkLxKEFxBlpRxS2nDEQ==", "path": "polly/7.2.3", "hashPath": "polly.7.2.3.nupkg.sha512"}, "protobuf-net/3.1.17": {"type": "package", "serviceable": true, "sha512": "sha512-lN6JWutKqn1Aq/hI1BXcR7VbgHgcerUTMav2rTenflE28GWC2y1JoF9XPkoQdCkf24olStY00r68PHJhUpWFaw==", "path": "protobuf-net/3.1.17", "hashPath": "protobuf-net.3.1.17.nupkg.sha512"}, "protobuf-net.Core/3.1.17": {"type": "package", "serviceable": true, "sha512": "sha512-6PK/ZswVXHdUfMFEv9bI+jkNipuy9ifKRKRBAZlqFrbGXOwvREdMA2H53GTqTc6728PMA0VGvsbnjPAQ0Cx59A==", "path": "protobuf-net.core/3.1.17", "hashPath": "protobuf-net.core.3.1.17.nupkg.sha512"}, "RabbitMQ.Client/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-9hY5HiWPtCla1/l0WmXmLnqoX7iKE3neBQUWnetIJrRpOvTbO//XQfQDh++xgHCshL40Kv/6bR0HDkmJz46twg==", "path": "rabbitmq.client/6.5.0", "hashPath": "rabbitmq.client.6.5.0.nupkg.sha512"}, "Refit/6.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-HxysoLBknW2FdxNXr56NjnfuxbgNh9z2QsUc9y5QtPkXSAWmShpUWDM1G+3Ls2yTyb4sItOii4gZ+GjxKkR7+A==", "path": "refit/6.3.2", "hashPath": "refit.6.3.2.nupkg.sha512"}, "Refit.HttpClientFactory/6.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-B+lvCus6p45hrBU6cNjnUNogtuTRMVyUv0tqQCgRR1K+oYWE7XGCl49Y76WKaBj4Pffsswpbr2uUjIzFFGxfeQ==", "path": "refit.httpclientfactory/6.3.2", "hashPath": "refit.httpclientfactory.6.3.2.nupkg.sha512"}, "SharpCompress/0.30.1": {"type": "package", "serviceable": true, "sha512": "sha512-XqD4TpfyYGa7QTPzaGlMVbcecKnXy4YmYLDWrU+JIj7IuRNl7DH2END+Ll7ekWIY8o3dAMWLFDE1xdhfIWD1nw==", "path": "sharpcompress/0.30.1", "hashPath": "sharpcompress.0.30.1.nupkg.sha512"}, "StackExchange.Redis/2.6.48": {"type": "package", "serviceable": true, "sha512": "sha512-T0rLGogyT6Zny+IMrDx1Z8r4nA3B0C7EVo5SHNjzT4ndOn9aGKe5K7KTVx0y41WaWmfSWpaX7HrPl0tfZ4zuUw==", "path": "stackexchange.redis/2.6.48", "hashPath": "stackexchange.redis.2.6.48.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.Collections.Immutable/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-l4zZJ1WU2hqpQQHXz1rvC3etVZN+2DLmQMO79FhOTZHMn8tDRr+WU287sbomD0BETlmKDn0ygUgVy9k5xkkJdA==", "path": "system.collections.immutable/6.0.0", "hashPath": "system.collections.immutable.6.0.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-aM7cbfEfVNlEEOj3DsZP+2g9NRwbkyiAv2isQEzw7pnkDg9ekCU2m1cdJLM02Uq691OaCS91tooaxcEn8d0q5w==", "path": "system.configuration.configurationmanager/5.0.0", "hashPath": "system.configuration.configurationmanager.5.0.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-frQDfv0rl209cKm1lnwTgFPzNigy2EKk1BS3uAvHvlBVKe5cymGyHO+Sj+NLv5VF/AhHsqPIUUwya5oV4CHMUw==", "path": "system.diagnostics.diagnosticsource/6.0.0", "hashPath": "system.diagnostics.diagnosticsource.6.0.0.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-kcQWWtGVC3MWMNXdMDWfrmIlFZZ2OdoeT6pSNVRtk9+Sa7jwdPiMlNwb0ZQcS7NRlT92pCfmjRtkSWUW3RAKwg==", "path": "system.diagnostics.performancecounter/5.0.0", "hashPath": "system.diagnostics.performancecounter.5.0.0.nupkg.sha512"}, "System.Drawing.Common/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-SztFwAnpfKC8+sEKXAFxCBWhKQaEd97EiOL7oZJZP56zbqnLpmxACWA8aGseaUExciuEAUuR9dY8f7HkTRAdnw==", "path": "system.drawing.common/5.0.0", "hashPath": "system.drawing.common.5.0.0.nupkg.sha512"}, "System.IO.Pipelines/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-qEePWsaq9LoEEIqhbGe6D5J8c9IqQOUuTzzV6wn1POlfdLkJliZY3OlB0j0f17uMWlqZYjH7txj+2YbyrIA8Yg==", "path": "system.io.pipelines/5.0.1", "hashPath": "system.io.pipelines.5.0.1.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Net.Http.Json/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GbIV4y344kGOKjshAKCIDCMUTTW/hyUC42wV0Y5SXEdIbaKBIHBUxZ2MOe4/ZiV2svUAGfQ0c8LGtUExpOI8tg==", "path": "system.net.http.json/6.0.0", "hashPath": "system.net.http.json.6.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Security.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "path": "system.security.accesscontrol/5.0.0", "hashPath": "system.security.accesscontrol.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-HGxMSAFAPLNoxBvSfW08vHde0F9uh7BjASwu6JF9JnXuEPhCY3YUqURn0+bQV/4UWeaqymmrHWV+Aw9riQCtCA==", "path": "system.security.cryptography.protecteddata/5.0.0", "hashPath": "system.security.cryptography.protecteddata.5.0.0.nupkg.sha512"}, "System.Security.Permissions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uE8juAhEkp7KDBCdjDIE3H9R1HJuEHqeqX8nLX9gmYKWwsqk3T5qZlPx8qle5DPKimC/Fy3AFTdV7HamgCh9qQ==", "path": "system.security.permissions/5.0.0", "hashPath": "system.security.permissions.5.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LSyCblMpvOe0N3E+8e0skHcrIhgV2huaNcjUUEa8hRtgEAm36aGkRoC8Jxlb6Ra6GSfF29ftduPNywin8XolzQ==", "path": "system.text.encoding.codepages/7.0.0", "hashPath": "system.text.encoding.codepages.7.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vg8eB5Tawm1IFqj4TVK1czJX89rhFxJo9ELqc/Eiq0eXy13RK00eubyU6TJE6y+GQXjyV5gSfiewDUZjQgSE0w==", "path": "system.text.encodings.web/6.0.0", "hashPath": "system.text.encodings.web.6.0.0.nupkg.sha512"}, "System.Text.Json/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zaJsHfESQvJ11vbXnNlkrR46IaMULk/gHxYsJphzSF+07kTjPHv+Oc14w6QEOfo3Q4hqLJgStUaYB9DBl0TmWg==", "path": "system.text.json/6.0.0", "hashPath": "system.text.json.6.0.0.nupkg.sha512"}, "System.Threading.Channels/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qmeeYNROMsONF6ndEZcIQ+VxR4Q/TX/7uIVLJqtwIWL7dDWeh0l1UIqgo4wYyjG//5lUNhwkLDSFl+pAWO6oiA==", "path": "system.threading.channels/7.0.0", "hashPath": "system.threading.channels.7.0.0.nupkg.sha512"}, "System.Windows.Extensions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-c1ho9WU9ZxMZawML+ssPKZfdnrg/OjR3pe0m9v8230z3acqphwvPJqzAkH54xRYm5ntZHGG1EPP3sux9H3qSPg==", "path": "system.windows.extensions/5.0.0", "hashPath": "system.windows.extensions.5.0.0.nupkg.sha512"}, "Yitter.IdGenerator/1.0.14": {"type": "package", "serviceable": true, "sha512": "sha512-F4nOJ7Geq41vgNWX9E6/vkxRzFInACGpDp4Kad2mA2WIKhEwgPyE9FpulBAuEmDByrfHHz6mOII3IIeLJAh91g==", "path": "yitter.idgenerator/1.0.14", "hashPath": "yitter.idgenerator.1.0.14.nupkg.sha512"}, "Adnc.Demo.Shared.Const/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Demo.Shared.Rpc.Event/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Infra.Core/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Infra.EventBus/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Infra.Helper/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Infra.IdGenerater/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Infra.Redis/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Infra.Repository/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Shared/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Shared.Domain/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Shared.Repository/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Shared.Rpc/*******": {"type": "project", "serviceable": false, "sha512": ""}}}