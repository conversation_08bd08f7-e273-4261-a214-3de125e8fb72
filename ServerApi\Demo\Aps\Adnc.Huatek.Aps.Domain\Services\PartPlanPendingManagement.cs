﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate;
using System.Net;

namespace Adnc.Huatek.Aps.Domain.Services
{
    /// <summary>
    /// 待排产计划
    /// </summary>
    public class PartPlanPendingManagement : IDomainService
    {
        private readonly IEfBasicRepository<PartPlanPending> _partPlanPendingRepo;

        public PartPlanPendingManagement(IEfBasicRepository<PartPlanPending> partPlanPendingRepo) 
        {
            _partPlanPendingRepo = partPlanPendingRepo;
        }


        public virtual async Task<PartPlanPending> CreateAsync(long orderId, long orderDetailId,string productCode, decimal qty, decimal productQty)
        {
            if (qty <= 0)
            {
                throw new BusinessException(HttpStatusCode.OK, $"计划排产数量必须大于0！");
            }
            var plans = _partPlanPendingRepo.Where(x => x.OrderId == orderId && x.ProductCode == productCode && !x.IsDeleted).ToList();
            //该产品已计划排产数量
            var qtyPlaned = plans.Sum(x => x.Qty);
            //计划排产数量不能大于产品实际数量
            if (qtyPlaned + qty > productQty)
                throw new BusinessException(HttpStatusCode.OK, $"计划排产数量不能大于产品总数量！");

            return new PartPlanPending()
            {
                Id = IdGenerater.GetNextId(),
                OrderId = orderId,
                IdDetail = orderDetailId,
                ProductCode = productCode,
                Qty = qty
            };
        }

        public virtual async Task<bool> UpdateAsync(int Status, int Priority, long id)
        {
            
            return true;
        }
    }
}
