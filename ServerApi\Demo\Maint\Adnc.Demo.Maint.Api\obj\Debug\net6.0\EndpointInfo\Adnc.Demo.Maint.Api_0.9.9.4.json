{"openapi": "3.0.1", "info": {"title": "maint-api", "version": "*******"}, "paths": {"/maint/api/cfgs": {"post": {"tags": ["Cfg"], "summary": "新增配置", "requestBody": {"description": "Adnc.Demo.Maint.Application.Dtos.CfgCreationDto", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CfgCreationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CfgCreationDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CfgCreationDto"}}}}, "responses": {"201": {"description": "Created", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/maint/api/cfgs/{id}": {"put": {"tags": ["Cfg"], "summary": "更新配置", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"description": "Adnc.Demo.Maint.Application.Dtos.CfgUpdationDto", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CfgUpdationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CfgUpdationDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CfgUpdationDto"}}}}, "responses": {"204": {"description": "No Content"}}}, "delete": {"tags": ["Cfg"], "summary": "删除配置节点", "parameters": [{"name": "id", "in": "path", "description": "节点id", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"204": {"description": "No Content"}}}, "get": {"tags": ["Cfg"], "summary": "获取单个配置节点", "parameters": [{"name": "id", "in": "path", "description": "节点id", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CfgDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CfgDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CfgDto"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/maint/api/cfgs/page": {"get": {"tags": ["Cfg"], "summary": "获取配置列表", "parameters": [{"name": "Name", "in": "query", "description": "参数名", "schema": {"type": "string"}}, {"name": "Value", "in": "query", "description": "参数值", "schema": {"type": "string"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CfgDtoPageModelDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CfgDtoPageModelDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CfgDtoPageModelDto"}}}}}}}, "/maint/api/dicts/add": {"post": {"tags": ["Dict"], "summary": "新增字典", "requestBody": {"description": "Adnc.Demo.Maint.Application.Dtos.DictCreationDto", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DictCreationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DictCreationDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DictCreationDto"}}}}, "responses": {"201": {"description": "Created", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/maint/api/dicts/edit": {"post": {"tags": ["Dict"], "summary": "修改字典", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"description": "Adnc.Demo.Maint.Application.Dtos.DictUpdationDto", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DictUpdationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DictUpdationDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DictUpdationDto"}}}}, "responses": {"204": {"description": "No Content"}}}}, "/maint/api/dicts/delete/{id}": {"delete": {"tags": ["Dict"], "summary": "删除字典", "parameters": [{"name": "id", "in": "path", "description": "字典ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"204": {"description": "No Content"}}}}, "/maint/api/dicts/page": {"post": {"tags": ["Dict"], "summary": "获取字典列表", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DictSearchDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DictSearchDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DictSearchDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DictDtoPageModelDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DictDtoPageModelDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DictDtoPageModelDto"}}}}}}}, "/getbyid/{id}": {"get": {"tags": ["Dict"], "summary": "获取单个字典数据", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DictDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DictDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DictDto"}}}}}}}, "/maint/api/dicts/getbyname/{name}": {"get": {"tags": ["Dict"], "parameters": [{"name": "name", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EnumDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EnumDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EnumDto"}}}}}}}}, "/maint/api/dicts/gettreebyname/{name}": {"get": {"tags": ["Dict"], "parameters": [{"name": "name", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/EnumTreeDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/EnumTreeDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EnumTreeDto"}}}}}}}, "/getDetail": {"post": {"tags": ["Dict"], "summary": "查询单个字典详情", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DictSearchDetailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DictSearchDetailDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DictSearchDetailDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DictDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DictDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DictDto"}}}}}}}, "/maint/api/opslogs": {"get": {"tags": ["Log"], "summary": "查询操作日志", "parameters": [{"name": "BeginTime", "in": "query", "description": "日志范围开始时间", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndTime", "in": "query", "description": "日志范围结束时间", "schema": {"type": "string", "format": "date-time"}}, {"name": "Account", "in": "query", "description": "账号", "schema": {"type": "string"}}, {"name": "Method", "in": "query", "description": "方法名", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON>", "in": "query", "description": "设备", "schema": {"type": "string"}}, {"name": "Level", "in": "query", "description": "日志级别", "schema": {"type": "string"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OpsLogDtoPageModelDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/OpsLogDtoPageModelDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OpsLogDtoPageModelDto"}}}}}}}, "/maint/api/users/opslogs": {"get": {"tags": ["Log"], "summary": "查询用户操作日志", "parameters": [{"name": "BeginTime", "in": "query", "description": "日志范围开始时间", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndTime", "in": "query", "description": "日志范围结束时间", "schema": {"type": "string", "format": "date-time"}}, {"name": "Account", "in": "query", "description": "账号", "schema": {"type": "string"}}, {"name": "Method", "in": "query", "description": "方法名", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON>", "in": "query", "description": "设备", "schema": {"type": "string"}}, {"name": "Level", "in": "query", "description": "日志级别", "schema": {"type": "string"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OpsLogDtoPageModelDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/OpsLogDtoPageModelDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OpsLogDtoPageModelDto"}}}}}}}, "/maint/api/loginlogs": {"get": {"tags": ["Log"], "summary": "查询登录日志", "parameters": [{"name": "BeginTime", "in": "query", "description": "日志范围开始时间", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndTime", "in": "query", "description": "日志范围结束时间", "schema": {"type": "string", "format": "date-time"}}, {"name": "Account", "in": "query", "description": "账号", "schema": {"type": "string"}}, {"name": "Method", "in": "query", "description": "方法名", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON>", "in": "query", "description": "设备", "schema": {"type": "string"}}, {"name": "Level", "in": "query", "description": "日志级别", "schema": {"type": "string"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LoginLogDtoPageModelDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LoginLogDtoPageModelDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginLogDtoPageModelDto"}}}}}}}, "/maint/api/nloglogs": {"get": {"tags": ["Log"], "summary": "查询Nlog日志", "parameters": [{"name": "BeginTime", "in": "query", "description": "日志范围开始时间", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndTime", "in": "query", "description": "日志范围结束时间", "schema": {"type": "string", "format": "date-time"}}, {"name": "Account", "in": "query", "description": "账号", "schema": {"type": "string"}}, {"name": "Method", "in": "query", "description": "方法名", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON>", "in": "query", "description": "设备", "schema": {"type": "string"}}, {"name": "Level", "in": "query", "description": "日志级别", "schema": {"type": "string"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/NlogLogDtoPageModelDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/NlogLogDtoPageModelDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NlogLogDtoPageModelDto"}}}}}}}, "/maint/api/notices": {"get": {"tags": ["Notice"], "summary": "获取通知消息列表", "parameters": [{"name": "Title", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NoticeDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NoticeDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NoticeDto"}}}}}}}}}, "components": {"schemas": {"CfgCreationDto": {"required": ["name", "value"], "type": "object", "properties": {"description": {"maxLength": 256, "type": "string", "description": "备注", "nullable": true}, "name": {"maxLength": 64, "minLength": 2, "type": "string", "description": "参数名"}, "value": {"maxLength": 128, "minLength": 2, "type": "string", "description": "参数值"}}, "additionalProperties": false, "description": "系统配置"}, "CfgDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "createBy": {"type": "integer", "format": "int64", "nullable": true}, "createTime": {"type": "string", "format": "date-time", "nullable": true}, "modifyBy": {"type": "integer", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "format": "date-time", "nullable": true}, "description": {"type": "string", "description": "备注", "nullable": true}, "name": {"type": "string", "description": "参数名", "nullable": true}, "value": {"type": "string", "description": "参数值", "nullable": true}}, "additionalProperties": false, "description": "系统配置"}, "CfgDtoPageModelDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/CfgDto"}, "nullable": true}, "rowsCount": {"type": "integer", "format": "int32", "readOnly": true}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32", "readOnly": true}, "xData": {"nullable": true}}, "additionalProperties": false}, "CfgUpdationDto": {"required": ["name", "value"], "type": "object", "properties": {"description": {"maxLength": 256, "type": "string", "description": "备注", "nullable": true}, "name": {"maxLength": 64, "minLength": 2, "type": "string", "description": "参数名"}, "value": {"maxLength": 128, "minLength": 2, "type": "string", "description": "参数值"}}, "additionalProperties": false, "description": "系统配置"}, "DictCreationDto": {"required": ["name"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"maxLength": 64, "minLength": 1, "type": "string"}, "value": {"maxLength": 16, "type": "string", "nullable": true}, "status": {"type": "boolean"}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/DictCreationDto"}, "nullable": true}}, "additionalProperties": false}, "DictDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string", "nullable": true}, "status": {"type": "boolean"}, "pid": {"type": "integer", "format": "int64", "nullable": true}, "createTime": {"type": "string", "description": "创建时间/注册时间", "format": "date-time"}, "value": {"type": "string", "nullable": true}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/DictDto"}, "nullable": true}}, "additionalProperties": false}, "DictDtoPageModelDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/DictDto"}, "nullable": true}, "rowsCount": {"type": "integer", "format": "int32", "readOnly": true}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32", "readOnly": true}, "xData": {"nullable": true}}, "additionalProperties": false}, "DictSearchDetailDto": {"type": "object", "properties": {"name": {"type": "string", "description": "字典名", "nullable": true}, "value": {"type": "string", "description": "字典值", "nullable": true}}, "additionalProperties": false, "description": "字典检索条件"}, "DictSearchDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "ids": {"type": "array", "items": {"type": "integer", "format": "int64"}, "description": "字典ids", "nullable": true}, "name": {"type": "string", "description": "字典名", "nullable": true}, "value": {"type": "string", "description": "字典值", "nullable": true}}, "additionalProperties": false, "description": "字典检索条件"}, "DictUpdationDto": {"required": ["name"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"maxLength": 64, "minLength": 1, "type": "string"}, "value": {"maxLength": 16, "type": "string", "nullable": true}, "status": {"type": "boolean"}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/DictCreationDto"}, "nullable": true}}, "additionalProperties": false}, "EnumDto": {"type": "object", "properties": {"label": {"type": "string", "nullable": true}, "value": {"nullable": true}}, "additionalProperties": false}, "EnumTreeDto": {"type": "object", "properties": {"label": {"type": "string", "nullable": true}, "value": {"nullable": true}, "childs": {"type": "array", "items": {"$ref": "#/components/schemas/EnumTreeDto"}, "nullable": true}}, "additionalProperties": false}, "LoginLogDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "device": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "succeed": {"type": "boolean"}, "statusCode": {"type": "integer", "format": "int32"}, "userId": {"type": "integer", "format": "int64", "nullable": true}, "account": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "remoteIpAddress": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time"}}, "additionalProperties": false, "description": "登录日志"}, "LoginLogDtoPageModelDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/LoginLogDto"}, "nullable": true}, "rowsCount": {"type": "integer", "format": "int32", "readOnly": true}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32", "readOnly": true}, "xData": {"nullable": true}}, "additionalProperties": false}, "NlogLogDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "date": {"type": "string", "format": "date-time"}, "level": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "logger": {"type": "string", "nullable": true}, "exception": {"type": "string", "nullable": true}, "threadID": {"type": "integer", "format": "int32"}, "threadName": {"type": "string", "nullable": true}, "processID": {"type": "integer", "format": "int32"}, "processName": {"type": "string", "nullable": true}, "properties": {"$ref": "#/components/schemas/NlogLogPropertyDto"}}, "additionalProperties": false, "description": "Nlog日志"}, "NlogLogDtoPageModelDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/NlogLogDto"}, "nullable": true}, "rowsCount": {"type": "integer", "format": "int32", "readOnly": true}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32", "readOnly": true}, "xData": {"nullable": true}}, "additionalProperties": false}, "NlogLogPropertyDto": {"type": "object", "properties": {"traceIdentifier": {"type": "string", "nullable": true}, "connectionId": {"type": "string", "nullable": true}, "eventId_Id": {"type": "string", "nullable": true}, "eventId_Name": {"type": "string", "nullable": true}, "eventId": {"type": "string", "nullable": true}, "remoteIpAddress": {"type": "string", "nullable": true}, "baseDir": {"type": "string", "nullable": true}, "queryUrl": {"type": "string", "nullable": true}, "requestMethod": {"type": "string", "nullable": true}, "controller": {"type": "string", "nullable": true}, "method": {"type": "string", "nullable": true}, "formContent": {"type": "string", "nullable": true}, "queryContent": {"type": "string", "nullable": true}}, "additionalProperties": false}, "NoticeDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "createBy": {"type": "integer", "format": "int64", "nullable": true}, "createTime": {"type": "string", "format": "date-time", "nullable": true}, "content": {"type": "string", "description": "内容", "nullable": true}, "title": {"type": "string", "description": "标题", "nullable": true}, "type": {"type": "integer", "description": "类型", "format": "int32"}}, "additionalProperties": false, "description": "系统通知"}, "OpsLogDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "className": {"type": "string", "description": "控制器类名", "nullable": true}, "logName": {"type": "string", "description": "日志业务名称", "nullable": true}, "logType": {"type": "string", "description": "日志类型", "nullable": true}, "message": {"type": "string", "description": "详细信息", "nullable": true}, "method": {"type": "string", "description": "控制器方法", "nullable": true}, "succeed": {"type": "string", "description": "是否操作成功", "nullable": true}, "userId": {"type": "integer", "description": "操作用户ID", "format": "int64"}, "account": {"type": "string", "description": "账号", "nullable": true}, "userName": {"type": "string", "description": "操作用户名", "nullable": true}, "remoteIpAddress": {"type": "string", "description": "Ip", "nullable": true}, "createTime": {"type": "string", "description": "操作时间", "format": "date-time"}}, "additionalProperties": false, "description": "操作日志"}, "OpsLogDtoPageModelDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/OpsLogDto"}, "nullable": true}, "rowsCount": {"type": "integer", "format": "int32", "readOnly": true}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32", "readOnly": true}, "xData": {"nullable": true}}, "additionalProperties": false}, "ProblemDetails": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "detail": {"type": "string", "nullable": true}, "instance": {"type": "string", "nullable": true}}, "additionalProperties": {}}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the Bear<PERSON> scheme.", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}], "tags": [{"name": "Log", "description": "日志管理"}, {"name": "Notice", "description": "通知管理"}, {"name": "Cfg", "description": "配置管理"}, {"name": "Dict", "description": "字典管理"}]}