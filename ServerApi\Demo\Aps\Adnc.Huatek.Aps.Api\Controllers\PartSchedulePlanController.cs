﻿using Adnc.Demo.Shared.Const;
using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Application.Services;
using Adnc.Shared;
using Adnc.Shared.Application.Contracts.ResultModels;
using Microsoft.AspNetCore.Authorization;

namespace Adnc.Huatek.Aps.Api.Controllers
{
    /// <summary>
    /// 排产计划
    /// </summary>
    [Route($"{RouteConsts.ApsRoot}/SchedulePlan")]
    [ApiController]
    public class PartSchedulePlanController : AdncControllerBase
    {
        private readonly IPartSchedulePlanService _scheduleSrv;
        private readonly IBasNumberManagementService _basNumSrv;

        public PartSchedulePlanController(IPartSchedulePlanService scheduleSrv, IBasNumberManagementService basNumSrv)
        {
            _scheduleSrv = scheduleSrv;
            _basNumSrv = basNumSrv;
        }

        /// <summary>
        /// 新增排产计划
        /// </summary>
        /// <param name=input></param>
        /// <returns></returns>
        [HttpPost("CreateSchedule")]
        [AllowAnonymous]
        public async Task<ResultJson> CreateSchedulePlanAsync([FromBody] PartSchedulePlanDto input)
        { 
            input.Code = _basNumSrv.GetNumberBySimpleName(CommonConst.SCHEDULNUMBER).Result;
            return await _scheduleSrv.CreateSchedulePlan(input);
        }
        /// <summary>
        /// 试算
        /// </summary>
        /// <param name=input></param>
        /// <returns></returns>
        [HttpPost("TrialSchedule")]
        [AllowAnonymous]
        public async Task<ActionResult<PlanGanttDto>> TrialSchedulePlanAsync([FromBody] PartSchedulePlanDto input)
        { 
            return await _scheduleSrv.TrialSchedulePlan(input);
        }

        
        /// <summary>
        /// 拖拉拽后保存接口
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("changeplan")]
        public async Task<ResultJson> ChangeScheduleStatusAsync([FromBody] PartSchedulePlanDto input)
        => await _scheduleSrv.ModuleSchedulePlan(input);


        /// <summary>
        /// 列表分页
        /// </summary>
        /// <param name="search"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("page")]
        public async Task<ActionResult<PageModelDto<PartSchedulePlanDto>>> GetPagedAsync([FromBody] PartSchedulPlanPageDto search)
        => await _scheduleSrv.GetPagedAsync(search);

        [AllowAnonymous]
        [HttpPost("changeState")]
        public async Task<ResultJson> ChangeScheduleStatusAsync([FromBody] SchedulStatusNew input)
        => await _scheduleSrv.SetScheduleStatusAsync(input);

        [AllowAnonymous]
        [HttpPost("CheckSourceConfilct")]
        public async Task<AppSrvResult<string>> CheckSourceConfilctAsync([FromBody] SchedulStatusNew input)
    => await _scheduleSrv.CheckSourceConfilct(input); 


        [AllowAnonymous]
        [HttpPost("CheckSource")]
        public async Task<ResultJson> CheckSourceEffiveAsync([FromBody] List<PartSchedulePlanStepDto> input)
       => await _scheduleSrv.CheckSourceEffiveAsync(input);
        /// <summary>
        /// 设备甘特图
        /// 设备甘特图
        /// </summary>
        /// <param name="search"></param>
        /// <returns></returns>

        [AllowAnonymous]
        [HttpPost("deviceGantt")]
        public async Task<List<DeviceGanttDto>> GetDeviceGanttPLanAsync([FromBody] PartSchedulPlanSourcePageDto search)
        => await _scheduleSrv.GetDeviceGanttPlan(search);

        //[AllowAnonymous]
        //[HttpPost("test")]
        //public async Task<AppSrvResult<string>> testBatch()
        //=> await _scheduleSrv.testBatch();

        /// <summary>
        /// 报工保存接口
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("ChangeProgress")]
        public async Task<AppSrvResult> ChangeStepProgressAsync([FromBody] PartSchedulePlanStepReportDto input)
        => await _scheduleSrv.SaveStepProgress(input);

        /// <summary>
        /// 分析资源冲突列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("QuerySourceConflict")]
        public async Task<ResultJson> CheckSourceConflictAsync([FromBody] QuerySourceConflict input)
        => await _scheduleSrv.CheckSourceConflict(input);

        [AllowAnonymous]
        [HttpPost("batchDel")]
        public async Task<ActionResult<long>> DeleteAsync([FromBody] List<string> codes)
           => CreatedResult(await _scheduleSrv.BatchDelAsync(codes));
         
    }
}
