﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.BasBomAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasMaterial;
using Adnc.Huatek.Aps.Domain.Aggregates.SysUserAggregate;
using Adnc.Huatek.Aps.Domain.Services;
using Adnc.Infra.Entities;
using Adnc.Infra.IdGenerater.Yitter;
using Adnc.Shared;
using Adnc.Shared.Application.Contracts.ResultModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Adnc.Huatek.Aps.Domain.Aggregates.BasDevice;

namespace Adnc.Huatek.Aps.Application.Services.Implements
{
    
    public class BasMaterialService : AbstractAppService, IBasMaterialService
    {
        private readonly BasMaterialManager _stationMgr;
        private readonly IEfBasicRepository<BasMaterial> _stationRepo;
        private readonly IEfBasicRepository<BasBom> _basBomRepo;
        private readonly BasNumberManagementService _basNumberManagementService;
        private readonly IEfBasicRepository<SysUser> _sysUserRepo;
        private readonly IMaintRestClient _maintRestClient;


        public BasMaterialService(
            IEfBasicRepository<BasMaterial> stationRepo,
            IEfBasicRepository<BasBom> basBomRepo,
            BasMaterialManager stationMgr,
            BasNumberManagementService basNumberManagementService,
            IMaintRestClient maintRestClient,
        IEfBasicRepository<SysUser> sysUserRepo)
        {
            _stationRepo = stationRepo;
            _stationMgr = stationMgr;
            _basBomRepo = basBomRepo;
            _basNumberManagementService = basNumberManagementService;
            _sysUserRepo = sysUserRepo;
            _maintRestClient = maintRestClient;
        }

        /// <summary>
        /// 新增物料
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<AppSrvResult<long>> CreateAsync(BasMaterialDto input)
        {
            var menu = Mapper.Map<BasMaterial>(input);
            var autoCode = _basNumberManagementService.GetNumberBySimpleName(CommonConst.MATERIALNUMBER).Result;
            menu.Code = autoCode;
            menu.Id = IdGenerater.GetNextId();
            await _stationRepo.InsertAsync(menu);
            return menu.Id;
        }

        /// <summary>
        /// 删除物料
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<AppSrvResult> DeleteAsync(long id)
        {
            var userProfile = await _stationRepo.GetAsync(id);
            if (userProfile != null)
            {
                var items = await _basBomRepo.Where(x => x.MaterialCode == userProfile.Code, false, false).ToListAsync();

                if (items?.Count > 0)
                {
                    return Problem(HttpStatusCode.BadRequest, "该物料已被配置，不可以删除！");
                }

                var menu = Mapper.Map<BasMaterial>(userProfile);
                menu.IsDeleted = true;
                await _stationRepo.UpdateAsync(menu);
            }
            return AppSrvResult();
        }

        /// <summary>
        /// 获取所有物料
        /// </summary>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<List<BasMaterialDto>> GetAllAsync()
        {
            var whereCondition = ExpressionCreator
                                          .New<BasMaterial>();
            var sysTests = await _stationRepo.Where(x=>!x.IsDeleted && x.Status == 1).Where(whereCondition).ToListAsync();

            var sysTestsDto = Mapper.Map<List<BasMaterialDto>>(sysTests);

            return sysTestsDto;
        }

        /// <summary>
        /// 获取指定物料
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<ResultJson> GetAppointAsync(long id)
        {
            var userProfile = await _stationRepo.Where(x => x.IsDeleted == false && x.Id == id).FirstAsync();
            if (userProfile == null)
                return new ResultJson("查询成功！无数据", null);
            var menu = Mapper.Map<BasMaterialDto>(userProfile);
              return new ResultJson("查询成功", menu, 0);
        }


        /// <summary>
        /// 批量禁用启用
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult> MakeEnableAsync(int status, List<long> ids)
        {
            var lines = await _stationRepo.Where(x => !x.IsDeleted && ids.Contains((long)x.Id), false, false).ToListAsync();

            if (lines.Any())
            {
                await lines.ForEachAsync(o =>
                {
                    o.Status = status;
                });
                await _stationRepo.UpdateRangeAsync(lines);
            }
            return AppSrvResult();
        }
        /// <summary>
        /// 修改物料
        /// </summary>
        /// <param name="id"></param>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<AppSrvResult> UpdateAsync(BasMaterialDto input)
        {
            input.TrimStringFields();
            var station = Mapper.Map<BasMaterial>(input);
            var userProfile = await _stationRepo.GetAsync(input.Id);
            if (userProfile != null)
            {
                userProfile.Status = station.Status;
                userProfile.Code = station.Code;
                userProfile.Mtype = station.Mtype;
                userProfile.Unit = station.Unit;
                userProfile.Name = station.Name;
                userProfile.Remark = station.Remark;
                userProfile.ModifyTime = station.ModifyTime;
                userProfile.ModifyBy = station.ModifyBy;
                await _stationRepo.UpdateAsync(userProfile);
            }

            return AppSrvResult();
        }

        public async Task<AppSrvResult> ChangeStatusAsync(long id, int status)
        {
            var userProfile = await _stationRepo.GetAsync(id);
            if (userProfile != null)
            {
                //如果数据已作废是否可以启用
                {

                }
                userProfile.Status = status;
                await _stationRepo.UpdateAsync(userProfile);
            }
            return AppSrvResult();
        }

        public async Task<AppSrvResult> ChangeStatusAsync(UpdateStatusDto input)
        {
            string[] Ids = new string[input.Ids.Length];
            for (int i = 0; i < input.Ids.Length; i++)
            {
                Ids[i] = input.Ids[i].ToString();
            }
            var userProfile = await _stationRepo.Where(x => x.Status != 2 & Ids.Contains(x.Id.ToString())).ToListAsync();
            if (userProfile != null)
            {
                //type==0为删除，type ==1为启用停用
                if (input.Type == 0)
                {
                    foreach (var item in userProfile)
                    {
                        item.IsDeleted = true;
                    }
                }
                else
                {
                    foreach (var item in userProfile)
                    {
                        item.Status = input.Status;
                    }
                }
                await _stationRepo.UpdateRangeAsync(userProfile);
            }
            return AppSrvResult();
        }


        public async Task<PageModelDto<BasMaterialDto>> GetPagedAsync(BasMaterialPagedDto search)
        {
            search.TrimStringFields();
            var whereExpression = ExpressionCreator
                                                .New<BasMaterial>()
                                                .AndIf(search.Code.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Code!, $"%{search.Code}%"))
                                                .AndIf(search.Name.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Name!, $"%{search.Name}%"))
                                                .AndIf(search.Status != -1, x => x.Status == search.Status)
                                                .AndIf(search.Mtype != "0", x => x.Mtype ==search.Mtype)
                                                .AndIf(search.Unit!="0", x => x.Unit == search.Unit)
                                                .And(x => x.IsDeleted == false);

            var users = _sysUserRepo.Where(x=>true);
            var materials = _stationRepo.Where(whereExpression);

            var entities = await (from m in materials
                                  join u in users on m.CreateBy equals u.Id
                                  join mu in users on m.ModifyBy equals mu.Id
                                  select new BasMaterialDto
                                  {
                                      Id = m.Id,
                                      Code = m.Code,
                                      Name = m.Name,
                                      Status = m.Status,
                                      Unit = m.Unit,
                                      Mtype = m.Mtype,
                                      Remark = m.Remark,
                                      CreateBy = m.CreateBy,
                                      CreatName = u.Name,
                                      CreateTime = m.CreateTime,
                                      ModifyBy = m.ModifyBy,
                                      ModifyName = mu.Name,
                                      ModifyTime = m.ModifyTime
                                  }).OrderByDescending(x => x.Id)
                                            .Skip(search.SkipRows())
                                            .Take(search.PageSize)
                                            .ToListAsync();


            var restRpcResult = await _maintRestClient.GetDictByNameAsync("单位");
            if (restRpcResult.IsSuccessStatusCode)
            {
                var unitEnums = restRpcResult.Content;
                if (unitEnums is not null)
                {
                    entities.ForEach(x =>
                    {
                        x.UnitDis = unitEnums.FirstOrDefault(d => d.value == x.Unit.ToString())?.label;
                    });
                }
            }
            restRpcResult = await _maintRestClient.GetDictByNameAsync("物料类型");
            if (restRpcResult.IsSuccessStatusCode)
            {
                var mtypeEnums = restRpcResult.Content;
                if (mtypeEnums is not null)
                {
                    entities.ForEach(x =>
                    {
                        x.MtypeDis = mtypeEnums.FirstOrDefault(d => d.value == x.Mtype.ToString())?.label;
                    });
                }
            }

            return new PageModelDto<BasMaterialDto>(search, entities, entities.Count());
        }




        /// <summary>
        /// import
        /// </summary>
        /// <param name="dtos"></param>
        /// <returns></returns>
        public async Task<ResultJson> ImportAsync(List<BasMaterialDto> dtos)
        {

            dtos.ForEach(x =>
            {
                x.Status = x.StatusDis == "启用" ? 1 : 0;
            });


            var restRpcResult = await _maintRestClient.GetDictByNameAsync("物料类型");
            if (restRpcResult.IsSuccessStatusCode)
            {
                var unitEnums = restRpcResult.Content;
                if (unitEnums is not null)
                {
                    dtos.ForEach(x =>
                    {
                        if (string.IsNullOrWhiteSpace(x.Code))
                            x.Code = _basNumberManagementService.GetNumberBySimpleName(CommonConst.MATERIALNUMBER).Result;
                        x.Mtype = unitEnums.FirstOrDefault(d => d.label == x.MtypeDis!)?.value ?? "0";

                    });
                }
            }

            restRpcResult = await _maintRestClient.GetDictByNameAsync("单位");
            if (restRpcResult.IsSuccessStatusCode)
            {
                var unitEnums = restRpcResult.Content;
                if (unitEnums is not null)
                {
                    dtos.ForEach(x =>
                    {
                        if (string.IsNullOrWhiteSpace(x.Code))
                            x.Code = _basNumberManagementService.GetNumberBySimpleName(CommonConst.MATERIALNUMBER).Result;
                        x.Unit = unitEnums.FirstOrDefault(d => d.label == x.UnitDis!)?.value ?? "0";

                    });
                }
            }
            var entities = Mapper.Map<List<BasMaterial>>(dtos);

            var resultObj = await _stationMgr.ImportAsync(entities);

            if (resultObj.insertEntities.Any())
                await _stationRepo.InsertRangeAsync(resultObj.insertEntities);

            if (resultObj.updateEntities.Any())
                await _stationRepo.UpdateRangeAsync(resultObj.updateEntities);

            return new ResultJson(resultObj.Msg);
        }

    }


   

}
