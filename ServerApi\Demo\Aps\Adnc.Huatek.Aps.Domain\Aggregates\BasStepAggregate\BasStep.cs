﻿using Adnc.Infra.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Aggregates.BasStepAggregate
{
    public class BasStep : EfFullAuditEntity
    {
        /// <summary>
        /// ID
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 工序名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 工序编码
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 工序时间(小时)
        /// </summary>
        public decimal? StepTime { get; set; }

        /// <summary>
        /// 工序时间(小时)
        /// </summary>
        public decimal? Tat { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public int? Status { get; set; }



        

       
    }

   
}
