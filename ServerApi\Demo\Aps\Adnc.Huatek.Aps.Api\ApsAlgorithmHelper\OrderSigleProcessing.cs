﻿namespace Adnc.Huatek.Aps.Api.ApsAlgorithmHelper
{
    public class OrderSigleProcessing
    {
        public static int CalculateTotalTime(Dictionary<char, List<char>> dependencies, Dictionary<char, int> timeMap, int orderQuantity)
        {
            Dictionary<char, int> inDegree = new Dictionary<char, int>();
            foreach (char process in dependencies.Keys)
            {
                inDegree[process] = 0;
            }

            foreach (char process in dependencies.Keys)
            {
                foreach (char dependency in dependencies[process])
                {
                    inDegree[dependency]++;
                }
            }

            Queue<char> queue = new Queue<char>();
            foreach (char process in inDegree.Keys)
            {
                if (inDegree[process] == 0)
                {
                    queue.Enqueue(process);
                }
            }

            Dictionary<char, int> dp = new Dictionary<char, int>();
            foreach (char process in timeMap.Keys)
            {
                dp[process] = timeMap[process] * orderQuantity;
            }

            int totalTime = 0;
            while (queue.Count > 0)
            {
                char process = queue.Dequeue();
                totalTime = Math.Max(totalTime, dp[process]);

                if (dependencies.ContainsKey(process))
                {
                    foreach (char dependency in dependencies[process])
                    {
                        inDegree[dependency]--;
                        if (inDegree[dependency] == 0)
                        {
                            queue.Enqueue(dependency);
                        }

                        dp[dependency] = Math.Max(dp[dependency], dp[process] + timeMap[dependency] * orderQuantity);
                    }
                }
            }

            return totalTime;
        }
    }
}
