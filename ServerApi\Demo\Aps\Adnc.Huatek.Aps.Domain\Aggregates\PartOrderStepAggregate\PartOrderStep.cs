﻿using Adnc.Infra.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Aggregates.PartOrderStepAggregate
{
    public class PartOrderStep : EfFullAuditEntity
    {
        /// <summary>
        /// ID
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }


        public long? IdStep { get; set; }

        public int? IsKey { get; set; }

        public int? RelationType { get; set; }

        public long? IdOrder { get; set; }

        public int? Sort { get; set; }
    }

   
}
