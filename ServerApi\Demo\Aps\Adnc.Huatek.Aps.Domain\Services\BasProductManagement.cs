﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasProductAggregate;
using Adnc.Infra.Helper;
using Adnc.Shared.Rpc.Event;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using static StackExchange.Redis.Role;

namespace Adnc.Huatek.Aps.Domain.Services
{
    public class BasProductManagement : IDomainService
    {
        private readonly IEfBasicRepository<BasProduct> _basProductRepo;

        public BasProductManagement(IEfBasicRepository<BasProduct> basProductRepo)
        {
            _basProductRepo = basProductRepo;
        }

        public virtual async Task<BasProduct> CreateAsync(string Code, string Name, int Status, string Remark, string tCode)
        {
            var exists = await _basProductRepo.AnyAsync(x => x.Procode == Code && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"产品编码:{Code}已存在，不可重复添加！");

            exists = await _basProductRepo.AnyAsync(x => x.Proname == Name && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"产品名称:{Name}已存在，不可重复添加！");

            return new BasProduct()
            {
                Id = IdGenerater.GetNextId(),
                Proname = Name,
                Procode = Code,
                TCode = tCode,
                Status = Status,
                Remark = Remark
            };
        }
        public virtual async Task<bool> UpdateAsync(string Code, string Name, long id)
        {
            var exists = await _basProductRepo.AnyAsync(x => x.Procode == Code && x.Id != id && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"产品编码:{Code}已存在，不可重复添加！");

            exists = await _basProductRepo.AnyAsync(x => x.Proname == Name && x.Id != id && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"产品名称:{Name}已存在，不可重复添加！");

            return true;
        }

        public virtual async Task<(List<BasProduct> insertEntities, List<BasProduct> updateEntities, string Msg)> ImportAsync(List<BasProduct> entities)
        {
            List<BasProduct> insertEntities = new List<BasProduct>();
            List<BasProduct> updateEntities = new List<BasProduct>();
            StringBuilder sb = new StringBuilder();
            if (entities.Any())
            {
                foreach (var item in entities)
                {
                    var oldObj = _basProductRepo.Where(x => x.Procode == item.Procode && !x.IsDeleted, noTracking: false).FirstOrDefault();
                    if (oldObj == null)
                    {
                        item.Id = IdGenerater.GetNextId();
                        insertEntities.Add(item);
                    }
                    else
                    {
                        oldObj.Proname = item.Proname;
                        oldObj.Status = item.Status;
                        oldObj.TCode = item.TCode;
                        oldObj.Remark = item.Remark;
                        updateEntities.Add(oldObj);
                    }
                }
            }
            return (insertEntities, updateEntities, sb.ToString());
        }
    }
}
