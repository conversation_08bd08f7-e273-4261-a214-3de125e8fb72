﻿using Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulStepDetailsAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.SysTestAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.SysUserAggregate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Services
{
    public class PlanProductionSchedulStepDetailsManagement : IDomainService
    {
        private readonly IEfBasicRepository<PlanProductionSchedulStepDetails> _planProductionSchedulStepDetailsManagementRepo;

        public PlanProductionSchedulStepDetailsManagement(IEfBasicRepository<PlanProductionSchedulStepDetails> planProductionSchedulStepDetailsManagementRepo)
        {
            _planProductionSchedulStepDetailsManagementRepo = planProductionSchedulStepDetailsManagementRepo;
        }
    }
}
