﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate;
using System;
using System.Collections.Generic;
using System.Drawing.Printing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Dtos
{
    /// <summary>
    /// 生产计划
    /// </summary>
    public class PlanProductionSchedulDto : Dto
    {
        /// <summary>
        /// ID
        /// </summary>
        public long Id { get; set; }
        /// <summary>
        /// 计划编码
        /// </summary>
        public string? PlanCode { get; set; }
        /// <summary>
        /// 排产编码
        /// </summary>
        public string? SchedulCode { get; set; }
        /// <summary>
        /// 计划开始日期
        /// </summary>
        public DateTime PlanStart { get; set; }
        /// <summary>
        /// 计划结束日期
        /// </summary>
        public DateTime? PlanEnd { get; set; }
        /// <summary>
        /// 排序方式
        /// </summary>
        public int Sorts { get; set; }
        /// <summary>
        /// 前置规则
        /// </summary>
        public int PreRule { get; set; }
        /// <summary>
        /// 产线编码
        /// </summary>
        public string? LCode { get; set; }
        /// <summary>
        /// 产线名称
        /// </summary>
        public string? LName { get; set; }
        /// <summary>
        /// 计划状态
        /// </summary>
        public int Status { get; set; }

        public string? Remark { get; set; }
        /// <summary>
        /// 计划类型
        /// </summary>
        public int? PlanType { get; set; }
        /// <summary>
        /// 优先级
        /// </summary>
        public int Priority { get; set; }
        /// <summary>
        /// 订单编号
        /// </summary>
        public string? OrderNumber { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// 交付日期
        /// </summary>
        public DateTime? DeliveryDate { get; set; }
        /// <summary>
        /// 产品编码
        /// </summary>
        public string? ProductCode { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string? ProductName { get; set; }
        /// <summary>
        /// 排产数量
        /// </summary>
        public decimal Qty { get; set; }


        public DateTime? SchedulTime { get; set; }

    }

    public class PlanProductionSchedulResultDto : Dto
    {
        /// <summary>
        /// ID
        /// </summary>
        public long Id { get; set; }
        /// <summary>
        /// 计划编码
        /// </summary>
        public string? PlanCode { get; set; }
        /// <summary>
        /// 排产编码
        /// </summary>
        public string? SchedulCode { get; set; }
        /// <summary>
        /// 计划开始日期
        /// </summary>
        public string? PlanStart { get; set; }
        /// <summary>
        /// 计划结束日期
        /// </summary>
        public string? PlanEnd { get; set; }
        /// <summary>
        /// 排序方式
        /// </summary>
        public int Sorts { get; set; }
        /// <summary>
        /// 前置规则
        /// </summary>
        public int PreRule { get; set; }
        /// <summary>
        /// 产线编码
        /// </summary>
        public string? LCode { get; set; }
        /// <summary>
        /// 产线名称
        /// </summary>
        public string? LName { get; set; }
        /// <summary>
        /// 计划状态
        /// </summary>
        public int Status { get; set; }

        public string? Remark { get; set; }
        /// <summary>
        /// 计划类型
        /// </summary>
        public int? PlanType { get; set; }
        /// <summary>
        /// 优先级
        /// </summary>
        public int Priority { get; set; }
        /// <summary>
        /// 订单编号
        /// </summary>
        public string? OrderNumber { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// 交付日期
        /// </summary>
        public string? DeliveryDate { get; set; }
        /// <summary>
        /// 产品编码
        /// </summary>
        public string? ProductCode { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string? ProductName { get; set; }
        /// <summary>
        /// 排产数量
        /// </summary>
        public decimal Qty { get; set; }

    }

    public class PlanProductionSchedulPageDto : SearchPagedDto
    {
        /// <summary>
        /// 计划状态
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 订单编号
        /// </summary>
        public string? OrderNumber { get; set; }

        /// <summary>
        /// 计划编码
        /// </summary>
        public string? PlanCode { get; set; }

        /// <summary>
        /// 排产编码
        /// </summary>
        public string? ScheduleCode { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string? ProductName { get; set; }
    }

    public class PlanMaterialDto
    {

        /// <summary>
        /// 订单编号
        /// </summary>
        public string? OrderNumber { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        public string? MaterialName { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        public string? MaterialCode { get; set; }
        /// <summary>
        /// 默认获取7天的物料数据
        /// </summary>
        public int? GetDays { get; set; }
    }

    public class PlanMaterialDetailsDto : PlanMaterialDto
    {
        public string? Day { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string? ProductName { get; set; }

        /// <summary>
        /// 产品数量
        /// </summary>
        public decimal? ProductQty { get; set; }

        /// <summary>
        /// 物料数量
        /// </summary>
        public decimal? MaterialQty { get; set; }
    }
    public class PlanMaterialChartDto
    {
        public string Day { get; set; }
        /// <summary>
        /// 所需物料数量
        /// </summary>
        public decimal? NeedQty { get; set; }

        /// <summary>
        /// 累计物料数量
        /// </summary>
        public decimal? TotalQty { get; set; }
    }
    public class PlanMaterialListDto
    {
        public List<PlanMaterialChartDto> Materials { get; set; } = new List<PlanMaterialChartDto>();
        /// <summary>
        /// 物料清单
        /// </summary>
        public List<PlanMaterialDetailsDto> Items { get; set; } = new List<PlanMaterialDetailsDto>();
    }
    /// <summary>
    /// 人员计划查询条件
    /// </summary>
    public class PersonnelDto
    {
        public DateTime? Start { get; set; }
        public DateTime? End { get; set; }
    }
    /// <summary>
    /// 人员计划返回结果
    /// </summary>
    public class PersonnelResultDto
    {
        /// <summary>
        /// 计划时间
        /// </summary>
        public string PlanDates { get; set; }
        public List<Personnel> Classes { get; set; } = new List<Personnel>();
    }
    public class Personnel
    {
        /// <summary>
        /// 班次名称
        /// </summary>
        public string ClassesName { get; set; }
        /// <summary>
        /// 班次颜色
        /// </summary>
        public string Color { get; set; }
        /// <summary>
        /// 人员数量
        /// </summary>
        public int Qty { get; set; }
    }

    public class PlanSolution 
    {
        /// <summary>
        /// 方案名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 执行时间
        /// </summary>
        public DateTime ExecuteTime { get; set; }

        public  string? StepName { get; set; }

        /// <summary>
        ///开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 工位编码
        /// </summary>
        public string StationCode { get; set; }

        public int? Sorts { get; set; }
    }


    public class StationClass
    {

        public string? BeginTime { get; set; }
        public string? EndTime { get; set; }

        public int? Duration { get; set; }

        public string? StationCode { get; set; }

        public string? DeviceCode { get; set; }
    }

    public class  StationTime
    {
        public TimeSpan begintime { get; set;}
        public TimeSpan endtime { get; set; }

    }

    public class CurrentTime 
    {
        public int begin { get; set;}
        
        public int end { get; set; }
    }

    public class PlanStepCapacitySource
    {
        public string SchedulCode { get; set; }
        public string StepCode { get; set; }
        public string TaskCode { get; set; }
        public double? RemainingTime { get; set; }
        public string DeviceCode { get; set; }
        public string StationCode { get; set; }
        public DateTime? SchedulDate { get; set; }
    }

}
