﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Dtos
{
    public class BasTechnologyStepRelationDto : IDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 工艺路线ID
        /// </summary>
        public long? IdTechnology { get; set; }
        /// <summary>
        /// 工序路线id
        /// </summary>
        public long?  IdStep { get; set; }
        public string? StepCode { get; set; }
        public string? StepName { get; set; }
        public decimal? StepTime { get; set; }
        public decimal? WaitTime { get; set; }
        public string? TCode { get; set; }
        /// <summary>
        /// 前置工序编码
        /// </summary>
        public string? PreStep { get; set; }
        /// <summary>
        /// 前置工序名称
        /// </summary>
        public string? PreStepName { get; set; }
        /// <summary>
        /// 前置工序间隔时间
        /// </summary>
        public decimal? IntervalTime { get; set; }
        /// <summary>
        /// 是否关键工序
        /// </summary>
        public int? IsKey { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int? Sort { get; set; }

        /// <summary>
        /// 与下一道工序的关系
        /// </summary>
        public int? RelationType { get; set; }

        /// <summary>
        /// 创建人ID
        /// </summary>
        public long? CreateBy { get; set; }

        /// <summary>
        /// 创建时间/注册时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 最后更新人
        /// </summary>
        public long? ModifyBy { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }


    }
}
