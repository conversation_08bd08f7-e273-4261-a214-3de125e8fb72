﻿using Adnc.Infra.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate
{
    /// <summary>
    /// 客户订单
    /// </summary>
    public class BasOrder : EfFullAuditEntity
    {
        /// <summary>
        /// ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 优先级
        /// </summary>
        public int Priority { get; set; }

        /// <summary>
        /// 生产状态
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 订单编号
        /// </summary>
        public string OrderNumber { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 交付日期
        /// </summary>
        public DateTime DeliveryDate { get; set; }

        /// <summary>
        /// 销售单号
        /// </summary>
        public string SaleNumber { get; set; }
    }
}