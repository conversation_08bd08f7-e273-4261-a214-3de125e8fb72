﻿using Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanSourceAggregate;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class PartSchedulePlanSourceConfig : AbstractEntityTypeConfiguration<PartSchedulePlanSource>
    {
        public override void Configure(EntityTypeBuilder<PartSchedulePlanSource> builder)
        {
            base.Configure(builder);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.ScheduleCode).HasColumnName("schedulecode");
            builder.Property(x => x.SourceCode).HasColumnName("sourcecode");
            builder.Property(x => x.DevType).HasColumnName("devtype");
            builder.Property(x => x.BeginDate ).HasColumnName("begindate");
            builder.Property(x => x.EndDate).HasColumnName("enddate");
            builder.Property(x => x.Tat).HasColumnName("tat");
            builder.Property(x => x.UnitTat).HasColumnName("unittat");
            builder.Property(x => x.UnitCapacity).HasColumnName("unitcapacity");
            builder.Property(x => x.TotalCapacity).HasColumnName("totalcapacity");
            builder.Property(x => x.SourceType).HasColumnName("sourcetype");
            builder.Property(x => x.StepCode).HasColumnName("stepcode");
            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.CreateBy).HasColumnName("createby");
            builder.Property(x => x.CreateTime).HasColumnName("createtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
           
        }
    }
}
