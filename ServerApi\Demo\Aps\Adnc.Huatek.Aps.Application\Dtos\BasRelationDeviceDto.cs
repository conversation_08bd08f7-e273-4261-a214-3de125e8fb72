﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Dtos
{
    public class BasRelationDeviceDto : IDto
    {

        /// <summary>
        /// ID
        /// </summary>
        public long Id { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 工序id
        /// </summary>
        public long? IdRelation  { get; set; }

        /// <summary>
        /// 工序编码
        /// </summary>
        public long? IdDevice { get; set; }

        public string? DeviceCode { get; set; }
        public string? DeviceName { get; set; }
        /// <summary>
        /// 创建人姓名
        /// </summary>

        public decimal? Capacity { get; set; }

        public bool IsDeleted { get; set; }

        public string? CreatName { get; set; }
        /// <summary>
        /// 更新人
        /// </summary>
        public string? ModifyName { get; set; }

        public long CreateBy { get; set; }

        /// <summary>
        /// 创建时间/注册时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 最后更新人
        /// </summary>
        public long? ModifyBy { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }


    }

    
}
