﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.BasDevicePreserveAggregate;
using Adnc.Infra.Redis.Caching.Core.Interceptor;
using Adnc.Shared.Application.Contracts.ResultModels;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services
{
    
    public interface IBasDeviceClassService : IAppService
    {
        /// <summary>
        /// 获取所有工位信息
        /// </summary>
        /// <returns></returns>
        [OperateLog(LogName = "获取所有设备班次信息")]
        Task<PageModelDto<DeviceWorkingDateDto>> GetPagedAsync(BasDeviceClassPagedDto search);

        [OperateLog(LogName = "创建设备班次信息")]
        Task<AppSrvResult<long>> CreateAsync(BasDeviceClassDto input);

        [OperateLog(LogName = "删除设备班次信息")]
        Task<AppSrvResult> DeleteAsync(long id);

        [OperateLog(LogName = "获取所有设备班次信息")]
        Task<List<BasDeviceClassDto>> GetAllAsync();

        [OperateLog(LogName = "更新设备班次信息")]
        Task<AppSrvResult> UpdateAsync(BasDeviceClassDto input);

        [OperateLog(LogName = "根据id获取设备班次信息")]
        Task<ResultJson> GetByIdAsync(long id);



    }
}
