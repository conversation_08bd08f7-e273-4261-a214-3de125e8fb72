﻿using Adnc.Demo.Shared.Const;
using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Application.Services;
using Microsoft.AspNetCore.Authorization;

namespace Adnc.Huatek.Aps.Api.Controllers
{
    /// <summary>
    /// 首页
    /// </summary>
    [Route($"{RouteConsts.ApsRoot}/home")]
    [ApiController]
    [AllowAnonymous]
    public class HomePageController : AdncControllerBase
    {
        private readonly IHomePageService _homeSrv;

        public HomePageController(IHomePageService homePageService) => _homeSrv = homePageService;


        /// <summary>
        /// 获取订单数量
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("orderNum/{status}")]
        public async Task<ActionResult<int>> GetOrderNumAsync([FromRoute] int status)
            => await _homeSrv.GetOrderNumByStatusAsync(status);


        /// <summary>
        /// 获取首页空闲设备数量
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getIdledevicescount")]
        public async Task<ActionResult<int>> GetIdleDevicesCount()
            => await _homeSrv.GetIdleDevicesCount();

        /// <summary>
        /// 获取首页设备数量饼型图统计
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getdevicesstatuscount")]
        public async Task<ActionResult<List<HomeDeviceNumDto>>> GetDevicesStatusCount()
            => await _homeSrv.GetDevicesStatusCount();


        /// <summary>
        /// 获取首页订单数量曲线图数据
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getordernumbydate")]
        public async Task<ActionResult<List<HomeOrderNumByDateDto>>> GetOrderNumByDateAsync()
            => await _homeSrv.GetOrderNumByDateAsync();

        /// <summary>
        /// 首页计划数量统计
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getplanbystatus")]
        public async Task<ActionResult<List<PlanNumByDateDto>>> GetPlanByStatusAsync()
            => await _homeSrv.GetPlanByStatusAsync();
    }
}
