﻿using Adnc.Demo.Shared.Const;
using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Application.Services;
using Adnc.Shared;
using Adnc.Shared.Application.Contracts.Dtos;
using Adnc.Shared.WebApi.Authorization;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Adnc.Huatek.Aps.Api.Controllers
{
    [Route($"{RouteConsts.ApsRoot}/station")]
    [ApiController]
    public class BasStationController : AdncControllerBase
    {
        private readonly IBasStationService _stationSrv;

        public BasStationController(IBasStationService stationSrv) => _stationSrv = stationSrv;


        /// <summary>
        /// 获取所有信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getallasync")]
        public async Task<ActionResult<List<BasStationDto>>> GetAllAsync() => await _stationSrv.GetAllAsync();



        /// <summary>
        /// 新增工位
        /// </summary>
        /// <param name="input">用户信息</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("createasync")]
        public async Task<ActionResult<long>> CreateAsync([FromBody] BasStationDto input)
            => CreatedResult(await _stationSrv.CreateAsync(input));

        /// <summary>
        /// 删除工位
        /// </summary>
        /// <param name="id">工位ID</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpDelete("deleteasync/{id}")]
        public async Task<ActionResult> DeleteAsync([FromRoute] long id)
            => Result(await _stationSrv.DeleteAsync(id));


        /// <summary>
        /// 修改工位
        /// </summary>
        /// <param name="id">id</param>
        /// <param name="input">用户信息</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPut("updateasync")]
        public async Task<ActionResult> UpdateAsync([FromBody] BasStationDto input)
            => Result(await _stationSrv.UpdateAsync(input));



        /// <summary>
        /// 变更工位状态
        /// </summary>
        /// <param name="id">工位ID</param>
        /// <param name="status">状态</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPut("batch/changestatus/{id}")]
        public async Task<ActionResult> ChangeStatus([FromRoute] long id, [FromBody] SimpleDto<int> status)
            => Result(await _stationSrv.ChangeStatusAsync(id, status.Value));

        /// <summary>
        /// 批量变更工位状态
        /// </summary>
        /// <param name="input">工位Ids与状态</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPut("changestatus")]
        public async Task<ActionResult> ChangeStatus([FromBody] UpdateStatusDto input)
            => Result(await _stationSrv.ChangeStatusAsync(input));

        /// <summary>
        /// 获取工位列表分页
        /// </summary>
        /// <param name="search">查询条件</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("page")]
        public async Task<ActionResult<PageModelDto<BasStationDto>>> GetPagedAsync([FromBody] BasStationPagedDto search)
            => await _stationSrv.GetPagedAsync(search);

        /// <summary>
        /// 按产线编码获取工位列表
        /// </summary>
        /// <param name="search">查询条件</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("list")]
        public async Task<ActionResult<List<BasStationDto>>> GetListAsync([FromBody] BasStationSearchDto search)
            => await _stationSrv.GetListAsync(search);

        /// <summary>
        /// 获取单个工位详情信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("current/{id}")]
        public async Task<ActionResult<ResultJson>> GetCurrentUserInfoAsync(long id) => await _stationSrv.GetAppointAsync(id);


        /// <summary>
        /// 获取工位中班次信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("classes/{id}")]
        public async Task<ActionResult<List<ClassesDto>>> GetClassesInfoAsync(long id) => await _stationSrv.GetClassesInfoAsync(id);

        /// <summary>
        /// 批量启用
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPut("changestatus/{status}")]
        public async Task<ActionResult> MakeEnableAsync([FromRoute] int status, [FromBody] List<long> data)
          => Result(await _stationSrv.MakeEnableAsync(status, data));
    }
}
