﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.BasLineAggregate;
using Adnc.Shared.Application.Contracts.ResultModels;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services
{
    public interface IBasLineService: IAppService
    {
        [OperateLog(LogName = "获取产线列表信息")]
        Task<PageModelDto<BasLineDto>> GetPagedAsync(LinePagedDto search);
  
        [OperateLog(LogName = "创建产线")]
        [UnitOfWork]
        Task<AppSrvResult<long>> CreateAsync(BasLineDto input);
        [OperateLog(LogName = "修改产线")]
        [UnitOfWork]
        Task<AppSrvResult> UpdateAsync(BasLineDto input);
        [OperateLog(LogName = "删除产线")]
        [UnitOfWork]
        Task<AppSrvResult> DeleteAsync(long id);
        [OperateLog(LogName = "根据ID获取产线数据")]
        Task<BasLineDto> GetByIdAsync(long id);

        [OperateLog(LogName = "获取所有产线列表信息")]
        Task<List<BasLineDto>> GetAllAsync();
        [OperateLog(LogName = "根据产品编码获取所有产线信息")]
        Task<List<ProductLineDto>> GetLinebyProductcode(string productCode);

        [OperateLog(LogName = "批量禁用启用产线")]
        Task<AppSrvResult> MakeEnableAsync(int status, List<long> objs);

    }
}
