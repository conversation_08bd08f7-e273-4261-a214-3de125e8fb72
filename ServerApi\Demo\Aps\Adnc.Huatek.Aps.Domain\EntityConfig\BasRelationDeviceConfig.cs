﻿
using Adnc.Huatek.Aps.Domain.Aggregates.BasRelationDeviceAggregate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    
    public class BasRelationDeviceConfig : AbstractEntityTypeConfiguration<BasRelationDevice>
    {

        public override void Configure(EntityTypeBuilder<BasRelationDevice> builder)
        {
            base.Configure(builder);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.Remark).HasColumnName("remark");
            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.CreateBy).HasColumnName("createby");
            builder.Property(x => x.CreateTime).HasColumnName("createtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
            builder.Property(x => x.IdRelation).HasColumnName("idrelation");
            builder.Property(x => x.IdDevice).HasColumnName("iddevice");
            builder.Property(x => x.Capacity).HasColumnName("capacity");

        }
    }
}
