﻿using Adnc.Demo.Shared.Const;
using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Application.Services;
using Adnc.Shared.Application.Contracts.ResultModels;
using Adnc.Shared.WebApi.Authorization;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Adnc.Huatek.Aps.Api.Controllers
{
    [Route($"{RouteConsts.ApsRoot}/Technologys")]
    [ApiController]
    public class BasTechnologyController : AdncControllerBase
    {
        private readonly IBasTechnologyService _techSrv;

        public BasTechnologyController(IBasTechnologyService techSrv) => _techSrv = techSrv;

        /// <summary>
        /// 获取工艺信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getall")]
        public async Task<ActionResult<List<BasTechnologyDto>>> GetAllAsync()
            => await _techSrv.GetAllAsync();
        /// <summary>
        /// 获取工序分页信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("page")]
        public async Task<ActionResult<PageModelDto<BasTechnologyDto>>> GetPagedAsync([FromBody] TechnologyPagedDto search) => await _techSrv.GetPagedAsync(search);

        /// <summary>
        /// 新增工艺
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("add")]
        public async Task<ActionResult<long>> CreateAsync([FromBody] BasTechnologyDto input)
           => CreatedResult(await _techSrv.CreateAsync(input));

        /// <summary>
        /// 更新工序
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("edit")]
        public async Task<ActionResult> UpdateAsync([FromBody] BasTechnologyDto input)
           => Result(await _techSrv.UpdateAsync(input));


        /// <summary>
        /// 删除工序
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpDelete("delete/{id}")]
        public async Task<ActionResult> DeleteAsync([FromRoute] long id)
          => Result(await _techSrv.DeleteAsync(id));

        /// <summary>
        /// 获取工序详细信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getbyid/{id}")]
        public async Task<ActionResult<BasTechnologyDto>> GetByIdAsync([FromRoute] long id)
            => await _techSrv.GetByIdAsync(id);

        /// <summary>
        /// 通过工艺编码获取工序详细信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("detailsbytcode/{tCode}")]
        public async Task<ActionResult<BasTechnologyDto>> GetByTCodeAsync([FromRoute] string tCode)
            => await _techSrv.GetByTCodeAsync(tCode);

        /// <summary>
        /// 更新启用/禁用状态
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("changestatus")]
        public async Task<ActionResult> ChangeStatus([FromBody] ChangeStatusDto input)
            => Result(await _techSrv.ChangeStatusAsync(input));
    }
}
