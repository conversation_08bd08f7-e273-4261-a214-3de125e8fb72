﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.BasLineAggregate;
using Adnc.Shared.Application.Contracts.ResultModels;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services
{
    public interface IBasHolidayService : IAppService
    {
        [OperateLog(LogName = "获取假期时间列表信息")]
        Task<PageModelDto<BasHolidayDto>> GetPagedAsync(HolidayPagedDto search);
  
        [OperateLog(LogName = "创建假期时间")]
        [UnitOfWork]
        Task<AppSrvResult<long>> CreateAsync(BasHolidayDto input);
        [OperateLog(LogName = "修改假期时间")]
        [UnitOfWork]
        Task<AppSrvResult> UpdateAsync(BasHolidayDto input);
        [OperateLog(LogName = "删除假期时间")]
        [UnitOfWork]
        Task<AppSrvResult> DeleteAsync(long id);
        [OperateLog(LogName = "根据ID获取假期时间数据")]
        Task<BasHolidayDto> GetByIdAsync(long id);

        [OperateLog(LogName = "获取所有假期时间列表信息")]
        Task<List<BasHolidayDto>> GetAllAsync();
        [OperateLog(LogName = "导入")]
        Task<ResultJson> ImportAsync(List<ImportHolidayDto> orders);
        [OperateLog(LogName = "删除全部假期时间数据")]
        Task<AppSrvResult> DeleteAllAsync();

        [OperateLog(LogName = "获取时间段内的非排产日期")]
        Task<List<string>> GetNonScheduleDateAsync(HolidayPagedDto search);



    }
}
