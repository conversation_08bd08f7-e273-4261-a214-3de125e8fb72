﻿using Adnc.Demo.Shared.Const;
using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Application.Services;
using Adnc.Shared.Application.Contracts.ResultModels;
using Adnc.Shared.WebApi.Authorization;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Adnc.Huatek.Aps.Api.Controllers
{
    /// <summary>
    /// 待排产计划
    /// </summary>
    [Route($"{RouteConsts.ApsRoot}/PendingPlans")]
    [ApiController]
    public class PartPlanPendingController : AdncControllerBase
    {
        private readonly IPartPlanPendingService _pendingPlanSrv;

        public PartPlanPendingController(IPartPlanPendingService pendingPlanSrv) => _pendingPlanSrv = pendingPlanSrv;


        /// <summary>
        /// 获取待排产计划分页信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("page")]
        public async Task<ActionResult<PageModelDto<PartPlanPendingResultDto>>> GetPagedAsync([FromBody] PartPlanPendingPagedDto search)
            => await _pendingPlanSrv.GetPagedAsync(search);


        /// <summary>
        /// 获取待排产计划分页信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getall")]
        public async Task<ActionResult<ResultJson>> GetAllAsync()
            => await _pendingPlanSrv.GetAllAsync();

        /// <summary>
        /// 新增待排产计划
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("add")]
        [AllowAnonymous]
        public async Task<ActionResult<long>> CreateAsync([FromBody] PartPlanPendingDto input)
           => CreatedResult(await _pendingPlanSrv.CreateAsync(input));

        /// <summary>
        /// 更新待排产计划
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("edit")]
        public async Task<ActionResult> UpdateAsync([FromBody] PartPlanPendingDto input)
           => Result(await _pendingPlanSrv.UpdateAsync(input));


        /// <summary>
        /// 删除待排产计划
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
         [AllowAnonymous]
        [HttpDelete("delete/{id}")]
        public async Task<ActionResult> DeleteAsync([FromRoute] long id)
           => Result(await _pendingPlanSrv.DeleteAsync(id));

        /// <summary>
        /// 根据ID获取待排产计划
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getbyid/{id}")]
        public async Task<ActionResult<ResultJson>> GetByIdAsync([FromRoute] long id)
            => await _pendingPlanSrv.GetByIdAsync(id);





        /// <summary>
        /// 根据ID获取待排产计划
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getPlanPendingById/{id}")]
        public async Task<ActionResult<ResultJson>> GetPlanPendingByIdAsync([FromRoute] long id)
            => await _pendingPlanSrv.GetPlanPendingByIdAsync(id);
    }
}
