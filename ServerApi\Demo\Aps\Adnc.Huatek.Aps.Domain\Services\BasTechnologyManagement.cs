﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasTechnologyAggregate;
using Adnc.Shared.Rpc.Event;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Services
{
    public class BasTechnologyManagement : IDomainService
    {
        private readonly IEfBasicRepository<BasTechnology> _basTechnologyRepo;

        public BasTechnologyManagement(IEfBasicRepository<BasTechnology> basTechnologyRepo) 
        {
            _basTechnologyRepo = basTechnologyRepo;
        }

        public virtual async Task<BasTechnology> CreateAsync(string Code, string Name, int Status, string Remark)
        {
            var exists = await _basTechnologyRepo.AnyAsync(x => x.Tcode == Code && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"工艺编码:{Code}已存在，不可重复添加！");

            exists = await _basTechnologyRepo.AnyAsync(x => x.Tname == Name && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"工艺名称:{Name}已存在，不可重复添加！");

            return new BasTechnology()
            {
                Id = IdGenerater.GetNextId(),
                Tname = Name,
                Tcode = Code,
                Status = Status,
                Remark = Remark
            };
        }


        public virtual async Task<bool> UpdateAsync(string Code, string Name, long id)
        {
            var exists = await _basTechnologyRepo.AnyAsync(x => x.Tcode == Code && x.Id != id && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"工艺编码:{Code}已存在，不可重复添加！");

            exists = await _basTechnologyRepo.AnyAsync(x => x.Tname == Name && x.Id != id && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"工艺名称:{Name}已存在，不可重复添加！");

            return true;
        }
    }
}
