﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasTechnologyStepRelationAggregate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class BasTechnologyStepRelationConfig : AbstractEntityTypeConfiguration<BasTechnologyStepRelation>
    {
        public override void Configure(EntityTypeBuilder<BasTechnologyStepRelation> builder)
        {
            base.Configure(builder);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.IdTechnology).HasColumnName("idtechnology");
            builder.Property(x => x.IdStep).HasColumnName("idstep");
            builder.Property(x => x.StepCode).HasColumnName("stepcode");
            builder.Property(x => x.TCode).HasColumnName("tcode");
            builder.Property(x => x.Sort).HasColumnName("sort");
            builder.Property(x => x.RelationType ).HasColumnName("relationtype");
            builder.Property(x => x.<PERSON>).HasColumnName("iskey");
            builder.Property(x => x.Remark).HasColumnName("remark");
            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.CreateBy).HasColumnName("createby");
            builder.Property(x => x.CreateTime).HasColumnName("createtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
        }
    }
}
