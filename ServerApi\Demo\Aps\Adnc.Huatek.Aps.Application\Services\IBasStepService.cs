﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStepAggregate;
using Adnc.Shared.Application.Contracts.ResultModels;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services
{
    public interface IBasStepService : IAppService
    {
        [OperateLog(LogName = "获取工序列表信息")]
        Task<PageModelDto<BasStepDto>> GetPagedAsync(StepPagedDto search);
        [OperateLog(LogName = "获取所有工序信息")]
        Task<ResultJson> GetAllAsync();
        [OperateLog(LogName = "创建工序")]
        Task<AppSrvResult<long>> CreateAsync(BasStepDto input);
        [OperateLog(LogName = "修改工序")]
        Task<AppSrvResult> UpdateAsync(BasStepDto input);
        [OperateLog(LogName = "删除工序")]
        Task<AppSrvResult> DeleteAsync(long id);

        [OperateLog(LogName = "根据ID获取工序")]
        Task<ResultJson> GetByIdAsync(long id);

        [OperateLog(LogName = "更新状态")]
        Task<AppSrvResult> ChangeStatusAsync(ChangeStatusDto input);

        [OperateLog(LogName = "导入工序")]
        Task<ResultJson> ImportAsync(List<BasStepDto> dtos);
    }
}
