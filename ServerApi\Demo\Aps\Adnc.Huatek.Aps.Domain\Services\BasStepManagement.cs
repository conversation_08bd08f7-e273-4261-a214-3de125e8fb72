﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasDevice;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStepAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasTechnologyStepRelationAggregate;
using Adnc.Infra.Helper;
using Adnc.Shared.Rpc.Event;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using static StackExchange.Redis.Role;

namespace Adnc.Huatek.Aps.Domain.Services
{
    public class BasStepManagement : IDomainService
    {
        private readonly IEfBasicRepository<BasStep> _basStepRepo;
        private readonly IEfBasicRepository<BasTechnologyStepRelation> _basTechnologyStepRelationRepo;

        public BasStepManagement(IEfBasicRepository<BasStep> basStepRepo,
            IEfBasicRepository<BasTechnologyStepRelation> basTechnologyStepRelationRepo) 
        {
            _basStepRepo = basStepRepo;
            _basTechnologyStepRelationRepo = basTechnologyStepRelationRepo;
        }


        public virtual async Task<BasStep> CreateAsync(string? Code, string? Name, int? Status, decimal? StepTime, string? Remark,decimal? tat)
        {
            var exists = await _basStepRepo.AnyAsync(x => x.Code == Code && !x.IsDeleted);
            if (exists)
                throw new BusinessException(HttpStatusCode.OK, $"工序编码:{Code}已存在，不可重复添加！");

            exists = await _basStepRepo.AnyAsync(x => x.Name == Name && !x.IsDeleted);
            if (exists)
                throw new BusinessException(HttpStatusCode.OK, $"工序名称:{Name}已存在，不可重复添加！");

            return new BasStep()
            {
                Id = IdGenerater.GetNextId(),
                Name = Name,
                Code = Code,
                StepTime = StepTime,
                Status = Status,
                Remark = Remark,
                Tat = tat
            };
        }

        public virtual async Task<bool> UpdateAsync(string Code, string Name, long id)
        {
            var exists = await _basStepRepo.AnyAsync(x => x.Code == Code && x.Id != id && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"工序编码:{Code}已存在，不可重复添加！");

            exists = await _basStepRepo.AnyAsync(x => x.Name == Name && x.Id != id && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"工序名称:{Name}已存在，不可重复添加！");

            return true;
        }

        public virtual async Task<bool> DeleteAsync(string code)
        {
            var exists = await _basTechnologyStepRelationRepo.AnyAsync(x => x.StepCode == code && !x.IsDeleted); ;
            if (exists)
                throw new BusinessException($"工序编码被工艺使用，不可删除！");

            return true;
        }


        public virtual async Task<(List<BasStep> insertEntities, List<BasStep> updateEntities, string Msg)> ImportAsync(List<BasStep> entities)
        {
            List<BasStep> insertEntities = new List<BasStep>();
            List<BasStep> updateEntities = new List<BasStep>();
            StringBuilder sb = new StringBuilder();
            if (entities.Any())
            {
                foreach (var item in entities)
                {
                    var oldObj = _basStepRepo.Where(x => x.Code == item.Code && !x.IsDeleted, noTracking: false).FirstOrDefault();
                    if (oldObj == null)
                    {
                        item.Id = IdGenerater.GetNextId();
                        insertEntities.Add(item);
                    }
                    else
                    {
                        oldObj.Name = item.Name;
                        oldObj.Status = item.Status;
                        oldObj.Tat = item.Tat;
                        oldObj.StepTime = item.StepTime;
                        oldObj.Remark = item.Remark;
                        updateEntities.Add(oldObj);
                    }
                }
            }
            return (insertEntities, updateEntities, sb.ToString());
        }
    }
}
