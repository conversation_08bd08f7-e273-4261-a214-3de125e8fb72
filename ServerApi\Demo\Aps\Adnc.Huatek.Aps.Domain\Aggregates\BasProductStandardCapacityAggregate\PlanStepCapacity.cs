﻿using Adnc.Infra.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Aggregates.BasProductStandardCapacityAggregate
{
    public class PlanStepCapacity : EfFullAuditEntity
    {
        /// <summary>
        /// ID
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }


        /// <summary>
        /// 任务编码
        /// </summary>
        public string? TaskCode { get; set; }

        /// <summary>
        /// 产能方案
        /// </summary>
        public string? SolutionName { get; set; }

        /// <summary>
        /// 产品编码
        /// </summary>
        public string? ProCode { get; set; }

        /// <summary>
        /// 工序编码
        /// </summary>
        public string? StepCode { get; set; }

        /// <summary>
        /// 工位编码
        /// </summary>
        public string? StationCode { get; set; }

        /// <summary>
        /// 产线编码
        /// </summary>
        public string? LineCode { get; set; }

        /// <summary>
        /// 设备编码
        /// </summary>
        public string? DeviceCode { get; set; }

        /// <summary>
        /// 产能
        /// </summary>
        public decimal? Capacity { get; set; }

        /// <summary>
        /// 单位工作时长
        /// </summary>
        public decimal? WorkUnitTime { get; set; }

        /// <summary>
        /// 标准工作时长
        /// </summary>
        public decimal? StandardWorkTime { get; set; }

        public string? StepName { get; set; }

    }


}
