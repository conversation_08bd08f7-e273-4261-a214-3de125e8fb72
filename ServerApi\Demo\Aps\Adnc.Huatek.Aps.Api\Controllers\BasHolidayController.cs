﻿using Adnc.Demo.Shared.Const;
using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Application.Services;
using Adnc.Shared;
using Adnc.Shared.Application.Contracts.ResultModels;
using Adnc.Shared.WebApi.Authorization;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Npoi.Mapper;

namespace Adnc.Huatek.Aps.Api.Controllers
{
    [Route($"{RouteConsts.ApsRoot}/holiday")]
    [ApiController]
    [AllowAnonymous]
    public class BasHolidayController : AdncControllerBase
    {
        private readonly IBasHolidayService _holidaySrv;

        public BasHolidayController(IBasHolidayService holidaySrv) => _holidaySrv = holidaySrv;

        /// <summary>
        /// 获取分页信息
        /// </summary>
        /// <returns></returns>
        [HttpPost("page")]
        public async Task<ActionResult<PageModelDto<BasHolidayDto>>> GetPagedAsync([FromBody] HolidayPagedDto search) => await _holidaySrv.GetPagedAsync(search);

        /// <summary>
        /// 获取所有信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getallasync")]
        public async Task<ActionResult<List<BasHolidayDto>>> GetAllAsync() => await _holidaySrv.GetAllAsync();



        /// <summary>
        /// 新增
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("add")]
        public async Task<ActionResult<long>> CreateAsync([FromBody] BasHolidayDto input)
         => CreatedResult(await _holidaySrv.CreateAsync(input));

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("edit")]
        public async Task<ActionResult> UpdateAsync([FromBody] BasHolidayDto input)
           => Result(await _holidaySrv.UpdateAsync(input));


        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpDelete("delete/{id}")]
        public async Task<ActionResult> DeleteAsync([FromRoute] long id)
          => Result(await _holidaySrv.DeleteAsync(id));

        /// <summary>
        /// 根据ID获取
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getbyid/{id}")]
        public async Task<ActionResult<BasHolidayDto>> GetByIdAsync([FromRoute] long id)
            => await _holidaySrv.GetByIdAsync(id);
        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="formFile"></param>
        /// <returns></returns>
        [HttpPost("importholidays")]
        public async Task<ActionResult<ResultJson>> ImportOrders(IFormFile formFile)
        {
            //通过上传文件流初始化Mapper
            var mapper = new Mapper(formFile.OpenReadStream());
            //读取sheet1的数据
            var entitys = mapper.Take<ImportHolidayDto>("Sheet1").Select(i => i.Value).ToList();
            var result = await _holidaySrv.ImportAsync(entitys);
            return result;
        }

        /// <summary>
        /// 删除全部假期
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpDelete("deleteall")]
        public async Task<ActionResult> DeleteAllAsync()
          => Result(await _holidaySrv.DeleteAllAsync());



        /// <summary>
        /// 获取时间段内的非排产时间
        /// </summary>
        /// <returns></returns>
        [HttpPost("getunplan")]
        public async Task<ActionResult<List<string>>> GetNonScheduleDateAsync([FromBody] HolidayPagedDto search) => await _holidaySrv.GetNonScheduleDateAsync(search);

        
    }
}
