﻿using Adnc.Huatek.Aps.Domain.Aggregates.PartOrderAggregate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class PartOrderConfig : AbstractEntityTypeConfiguration<PartOrder>
    {
        public override void Configure(EntityTypeBuilder<PartOrder> builder)
        {
            base.Configure(builder);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.Level).HasColumnName("level");
            builder.Property(x => x.IdProduct).HasColumnName("idproduct");
            builder.Property(x => x.OrderCode).HasColumnName("ordercode");
            builder.Property(x => x.IdTec).HasColumnName("idtec");
            builder.Property(x => x.Status).HasColumnName("status");
            builder.Property(x => x.Qty ).HasColumnName("qty");
            builder.Property(x => x.IdCustorm).HasColumnName("idcustorm");
            builder.Property(x => x.DeliveryTime).HasColumnName("deliverytime");
            builder.Property(x => x.Remark).HasColumnName("remark");
            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.CreateBy).HasColumnName("createby");
            builder.Property(x => x.CreateTime).HasColumnName("createtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
        }
    }
}
