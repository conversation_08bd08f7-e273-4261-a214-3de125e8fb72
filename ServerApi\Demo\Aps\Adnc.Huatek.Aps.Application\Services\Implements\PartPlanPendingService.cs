﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStation;
using Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate;
using Adnc.Huatek.Aps.Domain.Services;
using Adnc.Shared;
using Adnc.Shared.Application.Contracts.ResultModels;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Adnc.Huatek.Aps.Domain.Aggregates.SysUserAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasTechnologyAggregate;
using Adnc.Demo.Shared.Rpc.Http.Services;
using Microsoft.AspNetCore.Http;
using Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanRuleAggregate;

namespace Adnc.Huatek.Aps.Application.Services.Implements
{
    public class PartPlanPendingService : AbstractAppService, IPartPlanPendingService
    {
        private readonly PartPlanPendingManagement _basplanPendingMgr;
        private readonly IEfBasicRepository<PartPlanPending> _basplanPendingRepo;
        private readonly IEfBasicRepository<SysUser> _sysUserRepo;
        private readonly IEfBasicRepository<BasOrder> _basOrderRepo;
        private readonly BasNumberManagementService _basNumberManagementService;
        private readonly IEfBasicRepository<BasOrderProduct> _basOrderProductRepo;
        private readonly IEfBasicRepository<BasProduct> _basProductRepo;
        private readonly IEfBasicRepository<BasTechnology> _basTecRepo;
        private readonly IMaintRestClient _maintRestClient;
        private readonly IEfBasicRepository<PartSchedulePlanRule> _spRuleRepo;


        public PartPlanPendingService(
            IEfBasicRepository<PartPlanPending> basplanPendingRepo,
            IEfBasicRepository<SysUser> sysUserRepo,
            IEfBasicRepository<BasOrder> basOrderRepo,
            IEfBasicRepository<BasProduct> basProductRepo,
            IEfBasicRepository<BasTechnology> basTecRepo,
            PartPlanPendingManagement basplanPendingMgr,
            IMaintRestClient maintRestClient,
            IEfBasicRepository<PartSchedulePlanRule> spRuleRepo,
        BasNumberManagementService basNumberManagementService,
            IEfBasicRepository<BasOrderProduct> basOrderProductRepo)
        {
            _basplanPendingMgr = basplanPendingMgr;
            _basplanPendingRepo = basplanPendingRepo;
            _sysUserRepo = sysUserRepo;
            _basOrderRepo = basOrderRepo;
            _basProductRepo = basProductRepo;
            _basTecRepo = basTecRepo;
            _spRuleRepo = spRuleRepo;
            _maintRestClient = maintRestClient;
            _basNumberManagementService = basNumberManagementService;
            _basOrderProductRepo = basOrderProductRepo;
        }
        /// <summary>
        /// 获取分页列表
        /// </summary>
        /// <param name="search"></param>
        /// <returns></returns>
        public async Task<PageModelDto<PartPlanPendingResultDto>> GetPagedAsync(PartPlanPendingPagedDto search)
        {
            search.TrimStringFields();
            var whereExpression = ExpressionCreator
                                                .New<PartPlanPending>()
                                                .And(x => !x.IsDeleted)
                                                .AndIf(search.OrderNumber.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.OrderNumber!, $"%{search.OrderNumber}%"))
                                                .AndIf(search.UserName.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.UserName!, $"%{search.UserName}%"))
                                                .AndIf(search.Status.IsNotNullOrWhiteSpace(), x => x.Status == Convert.ToInt32(search.Status))
                                                .AndIf(search.Priority.IsNotNullOrWhiteSpace(), x => x.Priority == Convert.ToInt32(search.Status));

            var plans = _basplanPendingRepo.Where(whereExpression);
                                          
            var users = _sysUserRepo.Where(x => true);
            var pWhere = ExpressionCreator
                                                .New<BasProduct>()
                                                .And(x => !x.IsDeleted)
                                                .AndIf(search.ProductName.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Proname!, $"%{search.ProductName}%"))
                                                .AndIf(search.ProductCode.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Procode!, $"%{search.ProductCode}%"));
            var products = _basProductRepo.Where(pWhere);
            var techs = _basTecRepo.Where(x => !x.IsDeleted);
            var entities =(from p in plans
                            join u in users on p.CreateBy equals u.Id
                            join a in products on p.ProductCode equals a.Procode
                            join b in techs on a.TCode equals b.Tcode into xx
                            from xxx in xx.DefaultIfEmpty()
                            select new PartPlanPendingResultDto
                            {
                                Id = p.Id,
                                PlanCode = p.PlanCode,
                                Priority = p.Priority.ToString(),
                                TName = xxx.Tname,
                                OrderNumber = p.OrderNumber,
                                UserName = p.UserName,
                                ProductName = a.Proname,
                                ProductCode = p.ProductCode,
                                Status = p.Status.ToString(),
                                Qty = p.Qty,
                                DeliveryDate = $"{p.DeliveryDate:yyy-MM-dd}",
                                CreateBy = p.CreateBy,
                                CreateTime = p.CreateTime,
                                CreatName = u.Name
                            }
                       );

            //var devStateRpcResult = await _maintRestClient.GetDictByNameAsync("生产计划状态");
            //if (devStateRpcResult.IsSuccessStatusCode)
            //{
            //    var devStateEnums = devStateRpcResult.Content;
            //    if (devStateEnums is not null)
            //    {
            //        entities.ForEach(x =>
            //        {
            //            x.StatusDis = devStateEnums.FirstOrDefault(d => d.value == x.Status.ToString())?.label;
            //        });
            //    }
            //}

            //devStateRpcResult = await _maintRestClient.GetDictByNameAsync("排产优先级");
            //if (devStateRpcResult.IsSuccessStatusCode)
            //{
            //    var PriorityEnums = devStateRpcResult.Content;
            //    if (PriorityEnums is not null)
            //    {
            //        entities.ForEach(x =>
            //        {
            //            x.PriorityDis = PriorityEnums.FirstOrDefault(d => d.value == x.Priority.ToString())?.label;
            //        });
            //    }
            //}


            var total = entities.Count();
            var results=await entities.OrderByDescending(x => x.CreateTime)
                                            .Skip(search.SkipRows())
                                            .Take(search.PageSize).ToListAsync();
            return new PageModelDto<PartPlanPendingResultDto>(search, results, total);
        }
        /// <summary>
        /// 获取所有数据
        /// </summary>
        /// <returns></returns>

        public async Task<ResultJson> GetAllAsync()
        {
            var basOrders = await _basplanPendingRepo.Where(x => !x.IsDeleted && x.Status == 1).ToListAsync();

            var basOrdersDto = Mapper.Map<List<PartPlanPendingDto>>(basOrders);

            return new ResultJson("查询成功", basOrdersDto);
        }

        /// <summary>
        /// 创建生产计划
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult<long>> CreateAsync(PartPlanPendingDto input)
        {
            input.TrimStringFields();
            var product = _basOrderProductRepo.Where(x => x.ProductCode == input.ProductCode && x.OrderId == input.OrderId, noTracking: false).FirstOrDefault();
            var entity = await _basplanPendingMgr.CreateAsync(input.OrderId??0,input.IdDetail, input.ProductCode, input.Qty, product.Quantity);
            var order = await _basOrderRepo.GetAsync(input.OrderId ?? 0);
            if (order != null)
            {
                var autoCode = _basNumberManagementService.GetNumberBySimpleName(CommonConst.PLANNUMBER).Result;
                entity.PlanCode = autoCode;
                entity.Priority = input.Priority;
                entity.ProductName = input.ProductName;
                entity.ProductCode = input.ProductCode;
                entity.Status = OrderStatus.Schedule;
                entity.OrderNumber = order.OrderNumber;
                entity.DeliveryDate = order.DeliveryDate;
                entity.UserName = order.UserName;
                entity.Qty = input.Qty;
                entity.Priority = input.Priority;

                order.Status = PendingPlanStatusConst.Waiting;
                await _basOrderRepo.UpdateAsync(order);
            }
            await _basplanPendingRepo.InsertAsync(entity);

            //修改订单产品状态和待排产数量
           
            if (product != null)
            {
                product.Status = OrderStatus.Schedule;
                product.PendingNum = product.PendingNum - input.Qty;
                await _basOrderProductRepo.UpdateAsync(product);
                //if (product.PendingNum <= 0)
                //{
                //    await _basOrderRepo.UpdateAsync(order);
                //}
            }
            return entity.Id;
        }


        /// <summary>
        /// 更新生产计划
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult> UpdateAsync(PartPlanPendingDto input)
        {
            bool isUpdete = await _basplanPendingMgr.UpdateAsync(input.Status ?? 0, input.Priority, input.Id);
            if (isUpdete)
            {
                var plan = await _basplanPendingRepo.GetAsync(input.Id);
                if (plan != null)
                {
                    plan.Status = input.Status ?? 0;
                    plan.Priority = input.Priority;
                    await _basplanPendingRepo.UpdateAsync(plan);
                }
            }
            return AppSrvResult();
        }

        /// <summary>
        /// 删除生产计划
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult> DeleteAsync(long id)
        {
            var step = await _basplanPendingRepo.GetAsync(id);
            if (step != null)
            {
                step.IsDeleted = true;
                await _basplanPendingRepo.UpdateAsync(step);
            }
            return AppSrvResult();


        }


        /// <summary>
        /// 根据ID获取生产计划数据
        /// </summary>
        /// <returns></returns>

        public async Task<ResultJson> GetByIdAsync(long id)
        {
            var entity = await _basplanPendingRepo.GetAsync(id);

            var entityDto = Mapper.Map<PartPlanPendingDto>(entity);

            return new ResultJson("查询成功", entityDto);

        }



        public async Task<ResultJson> GetPlanPendingByIdAsync(long id)
        {
            var entity = await _basplanPendingRepo.GetAsync(id);
            var entityDto = Mapper.Map<PartPlanPendingDto>(entity);

            var restRpcResult = await _maintRestClient.GetDictByNameAsync("排产优先级");
            if (restRpcResult.IsSuccessStatusCode)
            {
                var priorityEnums = restRpcResult.Content;
                if (priorityEnums is not null)
                {
                    entityDto.PriorityDis = priorityEnums.FirstOrDefault(d => d.value == entityDto.Priority.ToString()!)?.label ?? "0";
                }
            }

            var ruleEntites = await _spRuleRepo.Where(x => !x.IsDeleted && string.IsNullOrWhiteSpace(x.ScheduleCode)).ToListAsync();

            var ruleDtos= Mapper.Map<List<PartSchedulePlanRuleDto>>(ruleEntites);


            var restRpcResultNew = await _maintRestClient.GetChildByCodeAsync("排产规则");
            if (restRpcResultNew.IsSuccessStatusCode)
            {
                var ruleEnum = restRpcResultNew.Content;
                if (ruleEnum is not null)
                {
                    ruleDtos.ForEach(d => 
                    {
                        var obj = ruleEnum.childs.FirstOrDefault(x => x.value == d.RuleType);
                        if (obj != null)
                        {
                            d.RuleTypeName = obj.label ?? "";
                            d.Nodes = obj.childs.ToList();
                        }

                    });
                }
            }

            entityDto.Rules = ruleDtos;
            return new ResultJson("查询成功", entityDto);

        }


    }
}
