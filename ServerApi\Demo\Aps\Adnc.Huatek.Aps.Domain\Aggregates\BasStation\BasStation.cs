﻿using Adnc.Infra.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Aggregates.BasStation
{
    public class BasStation : EfFullAuditEntity
    {

        
        /// <summary>
        /// 状态
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 工位编码
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 工位名称
        /// </summary>
        public string? Name { get; set; }

        ///// <summary>
        ///// 所属车间
        ///// </summary>
        //public long Worksjopid { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 车间名称
        /// </summary>
        //public string? WorksjopName { get; set; }
        /// <summary>
        /// 设备编码
        /// </summary>
        public string? DeviceCode { get; set; }

        /// <summary>
        /// 创建人名称
        /// </summary>
        public string? CreatedName { get; set; }

        /// <summary>
        /// 修改人名称
        /// </summary>
        public string? ModifyName { get; set; }
        /// <summary>
        /// 所属产线
        /// </summary>

        public string? LineCode { get; set; }

        /// <summary>
        /// 标准工作时长(h)
        /// </summary>
        public int? Duration { get; set; }
    }
}
