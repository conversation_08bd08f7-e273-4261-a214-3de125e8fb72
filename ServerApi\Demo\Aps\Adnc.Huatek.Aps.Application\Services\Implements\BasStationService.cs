﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.BasClassesAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasDevice;
using Adnc.Huatek.Aps.Domain.Aggregates.BasLineAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductStandardCapacityAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStation;
using Adnc.Huatek.Aps.Domain.Aggregates.SysUserAggregate;
using Adnc.Huatek.Aps.Domain.EntityConfig;
using Adnc.Huatek.Aps.Domain.Services;
using Adnc.Infra.Entities;
using Adnc.Infra.Helper;
using Adnc.Infra.IdGenerater.Yitter;
using Adnc.Shared;
using Adnc.Shared.Application.Contracts.ResultModels;
using ProtoBuf.Meta;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static System.Collections.Specialized.BitVector32;

namespace Adnc.Huatek.Aps.Application.Services.Implements
{
    public class BasStationService : AbstractAppService, IBasStationService
    {
        private readonly BasStationManager _stationMgr;
        private readonly IEfBasicRepository<BasStation> _stationRepo;
        private readonly BasNumberManagementService _basNumberManagementService;
        private readonly IEfBasicRepository<BasLine> _lineRepo;
        private readonly IEfBasicRepository<BasDevice> _deviceRepo;
        private readonly IEfBasicRepository<SysUser> _sysUserRepo;
        private readonly IEfBasicRepository<BasStationClasses> _stationClassRepo;
        private readonly IEfBasicRepository<BasProductStandardCapacity> _productStandardRepo;
        private readonly IEfBasicRepository<BasClasses> _basClassRepo;

        public BasStationService(
            IEfBasicRepository<BasStation> stationRepo,
            BasStationManager stationMgr,
             BasNumberManagementService basNumberManagementService,
             IEfBasicRepository<BasLine> lineRepo,
             IEfBasicRepository<BasDevice> deviceRepo,
              IEfBasicRepository<SysUser> sysUserRepo,
              IEfBasicRepository<BasStationClasses> stationClassRepo,
              IEfBasicRepository<BasProductStandardCapacity> productStandardRepo,
        IEfBasicRepository<BasClasses> basClassRepo)
        {
            _stationRepo = stationRepo;
            _stationMgr = stationMgr;
            _basNumberManagementService = basNumberManagementService;
            _lineRepo = lineRepo;
            _deviceRepo = deviceRepo;
            _sysUserRepo = sysUserRepo;
            _stationClassRepo = stationClassRepo;
            _productStandardRepo = productStandardRepo;
            _basClassRepo = basClassRepo;
        }

        /// <summary>
        /// 新增工位
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<AppSrvResult<long>> CreateAsync(BasStationDto input)
        {
            var sw = Mapper.Map<BasStation>(input);
            var autoCode = _basNumberManagementService.GetNumberBySimpleName(CommonConst.STATIONNUMBER).Result;

            var duration = input.Duration;
            var totaldur = input.Classes.Where(x => x.Status == 1).Sum(x => x.Duration);
            var station = await _stationMgr.CreateAsync(autoCode, sw.Name, duration ?? 0, totaldur ?? 0, sw.DeviceCode);
            station.Id = IdGenerater.GetNextId();
            station.Code = autoCode;
            station.Status = input.Status;
            station.LineCode = input.Linecode;
            station.DeviceCode = input.DeviceCode;
            station.Duration = input.Duration;
            station.Remark = input.Remark;
            List<BasStationClasses> sclasses = new List<BasStationClasses>();
            foreach (var c in input.Classes)
            {
                BasStationClasses classes = new BasStationClasses()
                {
                    Id = IdGenerater.GetNextId(),
                    StationId = station.Id,
                    StationCode = station.Code,
                    ClassesId = c.Id ?? 0,
                    Status = c.Status ?? 0,
                    Duration = c.Duration,
                    Qty = c.Qty
                };
                sclasses.Add(classes);
            }
            await _stationRepo.InsertAsync(station);
            await _stationClassRepo.InsertRangeAsync(sclasses);
            return sw.Id;
        }

        /// <summary>
        /// 删除工位
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<AppSrvResult> DeleteAsync(long id)
        {

            var userProfile = await _stationRepo.GetAsync(id);
            if (userProfile != null)
            {
                var menu = Mapper.Map<BasStation>(userProfile);
                menu.IsDeleted = true;
                await _stationRepo.UpdateAsync(menu);

                var items = await _stationClassRepo.Where(x => x.StationId == id, false, false).ToListAsync();
                await _stationClassRepo.RemoveRangeAsync(items);
            }
            return AppSrvResult();
        }

        /// <summary>
        /// 获取所有工位
        /// </summary>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<List<BasStationDto>> GetAllAsync()
        {
            var sysTests = await _stationRepo.Where(x => !x.IsDeleted && x.Status == 1).ToListAsync();
            var sysTestsDto = Mapper.Map<List<BasStationDto>>(sysTests);
            return sysTestsDto;
        }

        /// <summary>
        /// 获取指定工位
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<ResultJson> GetAppointAsync(long id)
        {
            var userProfile = await _stationRepo.Where(x => x.IsDeleted == false && x.Id == id).FirstAsync();
            if (userProfile == null)
                return new ResultJson("查询成功！无数据", null);

            var menu = Mapper.Map<BasStationDto>(userProfile);
            var exist = _productStandardRepo.Where(x => x.StationCode == menu.Code);
            if (!exist.Any())
            {
                menu.isEdit= false;
            }
            else
            {
                menu.isEdit = true;
            }

            return new ResultJson("查询成功", menu, 0);
        }

        /// <summary>
        /// 修改工位
        /// </summary>
        /// <param name="id"></param>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<AppSrvResult> UpdateAsync(BasStationDto input)
        {
            var duration = input.Duration;
            var totaldur = input.Classes.Where(x => x.Status == 1).Sum(x => x.Duration);

            bool isUpdete = await _stationMgr.UpdateAsync(input.Code, input.Name, input.Id ?? 0, duration ?? 0, totaldur ?? 0);
            if (isUpdete)
            {
                input.TrimStringFields();
                var station = Mapper.Map<BasStation>(input);
                var info = await _stationRepo.GetAsync(input.Id ?? 0);
                if (info != null)
                {
                    info.Status = station.Status;
                    info.Code = station.Code;
                    info.Name = station.Name;
                    info.LineCode = input.Linecode;
                    // userProfile.Worksjopid = station.Worksjopid;
                    info.Duration = totaldur;
                    info.Remark = station.Remark;
                    info.ModifyTime = station.ModifyTime;
                    info.ModifyBy = station.ModifyBy;
                    info.DeviceCode = station.DeviceCode;
                    await _stationRepo.UpdateAsync(info);
                
                        var items = await _stationClassRepo.Where(x => x.StationId == info.Id, false, false).ToListAsync();
                        await _stationClassRepo.RemoveRangeAsync(items);
                        List<BasStationClasses> sclasses = new List<BasStationClasses>();
                        foreach (var c in input.Classes)
                        {
                            BasStationClasses classes = new BasStationClasses()
                            {
                                Id = IdGenerater.GetNextId(),
                                StationId = station.Id,
                                StationCode = station.Code,
                                ClassesId = c.Id ?? 0,
                                Status = c.Status ?? 0,
                                Duration = c.Duration,
                                Qty = c.Qty
                            };
                            sclasses.Add(classes);
                        }
                        await _stationClassRepo.InsertRangeAsync(sclasses);
                }
            }

            return AppSrvResult();
        }

        public async Task<AppSrvResult> ChangeStatusAsync(long id, int status)
        {
            var userProfile = await _stationRepo.GetAsync(id);
            if (userProfile != null)
            {
                userProfile.Status = status;
                await _stationRepo.UpdateAsync(userProfile);
            }
            return AppSrvResult();
        }

        public async Task<AppSrvResult> ChangeStatusAsync(UpdateStatusDto input)
        {
            string[] Ids = new string[input.Ids.Length];
            for (int i = 0; i < input.Ids.Length; i++)
            {
                Ids[i] = input.Ids[i].ToString();
            }
            var userProfile = await _stationRepo.Where(x => Ids.Contains(x.Id.ToString()),false,false).ToListAsync();
            if (userProfile != null)
            {
                //type==0为删除，type ==1为启用停用
                if (input.Type == 0)
                {
                    foreach (var item in userProfile)
                    {
                        item.IsDeleted = true;
                    }
                }
                else
                {
                    foreach (var item in userProfile)
                    {
                        item.Status = input.Status;
                    }
                }
                await _stationRepo.UpdateRangeAsync(userProfile);
            }
            return AppSrvResult();
        }


        public async Task<PageModelDto<BasStationDto>> GetPagedAsync(BasStationPagedDto search)
        {
            search.TrimStringFields();
            var whereExpression = ExpressionCreator
                                                .New<BasStation>()
                                                .AndIf(search.Code.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Code!, $"%{search.Code}%"))
                                                .AndIf(search.Name.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Name!, $"%{search.Name}%"))
                                                .AndIf(search.Status != -1, x => x.Status == search.Status)
                                                .And(x => x.IsDeleted == false);

            var devWhereExpression = ExpressionCreator
                                                .New<BasDevice>()
                                                .AndIf(search.DevName.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Name!, $"%{search.DevName}%"))
                                                .And(x => x.Status == 1)
                                                .And(x => x.IsDeleted == false);

            var total = await _stationRepo.CountAsync(whereExpression);
            if (total == 0)
                return new PageModelDto<BasStationDto>(search);
            var entities = _stationRepo.Where(whereExpression)
                                            .OrderByDescending(x => x.Id)
                                            .Skip(search.SkipRows())
                                            .Take(search.PageSize);
            var lines = _lineRepo.Where(x => !x.IsDeleted && x.Status == 1);
            var devices = _deviceRepo.Where(devWhereExpression);
            var cusers = _sysUserRepo.Where(x => !x.IsDeleted);
            var modifyusers = _sysUserRepo.Where(x => !x.IsDeleted);
            var result = (from s in entities
                          join l in lines on s.LineCode equals l.Lcode
                          join d in devices on s.DeviceCode equals d.Code into device
                          from dd in device.DefaultIfEmpty()
                         // where dd != null
                          join cu in cusers on s.CreateBy equals cu.Id
                          join mu in modifyusers on s.ModifyBy equals mu.Id into musers
                          from muser in musers.DefaultIfEmpty()
                          select new BasStationDto
                          {
                              Id = s.Id,
                              CreatName = cu.Name,
                              ModifyName = muser.Name,
                              ModifyTime = s.ModifyTime,
                              LineName = l.Lname,
                              DeviceName = dd.Name,
                              CreateTime = s.CreateTime,
                              Name = s.Name,
                              Code = s.Code,
                              Status = s.Status
                          }).ToList();

            return new PageModelDto<BasStationDto>(search, result, total);
        }

        /// <summary>
        /// 获取所有班次信息
        /// </summary>
        /// <returns></returns>
        public async Task<List<ClassesDto>> GetClassesInfoAsync(long stationId)
        {
            var infos = _stationClassRepo.Where(x => x.StationId == stationId);
            var classes = _basClassRepo.Where(x => x.Status == 1 && !x.IsDeleted);

            var results = await (from r in infos
                                 join c in classes on r.ClassesId equals c.Id
                                 select new ClassesDto
                                 {
                                     Id = r.ClassesId,
                                     Cname = c.Cname,
                                     BeginTime = c.BeginTime,
                                     EndTime = c.EndTime,
                                     Status = r.Status,
                                     Qty = r.Qty
                                 }).ToListAsync();
            results.ForEach(x =>
            {
                x.Duration = InfraHelper.GetDuration(x.EndTime, x.BeginTime);
            });
            return results;
        }
        /// <summary>
        /// 批量禁用启用
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult> MakeEnableAsync(int status, List<long> ids)
        {
            var lines = await _stationRepo.Where(x => !x.IsDeleted && ids.Contains((long)x.Id), false, false).ToListAsync();

            if (lines.Any())
            {
                lines.ForEachAsync(o =>
                {
                    o.Status = status;
                });
                await _stationRepo.UpdateRangeAsync(lines);
            }
            return AppSrvResult();
        }
        public async Task<List<BasStationDto>> GetListAsync(BasStationSearchDto search)
        {
            search.TrimStringFields();
            var result = new List<BasStationDto>();
            var whereExpression = ExpressionCreator
                                                .New<BasStation>()
                                                .AndIf(search.LineCode.IsNotNullOrWhiteSpace(), x => x.LineCode == search.LineCode)
                                                .And(x => x.IsDeleted == false);
            var entities = _stationRepo.Where(whereExpression);
            var devices = await _deviceRepo.Where(x => true).ToListAsync();

            result = Mapper.Map<List<BasStationDto>>(entities);

            foreach (var res in result)
            {
                var dev = devices.Find(d => d.Code == res.DeviceCode);
                res.DeviceName = dev?.Name;
            }

            return result;
        }
    }
}
