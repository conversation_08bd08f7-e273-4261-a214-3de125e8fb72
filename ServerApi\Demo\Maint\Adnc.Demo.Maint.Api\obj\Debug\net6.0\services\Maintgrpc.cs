// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: services/maintgrpc.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Adnc.Demo.Shared.Rpc.Grpc.Services {

  /// <summary>Holder for reflection information generated from services/maintgrpc.proto</summary>
  public static partial class MaintgrpcReflection {

    #region Descriptor
    /// <summary>File descriptor for services/maintgrpc.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static MaintgrpcReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChhzZXJ2aWNlcy9tYWludGdycGMucHJvdG8SCHNlcnZpY2VzGhVtZXNzYWdl",
            "cy9jb21tb24ucHJvdG8aIG1lc3NhZ2VzL21haW50Z3JwY19nZXRkaWN0LnBy",
            "b3RvMkUKCU1haW50R3JwYxI4CgdHZXREaWN0EhUubWVzc2FnZXMuRGljdFJl",
            "cXVlc3QaFi5tZXNzYWdlcy5HcnBjUmVzcG9uc2VCJaoCIkFkbmMuRGVtby5T",
            "aGFyZWQuUnBjLkdycGMuU2VydmljZXNiBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Adnc.Demo.Shared.Rpc.Grpc.Messages.CommonReflection.Descriptor, global::Adnc.Demo.Shared.Rpc.Grpc.Messages.MaintgrpcGetdictReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, null, null));
    }
    #endregion

  }
}

#endregion Designer generated code
