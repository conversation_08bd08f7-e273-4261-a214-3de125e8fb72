﻿using Adnc.Infra.Entities;

namespace Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate
{
    /// <summary>
    /// 客户订单产品
    /// </summary>
    public class BasOrderProduct : EfFullAuditEntity
    {
        /// <summary>
        /// ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// orderId
        /// </summary>
        public long OrderId { get; set; }

        /// <summary>
        /// 产品id
        /// </summary>
        public long ProductId { get; set; }

        /// <summary>
        ///  产品编码
        /// </summary>
        public string ProductCode { get; set; }

        /// <summary>
        /// 生产状态
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 生产日期
        /// </summary>
        public decimal? ProductiveTime { get; set; }

        /// <summary>
        /// 产品数量
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// 待排产产品数量
        /// </summary>
        public decimal PendingNum { get; set; }
    }
}