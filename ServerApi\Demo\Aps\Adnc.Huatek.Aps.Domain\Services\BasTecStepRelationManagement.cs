﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasTechnologyStepRelationAggregate;
using Adnc.Shared.Rpc.Event;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace Adnc.Huatek.Aps.Domain.Services
{
    public class BasTecStepRelationManagement : IDomainService
    {
        private readonly IEfBasicRepository<BasTechnologyStepRelation> _basTecStepRelRepo;

        public BasTecStepRelationManagement(IEfBasicRepository<BasTechnologyStepRelation> basTecStepRelRepo) 
        {
            _basTecStepRelRepo = basTecStepRelRepo;
        }
    }
}
