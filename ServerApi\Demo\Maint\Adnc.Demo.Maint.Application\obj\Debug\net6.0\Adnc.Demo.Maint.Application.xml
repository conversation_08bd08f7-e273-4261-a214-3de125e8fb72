<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Adnc.Demo.Maint.Application</name>
    </assembly>
    <members>
        <member name="T:Adnc.Demo.Maint.Application.Dtos.CfgCreationDto">
            <summary>
            系统配置
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.CfgCreationDto.Description">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.CfgCreationDto.Name">
            <summary>
            参数名
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.CfgCreationDto.Value">
            <summary>
            参数值
            </summary>
        </member>
        <member name="T:Adnc.Demo.Maint.Application.Dtos.CfgDto">
            <summary>
            系统配置
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.CfgDto.Description">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.CfgDto.Name">
            <summary>
            参数名
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.CfgDto.Value">
            <summary>
            参数值
            </summary>
        </member>
        <member name="T:Adnc.Demo.Maint.Application.Dtos.CfgSearchPagedDto">
            <summary>
            系统配置
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.CfgSearchPagedDto.Name">
            <summary>
            参数名
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.CfgSearchPagedDto.Value">
            <summary>
            参数值
            </summary>
        </member>
        <member name="T:Adnc.Demo.Maint.Application.Dtos.CfgUpdationDto">
            <summary>
            系统配置
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.DictDto.CreateTime">
            <summary>
            创建时间/注册时间
            </summary>
        </member>
        <member name="T:Adnc.Demo.Maint.Application.Dtos.DictSearchDto">
            <summary>
            字典检索条件
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.DictSearchDto.Ids">
            <summary>
            字典ids
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.DictSearchDto.Name">
            <summary>
            字典名
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.DictSearchDto.Value">
            <summary>
            字典值
            </summary>
        </member>
        <member name="T:Adnc.Demo.Maint.Application.Dtos.DictSearchDetailDto">
            <summary>
            字典检索条件
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.DictSearchDetailDto.Name">
            <summary>
            字典名
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.DictSearchDetailDto.Value">
            <summary>
            字典值
            </summary>
        </member>
        <member name="T:Adnc.Demo.Maint.Application.Dtos.LoginLogDto">
            <summary>
            登录日志
            </summary>
        </member>
        <member name="T:Adnc.Demo.Maint.Application.Dtos.LogSearchPagedDto">
            <summary>
            日志检索条件
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.LogSearchPagedDto.BeginTime">
            <summary>
            日志范围开始时间
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.LogSearchPagedDto.EndTime">
            <summary>
            日志范围结束时间
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.LogSearchPagedDto.Account">
            <summary>
            账号
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.LogSearchPagedDto.Method">
            <summary>
            方法名
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.LogSearchPagedDto.Device">
            <summary>
            设备
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.LogSearchPagedDto.Level">
            <summary>
            日志级别
            </summary>
        </member>
        <member name="T:Adnc.Demo.Maint.Application.Dtos.NlogLogDto">
            <summary>
            Nlog日志
            </summary>
        </member>
        <member name="T:Adnc.Demo.Maint.Application.Dtos.NoticeDto">
            <summary>
            系统通知
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.NoticeDto.Content">
            <summary>
            内容
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.NoticeDto.Title">
            <summary>
            标题
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.NoticeDto.Type">
            <summary>
            类型
            </summary>
        </member>
        <member name="T:Adnc.Demo.Maint.Application.Dtos.OpsLogCreationDto">
            <summary>
            操作日志
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.OpsLogCreationDto.ClassName">
            <summary>
            控制器类名
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.OpsLogCreationDto.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.OpsLogCreationDto.LogName">
            <summary>
            日志业务名称
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.OpsLogCreationDto.LogType">
            <summary>
            日志类型
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.OpsLogCreationDto.Message">
            <summary>
            详细信息
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.OpsLogCreationDto.Method">
            <summary>
            控制器方法
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.OpsLogCreationDto.Succeed">
            <summary>
            是否操作成功
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.OpsLogCreationDto.UserId">
            <summary>
            操作用户ID
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.OpsLogCreationDto.Account">
            <summary>
            操作用户账号
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.OpsLogCreationDto.UserName">
            <summary>
            操作用户姓名
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.OpsLogCreationDto.RemoteIpAddress">
            <summary>
            操作用户Ip
            </summary>
        </member>
        <member name="T:Adnc.Demo.Maint.Application.Dtos.OpsLogDto">
            <summary>
            操作日志
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.OpsLogDto.ClassName">
            <summary>
            控制器类名
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.OpsLogDto.LogName">
            <summary>
            日志业务名称
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.OpsLogDto.LogType">
            <summary>
            日志类型
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.OpsLogDto.Message">
            <summary>
            详细信息
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.OpsLogDto.Method">
            <summary>
            控制器方法
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.OpsLogDto.Succeed">
            <summary>
            是否操作成功
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.OpsLogDto.UserId">
            <summary>
            操作用户ID
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.OpsLogDto.Account">
            <summary>
            账号
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.OpsLogDto.UserName">
            <summary>
            操作用户名
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.OpsLogDto.RemoteIpAddress">
            <summary>
            Ip
            </summary>
        </member>
        <member name="P:Adnc.Demo.Maint.Application.Dtos.OpsLogDto.CreateTime">
            <summary>
            操作时间
            </summary>
        </member>
        <member name="T:Adnc.Demo.Maint.Application.Services.ICfgAppService">
            <summary>
            配置管理
            </summary>
        </member>
        <member name="M:Adnc.Demo.Maint.Application.Services.ICfgAppService.CreateAsync(Adnc.Demo.Maint.Application.Dtos.CfgCreationDto)">
            <summary>
            新增配置
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Demo.Maint.Application.Services.ICfgAppService.UpdateAsync(System.Int64,Adnc.Demo.Maint.Application.Dtos.CfgUpdationDto)">
            <summary>
            修改配置
            </summary>
            <param name="id"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Demo.Maint.Application.Services.ICfgAppService.DeleteAsync(System.Int64)">
            <summary>
            删除配置
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Demo.Maint.Application.Services.ICfgAppService.GetAsync(System.Int64)">
            <summary>
            获取配置
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Demo.Maint.Application.Services.ICfgAppService.GetPagedAsync(Adnc.Demo.Maint.Application.Dtos.CfgSearchPagedDto)">
            <summary>
            配置列表
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="T:Adnc.Demo.Maint.Application.Services.IDictAppService">
            <summary>
            字典管理
            </summary>
        </member>
        <member name="M:Adnc.Demo.Maint.Application.Services.IDictAppService.CreateAsync(Adnc.Demo.Maint.Application.Dtos.DictCreationDto)">
            <summary>
            新增字典
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Demo.Maint.Application.Services.IDictAppService.UpdateAsync(System.Int64,Adnc.Demo.Maint.Application.Dtos.DictUpdationDto)">
            <summary>
            修改字典
            </summary>
            <param name="id"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Demo.Maint.Application.Services.IDictAppService.DeleteAsync(System.Int64)">
            <summary>
            删除字典
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Demo.Maint.Application.Services.IDictAppService.GetListAsync(Adnc.Demo.Maint.Application.Dtos.DictSearchDto)">
            <summary>
            字典列表
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Demo.Maint.Application.Services.IDictAppService.GetAsync(System.Int64)">
            <summary>
            获取单个字典
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Demo.Maint.Application.Services.IDictAppService.GetDetail(Adnc.Demo.Maint.Application.Dtos.DictSearchDetailDto)">
            <summary>
            获取单个字典详情
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Demo.Maint.Application.Services.IDictAppService.GetByCodeAsync(System.String)">
            <summary>
            根据Name获取枚举以及明细
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Demo.Maint.Application.Services.IDictAppService.GetChildByCodeAsync(System.String)">
            <summary>
            根据Name获取枚举数
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="T:Adnc.Demo.Maint.Application.Services.ILogAppService">
            <summary>
            日志查询
            </summary>
        </member>
        <member name="M:Adnc.Demo.Maint.Application.Services.ILogAppService.GetLoginLogsPagedAsync(Adnc.Demo.Maint.Application.Dtos.LogSearchPagedDto)">
            <summary>
            登录日志
            </summary>
            <param name="searchDto"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Demo.Maint.Application.Services.ILogAppService.GetOpsLogsPagedAsync(Adnc.Demo.Maint.Application.Dtos.LogSearchPagedDto)">
            <summary>
            操作日志
            </summary>
            <param name="searchDto"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Demo.Maint.Application.Services.ILogAppService.GetNlogLogsPagedAsync(Adnc.Demo.Maint.Application.Dtos.LogSearchPagedDto)">
            <summary>
            异常日志
            </summary>
            <param name="searchDto"></param>
            <returns></returns>
        </member>
        <member name="T:Adnc.Demo.Maint.Application.Services.INoticeAppService">
            <summary>
            通知管理
            </summary>
        </member>
        <member name="M:Adnc.Demo.Maint.Application.Services.INoticeAppService.GetListAsync(Adnc.Demo.Maint.Application.Dtos.NoticeSearchDto)">
            <summary>
            获取通知列表
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="T:Adnc.Demo.Maint.Application.Subscribers.LoginLogMqConsumer">
            <summary>
            登录日志消费者
            </summary>
        </member>
        <member name="M:Adnc.Demo.Maint.Application.Subscribers.LoginLogMqConsumer.GetExchageConfig">
            <summary>
            配置Exchange
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Demo.Maint.Application.Subscribers.LoginLogMqConsumer.GetRoutingKeys">
            <summary>
            设置路由Key
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Demo.Maint.Application.Subscribers.LoginLogMqConsumer.GetQueueConfig">
            <summary>
            配置队列
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Demo.Maint.Application.Subscribers.LoginLogMqConsumer.Process(System.String,System.String,System.String)">
            <summary>
            消息处理
            </summary>
            <param name="exchage">交换机</param>
            <param name="routingKey">路由Key</param>
            <param name="message">消息内容</param>
            <returns></returns>
        </member>
        <member name="T:Adnc.Demo.Maint.Application.Subscribers.OpsLogMqConsumer">
            <summary>
            操作日志消费者
            </summary>
        </member>
        <member name="M:Adnc.Demo.Maint.Application.Subscribers.OpsLogMqConsumer.GetExchageConfig">
            <summary>
            配置Exchange
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Demo.Maint.Application.Subscribers.OpsLogMqConsumer.GetRoutingKeys">
            <summary>
            设置路由Key
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Demo.Maint.Application.Subscribers.OpsLogMqConsumer.GetQueueConfig">
            <summary>
            配置队列
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Demo.Maint.Application.Subscribers.OpsLogMqConsumer.Process(System.String,System.String,System.String)">
            <summary>
            消息处理
            </summary>
            <param name="exchage">交换机</param>
            <param name="routingKey">路由Key</param>
            <param name="message">消息内容</param>
            <returns></returns>
        </member>
    </members>
</doc>
