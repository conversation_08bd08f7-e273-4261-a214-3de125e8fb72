﻿using Adnc.Infra.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Aggregates.BasProductStepRelationAggregate
{
    public class BasProductStepRelationMaterial : EfFullAuditEntity
    {
        /// <summary>
        /// ID
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// ID
        /// </summary>
        public long? MainId { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        public string? MaterialCode { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal? Qty { get; set; }

        /// <summary>
        /// 结批量
        /// </summary>
        public decimal? BatchQty { get; set; }

        /// <summary>
        /// 物料类型IN OUT
        /// </summary>
        public string? MaterialType { get; set; }






    }


}
