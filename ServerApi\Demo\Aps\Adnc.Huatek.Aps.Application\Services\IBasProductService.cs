﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductAggregate;
using Adnc.Shared.Application.Contracts.ResultModels;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services
{
    public interface IBasProductService: IAppService
    {
        [OperateLog(LogName = "获取产品列表信息")]
        Task<PageModelDto<BasProductDto>> GetPagedAsync(ProductPagedDto search);
        [OperateLog(LogName = "获取所有产品信息")]
        Task<List<BasProductDto>> GetAllAsync();
        [OperateLog(LogName = "创建产品")]
        [UnitOfWork]
        Task<AppSrvResult<long>> CreateAsync(BasProductBomDto input);
        [OperateLog(LogName = "修改产品")]
        [UnitOfWork]
        Task<AppSrvResult> UpdateAsync(BasProductBomDto input);
        [OperateLog(LogName = "修改产品工序物料")]
        [UnitOfWork]
        Task<AppSrvResult> UpdateStepMaterialAsync(BasProductDto input);
        [OperateLog(LogName = "修改产品产能")]
        [UnitOfWork]
        Task<AppSrvResult> UpdateProductCapacityAsync(BasProductDto input);        
        [OperateLog(LogName = "删除产品")]
        [UnitOfWork]
        Task<AppSrvResult> DeleteAsync(long id);
        [OperateLog(LogName = "根据ID获取产品数据")]
        Task<BasProductDto> GetByIdAsync(long id);
        [OperateLog(LogName = "更新状态")]
        Task<AppSrvResult> ChangeStatusAsync(ChangeStatusDto input);
        [OperateLog(LogName = "获取产能模型")]
        Task<BasProductDto> GetCapacityModuleByIdAsync(long id);

        [OperateLog(LogName = "导入产品")]
        Task<ResultJson> ImportAsync(List<BasProductDto> dtos);

    }
}
