﻿using Adnc.Demo.Shared.Const;
using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Application.Services;
using Microsoft.AspNetCore.Authorization;
using Npoi.Mapper;

namespace Adnc.Huatek.Aps.Api.Controllers
{
    [Route($"{RouteConsts.ApsRoot}/device")]
    [ApiController]
    public class BasDeviceController : AdncControllerBase
    {
        private readonly IBasDeviceService _stationSrv;

        public BasDeviceController(IBasDeviceService stationSrv) => _stationSrv = stationSrv;

        /// <summary>
        /// 获取所有信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getallasync")]
        public async Task<ActionResult<List<BasDeviceDto>>> GetAllAsync() => await _stationSrv.GetAllAsync();



        /// <summary>
        /// 新增设备
        /// </summary>
        /// <param name="input">用户信息</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("createasync")]
        public async Task<ActionResult<long>> CreateAsync([FromBody] BasDeviceDto input)
            => CreatedResult(await _stationSrv.CreateAsync(input));

        /// <summary>
        /// 删除设备
        /// </summary>
        /// <param name="id">设备ID</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpDelete("deleteasync/{id}")]
        public async Task<ActionResult> DeleteAsync([FromRoute] long id)
            => Result(await _stationSrv.DeleteAsync(id));


        /// <summary>
        /// 修改设备
        /// </summary>
        /// <param name="id">id</param>
        /// <param name="input">用户信息</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPut("updateasync")]
        public async Task<ActionResult> UpdateAsync([FromBody] BasDeviceDto input)
            => Result(await _stationSrv.UpdateAsync(input));



        /// <summary>
        /// 变更设备状态
        /// </summary>
        /// <param name="id">设备ID</param>
        /// <param name="status">状态</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPut("batch/changestatus/{id}")]
        public async Task<ActionResult> ChangeStatus([FromRoute] long id, [FromBody] SimpleDto<int> status)
            => Result(await _stationSrv.ChangeStatusAsync(id, status.Value));

        /// <summary>
        /// 批量变更设备状态
        /// </summary>
        /// <param name="input">设备Ids与状态</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPut("changestatus")]
        public async Task<ActionResult> ChangeStatus([FromBody] UpdateStatusDto input)
            => Result(await _stationSrv.ChangeStatusAsync(input));

        /// <summary>
        /// 获取设备列表分页
        /// </summary>
        /// <param name="search">查询条件</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("page")]
        public async Task<ActionResult<PageModelDto<BasDeviceDto>>> GetPagedAsync([FromBody] BasDevicePagedDto search)
            => await _stationSrv.GetPagedAsync(search);

        /// <summary>
        /// 获取单个设备详情信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("current/{id}")]
        public async Task<ActionResult<ResultJson>> GetCurrentUserInfoAsync(long id) => await _stationSrv.GetAppointAsync(id);

        /// <summary>
        /// 设备日历-获取单个设备日历
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("calendar")]
        public async Task<ActionResult<List<BasDeviceCalendarResultDto>>> GetCalendarAsync([FromBody] BasDeviceCalendarDto dto)
            => await _stationSrv.GetCalendarAsync(dto);

        /// <summary>
        /// 设备日历-设置设备保养状态
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPut("calendar/setpreserve")]
        public async Task<ActionResult> UpdateCalendarAsync([FromBody] BasDeviceCalendarDto dto)
            => Result(await _stationSrv.UpdateCalendarAsync(0, dto));

        /// <summary>
        /// 设备日历-删除设备保养状态
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPut("calendar/delpreserve")]
        public async Task<ActionResult> DelCalendarAsync([FromBody] BasDeviceCalendarDto dto)
            => Result(await _stationSrv.UpdateCalendarAsync(1, dto));

        /// <summary>
        /// 更新启用/禁用状态
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("changestatus")]
        public async Task<ActionResult> ChangeStatus([FromBody] ChangeStatusDto input)
            => Result(await _stationSrv.ChangeStatusAsync(input));

        /// <summary>
        /// 获取设备保养列表
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("getdevicepreserve/{id}")]
        public async Task<ActionResult<ResultJson>> GetDevicePreserveAsync(long id)
            => await _stationSrv.GetDevicePreserveAsync(id);



        /// <summary>
        /// 新增物料
        /// </summary>
        /// <param name="input">物料信息</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("importasync")]
        public async Task<ResultJson> ImportAsync(IFormFile formFile)
        {
            //通过上传文件流初始化Mapper
            var mapper = new Mapper(formFile.OpenReadStream());
            //读取sheet1的数据
            var devices = mapper.Take<BasDeviceDto>("Sheet1").Select(i => i.Value).ToList();
            return await _stationSrv.ImportAsync(devices);
        }


        /// <summary>
        /// 获取设备列表分页
        /// </summary>
        /// <param name="search">查询条件</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("getallType")]
        public async Task<PageModelDto<PartDeviceStateDto>> GetTypeDeviceAsync([FromBody] BasDevicePagedDto search)
            => await _stationSrv.GetDeviceStateAsync(search);


        /// <summary>
        /// 获取设备列表分页
        /// </summary>
        /// <param name="search">查询条件</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("getdevicestate")]
        public async Task<List<DeviceStateDto>> GetDeviceStateAsync([FromBody] QueryDevStateDto queryDate)
            => await _stationSrv.QueryDeviceState(queryDate);



        /// <summary>
        /// 获取单个设备状态详情信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("currentdevicestate/{id}")]
        public async Task<ActionResult<ResultJson>> GetCurrentDeviceStateInfoAsync(long id) => await _stationSrv.GetDeviceStateAsync(id);



          /// <summary>
        /// 获取设备列表分页
        /// </summary>
        /// <param name=search>查询条件</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPut("editdevicestate")]
        public async Task<ActionResult> UpdateDeviceStateAsync([FromBody] PartDeviceStateDto input)
            => Result(await _stationSrv.UpdateDeviceStateAsync(input));

        


        /// <summary>
        /// 删除设备
        /// </summary>
        /// <param name="id">设备ID</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpDelete("deleteDeviceState/{id}")]
        public async Task<ActionResult> DeleteDeviceStateAsync([FromRoute] long id)
            => Result(await _stationSrv.DeleteDeviceStateAsync(id));





        /// <summary>
        /// 新增物料
        /// </summary>
        /// <param name="input">物料信息</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("importdevicetstateasync")]
        public async Task<ResultJson> ImportDeviceStateAsync(IFormFile formFile)
        {
            //通过上传文件流初始化Mapper
            var mapper = new Mapper(formFile.OpenReadStream());
            //读取sheet1的数据
            var devices = mapper.Take<PartDeviceStateDto>("Sheet1").Select(i => i.Value).ToList();
            return await _stationSrv.ImportDeviceStateAsync(devices);
        }

    }
}
