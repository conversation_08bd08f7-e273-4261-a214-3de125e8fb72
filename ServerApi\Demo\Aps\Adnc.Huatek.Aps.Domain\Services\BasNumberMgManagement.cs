﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasNumberManagementAggregate;

namespace Adnc.Huatek.Aps.Domain.Services
{
    public class BasNumberMgManagement : IDomainService
    {
        private readonly IEfBasicRepository<BasNumberManagement> _basNumberManagementRepo;

        public BasNumberMgManagement(IEfBasicRepository<BasNumberManagement> basNumberManagementRepo)
        {
            _basNumberManagementRepo = basNumberManagementRepo;
        }

        public BasNumberManagement CreateAsync(string name, string simpleName, long numberFormat, string? startTarget, string? currentNumber, string? description, string? isRound, string? identityNumber, string? isContainerDate, string? startNumber, string? timeFormat)
        {

          return  new BasNumberManagement() 
          {
           IsRound = isRound,
            IsDeleted=false,
             TimeFormat = timeFormat,
              StartNumber = startNumber,
               IsContainerDate = isContainerDate,
                CurrentNumber = currentNumber,
                 Description = description,
                  IdentityNumber = identityNumber,
                   Name = name,
                    NumberFormat = numberFormat,
                     SimpleName = simpleName,
                      StartTarget = startTarget,
                       Id = IdGenerater.GetNextId()
          };
        }
    }
}
