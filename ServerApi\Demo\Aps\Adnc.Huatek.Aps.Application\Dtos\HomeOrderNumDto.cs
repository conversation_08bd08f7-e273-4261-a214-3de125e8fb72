﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Dtos
{
    public class HomeOrderNumDto : IDto
    {
        /// <summary>
        /// 订单状态
        /// </summary>
        public int? status { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public int? num { get; set; }

    }

    public class HomeOrderNumByDateDto : IDto
    {
        /// <summary>
        /// 订单创建时间(月份)
        /// </summary>
        public string? Orderdate { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public int? Num { get; set; }

    }

    public class PlanNumByDateDto : IDto
    {
        /// <summary>
        /// 计划创建时间
        /// </summary>
        public string? PlanDate { get; set; }

        /// <summary>
        /// 生产计划数量
        /// </summary>
        public int? PlanNum { get; set; }

        /// <summary>
        /// 已排产数量
        /// </summary>
        public int? SchedulingNum { get; set; }

    }
    public class HomeDeviceNumDto : IDto
    {
        /// <summary>
        ///设备状态
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal? Num { get; set; }

    }
}
