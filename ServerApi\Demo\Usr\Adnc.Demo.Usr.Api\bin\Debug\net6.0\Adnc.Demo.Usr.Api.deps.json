{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"Adnc.Demo.Usr.Api/*******": {"dependencies": {"Adnc.Demo.Usr.Application": "*******", "Adnc.Demo.Usr.Application.Contracts": "*******", "Adnc.Shared.WebApi": "*******", "Grpc.AspNetCore": "2.47.0", "Microsoft.EntityFrameworkCore.Design": "6.0.6", "Microsoft.EntityFrameworkCore.Tools": "6.0.6"}, "runtime": {"Adnc.Demo.Usr.Api.dll": {}}}, "AspectCore.Extensions.Reflection/1.2.0": {"dependencies": {"System.Reflection.Emit.Lightweight": "4.3.0", "System.Threading.Tasks.Extensions": "4.4.0"}, "runtime": {"lib/netstandard2.0/AspectCore.Extensions.Reflection.dll": {"assemblyVersion": "1.2.0.0", "fileVersion": "1.2.0.0"}}}, "AspNetCore.HealthChecks.MongoDb/6.0.2": {"dependencies": {"Microsoft.Extensions.Diagnostics.HealthChecks": "6.0.2", "MongoDB.Driver": "2.16.1"}, "runtime": {"lib/net6.0/HealthChecks.MongoDb.dll": {"assemblyVersion": "6.0.2.0", "fileVersion": "6.0.2.0"}}}, "AspNetCore.HealthChecks.MySql/6.0.2": {"dependencies": {"Microsoft.Extensions.Diagnostics.HealthChecks": "6.0.2", "MySqlConnector": "2.1.2"}, "runtime": {"lib/net6.0/HealthChecks.MySql.dll": {"assemblyVersion": "6.0.2.0", "fileVersion": "6.0.2.0"}}}, "AspNetCore.HealthChecks.Rabbitmq/6.0.2": {"dependencies": {"Microsoft.Extensions.Diagnostics.HealthChecks": "6.0.2", "RabbitMQ.Client": "6.5.0"}, "runtime": {"lib/net6.0/HealthChecks.Rabbitmq.dll": {"assemblyVersion": "6.0.2.0", "fileVersion": "6.0.2.0"}}}, "AspNetCore.HealthChecks.UI.Client/6.0.4": {"dependencies": {"AspNetCore.HealthChecks.UI.Core": "6.0.4", "Microsoft.Extensions.Diagnostics.HealthChecks": "6.0.2"}, "runtime": {"lib/net6.0/HealthChecks.UI.Client.dll": {"assemblyVersion": "6.0.4.0", "fileVersion": "6.0.4.0"}}}, "AspNetCore.HealthChecks.UI.Core/6.0.4": {"dependencies": {"Microsoft.EntityFrameworkCore": "6.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Diagnostics.HealthChecks": "6.0.2", "System.Text.Json": "6.0.0"}, "runtime": {"lib/net6.0/HealthChecks.UI.Core.dll": {"assemblyVersion": "6.0.4.0", "fileVersion": "6.0.4.0"}}}, "AutoMapper/11.0.0": {"dependencies": {"Microsoft.CSharp": "4.7.0"}, "runtime": {"lib/netstandard2.1/AutoMapper.dll": {"assemblyVersion": "1*******", "fileVersion": "1*******"}}}, "AutoMapper.Extensions.Microsoft.DependencyInjection/11.0.0": {"dependencies": {"AutoMapper": "11.0.0", "Microsoft.Extensions.Options": "6.0.0"}, "runtime": {"lib/netstandard2.1/AutoMapper.Extensions.Microsoft.DependencyInjection.dll": {"assemblyVersion": "1*******", "fileVersion": "1*******"}}}, "Castle.Core/4.4.0": {"dependencies": {"NETStandard.Library": "1.6.1", "System.Collections.Specialized": "4.3.0", "System.ComponentModel": "4.3.0", "System.ComponentModel.TypeConverter": "4.3.0", "System.Diagnostics.TraceSource": "4.3.0", "System.Dynamic.Runtime": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.TypeExtensions": "4.7.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard1.5/Castle.Core.dll": {"assemblyVersion": "*******", "fileVersion": "4.4.0.0"}}}, "Castle.Core.AsyncInterceptor/2.1.0": {"dependencies": {"Castle.Core": "4.4.0"}, "runtime": {"lib/net6.0/Castle.Core.AsyncInterceptor.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "2.1.0.0"}}}, "Consul/********": {"dependencies": {"Newtonsoft.Json": "11.0.2"}, "runtime": {"lib/netstandard2.0/Consul.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Dapper/2.0.123": {"runtime": {"lib/net5.0/Dapper.dll": {"assemblyVersion": "*******", "fileVersion": "2.0.123.33578"}}}, "DnsClient/1.6.1": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0"}, "runtime": {"lib/net5.0/DnsClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "DotNetCore.CAP/6.1.0": {"dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.1", "Microsoft.Extensions.Options": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.0", "System.Text.Json": "6.0.0", "System.Threading.Channels": "7.0.0"}, "runtime": {"lib/netstandard2.1/DotNetCore.CAP.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "DotNetCore.CAP.Dashboard/6.1.0": {"dependencies": {"Consul": "********", "DotNetCore.CAP": "6.1.0"}, "runtime": {"lib/net6.0/DotNetCore.CAP.Dashboard.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "DotNetCore.CAP.MySql/6.1.0": {"dependencies": {"DotNetCore.CAP": "6.1.0", "Microsoft.EntityFrameworkCore": "6.0.6", "Microsoft.EntityFrameworkCore.Relational": "6.0.6", "MySqlConnector": "2.1.2"}, "runtime": {"lib/net6.0/DotNetCore.CAP.MySql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "DotNetCore.CAP.RabbitMQ/6.1.0": {"dependencies": {"DotNetCore.CAP": "6.1.0", "RabbitMQ.Client": "6.5.0"}, "runtime": {"lib/netstandard2.1/DotNetCore.CAP.RabbitMQ.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "EFCore.NamingConventions/6.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "6.0.6", "Microsoft.EntityFrameworkCore.Relational": "6.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0"}, "runtime": {"lib/net6.0/EFCore.NamingConventions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "FluentValidation/11.1.0": {"runtime": {"lib/net6.0/FluentValidation.dll": {"assemblyVersion": "1*******", "fileVersion": "11.1.0.0"}}}, "FluentValidation.AspNetCore/11.1.2": {"dependencies": {"FluentValidation": "11.1.0", "FluentValidation.DependencyInjectionExtensions": "11.1.0"}, "runtime": {"lib/net6.0/FluentValidation.AspNetCore.dll": {"assemblyVersion": "1*******", "fileVersion": "11.1.2.0"}}}, "FluentValidation.DependencyInjectionExtensions/11.1.0": {"dependencies": {"FluentValidation": "11.1.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0"}, "runtime": {"lib/netstandard2.1/FluentValidation.DependencyInjectionExtensions.dll": {"assemblyVersion": "1*******", "fileVersion": "11.1.0.0"}}}, "Google.Protobuf/3.21.2": {"runtime": {"lib/net5.0/Google.Protobuf.dll": {"assemblyVersion": "3.21.2.0", "fileVersion": "3.21.2.0"}}}, "Grpc/2.37.0": {"dependencies": {"Grpc.Core": "2.37.0"}}, "Grpc.AspNetCore/2.47.0": {"dependencies": {"Google.Protobuf": "3.21.2", "Grpc.AspNetCore.Server.ClientFactory": "2.47.0", "Grpc.Tools": "2.47.0"}}, "Grpc.AspNetCore.Server/2.47.0": {"dependencies": {"Grpc.Net.Common": "2.47.0"}, "runtime": {"lib/net6.0/Grpc.AspNetCore.Server.dll": {"assemblyVersion": "*******", "fileVersion": "2.47.0.0"}}}, "Grpc.AspNetCore.Server.ClientFactory/2.47.0": {"dependencies": {"Grpc.AspNetCore.Server": "2.47.0", "Grpc.Net.ClientFactory": "2.47.0"}, "runtime": {"lib/net6.0/Grpc.AspNetCore.Server.ClientFactory.dll": {"assemblyVersion": "*******", "fileVersion": "2.47.0.0"}}}, "Grpc.Core/2.37.0": {"dependencies": {"Grpc.Core.Api": "2.47.0", "System.Memory": "4.5.5"}, "runtime": {"lib/netstandard2.0/Grpc.Core.dll": {"assemblyVersion": "*******", "fileVersion": "2.37.0.0"}}, "runtimeTargets": {"runtimes/linux-arm64/native/libgrpc_csharp_ext.arm64.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libgrpc_csharp_ext.x64.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libgrpc_csharp_ext.x64.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/grpc_csharp_ext.x64.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/grpc_csharp_ext.x86.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Grpc.Core.Api/2.47.0": {"dependencies": {"System.Memory": "4.5.5"}, "runtime": {"lib/netstandard2.1/Grpc.Core.Api.dll": {"assemblyVersion": "*******", "fileVersion": "2.47.0.0"}}}, "Grpc.Net.Client/2.47.0": {"dependencies": {"Grpc.Net.Common": "2.47.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.1"}, "runtime": {"lib/net6.0/Grpc.Net.Client.dll": {"assemblyVersion": "*******", "fileVersion": "2.47.0.0"}}}, "Grpc.Net.ClientFactory/2.47.0": {"dependencies": {"Grpc.Net.Client": "2.47.0", "Microsoft.Extensions.Http": "6.0.0"}, "runtime": {"lib/net6.0/Grpc.Net.ClientFactory.dll": {"assemblyVersion": "*******", "fileVersion": "2.47.0.0"}}}, "Grpc.Net.Common/2.47.0": {"dependencies": {"Grpc.Core.Api": "2.47.0"}, "runtime": {"lib/net6.0/Grpc.Net.Common.dll": {"assemblyVersion": "*******", "fileVersion": "2.47.0.0"}}}, "Grpc.Tools/2.47.0": {}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "2.14.0.0", "fileVersion": "2.14.1.48190"}}}, "LiteX.HealthChecks.Redis/3.0.0": {"dependencies": {"Microsoft.Extensions.Diagnostics.HealthChecks": "6.0.2", "StackExchange.Redis": "2.6.48"}, "runtime": {"lib/net5.0/LiteX HealthChecks Redis.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MicroElements.Swashbuckle.FluentValidation/5.7.0": {"dependencies": {"FluentValidation": "11.1.0", "Swashbuckle.AspNetCore.SwaggerGen": "6.3.1"}, "runtime": {"lib/netstandard2.0/MicroElements.Swashbuckle.FluentValidation.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/6.0.6": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.10.0"}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.622.26805"}}}, "Microsoft.AspNetCore.Hosting/2.2.7": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.2", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Configuration": "6.0.1", "Microsoft.Extensions.Configuration.EnvironmentVariables": "3.1.0", "Microsoft.Extensions.Configuration.FileExtensions": "3.1.0", "Microsoft.Extensions.DependencyInjection": "6.0.0", "Microsoft.Extensions.FileProviders.Physical": "3.1.0", "Microsoft.Extensions.Hosting.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.0", "System.Reflection.Metadata": "1.6.0"}}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "6.0.0"}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}}, "Microsoft.AspNetCore.Http/2.2.2": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Net.Http.Headers": "2.2.0"}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "6.0.0"}}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Net.Http.Headers": "2.2.0", "System.Buffers": "4.5.1"}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"dependencies": {"Microsoft.Net.Http.Headers": "2.2.0", "System.Text.Encodings.Web": "6.0.0"}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.EntityFrameworkCore/6.0.6": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "6.0.6", "Microsoft.EntityFrameworkCore.Analyzers": "6.0.6", "Microsoft.Extensions.Caching.Memory": "6.0.1", "Microsoft.Extensions.DependencyInjection": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "System.Collections.Immutable": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.622.26602"}}}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.6": {"runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.622.26602"}}}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.6": {}, "Microsoft.EntityFrameworkCore.Design/6.0.6": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.EntityFrameworkCore.Relational": "6.0.6"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Design.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.622.26602"}}}, "Microsoft.EntityFrameworkCore.Relational/6.0.6": {"dependencies": {"Microsoft.EntityFrameworkCore": "6.0.6", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.622.26602"}}}, "Microsoft.EntityFrameworkCore.Tools/6.0.6": {"dependencies": {"Microsoft.EntityFrameworkCore.Design": "6.0.6"}}, "Microsoft.Extensions.ApiDescription.Server/3.0.0": {}, "Microsoft.Extensions.Caching.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.Caching.Memory/6.0.1": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.1", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.222.6406"}}}, "Microsoft.Extensions.Configuration/6.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.322.12309"}}}, "Microsoft.Extensions.Configuration.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.Configuration.Binder/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/3.1.0": {"dependencies": {"Microsoft.Extensions.Configuration": "6.0.1"}}, "Microsoft.Extensions.Configuration.FileExtensions/3.1.0": {"dependencies": {"Microsoft.Extensions.Configuration": "6.0.1", "Microsoft.Extensions.FileProviders.Physical": "3.1.0"}}, "Microsoft.Extensions.Configuration.Json/3.1.0": {"dependencies": {"Microsoft.Extensions.Configuration": "6.0.1", "Microsoft.Extensions.Configuration.FileExtensions": "3.1.0"}}, "Microsoft.Extensions.DependencyInjection/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {}, "Microsoft.Extensions.Diagnostics.HealthChecks/6.0.2": {"dependencies": {"Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "6.0.2", "Microsoft.Extensions.Hosting.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.1", "Microsoft.Extensions.Options": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Diagnostics.HealthChecks.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.222.6412"}}}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/6.0.2": {"runtime": {"lib/net6.0/Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.222.6412"}}}, "Microsoft.Extensions.FileProviders.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.FileProviders.Physical/3.1.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Extensions.FileSystemGlobbing": "3.1.0"}}, "Microsoft.Extensions.FileSystemGlobbing/3.1.0": {}, "Microsoft.Extensions.Hosting.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0"}}, "Microsoft.Extensions.Http/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.1", "Microsoft.Extensions.Options": "6.0.0"}}, "Microsoft.Extensions.Http.Polly/6.0.6": {"dependencies": {"Microsoft.Extensions.Http": "6.0.0", "Polly": "7.2.3", "Polly.Extensions.Http": "3.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Http.Polly.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.622.26805"}}}, "Microsoft.Extensions.Logging/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.1", "Microsoft.Extensions.Options": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.0"}}, "Microsoft.Extensions.Logging.Abstractions/6.0.1": {"runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.322.12309"}}}, "Microsoft.Extensions.ObjectPool/2.2.0": {}, "Microsoft.Extensions.Options/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.Options.ConfigurationExtensions/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.Binder": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.Primitives/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "Microsoft.IdentityModel.JsonWebTokens/6.10.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.10.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "6.10.0.0", "fileVersion": "6.10.0.20330"}}}, "Microsoft.IdentityModel.Logging/6.10.0": {"runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "6.10.0.0", "fileVersion": "6.10.0.20330"}}}, "Microsoft.IdentityModel.Protocols/6.10.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "6.10.0", "Microsoft.IdentityModel.Tokens": "6.10.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "6.10.0.0", "fileVersion": "6.10.0.20330"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.10.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "6.10.0", "System.IdentityModel.Tokens.Jwt": "6.10.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "6.10.0.0", "fileVersion": "6.10.0.20330"}}}, "Microsoft.IdentityModel.Tokens/6.10.0": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Microsoft.IdentityModel.Logging": "6.10.0", "System.Security.Cryptography.Cng": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "6.10.0.0", "fileVersion": "6.10.0.20330"}}}, "Microsoft.Net.Http.Headers/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0", "System.Buffers": "4.5.1"}}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.OpenApi/1.2.3": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets/1.16.1": {}, "Microsoft.Win32.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "Microsoft.Win32.SystemEvents/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "MiniProfiler.AspNetCore/4.2.22": {"dependencies": {"MiniProfiler.Shared": "4.2.22", "System.Text.Json": "6.0.0"}, "runtime": {"lib/netcoreapp3.0/MiniProfiler.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "4.2.22.25413"}}}, "MiniProfiler.AspNetCore.Mvc/4.2.22": {"dependencies": {"MiniProfiler.AspNetCore": "4.2.22"}, "runtime": {"lib/netcoreapp3.0/MiniProfiler.AspNetCore.Mvc.dll": {"assemblyVersion": "*******", "fileVersion": "4.2.22.25413"}}}, "MiniProfiler.EntityFrameworkCore/4.2.22": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "6.0.6", "MiniProfiler.Shared": "4.2.22"}, "runtime": {"lib/netstandard2.0/MiniProfiler.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "4.2.22.25413"}}}, "MiniProfiler.Shared/4.2.22": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Newtonsoft.Json": "11.0.2", "System.ComponentModel.Primitives": "4.3.0", "System.Data.Common": "4.3.0", "System.Diagnostics.DiagnosticSource": "6.0.0", "System.Diagnostics.StackTrace": "4.3.0", "System.Dynamic.Runtime": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0", "System.Threading.Tasks.Parallel": "4.3.0"}, "runtime": {"lib/netstandard2.0/MiniProfiler.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "4.2.22.25413"}}}, "MongoDB.Bson/2.16.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/netstandard2.1/MongoDB.Bson.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MongoDB.Driver/2.16.1": {"dependencies": {"MongoDB.Bson": "2.16.1", "MongoDB.Driver.Core": "2.16.1", "MongoDB.Libmongocrypt": "1.5.3"}, "runtime": {"lib/netstandard2.1/MongoDB.Driver.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MongoDB.Driver.Core/2.16.1": {"dependencies": {"DnsClient": "1.6.1", "MongoDB.Bson": "2.16.1", "MongoDB.Libmongocrypt": "1.5.3", "SharpCompress": "0.30.1", "System.Buffers": "4.5.1"}, "runtime": {"lib/netstandard2.1/MongoDB.Driver.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "runtimeTargets": {"runtimes/linux/native/libsnappy64.so": {"rid": "linux", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux/native/libzstd.so": {"rid": "linux", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx/native/libsnappy64.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx/native/libzstd.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win/native/libzstd.dll": {"rid": "win", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win/native/snappy32.dll": {"rid": "win", "assetType": "native", "fileVersion": "1.1.1.7"}, "runtimes/win/native/snappy64.dll": {"rid": "win", "assetType": "native", "fileVersion": "1.1.1.7"}}}, "MongoDB.Libmongocrypt/1.5.3": {"runtime": {"lib/netstandard2.1/MongoDB.Libmongocrypt.dll": {"assemblyVersion": "1.5.3.0", "fileVersion": "1.5.3.0"}}, "runtimeTargets": {"runtimes/linux/native/libmongocrypt.so": {"rid": "linux", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx/native/libmongocrypt.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win/native/mongocrypt.dll": {"rid": "win", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "MySqlConnector/2.1.2": {"runtime": {"lib/net6.0/MySqlConnector.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.2.0"}}}, "NETStandard.Library/1.6.1": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.Win32.Primitives": "4.3.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.Compression.ZipFile": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Net.Http": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Net.Sockets": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Timer": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0"}}, "Newtonsoft.Json/11.0.2": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "1*******", "fileVersion": "11.0.2.21924"}}}, "NLog/5.0.0": {"runtime": {"lib/netstandard2.0/NLog.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.0.1032"}}}, "NLog.Extensions.Logging/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "NLog": "5.0.0"}, "runtime": {"lib/net5.0/NLog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.0.154"}}}, "NLog.Mongo/*********": {"dependencies": {"MongoDB.Driver": "2.16.1", "NLog": "5.0.0"}, "runtime": {"lib/netstandard2.0/NLog.Mongo.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "NLog.Targets.Loki/1.4.4": {"dependencies": {"NLog": "5.0.0"}, "runtime": {"lib/net6.0/NLog.Loki.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "NLog.Web.AspNetCore/5.0.0": {"dependencies": {"NLog.Extensions.Logging": "5.0.0"}, "runtime": {"lib/net5.0/NLog.Web.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*********"}}}, "Pipelines.Sockets.Unofficial/2.2.2": {"dependencies": {"System.IO.Pipelines": "5.0.1"}, "runtime": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.2.34088"}}}, "Polly/7.2.3": {"runtime": {"lib/netstandard2.0/Polly.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Polly.Extensions.Http/3.0.0": {"dependencies": {"Polly": "7.2.3"}, "runtime": {"lib/netstandard2.0/Polly.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Pomelo.EntityFrameworkCore.MySql/6.0.1": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "6.0.6", "Microsoft.Extensions.DependencyInjection": "6.0.0", "MySqlConnector": "2.1.2"}, "runtime": {"lib/net6.0/Pomelo.EntityFrameworkCore.MySql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "prometheus-net/6.0.0": {"dependencies": {"Microsoft.Extensions.Http": "6.0.0"}, "runtime": {"lib/net6.0/Prometheus.NetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net6.0/Prometheus.NetStandard.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "prometheus-net.AspNetCore/6.0.0": {"dependencies": {"prometheus-net": "6.0.0"}, "runtime": {"lib/netcoreapp3.1/Prometheus.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "prometheus-net.DotNetRuntime/4.2.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "6.0.0", "prometheus-net": "6.0.0"}, "runtime": {"lib/net5.0/prometheus-net.DotNetRuntime.dll": {"assemblyVersion": "4.2.4.0", "fileVersion": "4.2.4.0"}}}, "protobuf-net/3.1.17": {"dependencies": {"protobuf-net.Core": "3.1.17"}, "runtime": {"lib/net5.0/protobuf-net.dll": {"assemblyVersion": "*******", "fileVersion": "3.1.17.6991"}}}, "protobuf-net.Core/3.1.17": {"runtime": {"lib/net5.0/protobuf-net.Core.dll": {"assemblyVersion": "*******", "fileVersion": "3.1.17.6991"}}}, "RabbitMQ.Client/6.5.0": {"dependencies": {"System.Memory": "4.5.5", "System.Threading.Channels": "7.0.0"}, "runtime": {"lib/netstandard2.0/RabbitMQ.Client.dll": {"assemblyVersion": "*******", "fileVersion": "6.5.0.0"}}}, "Refit/6.3.2": {"dependencies": {"System.Net.Http.Json": "6.0.0"}, "runtime": {"lib/net6.0/Refit.dll": {"assemblyVersion": "6.3.0.0", "fileVersion": "6.3.2.28196"}}}, "Refit.HttpClientFactory/6.3.2": {"dependencies": {"Microsoft.Extensions.Http": "6.0.0", "Refit": "6.3.2"}, "runtime": {"lib/net6.0/Refit.HttpClientFactory.dll": {"assemblyVersion": "6.3.0.0", "fileVersion": "6.3.2.28196"}}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "Serilog/2.3.0": {"dependencies": {"Microsoft.CSharp": "4.7.0", "System.Collections": "4.3.0", "System.Dynamic.Runtime": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.3/Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "2.3.0.0"}}}, "Serilog.Extensions.Logging/2.0.2": {"dependencies": {"Microsoft.Extensions.Logging": "6.0.0", "Serilog": "2.3.0"}, "runtime": {"lib/netstandard2.0/Serilog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "2.0.2.0"}}}, "Serilog.Sinks.File/3.2.0": {"dependencies": {"Serilog": "2.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Timer": "4.3.0"}, "runtime": {"lib/netstandard1.3/Serilog.Sinks.File.dll": {"assemblyVersion": "*******", "fileVersion": "3.2.0.0"}}}, "Serilog.Sinks.RollingFile/3.3.0": {"dependencies": {"Serilog.Sinks.File": "3.2.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0"}, "runtime": {"lib/netstandard1.3/Serilog.Sinks.RollingFile.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SharpCompress/0.30.1": {"runtime": {"lib/net5.0/SharpCompress.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SkyAPM.Abstractions/1.3.0": {"runtime": {"lib/netstandard2.0/SkyAPM.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Agent.AspNetCore/1.3.0": {"dependencies": {"SkyAPM.Agent.Hosting": "1.3.0", "SkyAPM.Diagnostics.AspNetCore": "1.3.0"}, "runtime": {"lib/netcoreapp3.1/SkyAPM.Agent.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Agent.Hosting/1.3.0": {"dependencies": {"SkyAPM.Abstractions": "1.3.0", "SkyAPM.Core": "1.3.0", "SkyAPM.Diagnostics.EntityFrameworkCore": "1.3.0", "SkyAPM.Diagnostics.EntityFrameworkCore.Npgsql": "1.3.0", "SkyAPM.Diagnostics.EntityFrameworkCore.Pomelo.MySql": "1.3.0", "SkyAPM.Diagnostics.EntityFrameworkCore.Sqlite": "1.3.0", "SkyAPM.Diagnostics.HttpClient": "1.3.0", "SkyAPM.Diagnostics.SqlClient": "1.3.0", "SkyAPM.Transport.Grpc": "1.3.0", "SkyAPM.Utilities.Configuration": "1.3.0", "SkyAPM.Utilities.DependencyInjection": "1.3.0", "SkyAPM.Utilities.Logging": "1.3.0", "SkyApm.Diagnostics.Grpc": "1.0.0", "SkyApm.Diagnostics.Grpc.Net.Client": "1.0.0"}, "runtime": {"lib/netcoreapp3.1/SkyAPM.Agent.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Core/1.3.0": {"dependencies": {"AspectCore.Extensions.Reflection": "1.2.0", "SkyAPM.Abstractions": "1.3.0", "System.Diagnostics.DiagnosticSource": "6.0.0", "System.Memory": "4.5.5", "System.ValueTuple": "4.5.0"}, "runtime": {"lib/netcoreapp3.1/SkyAPM.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Diagnostics.AspNetCore/1.3.0": {"dependencies": {"SkyAPM.Abstractions": "1.3.0", "SkyAPM.Utilities.DependencyInjection": "1.3.0"}, "runtime": {"lib/netcoreapp3.1/SkyAPM.Diagnostics.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Diagnostics.CAP/1.3.0": {"dependencies": {"SkyAPM.Abstractions": "1.3.0", "SkyAPM.Core": "1.3.0", "SkyAPM.Utilities.DependencyInjection": "1.3.0"}, "runtime": {"lib/netstandard2.0/SkyAPM.Diagnostics.CAP.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Diagnostics.EntityFrameworkCore/1.3.0": {"dependencies": {"SkyAPM.Abstractions": "1.3.0", "SkyAPM.Core": "1.3.0", "SkyAPM.Utilities.DependencyInjection": "1.3.0"}, "runtime": {"lib/netstandard2.0/SkyAPM.Diagnostics.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Diagnostics.EntityFrameworkCore.Npgsql/1.3.0": {"dependencies": {"SkyAPM.Abstractions": "1.3.0", "SkyAPM.Core": "1.3.0", "SkyAPM.Diagnostics.EntityFrameworkCore": "1.3.0", "SkyAPM.Utilities.DependencyInjection": "1.3.0"}, "runtime": {"lib/netstandard2.0/SkyAPM.Diagnostics.EntityFrameworkCore.Npgsql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Diagnostics.EntityFrameworkCore.Pomelo.MySql/1.3.0": {"dependencies": {"SkyAPM.Abstractions": "1.3.0", "SkyAPM.Core": "1.3.0", "SkyAPM.Diagnostics.EntityFrameworkCore": "1.3.0", "SkyAPM.Utilities.DependencyInjection": "1.3.0"}, "runtime": {"lib/netstandard2.0/SkyAPM.Diagnostics.EntityFrameworkCore.Pomelo.MySql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Diagnostics.EntityFrameworkCore.Sqlite/1.3.0": {"dependencies": {"SkyAPM.Abstractions": "1.3.0", "SkyAPM.Core": "1.3.0", "SkyAPM.Diagnostics.EntityFrameworkCore": "1.3.0", "SkyAPM.Utilities.DependencyInjection": "1.3.0"}, "runtime": {"lib/netstandard2.0/SkyAPM.Diagnostics.EntityFrameworkCore.Sqlite.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyApm.Diagnostics.Grpc/1.0.0": {"dependencies": {"Grpc": "2.37.0", "SkyAPM.Abstractions": "1.3.0", "SkyAPM.Utilities.DependencyInjection": "1.3.0"}, "runtime": {"lib/netstandard2.0/SkyApm.Diagnostics.Grpc.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyApm.Diagnostics.Grpc.Net.Client/1.0.0": {"dependencies": {"Grpc.Core.Api": "2.47.0", "SkyAPM.Abstractions": "1.3.0", "SkyAPM.Core": "1.3.0", "SkyAPM.Utilities.DependencyInjection": "1.3.0"}, "runtime": {"lib/netstandard2.0/SkyApm.Diagnostics.Grpc.Net.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Diagnostics.HttpClient/1.3.0": {"dependencies": {"SkyAPM.Abstractions": "1.3.0", "SkyAPM.Core": "1.3.0", "SkyAPM.Utilities.DependencyInjection": "1.3.0"}, "runtime": {"lib/netstandard2.0/SkyAPM.Diagnostics.HttpClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Diagnostics.SqlClient/1.3.0": {"dependencies": {"SkyAPM.Abstractions": "1.3.0", "SkyAPM.Core": "1.3.0", "SkyAPM.Utilities.DependencyInjection": "1.3.0"}, "runtime": {"lib/netstandard2.0/SkyAPM.Diagnostics.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Transport.Grpc/1.3.0": {"dependencies": {"SkyAPM.Abstractions": "1.3.0", "SkyAPM.Transport.Grpc.Protocol": "1.3.0"}, "runtime": {"lib/netstandard2.0/SkyAPM.Transport.Grpc.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Transport.Grpc.Protocol/1.3.0": {"dependencies": {"Google.Protobuf": "3.21.2", "Grpc": "2.37.0"}, "runtime": {"lib/netstandard2.0/SkyAPM.Transport.Grpc.Protocol.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Utilities.Configuration/1.3.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.Binder": "6.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "3.1.0", "Microsoft.Extensions.Configuration.Json": "3.1.0", "SkyAPM.Abstractions": "1.3.0"}, "runtime": {"lib/netstandard2.0/SkyAPM.Utilities.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Utilities.DependencyInjection/1.3.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "SkyAPM.Core": "1.3.0"}, "runtime": {"lib/netstandard2.0/SkyAPM.Utilities.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Utilities.Logging/1.3.0": {"dependencies": {"Microsoft.Extensions.Logging": "6.0.0", "Serilog.Extensions.Logging": "2.0.2", "Serilog.Sinks.RollingFile": "3.3.0", "SkyAPM.Abstractions": "1.3.0"}, "runtime": {"lib/netstandard2.0/SkyAPM.Utilities.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "StackExchange.Redis/2.6.48": {"dependencies": {"Pipelines.Sockets.Unofficial": "2.2.2", "System.Diagnostics.PerformanceCounter": "5.0.0"}, "runtime": {"lib/net5.0/StackExchange.Redis.dll": {"assemblyVersion": "*******", "fileVersion": "2.6.48.48654"}}}, "Swashbuckle.AspNetCore/6.3.1": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "3.0.0", "Swashbuckle.AspNetCore.Swagger": "6.3.1", "Swashbuckle.AspNetCore.SwaggerGen": "6.3.1", "Swashbuckle.AspNetCore.SwaggerUI": "6.3.1"}}, "Swashbuckle.AspNetCore.Swagger/6.3.1": {"dependencies": {"Microsoft.OpenApi": "1.2.3"}, "runtime": {"lib/net6.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.SwaggerGen/6.3.1": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.3.1"}, "runtime": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.3.1": {"runtime": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.AppContext/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Buffers/4.5.1": {}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.Immutable/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Collections.NonGeneric/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Collections.Specialized/4.3.0": {"dependencies": {"System.Collections.NonGeneric": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.ComponentModel/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.ComponentModel.Primitives/4.3.0": {"dependencies": {"System.ComponentModel": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.ComponentModel.TypeConverter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Collections.NonGeneric": "4.3.0", "System.Collections.Specialized": "4.3.0", "System.ComponentModel": "4.3.0", "System.ComponentModel.Primitives": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.7.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Configuration.ConfigurationManager/6.0.0": {"dependencies": {"System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Permissions": "6.0.0"}, "runtime": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Console/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Data.Common/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Diagnostics.PerformanceCounter/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.Win32.Registry": "5.0.0", "System.Configuration.ConfigurationManager": "6.0.0", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Diagnostics.PerformanceCounter.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Diagnostics.StackTrace/4.3.0": {"dependencies": {"System.IO.FileSystem": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Metadata": "1.6.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.Tools/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.TraceSource/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Drawing.Common/6.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}, "runtime": {"lib/net6.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/System.Drawing.Common.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}, "runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Dynamic.Runtime/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.7.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Calendars/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/6.10.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.10.0", "Microsoft.IdentityModel.Tokens": "6.10.0"}, "runtime": {"lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "6.10.0.0", "fileVersion": "6.10.0.20330"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Buffers": "4.5.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}}, "System.IO.Compression.ZipFile/4.3.0": {"dependencies": {"System.Buffers": "4.5.1", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.IO.Pipelines/5.0.1": {}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.7.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Linq.Queryable/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Memory/4.5.5": {}, "System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "6.0.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Net.Http.Json/6.0.0": {"dependencies": {"System.Text.Json": "6.0.0"}}, "System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Net.Sockets/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit/4.7.0": {}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Metadata/1.6.0": {}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.7.0": {}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Runtime.Serialization.Primitives/4.3.0": {"dependencies": {"System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Security.AccessControl/6.0.0": {}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Cng/4.5.0": {}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.ProtectedData/6.0.0": {"runtime": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Cryptography.X509Certificates/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.5.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Permissions/6.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Windows.Extensions": "6.0.0"}, "runtime": {"lib/net6.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages/7.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Json/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "6.0.0"}}, "System.Text.RegularExpressions/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Channels/7.0.0": {"runtime": {"lib/net6.0/System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.4.0": {}, "System.Threading.Tasks.Parallel/4.3.0": {"dependencies": {"System.Collections.Concurrent": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Timer/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.ValueTuple/4.5.0": {}, "System.Windows.Extensions/6.0.0": {"dependencies": {"System.Drawing.Common": "6.0.0"}, "runtime": {"lib/net6.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.4.0"}}, "System.Xml.XDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XmlDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XPath/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "Yitter.IdGenerator/1.0.14": {"runtime": {"lib/netstandard2.0/Yitter.IdGenerator.dll": {"assemblyVersion": "1.0.14.0", "fileVersion": "1.0.14.0"}}}, "Z.EntityFramework.Extensions.EFCore/6.14.2": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Microsoft.EntityFrameworkCore.Relational": "6.0.6", "Newtonsoft.Json": "11.0.2", "System.Configuration.ConfigurationManager": "6.0.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.TypeExtensions": "4.7.0"}, "runtime": {"lib/net6.0/Z.EntityFramework.Extensions.EFCore.dll": {"assemblyVersion": "6.14.2.0", "fileVersion": "6.14.2.0"}}}, "Z.EntityFramework.Plus.EFCore/6.14.2": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Microsoft.EntityFrameworkCore.Relational": "6.0.6", "System.Reflection.Emit": "4.7.0", "System.Reflection.TypeExtensions": "4.7.0", "Z.EntityFramework.Extensions.EFCore": "6.14.2", "Z.Expressions.Eval": "4.0.85"}, "runtime": {"lib/net6.0/Z.EntityFramework.Plus.EFCore.dll": {"assemblyVersion": "6.14.2.0", "fileVersion": "6.14.2.0"}}}, "Z.Expressions.Eval/4.0.85": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Microsoft.Extensions.Caching.Memory": "6.0.1", "Newtonsoft.Json": "11.0.2", "System.Collections.Concurrent": "4.3.0", "System.Collections.NonGeneric": "4.3.0", "System.Configuration.ConfigurationManager": "6.0.0", "System.Data.Common": "4.3.0", "System.Linq.Queryable": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.TypeExtensions": "4.7.0", "System.Xml.XPath": "4.3.0"}, "runtime": {"lib/net6.0/Z.Expressions.Eval.dll": {"assemblyVersion": "4.0.85.0", "fileVersion": "4.0.85.0"}}}, "Adnc.Demo.Shared.Const/*******": {"runtime": {"Adnc.Demo.Shared.Const.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Demo.Shared.Rpc.Grpc/*******": {"dependencies": {"Adnc.Infra.Core": "*******", "Adnc.Infra.Helper": "*******", "Adnc.Shared": "*******", "Adnc.Shared.Rpc": "*******", "Google.Protobuf": "3.21.2", "Grpc.Net.ClientFactory": "2.47.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Caching.Memory": "6.0.1", "Microsoft.Extensions.Configuration": "6.0.1", "Microsoft.Extensions.Options.ConfigurationExtensions": "6.0.0"}, "runtime": {"Adnc.Demo.Shared.Rpc.Grpc.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Demo.Shared.Rpc.Http/*******": {"dependencies": {"Adnc.Infra.Core": "*******", "Adnc.Infra.Helper": "*******", "Adnc.Shared": "*******", "Adnc.Shared.Rpc": "*******", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Caching.Memory": "6.0.1", "Microsoft.Extensions.Configuration": "6.0.1", "Microsoft.Extensions.Options.ConfigurationExtensions": "6.0.0", "Refit.HttpClientFactory": "6.3.2"}, "runtime": {"Adnc.Demo.Shared.Rpc.Http.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Demo.Usr.Application/*******": {"dependencies": {"Adnc.Demo.Shared.Rpc.Grpc": "*******", "Adnc.Demo.Shared.Rpc.Http": "*******", "Adnc.Demo.Usr.Application.Contracts": "*******", "Adnc.Demo.Usr.Repository": "*******", "Adnc.Shared.Application": "*******"}, "runtime": {"Adnc.Demo.Usr.Application.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Demo.Usr.Application.Contracts/*******": {"dependencies": {"Adnc.Demo.Shared.Const": "*******", "Adnc.Infra.Helper": "*******", "Adnc.Infra.Redis.Caching": "*******", "Adnc.Shared": "*******", "Adnc.Shared.Application.Contracts": "*******"}, "runtime": {"Adnc.Demo.Usr.Application.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Demo.Usr.Repository/*******": {"dependencies": {"Adnc.Demo.Shared.Const": "*******", "Adnc.Infra.Repository": "*******", "Adnc.Infra.Repository.EfCore.MySql": "*******", "Adnc.Shared.Repository": "*******"}, "runtime": {"Adnc.Demo.Usr.Repository.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Infra.Consul/*******": {"dependencies": {"Adnc.Infra.Core": "*******", "Consul": "********", "Grpc.Net.Client": "2.47.0", "Microsoft.AspNetCore.Hosting": "2.2.7", "Microsoft.Extensions.Caching.Abstractions": "6.0.0", "Microsoft.Extensions.Caching.Memory": "6.0.1", "Microsoft.Extensions.Configuration": "6.0.1", "Microsoft.Extensions.Options.ConfigurationExtensions": "6.0.0"}, "runtime": {"Adnc.Infra.Consul.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Infra.Core/*******": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Hosting.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.1", "Microsoft.Extensions.Options": "6.0.0"}, "runtime": {"Adnc.Infra.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Infra.EventBus/*******": {"dependencies": {"Adnc.Infra.Core": "*******", "DotNetCore.CAP": "6.1.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "6.0.0", "Polly": "7.2.3", "RabbitMQ.Client": "6.5.0"}, "runtime": {"Adnc.Infra.EventBus.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Infra.Helper/*******": {"dependencies": {"Adnc.Infra.Core": "*******", "Microsoft.AspNetCore.Http": "2.2.2", "System.Text.Encoding.CodePages": "7.0.0"}, "runtime": {"Adnc.Infra.Helper.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Infra.IdGenerater/*******": {"dependencies": {"Adnc.Infra.Core": "*******", "Adnc.Infra.Redis": "*******", "Yitter.IdGenerator": "1.0.14"}, "runtime": {"Adnc.Infra.IdGenerater.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Infra.Mapper/*******": {"dependencies": {"Adnc.Infra.Core": "*******", "AutoMapper.Extensions.Microsoft.DependencyInjection": "11.0.0"}, "runtime": {"Adnc.Infra.Mapper.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Infra.Redis/*******": {"dependencies": {"Adnc.Infra.Core": "*******", "Microsoft.Extensions.Options.ConfigurationExtensions": "6.0.0", "StackExchange.Redis": "2.6.48", "protobuf-net": "3.1.17"}, "runtime": {"Adnc.Infra.Redis.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Infra.Redis.Caching/*******": {"dependencies": {"Adnc.Infra.Core": "*******", "Adnc.Infra.Redis": "*******", "Castle.Core.AsyncInterceptor": "2.1.0", "Polly": "7.2.3"}, "runtime": {"Adnc.Infra.Redis.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Infra.Repository/*******": {"dependencies": {"MongoDB.Driver": "2.16.1"}, "runtime": {"Adnc.Infra.Repository.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Infra.Repository.Dapper/*******": {"dependencies": {"Adnc.Infra.Core": "*******", "Adnc.Infra.Repository": "*******", "Dapper": "2.0.123", "MySqlConnector": "2.1.2"}, "runtime": {"Adnc.Infra.Repository.Dapper.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Infra.Repository.EfCore/*******": {"dependencies": {"Adnc.Infra.Core": "*******", "Adnc.Infra.Repository": "*******", "Z.EntityFramework.Plus.EFCore": "6.14.2"}, "runtime": {"Adnc.Infra.Repository.EfCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Infra.Repository.EfCore.MySql/*******": {"dependencies": {"Adnc.Infra.Repository": "*******", "Adnc.Infra.Repository.EfCore": "*******", "DotNetCore.CAP.MySql": "6.1.0", "Pomelo.EntityFrameworkCore.MySql": "6.0.1"}, "runtime": {"Adnc.Infra.Repository.EfCore.MySql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Infra.Repository.Mongo/*******": {"dependencies": {"Adnc.Infra.Core": "*******", "Adnc.Infra.Repository": "*******", "Humanizer.Core": "2.14.1"}, "runtime": {"Adnc.Infra.Repository.Mongo.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Shared/*******": {"runtime": {"Adnc.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Shared.Application/*******": {"dependencies": {"Adnc.Infra.Consul": "*******", "Adnc.Infra.Core": "*******", "Adnc.Infra.EventBus": "*******", "Adnc.Infra.Helper": "*******", "Adnc.Infra.IdGenerater": "*******", "Adnc.Infra.Mapper": "*******", "Adnc.Infra.Redis.Caching": "*******", "Adnc.Infra.Repository.Dapper": "*******", "Adnc.Infra.Repository.EfCore.MySql": "*******", "Adnc.Infra.Repository.Mongo": "*******", "Adnc.Shared": "*******", "Adnc.Shared.Application.Contracts": "*******", "Adnc.Shared.Repository": "*******", "Adnc.Shared.Rpc": "*******", "DotNetCore.CAP.Dashboard": "6.1.0", "DotNetCore.CAP.RabbitMQ": "6.1.0", "EFCore.NamingConventions": "6.0.0", "FluentValidation.DependencyInjectionExtensions": "11.1.0", "Grpc.Net.ClientFactory": "2.47.0", "Microsoft.Extensions.Http.Polly": "6.0.6", "SkyAPM.Diagnostics.CAP": "1.3.0"}, "runtime": {"Adnc.Shared.Application.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Shared.Application.Contracts/*******": {"dependencies": {"Adnc.Infra.Core": "*******", "FluentValidation": "11.1.0"}, "runtime": {"Adnc.Shared.Application.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Shared.Repository/*******": {"dependencies": {"Adnc.Infra.Core": "*******", "Adnc.Infra.Repository": "*******", "Adnc.Shared": "*******", "Microsoft.EntityFrameworkCore": "6.0.6", "Microsoft.EntityFrameworkCore.Relational": "6.0.6"}, "runtime": {"Adnc.Shared.Repository.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Shared.Rpc/*******": {"dependencies": {"Adnc.Infra.Core": "*******", "Adnc.Infra.Helper": "*******", "Adnc.Shared": "*******", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Caching.Memory": "6.0.1", "Microsoft.Extensions.Configuration": "6.0.1", "Microsoft.Extensions.Options.ConfigurationExtensions": "6.0.0", "Refit.HttpClientFactory": "6.3.2"}, "runtime": {"Adnc.Shared.Rpc.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Adnc.Shared.WebApi/*******": {"dependencies": {"Adnc.Infra.Consul": "*******", "Adnc.Infra.EventBus": "*******", "Adnc.Infra.Helper": "*******", "Adnc.Infra.Redis.Caching": "*******", "Adnc.Shared": "*******", "Adnc.Shared.Application.Contracts": "*******", "Adnc.Shared.Rpc": "*******", "AspNetCore.HealthChecks.MongoDb": "6.0.2", "AspNetCore.HealthChecks.MySql": "6.0.2", "AspNetCore.HealthChecks.Rabbitmq": "6.0.2", "AspNetCore.HealthChecks.UI.Client": "6.0.4", "FluentValidation": "11.1.0", "FluentValidation.AspNetCore": "11.1.2", "LiteX.HealthChecks.Redis": "3.0.0", "MicroElements.Swashbuckle.FluentValidation": "5.7.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "6.0.6", "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": "1.16.1", "MiniProfiler.AspNetCore.Mvc": "4.2.22", "MiniProfiler.EntityFrameworkCore": "4.2.22", "NLog.Mongo": "*********", "NLog.Targets.Loki": "1.4.4", "NLog.Web.AspNetCore": "5.0.0", "SkyAPM.Agent.AspNetCore": "1.3.0", "Swashbuckle.AspNetCore": "6.3.1", "Swashbuckle.AspNetCore.SwaggerGen": "6.3.1", "System.ValueTuple": "4.5.0", "prometheus-net.AspNetCore": "6.0.0", "prometheus-net.DotNetRuntime": "4.2.4"}, "runtime": {"Adnc.Shared.WebApi.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"Adnc.Demo.Usr.Api/*******": {"type": "project", "serviceable": false, "sha512": ""}, "AspectCore.Extensions.Reflection/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-+9Ebk4YNMIueiJGhFbrfF8Zr9XY462lRaw6IgYgtfxSopoZSMRlksPnCIxWGoXBijuMeUjRbnULWaTwuZXZLgw==", "path": "aspectcore.extensions.reflection/1.2.0", "hashPath": "aspectcore.extensions.reflection.1.2.0.nupkg.sha512"}, "AspNetCore.HealthChecks.MongoDb/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-0R3NVbsjMhS5fd2hGijzQNKJ0zQBv/qMC7nkpmnbtgribCj7vfNdAhSqv4lwbibffRWPW5A/7VNJMX4aPej0WQ==", "path": "aspnetcore.healthchecks.mongodb/6.0.2", "hashPath": "aspnetcore.healthchecks.mongodb.6.0.2.nupkg.sha512"}, "AspNetCore.HealthChecks.MySql/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-v69xPJQEvrOdR/7/eglhVf7kQV/R7Wgef3mEBXHdf1tqQYG/e6PcuVIi7QwVf1+MBnnNejxKcDvTvOKV6Ravqg==", "path": "aspnetcore.healthchecks.mysql/6.0.2", "hashPath": "aspnetcore.healthchecks.mysql.6.0.2.nupkg.sha512"}, "AspNetCore.HealthChecks.Rabbitmq/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-Ch5/dOHpGfl8uGBdFEsbHU+xVsJtMdraBQ2d+MdKMxGjcn2tAubo7hRR8Vdwr5biLyCOY5qZR1zuVCteKQ5WIQ==", "path": "aspnetcore.healthchecks.rabbitmq/6.0.2", "hashPath": "aspnetcore.healthchecks.rabbitmq.6.0.2.nupkg.sha512"}, "AspNetCore.HealthChecks.UI.Client/6.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-I4ZZ1rcQpkPXf0GsDMfLptPdYUdKLaWB2MjoOc5er9FhX5BN88sxeBd6Q1S33Pnoq0YZjOT1yBoAJKIcZ/PBQA==", "path": "aspnetcore.healthchecks.ui.client/6.0.4", "hashPath": "aspnetcore.healthchecks.ui.client.6.0.4.nupkg.sha512"}, "AspNetCore.HealthChecks.UI.Core/6.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-iVMHvU8S+Vbz7JohEj+9XeRpksJVHqBy/t4jE3NdNBEGABuIWR34b26LaH4HSOBAYJjjus5h0+a5PfBDsoXKEQ==", "path": "aspnetcore.healthchecks.ui.core/6.0.4", "hashPath": "aspnetcore.healthchecks.ui.core.6.0.4.nupkg.sha512"}, "AutoMapper/11.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+596AnKykYCk9RxXCEF4GYuapSebQtFVvIA1oVG1rrRkCLAC7AkWehJ0brCfYUbdDW3v1H/p0W3hob7JoXGjMw==", "path": "automapper/11.0.0", "hashPath": "automapper.11.0.0.nupkg.sha512"}, "AutoMapper.Extensions.Microsoft.DependencyInjection/11.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0asw5WxdCFh2OTi9Gv+oKyH9SzxwYQSnO8TV5Dd0GggovILzJW4UimP26JAcxc3yB5NnC5urooZ1BBs8ElpiBw==", "path": "automapper.extensions.microsoft.dependencyinjection/11.0.0", "hashPath": "automapper.extensions.microsoft.dependencyinjection.11.0.0.nupkg.sha512"}, "Castle.Core/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-b5rRL5zeaau1y/5hIbI+6mGw3cwun16YjkHZnV9RRT5UyUIFsgLmNXJ0YnIN9p8Hw7K7AbG1q1UclQVU3DinAQ==", "path": "castle.core/4.4.0", "hashPath": "castle.core.4.4.0.nupkg.sha512"}, "Castle.Core.AsyncInterceptor/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-1vOovJnbjjoCFbPPNLvLTeiqJwoA+aRXkhXcgCQY0pi1eejGqCegJwl58pIIPH/uKDfUXnPIo7aqSrcXEyEH1Q==", "path": "castle.core.asyncinterceptor/2.1.0", "hashPath": "castle.core.asyncinterceptor.2.1.0.nupkg.sha512"}, "Consul/********": {"type": "package", "serviceable": true, "sha512": "sha512-Rt0uQw8f8wtRDwp1B5NIemC094jbkOv3b3Jzy9HOc0FIAJhMEaSRpcZCzjLGKMvPAfw7TKxy07CiYMNXvbMARA==", "path": "consul/********", "hashPath": "consul.********.nupkg.sha512"}, "Dapper/2.0.123": {"type": "package", "serviceable": true, "sha512": "sha512-RDFF4rBLLmbpi6pwkY7q/M6UXHRJEOerplDGE5jwEkP/JGJnBauAClYavNKJPW1yOTWRPIyfj4is3EaJxQXILQ==", "path": "dapper/2.0.123", "hashPath": "dapper.2.0.123.nupkg.sha512"}, "DnsClient/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-4H/f2uYJOZ+YObZjpY9ABrKZI+JNw3uizp6oMzTXwDw6F+2qIPhpRl/1t68O/6e98+vqNiYGu+lswmwdYUy3gg==", "path": "dnsclient/1.6.1", "hashPath": "dnsclient.1.6.1.nupkg.sha512"}, "DotNetCore.CAP/6.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-KypVROHMh05rLYvAyHhRJpEpAfHJPG4lPs6nAzRznJp4c0SQ6PC2ADgB1B4ssAJfZubgYd1nyt7S5YcsXxp7ng==", "path": "dotnetcore.cap/6.1.0", "hashPath": "dotnetcore.cap.6.1.0.nupkg.sha512"}, "DotNetCore.CAP.Dashboard/6.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-0SG3FK5tHlPxqmtNYvq2hYKPp8CFp9xDOwFDTHRLPNPxTCqQZKe5Hd+EgTfY8kZoBe/nrid8ewetQREgg35Eow==", "path": "dotnetcore.cap.dashboard/6.1.0", "hashPath": "dotnetcore.cap.dashboard.6.1.0.nupkg.sha512"}, "DotNetCore.CAP.MySql/6.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-IfDQSEcMdvqYSgUbzHSw53r5LXjoCr7uljYY4CpR+Nw+yqzhH76Ln6NQ43BGkH1rL6q+zFvEvTKWI669pLTkXQ==", "path": "dotnetcore.cap.mysql/6.1.0", "hashPath": "dotnetcore.cap.mysql.6.1.0.nupkg.sha512"}, "DotNetCore.CAP.RabbitMQ/6.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-claGx0Ggs24VikQ/isGmyzLA9jmoM9V+WPDJBTpBS1oHXmQaDjM9srKHCb+K0pGaUIiBwj8aQOSxQxAxms3Rkg==", "path": "dotnetcore.cap.rabbitmq/6.1.0", "hashPath": "dotnetcore.cap.rabbitmq.6.1.0.nupkg.sha512"}, "EFCore.NamingConventions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ij0Z59hA3hattuY+FFmfGhJwLmSw8uz/+01lMRnD5YOYJM7hN7JJUZ92U3rZL6nTJQL5xOf7Rt39zdeNSloImw==", "path": "efcore.namingconventions/6.0.0", "hashPath": "efcore.namingconventions.6.0.0.nupkg.sha512"}, "FluentValidation/11.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Ld7w+2jOmnhQqFkngEGuEWTRa5j6u4Xzli7jKxRtbA5PtuHHcpin01qxvxYL0LomeJuzRMGAOURu2rKONdjKQA==", "path": "fluentvalidation/11.1.0", "hashPath": "fluentvalidation.11.1.0.nupkg.sha512"}, "FluentValidation.AspNetCore/11.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-Lw/8btATvMYW9QYR75CAw1Wp0AJhr9f7cxs1IyklQo6emvy+JaM27BCUe2MZKDOQTLpP4fmVVppPQHHc7HOEjQ==", "path": "fluentvalidation.aspnetcore/11.1.2", "hashPath": "fluentvalidation.aspnetcore.11.1.2.nupkg.sha512"}, "FluentValidation.DependencyInjectionExtensions/11.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-M9U2hLZc8bx0JthQKsN9SVgQSHJeLMcSV/uCmJ23U+/7HCSeOYD4OXoF+p5U3bLGz6Yp7h0/0XbSL+Qv2S38EQ==", "path": "fluentvalidation.dependencyinjectionextensions/11.1.0", "hashPath": "fluentvalidation.dependencyinjectionextensions.11.1.0.nupkg.sha512"}, "Google.Protobuf/3.21.2": {"type": "package", "serviceable": true, "sha512": "sha512-91vdjMcbesfibUtE2n6bVWelT+7J7hExMb9WAEQXuM748+aoIrrUECkIBCmQG+RiM4KvEccVgutFdzl1l6G+Rw==", "path": "google.protobuf/3.21.2", "hashPath": "google.protobuf.3.21.2.nupkg.sha512"}, "Grpc/2.37.0": {"type": "package", "serviceable": true, "sha512": "sha512-Hl4+qpEfTvKP95diLRZMi3nAaPp91myTuM8Z/lmC9XsbxUroMR4OM0HMKNC9rty2Gz+CHq4mq/E7CBqbcVdnPA==", "path": "grpc/2.37.0", "hashPath": "grpc.2.37.0.nupkg.sha512"}, "Grpc.AspNetCore/2.47.0": {"type": "package", "serviceable": true, "sha512": "sha512-KNEsQgdeimMU996ug2d8+nd+yG00ABoZESeIDr9T8quDZMqJqaqSDFGHc32fFTsmz/zB1xM8Wh5JYTXrXwGyPg==", "path": "grpc.aspnetcore/2.47.0", "hashPath": "grpc.aspnetcore.2.47.0.nupkg.sha512"}, "Grpc.AspNetCore.Server/2.47.0": {"type": "package", "serviceable": true, "sha512": "sha512-SFoNM+2PbmvxH8OnwhNG7PNCI3twowtUio5WqzwuwW3GkMOcdxyZejo8YXlTw9vZTNvg2x01HDkrEGjSGsTx3w==", "path": "grpc.aspnetcore.server/2.47.0", "hashPath": "grpc.aspnetcore.server.2.47.0.nupkg.sha512"}, "Grpc.AspNetCore.Server.ClientFactory/2.47.0": {"type": "package", "serviceable": true, "sha512": "sha512-DkrUpZBm93j+Bdn8+m4UYJVTHIXJVT/WRSqJ/7JCv+rcXQP0kyT/DOb/52jyzI0uyHn3t50j1f5ktcRpMpvz1w==", "path": "grpc.aspnetcore.server.clientfactory/2.47.0", "hashPath": "grpc.aspnetcore.server.clientfactory.2.47.0.nupkg.sha512"}, "Grpc.Core/2.37.0": {"type": "package", "serviceable": true, "sha512": "sha512-bOmeCLyygA3ZWB7XOjPH5qWcfsw20XNWUA1AMQbFvBig3pXQR0Tz7FMSqLFW0oa34E0iiIlgROFoncOVBfVnRw==", "path": "grpc.core/2.37.0", "hashPath": "grpc.core.2.37.0.nupkg.sha512"}, "Grpc.Core.Api/2.47.0": {"type": "package", "serviceable": true, "sha512": "sha512-oZXapxH/2WHAVALghNauo+r/bp6zjgQ6r0v8FizLLQg0/j/FkK2u3WZ7cLOL9Y5H4oLg+wLclO8FSvNTQpNR5Q==", "path": "grpc.core.api/2.47.0", "hashPath": "grpc.core.api.2.47.0.nupkg.sha512"}, "Grpc.Net.Client/2.47.0": {"type": "package", "serviceable": true, "sha512": "sha512-DLbUC3T8dMmhA2iqpaJo0X4g7fAi3FyVbDd0/jBXrlqc/bcyA1wPBzLx6mWOWoGUb5S89xL2svnsM8SfzzNa2Q==", "path": "grpc.net.client/2.47.0", "hashPath": "grpc.net.client.2.47.0.nupkg.sha512"}, "Grpc.Net.ClientFactory/2.47.0": {"type": "package", "serviceable": true, "sha512": "sha512-+LKvNYZ/st8wfgP68E7/n94m7kNmJ1zg8nLQaWP7t68jg6fokMhmTOVsGBVuBuaZckQjqX+nwQA7dXGx/ChbpA==", "path": "grpc.net.clientfactory/2.47.0", "hashPath": "grpc.net.clientfactory.2.47.0.nupkg.sha512"}, "Grpc.Net.Common/2.47.0": {"type": "package", "serviceable": true, "sha512": "sha512-Cv76h7noEN2s9cwIdEspOeDjeqcU6Nm34OmjSbRhD/FDBXFmG7rmSfJTPCEB1LYjZWfmf7uUH+nYcHR1I7oQqw==", "path": "grpc.net.common/2.47.0", "hashPath": "grpc.net.common.2.47.0.nupkg.sha512"}, "Grpc.Tools/2.47.0": {"type": "package", "serviceable": true, "sha512": "sha512-nInNoLfT/zR7+0VNIC4Lu5nF8azjTz3KwHB1ckwsYUxvof4uSxIt/LlCKb/NH7GPfXfdvqDDinguPpP5t55nuA==", "path": "grpc.tools/2.47.0", "hashPath": "grpc.tools.2.47.0.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "LiteX.HealthChecks.Redis/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OGllqVuhoaQ4bqa/WGgmrPClh0ufaOBSrcYZeUf+GAr6MN6862VzAecYZRztHkQhszN2hTD+l9SlLtbm5x9v5w==", "path": "litex.healthchecks.redis/3.0.0", "hashPath": "litex.healthchecks.redis.3.0.0.nupkg.sha512"}, "MicroElements.Swashbuckle.FluentValidation/5.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-cRp+pFKMjxyw1SfqbRuB1ufr6yUWq5RBmIu0Ff4Kf/DbUZpCAmilXNStjrARQXrJqJCFs55Z+eMhtOFADxvpEw==", "path": "microelements.swashbuckle.fluentvalidation/5.7.0", "hashPath": "microelements.swashbuckle.fluentvalidation.5.7.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/6.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-lrfPEeisa0xCmZKKQIKxuks37aGE6jgGqDq1ikZDwNLWfIt2/ajOQpHMSE6a5uLjEVqkChKpXpvA7ZvfIKvdzA==", "path": "microsoft.aspnetcore.authentication.jwtbearer/6.0.6", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.6.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting/2.2.7": {"type": "package", "serviceable": true, "sha512": "sha512-O0ZBE53Fa9bVGXykDzvgMFW0Pe1QyPPFg1pazN8l3RUFSWBsDJ9/iD1LHXgADA8+ZD3R/1zkvraPa9SZdievxQ==", "path": "microsoft.aspnetcore.hosting/2.2.7", "hashPath": "microsoft.aspnetcore.hosting.2.2.7.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ubycklv+ZY7Kutdwuy1W4upWcZ6VFR8WUXU7l7B2+mvbDBBPAcfpi+E+Y5GFe+Q157YfA3C49D2GCjAZc7Mobw==", "path": "microsoft.aspnetcore.hosting.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-1PMijw8RMtuQF60SsD/JlKtVfvh4NORAhF4wjysdABhlhTrYmtgssqyncR0Stq5vqtjplZcj6kbT4LRTglt9IQ==", "path": "microsoft.aspnetcore.hosting.server.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http/2.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-BAibpoItxI5puk7YJbIGj95arZueM8B8M5xT1fXBn3hb3L2G3ucrZcYXv1gXdaroLbntUs8qeV8iuBrpjQsrKw==", "path": "microsoft.aspnetcore.http/2.2.2", "hashPath": "microsoft.aspnetcore.http.2.2.2.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-2DgZ9rWrJtuR7RYiew01nGRzuQBDaGHGmK56Rk54vsLLsCdzuFUPqbDTJCS1qJQWTbmbIQ9wGIOjpxA1t0l7/w==", "path": "microsoft.aspnetcore.http.extensions/2.2.0", "hashPath": "microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "path": "microsoft.aspnetcore.http.features/2.2.0", "hashPath": "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ErxAAKaDzxXASB/b5uLEkLgUWv1QbeVxyJYEHQwMaxXOeFFVkQxiq8RyfVcifLU7NR0QY0p3acqx4ZpYfhHDg==", "path": "microsoft.aspnetcore.webutilities/2.2.0", "hashPath": "microsoft.aspnetcore.webutilities.2.2.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/6.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-icy5aMdp9R984RGmbgZvcjEX9HYcwqF+6HXLFydL7PJBlc9eVVPRdBSFS9mCFwXyFl24x7xUORhZx/cSLvwH7Q==", "path": "microsoft.entityframeworkcore/6.0.6", "hashPath": "microsoft.entityframeworkcore.6.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Z4Ep2LDUSSNjriin0wKH4jF4vsjQ2ICwC9/5ntDVShQqy1C8AmmE5oK25jfthEVSIosDhJoWCescV3xKa9kcpg==", "path": "microsoft.entityframeworkcore.abstractions/6.0.6", "hashPath": "microsoft.entityframeworkcore.abstractions.6.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-EqBhW1bJnDw42CIGf/Dh1TbYXFUh53pghb5KGMTrxcNU6Ntfd8UEHs7LntZrMQrECrkhW7zBvfGvv9SbxOj5VQ==", "path": "microsoft.entityframeworkcore.analyzers/6.0.6", "hashPath": "microsoft.entityframeworkcore.analyzers.6.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/6.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-QaQjHVsGxfbmUJQY2RCxfbsNJPmzZTeP4OciunxtBhfLQB7Gfc7OsGzoBmGkuvKkwzqh8j/RZB5Hnqiy70Jjxw==", "path": "microsoft.entityframeworkcore.design/6.0.6", "hashPath": "microsoft.entityframeworkcore.design.6.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/6.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-sMFh4InretduD2ppxRJ9aArwDgg1EqUMp8iXaMoXn64eGW+8hyvuZGD3VdwJF6qQJWd4B6ns1/zkASR7MHOF2g==", "path": "microsoft.entityframeworkcore.relational/6.0.6", "hashPath": "microsoft.entityframeworkcore.relational.6.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Tools/6.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-JJRHejJF7mvg6tQo5snl0C7vwotnevcSW2As2swh8OttBkTGWV5KkETWdUnq+qNck9C8IDdNPOOsOjdizvIT+g==", "path": "microsoft.entityframeworkcore.tools/6.0.6", "hashPath": "microsoft.entityframeworkcore.tools.6.0.6.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LH4OE/76F6sOCslif7+Xh3fS/wUUrE5ryeXAMcoCnuwOQGT5Smw0p57IgDh/pHgHaGz/e+AmEQb7pRgb++wt0w==", "path": "microsoft.extensions.apidescription.server/3.0.0", "hashPath": "microsoft.extensions.apidescription.server.3.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bcz5sSFJbganH0+YrfvIjJDIcKNW7TL07C4d1eTmXy/wOt52iz4LVogJb6pazs7W0+74j0YpXFErvp++Aq5Bsw==", "path": "microsoft.extensions.caching.abstractions/6.0.0", "hashPath": "microsoft.extensions.caching.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-B4y+Cev05eMcjf1na0v9gza6GUtahXbtY1JCypIgx3B4Ea/KAgsWyXEmW4q6zMbmTMtKzmPVk09rvFJirvMwTg==", "path": "microsoft.extensions.caching.memory/6.0.1", "hashPath": "microsoft.extensions.caching.memory.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-BUyFU9t+HzlSE7ri4B+AQN2BgTgHv/uM82s5ZkgU1BApyzWzIl48nDsG5wR1t0pniNuuyTBzG3qCW8152/NtSw==", "path": "microsoft.extensions.configuration/6.0.1", "hashPath": "microsoft.extensions.configuration.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qWzV9o+ZRWq+pGm+1dF+R7qTgTYoXvbyowRoBxQJGfqTpqDun2eteerjRQhq5PQ/14S+lqto3Ft4gYaRyl4rdQ==", "path": "microsoft.extensions.configuration.abstractions/6.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-b3<PERSON>rKzND8LIC7o08QAVlKfaEIYEvLJbtmVbFZVBRXeu9YkKfSSzLZfR1SUfQPBIy9mKLhEtJgGYImkcMNaKE0A==", "path": "microsoft.extensions.configuration.binder/6.0.0", "hashPath": "microsoft.extensions.configuration.binder.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-WryksPlAFFRMWIGpFwDDbrVSD/kSO7P7fRRzBHh6vEIrgflsM8tpPCcgIvKszH4fz4vcuapih9RMdiiJ2VS7aw==", "path": "microsoft.extensions.configuration.environmentvariables/3.1.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-OjRJIkVxUFiVkr9a39AqVThft9QHoef4But5pDCydJOXJ4D/SkmzuW1tm6J2IXynxj6qfeAz9QTnzQAvOcGvzg==", "path": "microsoft.extensions.configuration.fileextensions/3.1.0", "hashPath": "microsoft.extensions.configuration.fileextensions.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-gBpBE1GoaCf1PKYC7u0Bd4mVZ/eR2bnOvn7u8GBXEy3JGar6sC3UVpVfTB9w+biLPtzcukZynBG9uchSBbLTNQ==", "path": "microsoft.extensions.configuration.json/3.1.0", "hashPath": "microsoft.extensions.configuration.json.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-k6PWQMuoBDGGHOQTtyois2u4AwyVcIwL2LaSLlTZQm2CYcJ1pxbt6jfAnpWmzENA/wfrYRI/X9DTLoUkE4AsLw==", "path": "microsoft.extensions.dependencyinjection/6.0.0", "hashPath": "microsoft.extensions.dependencyinjection.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xlzi2IYREJH3/m6+lUrQlujzX8wDitm4QGnUu6kUXTQAWPuZY8i+ticFJbzfqaetLA6KR/rO6Ew/HuYD+bxifg==", "path": "microsoft.extensions.dependencyinjection.abstractions/6.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.HealthChecks/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-ffgCDbl65v/Flx0v4EaWgn+V1cqKflPZVXjf4BZimOUY/yy+H0jNeoPtVl9BXZtQCP/A+q6D9asXkxTPhP8+GQ==", "path": "microsoft.extensions.diagnostics.healthchecks/6.0.2", "hashPath": "microsoft.extensions.diagnostics.healthchecks.6.0.2.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-d2XN/F1NnceJHxgPqziGPQ9rzSw4uK2n3UqCXZLMqP6o+xzcUyh8LgjsP5YQy6MXT5gXemp00irx42tzWPO2Eg==", "path": "microsoft.extensions.diagnostics.healthchecks.abstractions/6.0.2", "hashPath": "microsoft.extensions.diagnostics.healthchecks.abstractions.6.0.2.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0pd4/fho0gC12rQswaGQxbU34jOS1TPS8lZPpkFCH68ppQjHNHYle9iRuHeev1LhrJ94YPvzcRd8UmIuFk23Qw==", "path": "microsoft.extensions.fileproviders.abstractions/6.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-KsvgrYp2fhNXoD9gqSu8jPK9Sbvaa7SqNtsLqHugJkCwFmgRvdz76z6Jz2tlFlC7wyMTZxwwtRF8WAorRQWTEA==", "path": "microsoft.extensions.fileproviders.physical/3.1.0", "hashPath": "microsoft.extensions.fileproviders.physical.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-tK5HZOmVv0kUYkonMjuSsxR0CBk+Rd/69QU3eOMv9FvODGZ2d0SR+7R+n8XIgBcCCoCHJBSsI4GPRaoN3Le4rA==", "path": "microsoft.extensions.filesystemglobbing/3.1.0", "hashPath": "microsoft.extensions.filesystemglobbing.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GcT5l2CYXL6Sa27KCSh0TixsRfADUgth+ojQSD5EkzisZxmGFh7CwzkcYuGwvmXLjr27uWRNrJ2vuuEjMhU05Q==", "path": "microsoft.extensions.hosting.abstractions/6.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-15+pa2G0bAMHbHewaQIdr/y6ag2H3yh4rd9hTXavtWDzQBkvpe2RMqFg8BxDpcQWssmjmBApGPcw93QRz6YcMg==", "path": "microsoft.extensions.http/6.0.0", "hashPath": "microsoft.extensions.http.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http.Polly/6.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-IRnV13JgkBFT2LZ8FHSXDxWgSV/u/c254giaLEAdZh5dsHD4dmTkx/ABWoKMjlqSRgNWL+yv6CIQAgtmP3MjNg==", "path": "microsoft.extensions.http.polly/6.0.6", "hashPath": "microsoft.extensions.http.polly.6.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eIbyj40QDg1NDz0HBW0S5f3wrLVnKWnDJ/JtZ+yJDFnDj90VoPuoPmFkeaXrtu+0cKm5GRAwoDf+dBWXK0TUdg==", "path": "microsoft.extensions.logging/6.0.0", "hashPath": "microsoft.extensions.logging.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-dzB2Cgg+JmrouhjkcQGzSFjjvpwlq353i8oBQO2GWNjCXSzhbtBRUf28HSauWe7eib3wYOdb3tItdjRwAdwCSg==", "path": "microsoft.extensions.logging.abstractions/6.0.1", "hashPath": "microsoft.extensions.logging.abstractions.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-gA8H7uQOnM5gb+L0uTNjViHYr+hRDqCdfugheGo/MxQnuHzmhhzCBTIPm19qL1z1Xe0NEMabfcOBGv9QghlZ8g==", "path": "microsoft.extensions.objectpool/2.2.0", "hashPath": "microsoft.extensions.objectpool.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Options/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dzXN0+V1AyjOe2xcJ86Qbo233KHuLEY0njf/P2Kw8SfJU+d45HNS2ctJdnEnrWbM9Ye2eFgaC5Mj9otRMU6IsQ==", "path": "microsoft.extensions.options/6.0.0", "hashPath": "microsoft.extensions.options.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXWINbTn0vC0FYc9GaQTISbxhQLAMrvtbuvD9N6JelEaIS/Pr62wUCinrq5bf1WRBGczt1v4wDhxFtVFNcMdUQ==", "path": "microsoft.extensions.options.configurationextensions/6.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9+PnzmQFfEFNR9J2aDTfJGGupShHjOuGw4VUv+JB044biSHrnmCIMD+mJHmb2H7YryrfBEXDurxQ47gJZdCKNQ==", "path": "microsoft.extensions.primitives/6.0.0", "hashPath": "microsoft.extensions.primitives.6.0.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/6.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-0qjS31rN1MQTc46tAYbzmMTSRfdV5ndZxSjYxIGqKSidd4wpNJfNII/pdhU5Fx8olarQoKL9lqqYw4yNOIwT0Q==", "path": "microsoft.identitymodel.jsonwebtokens/6.10.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.6.10.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/6.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-zbcwV6esnNzhZZ/VP87dji6VrUBLB5rxnZBkDMqNYpyG+nrBnBsbm4PUYLCBMUflHCM9EMLDG0rLnqqT+l0ldA==", "path": "microsoft.identitymodel.logging/6.10.0", "hashPath": "microsoft.identitymodel.logging.6.10.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/6.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-DFyXD0xylP+DknCT3hzJ7q/Q5qRNu0hO/gCU90O0ATdR0twZmlcuY9RNYaaDofXKVbzcShYNCFCGle2G/o8mkg==", "path": "microsoft.identitymodel.protocols/6.10.0", "hashPath": "microsoft.identitymodel.protocols.6.10.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-LVvMXAWPbPeEWTylDrxunlHH2wFyE4Mv0L4gZrJHC4HTESbWHquKZb/y/S8jgiQEDycOP0PDQvbG4RR/tr2TVQ==", "path": "microsoft.identitymodel.protocols.openidconnect/6.10.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.6.10.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/6.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-qbf1NslutDB4oLrriYTJpy7oB1pbh2ej2lEHd2IPDQH9C74ysOdhU5wAC7KoXblldbo7YsNR2QYFOqQM/b0Rsg==", "path": "microsoft.identitymodel.tokens/6.10.0", "hashPath": "microsoft.identitymodel.tokens.6.10.0.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-iZNkjYqlo8sIOI0bQfpsSoMTmB/kyvmV2h225ihyZT33aTp48ZpF6qYnXxzSXmHt8DpBAwBTX+1s1UFLbYfZKg==", "path": "microsoft.net.http.headers/2.2.0", "hashPath": "microsoft.net.http.headers.2.2.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.OpenApi/1.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-Nug3rO+7Kl5/SBAadzSMAVgqDlfGjJZ0GenQrLywJ84XGKO0uRqkunz5Wyl0SDwcR71bAATXvSdbdzPrYRYKGw==", "path": "microsoft.openapi/1.2.3", "hashPath": "microsoft.openapi.1.2.3.nupkg.sha512"}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets/1.16.1": {"type": "package", "serviceable": true, "sha512": "sha512-LbhMLd8inAwC5UvmzCfgT869zCmuXNNF1ICaBPROtB5TfGoMEpk9zEiTfa/e3PKOla/uz42WujhspNrv7d35AQ==", "path": "microsoft.visualstudio.azure.containers.tools.targets/1.16.1", "hashPath": "microsoft.visualstudio.azure.containers.tools.targets.1.16.1.nupkg.sha512"}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "path": "microsoft.win32.primitives/4.3.0", "hashPath": "microsoft.win32.primitives.4.3.0.nupkg.sha512"}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "path": "microsoft.win32.systemevents/6.0.0", "hashPath": "microsoft.win32.systemevents.6.0.0.nupkg.sha512"}, "MiniProfiler.AspNetCore/4.2.22": {"type": "package", "serviceable": true, "sha512": "sha512-bBirB5d4Q0Bgx05Zg4yzXSmOHZQV4ZJhmxU3DGya4FZxNBwjaVHchqEKY0MJW5XLZo8axMAQm4yywgCvUlTymA==", "path": "miniprofiler.aspnetcore/4.2.22", "hashPath": "miniprofiler.aspnetcore.4.2.22.nupkg.sha512"}, "MiniProfiler.AspNetCore.Mvc/4.2.22": {"type": "package", "serviceable": true, "sha512": "sha512-nzCEaZnh77U9jw+c/qu4CtwYUpHEf+FH1ZMbYKMzIXr8CNNPlypSR6AJEAwjo3bq9TIJIpBMZIaK3inRLUCg4g==", "path": "miniprofiler.aspnetcore.mvc/4.2.22", "hashPath": "miniprofiler.aspnetcore.mvc.4.2.22.nupkg.sha512"}, "MiniProfiler.EntityFrameworkCore/4.2.22": {"type": "package", "serviceable": true, "sha512": "sha512-DV8jDHmp7DVN3B/FevAaAqLDBnPYRxjS8NDF7ZAJ3/6ZqOGjhyvIG1e44XjUUOeUtmJQoPSwqmeX692Y6m+G/A==", "path": "miniprofiler.entityframeworkcore/4.2.22", "hashPath": "miniprofiler.entityframeworkcore.4.2.22.nupkg.sha512"}, "MiniProfiler.Shared/4.2.22": {"type": "package", "serviceable": true, "sha512": "sha512-OOA99Iu7FjFrdYaADcWL78KK9Kq6M+hfnZac5577aSrx0UYOM2apKlhBPKzoPtGPTRtQNKe4RK00u/FmahcU3g==", "path": "miniprofiler.shared/4.2.22", "hashPath": "miniprofiler.shared.4.2.22.nupkg.sha512"}, "MongoDB.Bson/2.16.1": {"type": "package", "serviceable": true, "sha512": "sha512-d647KTPQ7hWqJmvCg80iiVdm239llaUwMyIeRF/0zKpN+ddRWh9jT/g6R6WQrmi4qd7yodvv/Jn0rpEqh4QN8g==", "path": "mongodb.bson/2.16.1", "hashPath": "mongodb.bson.2.16.1.nupkg.sha512"}, "MongoDB.Driver/2.16.1": {"type": "package", "serviceable": true, "sha512": "sha512-LqDWfRG7gM1tZKcG9Q/vuQGYF5GsBuxUF5KcOgvWrE6P7pSkOSL9xGZ7ABZ6XRVECGEQs+N6GyU1E4ViI4lh+g==", "path": "mongodb.driver/2.16.1", "hashPath": "mongodb.driver.2.16.1.nupkg.sha512"}, "MongoDB.Driver.Core/2.16.1": {"type": "package", "serviceable": true, "sha512": "sha512-oJZnXHnAyTd/haHYdbfKMRmPEZ1fFrndv8xhorHeTkUDeGgsPA89vpGMHcjppUyHMYBpLgHbn6GoMkleHKsTHg==", "path": "mongodb.driver.core/2.16.1", "hashPath": "mongodb.driver.core.2.16.1.nupkg.sha512"}, "MongoDB.Libmongocrypt/1.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-OWWxuyuxbjAmOLPoaoUEYnIW4F7qexS6XYOdu6absxyGAqLBWEY+M4WY2Y0km2UUG1+QOPdebpb/7cg5BIEbdw==", "path": "mongodb.libmongocrypt/1.5.3", "hashPath": "mongodb.libmongocrypt.1.5.3.nupkg.sha512"}, "MySqlConnector/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-JVokQTUNN3WHAu9Vw8ieeq1dXTFokJiig5P0VJ4f439UxRrsPo6SaVWC8Zdm6mkPeQFhZ0/9afdWa02EY/1j/w==", "path": "mysqlconnector/2.1.2", "hashPath": "mysqlconnector.2.1.2.nupkg.sha512"}, "NETStandard.Library/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-WcSp3+vP+yHNgS8EV5J7pZ9IRpeDuARBPN28by8zqff1wJQXm26PVU8L3/fYLBJVU7BtDyqNVWq2KlCVvSSR4A==", "path": "netstandard.library/1.6.1", "hashPath": "netstandard.library.1.6.1.nupkg.sha512"}, "Newtonsoft.Json/11.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-IvJe1pj7JHEsP8B8J8DwlMEx8UInrs/x+9oVY+oCD13jpLu4JbJU2WCIsMRn5C4yW9+DgkaO8uiVE5VHKjpmdQ==", "path": "newtonsoft.json/11.0.2", "hashPath": "newtonsoft.json.11.0.2.nupkg.sha512"}, "NLog/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OG9UQhhSnF31k1OImuZ3ogvJ+ktMI+P3QYhwA4cg3Fg5wznYS7PVQMzFglY2Bf4dHTYR/8CrNVgO7dFTfmvF2w==", "path": "nlog/5.0.0", "hashPath": "nlog.5.0.0.nupkg.sha512"}, "NLog.Extensions.Logging/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-K8hXJf9bGzm66n9jUBK0HEbH6Ei6u01oiYdHbITEWeP8X1bVCki5QJviWkHiGaqa4oMDit8Bdf+wdYdZf2WfRA==", "path": "nlog.extensions.logging/5.0.0", "hashPath": "nlog.extensions.logging.5.0.0.nupkg.sha512"}, "NLog.Mongo/*********": {"type": "package", "serviceable": true, "sha512": "sha512-n2+HA8fr1GT6U4+YlzJJA0S+MkZL4I9wbQZknvsCjDku3kJE7MD3SXPnnGN9h2B1vHuUEDdtUMyYb63b33mK+w==", "path": "nlog.mongo/*********", "hashPath": "nlog.mongo.*********.nupkg.sha512"}, "NLog.Targets.Loki/1.4.4": {"type": "package", "serviceable": true, "sha512": "sha512-dUSzZWfNm4sMM8mFuNyfQhmidds32aLaDfExAznuuAo8k3xmVWY+penq/xD96M69jkLb3jXUBDfsh1rrIImvnA==", "path": "nlog.targets.loki/1.4.4", "hashPath": "nlog.targets.loki.1.4.4.nupkg.sha512"}, "NLog.Web.AspNetCore/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GT0BeWsVG9kLdZolsBPAmKw08wSpcZDkhDfmMDxeN6X8gpA8frV9Ji8L4d5JJA3BLy/Ii7ekOF9OFlsQKu+wFQ==", "path": "nlog.web.aspnetcore/5.0.0", "hashPath": "nlog.web.aspnetcore.5.0.0.nupkg.sha512"}, "Pipelines.Sockets.Unofficial/2.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-Bhk0FWxH1paI+18zr1g5cTL+ebeuDcBCR+rRFO+fKEhretgjs7MF2Mc1P64FGLecWp4zKCUOPzngBNrqVyY7Zg==", "path": "pipelines.sockets.unofficial/2.2.2", "hashPath": "pipelines.sockets.unofficial.2.2.2.nupkg.sha512"}, "Polly/7.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-DeCY0OFbNdNxsjntr1gTXHJ5pKUwYzp04Er2LLeN3g6pWhffsGuKVfMBLe1lw7x76HrPkLxKEFxBlpRxS2nDEQ==", "path": "polly/7.2.3", "hashPath": "polly.7.2.3.nupkg.sha512"}, "Polly.Extensions.Http/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-drrG+hB3pYFY7w1c3BD+lSGYvH2oIclH8GRSehgfyP5kjnFnHKQuuBhuHLv+PWyFuaTDyk/vfRpnxOzd11+J8g==", "path": "polly.extensions.http/3.0.0", "hashPath": "polly.extensions.http.3.0.0.nupkg.sha512"}, "Pomelo.EntityFrameworkCore.MySql/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-sFIo5e9RmQoCTEvH6EeSV8ptmX3dw/6XgyD8R93X/i7A9+XCeG9KTjSNjrszVjVOtCu/eyvYqqcv2uZ/BHhlYA==", "path": "pomelo.entityframeworkcore.mysql/6.0.1", "hashPath": "pomelo.entityframeworkcore.mysql.6.0.1.nupkg.sha512"}, "prometheus-net/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rD6iXx1mKx6uy5E7YkktLxvBQKFTbNfe3qc43s+9ZuYWgEm41QMTlw/a+R+Hs86NkfHG3oHYAJuP7c7HQ+oj9Q==", "path": "prometheus-net/6.0.0", "hashPath": "prometheus-net.6.0.0.nupkg.sha512"}, "prometheus-net.AspNetCore/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NCSrvXuR2oYH2SRjC+Ia6ikepD3m8pbRMZjSU639MKwE+W0pLWFvb5ZBXRGuSj7BW8JbFvgBsxL61E1L5OAY9A==", "path": "prometheus-net.aspnetcore/6.0.0", "hashPath": "prometheus-net.aspnetcore.6.0.0.nupkg.sha512"}, "prometheus-net.DotNetRuntime/4.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-2+vPERYVADafVvD8NPus/LVzp7/lvjhDMRkVZJZjf4gL/M3U91xAwuq4JgDwm5F6ltwZraUDF4m0+vwVwZxGZA==", "path": "prometheus-net.dotnetruntime/4.2.4", "hashPath": "prometheus-net.dotnetruntime.4.2.4.nupkg.sha512"}, "protobuf-net/3.1.17": {"type": "package", "serviceable": true, "sha512": "sha512-lN6JWutKqn1Aq/hI1BXcR7VbgHgcerUTMav2rTenflE28GWC2y1JoF9XPkoQdCkf24olStY00r68PHJhUpWFaw==", "path": "protobuf-net/3.1.17", "hashPath": "protobuf-net.3.1.17.nupkg.sha512"}, "protobuf-net.Core/3.1.17": {"type": "package", "serviceable": true, "sha512": "sha512-6PK/ZswVXHdUfMFEv9bI+jkNipuy9ifKRKRBAZlqFrbGXOwvREdMA2H53GTqTc6728PMA0VGvsbnjPAQ0Cx59A==", "path": "protobuf-net.core/3.1.17", "hashPath": "protobuf-net.core.3.1.17.nupkg.sha512"}, "RabbitMQ.Client/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-9hY5HiWPtCla1/l0WmXmLnqoX7iKE3neBQUWnetIJrRpOvTbO//XQfQDh++xgHCshL40Kv/6bR0HDkmJz46twg==", "path": "rabbitmq.client/6.5.0", "hashPath": "rabbitmq.client.6.5.0.nupkg.sha512"}, "Refit/6.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-HxysoLBknW2FdxNXr56NjnfuxbgNh9z2QsUc9y5QtPkXSAWmShpUWDM1G+3Ls2yTyb4sItOii4gZ+GjxKkR7+A==", "path": "refit/6.3.2", "hashPath": "refit.6.3.2.nupkg.sha512"}, "Refit.HttpClientFactory/6.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-B+lvCus6p45hrBU6cNjnUNogtuTRMVyUv0tqQCgRR1K+oYWE7XGCl49Y76WKaBj4Pffsswpbr2uUjIzFFGxfeQ==", "path": "refit.httpclientfactory/6.3.2", "hashPath": "refit.httpclientfactory.6.3.2.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-HdSSp5MnJSsg08KMfZThpuLPJpPwE5hBXvHwoKWosyHHfe8Mh5WKT0ylEOf6yNzX6Ngjxe4Whkafh5q7Ymac4Q==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+yH1a49wJMy8Zt4yx5RhJrxO/DBDByAiCzNwiETI+1S4mPdCu0OY4djdciC7Vssk0l22wQaDLrXxXkp+3+7bVA==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c3YNH1GQJbfIPJeCnr4avseugSqPrxwIqzthYyZDN6EuOyNOzq+y2KSUfRcXauya1sF4foESTgwM5e1A8arAKw==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "path": "runtime.native.system.io.compression/4.3.0", "hashPath": "runtime.native.system.io.compression.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "path": "runtime.native.system.net.http/4.3.0", "hashPath": "runtime.native.system.net.http.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-NS1U+700m4KFRHR5o4vo9DSlTmlCKu/u7dtE5sUHVIPB+xpXxYQvgBgA6wEIeCz6Yfn0Z52/72WYsToCEPJnrw==", "path": "runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-b3pthNgxxFcD+Pc0WSEoC0+md3MyhRS6aCEeenvNE3Fdw1HyJ18ZhRFVJJzIeR/O/jpxPboB805Ho0T3Ul7w8A==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KeLz4HClKf+nFS7p/6Fi/CqyLXh81FpiGzcmuS8DGi9lUqSnZ6Es23/gv2O+1XVGfrbNmviF7CckBpavkBoIFQ==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X7IdhILzr4ROXd8mI1BUCQMSHSQwelUlBjF1JyTKCjXaOGn2fB4EKBxQbCK2VjO3WaWIdlXZL3W6TiIVnrhX4g==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-nyFNiCk/r+VOiIqreLix8yN+q3Wga9+SE8BCgkf+2BwEKiNx6DyvFjCgkfV743/grxv8jHJ8gUK4XEQw7yzRYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ytoewC6wGorL7KoCAvRfsgoJPJbNq+64k2SqW6JcOAebWsFUvCCYgfzQMrnpvPiEl4OrblUlhF2ji+Q1+SVLrQ==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I8bKw2I8k58Wx7fMKQJn2R8lamboCAiHfHeV/pS65ScKWMMI0+wJkLYlEKvgW1D/XvSl/221clBoR2q9QNNM7A==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VB5cn/7OzUfzdnC8tqAIMQciVLiq2epm2NrAm1E9OjNRyG4lVhfR61SMcLizejzQP8R8Uf/0l5qOIbUEi+RdEg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "Serilog/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JJMEqTUGe/bA4OEMefGd8W6si9oStSa3CF47dIHzkRKJHqFWFOW8D2aZTOW6VIgNLY2hzruQXhvp2tX0NVkgsw==", "path": "serilog/2.3.0", "hashPath": "serilog.2.3.0.nupkg.sha512"}, "Serilog.Extensions.Logging/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-PYAkzUn/VV16Es7U06BfEhNZEltnYWu0WFCM4d2lLY/dvlA7xMwFXBuGRxR0XvEBPoOxPorjhFLy9txwiMO6rg==", "path": "serilog.extensions.logging/2.0.2", "hashPath": "serilog.extensions.logging.2.0.2.nupkg.sha512"}, "Serilog.Sinks.File/3.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-VHbo68pMg5hwSWrzLEdZv5b/rYmIgHIRhd4d5rl8GnC5/a8Fr+RShT5kWyeJOXax1el6mNJ+dmHDOVgnNUQxaw==", "path": "serilog.sinks.file/3.2.0", "hashPath": "serilog.sinks.file.3.2.0.nupkg.sha512"}, "Serilog.Sinks.RollingFile/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-2lT5X1r3GH4P0bRWJfhA7etGl8Q2Ipw9AACvtAHWRUSpYZ42NGVyHoVs2ALBZ/cAkkS+tA4jl80Zie144eLQPg==", "path": "serilog.sinks.rollingfile/3.3.0", "hashPath": "serilog.sinks.rollingfile.3.3.0.nupkg.sha512"}, "SharpCompress/0.30.1": {"type": "package", "serviceable": true, "sha512": "sha512-XqD4TpfyYGa7QTPzaGlMVbcecKnXy4YmYLDWrU+JIj7IuRNl7DH2END+Ll7ekWIY8o3dAMWLFDE1xdhfIWD1nw==", "path": "sharpcompress/0.30.1", "hashPath": "sharpcompress.0.30.1.nupkg.sha512"}, "SkyAPM.Abstractions/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YYipEOeNvCQgG28mV4xqcT7QG08so5RLkRRw7P/VZMC8K3kyqVKSaeDrGWU/noWhIdG0qmFGMWdFCD9gzSALeA==", "path": "skyapm.abstractions/1.3.0", "hashPath": "skyapm.abstractions.1.3.0.nupkg.sha512"}, "SkyAPM.Agent.AspNetCore/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-xVkv7VnGKGaX82AlT7D1jkDb+pGqNIlCPEpLOeFGQMF559bRN2OpBzSgOFt9FOrniuPjuQSGxkgoA83tK9YfsA==", "path": "skyapm.agent.aspnetcore/1.3.0", "hashPath": "skyapm.agent.aspnetcore.1.3.0.nupkg.sha512"}, "SkyAPM.Agent.Hosting/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Rzb7CRl13g5XOHLln6VTdaim42GBelb6ua/lprjxlJRGshTJKLSKDzMdyAlZCQ/1MMAvihD3ad9ZrgjgBBBSJQ==", "path": "skyapm.agent.hosting/1.3.0", "hashPath": "skyapm.agent.hosting.1.3.0.nupkg.sha512"}, "SkyAPM.Core/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JA30FQVAmZyOG5NL07s7kfY/SEqIQ3aWg/VRsNhpblcaCCp6GuaCyEeowqDCekpxuwxmpfYSfdiWy6bzrdSFfg==", "path": "skyapm.core/1.3.0", "hashPath": "skyapm.core.1.3.0.nupkg.sha512"}, "SkyAPM.Diagnostics.AspNetCore/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RkZdEJxaKtouWDpAM6oBxWystLJDUF4A+58JHQZnf6+ExrpsnbemAc8jV7lJanUVmQxss8x3DyDQCMd8jefN9A==", "path": "skyapm.diagnostics.aspnetcore/1.3.0", "hashPath": "skyapm.diagnostics.aspnetcore.1.3.0.nupkg.sha512"}, "SkyAPM.Diagnostics.CAP/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-TbRChDDy6cPaaohQQvzM0lrgcawHJlyMRCyYao6TRWP/wPPw2eqOlsPo2t1l1dig5fGkKtERvPjTUeHOyoDnXg==", "path": "skyapm.diagnostics.cap/1.3.0", "hashPath": "skyapm.diagnostics.cap.1.3.0.nupkg.sha512"}, "SkyAPM.Diagnostics.EntityFrameworkCore/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-jJcpGY/Zn67kPXVzq8xp7bf+/aDkAJaKazwCmR42H2AGlKrcUETP7wq4ahOMi2S1X0kOLlcc/tObhOnCYdlnTw==", "path": "skyapm.diagnostics.entityframeworkcore/1.3.0", "hashPath": "skyapm.diagnostics.entityframeworkcore.1.3.0.nupkg.sha512"}, "SkyAPM.Diagnostics.EntityFrameworkCore.Npgsql/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-dxfvfdKod2FutgogwbNqeIF8rvswZ3r3d042+LP6BwADSb3JWttjzGOskSW+Fu+m4/Ikq2WWWi8oqnSvg3kPSQ==", "path": "skyapm.diagnostics.entityframeworkcore.npgsql/1.3.0", "hashPath": "skyapm.diagnostics.entityframeworkcore.npgsql.1.3.0.nupkg.sha512"}, "SkyAPM.Diagnostics.EntityFrameworkCore.Pomelo.MySql/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vqt2rF+SPuSjquO08OB/450ma2TOHp5/g4cSkQR31CRFg5GviB2fRGg8IsCun02IhMDHLdGtre5YgjyyBMHUEQ==", "path": "skyapm.diagnostics.entityframeworkcore.pomelo.mysql/1.3.0", "hashPath": "skyapm.diagnostics.entityframeworkcore.pomelo.mysql.1.3.0.nupkg.sha512"}, "SkyAPM.Diagnostics.EntityFrameworkCore.Sqlite/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-zDf325OiCEtijxL7/0hZNsIa7BWEU4n4hKpx8/M+nSBG9z/ECkv2ROTpOqexSP0JxfsMv6gtO0uFwwnwS42lSQ==", "path": "skyapm.diagnostics.entityframeworkcore.sqlite/1.3.0", "hashPath": "skyapm.diagnostics.entityframeworkcore.sqlite.1.3.0.nupkg.sha512"}, "SkyApm.Diagnostics.Grpc/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-6ooWfCCIJHN+nEjXXzI7xgH+P6p3v6gJqbWBo39g7f6e7nxmeYOjecKmv5kMas11sJZ6enVstaM03vemOPb37A==", "path": "skyapm.diagnostics.grpc/1.0.0", "hashPath": "skyapm.diagnostics.grpc.1.0.0.nupkg.sha512"}, "SkyApm.Diagnostics.Grpc.Net.Client/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-wos7+HP2FScWsfXymXu2YSve2rWykhH2UT+6ZW4tFoRo3OEu3g2BNxsR5qoXwP+VLMchJfo7eaAY+FvGOMUK1w==", "path": "skyapm.diagnostics.grpc.net.client/1.0.0", "hashPath": "skyapm.diagnostics.grpc.net.client.1.0.0.nupkg.sha512"}, "SkyAPM.Diagnostics.HttpClient/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-j3ZwrCse3gx9Vyx2xa/8aqub+jk+HM7Xwe5IjYH9l6t7h3qiY4jsMBpr4/JFaMxEKPvASxbA3e6cPPMJb7JQPg==", "path": "skyapm.diagnostics.httpclient/1.3.0", "hashPath": "skyapm.diagnostics.httpclient.1.3.0.nupkg.sha512"}, "SkyAPM.Diagnostics.SqlClient/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GoYgBWpwdixBZcL+4LAxmNhRNTpdqhmVGkv5tl8cIm5fvKlX0YpIpW/n8+uabEoBJ2uw391HM1uSgcYvIlQJGQ==", "path": "skyapm.diagnostics.sqlclient/1.3.0", "hashPath": "skyapm.diagnostics.sqlclient.1.3.0.nupkg.sha512"}, "SkyAPM.Transport.Grpc/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-2//10IXoRhQYaShyv8WhE9OxpzcbbOgQKAO3GOD6v4FyUwEPkiyipu10q2HI4jUWmYEMUy2sMFrjwWt0+CguWw==", "path": "skyapm.transport.grpc/1.3.0", "hashPath": "skyapm.transport.grpc.1.3.0.nupkg.sha512"}, "SkyAPM.Transport.Grpc.Protocol/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9Rz3PLq3Z8kvpMTBE4CzaE7JqSGBtfL01pUaDzlbsGsLHz7ox3TO+DQXei+uczw6S1VCHUWjiLX0zsFZ5b5kHA==", "path": "skyapm.transport.grpc.protocol/1.3.0", "hashPath": "skyapm.transport.grpc.protocol.1.3.0.nupkg.sha512"}, "SkyAPM.Utilities.Configuration/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oH4eE748fLULwDRlAGQsGjyhQM9UWsqZMKmxiEM13wzvjQlKbezyNv056yNqyUbStRNZtIwzFld3Afo6wXe1EA==", "path": "skyapm.utilities.configuration/1.3.0", "hashPath": "skyapm.utilities.configuration.1.3.0.nupkg.sha512"}, "SkyAPM.Utilities.DependencyInjection/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-K0jbdw5wapzwKRLk3b6wvcS6u48RRKMvJ1X6Jxwg7EkVSk0ayms4M2UlXkoPv4yv7Zlut1sphhVE32r0l68qeg==", "path": "skyapm.utilities.dependencyinjection/1.3.0", "hashPath": "skyapm.utilities.dependencyinjection.1.3.0.nupkg.sha512"}, "SkyAPM.Utilities.Logging/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-UpEY/8uXSs99vxXHGSNZ2tzbvV+IW4GGfVBZx+Q+2qMFWHgtYpTjxI9eeltcJsaRarLEkez3eIJ7OsPvZmMMtQ==", "path": "skyapm.utilities.logging/1.3.0", "hashPath": "skyapm.utilities.logging.1.3.0.nupkg.sha512"}, "StackExchange.Redis/2.6.48": {"type": "package", "serviceable": true, "sha512": "sha512-T0rLGogyT6Zny+IMrDx1Z8r4nA3B0C7EVo5SHNjzT4ndOn9aGKe5K7KTVx0y41WaWmfSWpaX7HrPl0tfZ4zuUw==", "path": "stackexchange.redis/2.6.48", "hashPath": "stackexchange.redis.2.6.48.nupkg.sha512"}, "Swashbuckle.AspNetCore/6.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-JFk0+HHUPdjYuPhkpGBMLi2JtnEuWkE2pp0yXQp64DmeMe+Fb0hZyVNq/ENJ2vQNso7Zg+C758WmR/xyAl36bA==", "path": "swashbuckle.aspnetcore/6.3.1", "hashPath": "swashbuckle.aspnetcore.6.3.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/6.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-idAFh4xhyJHYHfdLVOOn+BmscBul1OQbWsnL6YPJE8tO/0y6S79hDCvs6OY5VI093/9+1pYY3j31Zet9yaDZjA==", "path": "swashbuckle.aspnetcore.swagger/6.3.1", "hashPath": "swashbuckle.aspnetcore.swagger.6.3.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/6.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-+uoBV4h/6NhCPLoTofSmuOnZ+usu4PW1jP6l4OHwPyu2frbYXGNpJsHs5uUXXn929OiVQkT8wo3Lj/o+P99Ejg==", "path": "swashbuckle.aspnetcore.swaggergen/6.3.1", "hashPath": "swashbuckle.aspnetcore.swaggergen.6.3.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/6.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-JLm9hN67jh7RHsX3H30+tb432Li8xm/qV5lRyMMkyHYMfWitIuKAAdrpo2ILcHOIeH7CLMuOO2hp/iLBmE+Bkw==", "path": "swashbuckle.aspnetcore.swaggerui/6.3.1", "hashPath": "swashbuckle.aspnetcore.swaggerui.6.3.1.nupkg.sha512"}, "System.AppContext/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fKC+rmaLfeIzUhagxY17Q9siv/sPrjjKcfNg1Ic8IlQkZLipo8ljcaZQu4VtI4Jqbzjc2VTjzGLF6WmsRXAEgA==", "path": "system.appcontext/4.3.0", "hashPath": "system.appcontext.4.3.0.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-l4zZJ1WU2hqpQQHXz1rvC3etVZN+2DLmQMO79FhOTZHMn8tDRr+WU287sbomD0BETlmKDn0ygUgVy9k5xkkJdA==", "path": "system.collections.immutable/6.0.0", "hashPath": "system.collections.immutable.6.0.0.nupkg.sha512"}, "System.Collections.NonGeneric/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-prtjIEMhGUnQq6RnPEYLpFt8AtLbp9yq2zxOSrY7KJJZrw25Fi97IzBqY7iqssbM61Ek5b8f3MG/sG1N2sN5KA==", "path": "system.collections.nongeneric/4.3.0", "hashPath": "system.collections.nongeneric.4.3.0.nupkg.sha512"}, "System.Collections.Specialized/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Epx8PoVZR0iuOnJJDzp7pWvdfMMOAvpUo95pC4ScH2mJuXkKA2Y4aR3cG9qt2klHgSons1WFh4kcGW7cSXvrxg==", "path": "system.collections.specialized/4.3.0", "hashPath": "system.collections.specialized.4.3.0.nupkg.sha512"}, "System.ComponentModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyGn1jGRZVfxnh8EdvDCi71v3bMXrsu8aYJOwoV7SNDLVhiEqwP86pPMyRGsDsxhXAm2b3o9OIqeETfN5qfezw==", "path": "system.componentmodel/4.3.0", "hashPath": "system.componentmodel.4.3.0.nupkg.sha512"}, "System.ComponentModel.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-j8GUkCpM8V4d4vhLIIoBLGey2Z5bCkMVNjEZseyAlm4n5arcsJOeI3zkUP+zvZgzsbLTYh4lYeP/ZD/gdIAPrw==", "path": "system.componentmodel.primitives/4.3.0", "hashPath": "system.componentmodel.primitives.4.3.0.nupkg.sha512"}, "System.ComponentModel.TypeConverter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-16pQ6P+EdhcXzPiEK4kbA953Fu0MNG2ovxTZU81/qsCd1zPRsKc3uif5NgvllCY598k6bI0KUyKW8fanlfaDQg==", "path": "system.componentmodel.typeconverter/4.3.0", "hashPath": "system.componentmodel.typeconverter.4.3.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7T+m0kDSlIPTHIkPMIu6m6tV6qsMqJpvQWW2jIc2qi7sn40qxFo0q+7mEQAhMPXZHMKnWrnv47ntGlM/ejvw3g==", "path": "system.configuration.configurationmanager/6.0.0", "hashPath": "system.configuration.configurationmanager.6.0.0.nupkg.sha512"}, "System.Console/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHDrIxiqk1h03m6khKWV2X8p/uvN79rgSqpilL6uzpmSfxfU5ng8VcPtW4qsDsQDHiTv6IPV9TmD5M/vElPNLg==", "path": "system.console/4.3.0", "hashPath": "system.console.4.3.0.nupkg.sha512"}, "System.Data.Common/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lm6E3T5u7BOuEH0u18JpbJHxBfOJPuCyl4Kg1RH10ktYLp5uEEE1xKrHW56/We4SnZpGAuCc9N0MJpSDhTHZGQ==", "path": "system.data.common/4.3.0", "hashPath": "system.data.common.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-frQDfv0rl209cKm1lnwTgFPzNigy2EKk1BS3uAvHvlBVKe5cymGyHO+Sj+NLv5VF/AhHsqPIUUwya5oV4CHMUw==", "path": "system.diagnostics.diagnosticsource/6.0.0", "hashPath": "system.diagnostics.diagnosticsource.6.0.0.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-kcQWWtGVC3MWMNXdMDWfrmIlFZZ2OdoeT6pSNVRtk9+Sa7jwdPiMlNwb0ZQcS7NRlT92pCfmjRtkSWUW3RAKwg==", "path": "system.diagnostics.performancecounter/5.0.0", "hashPath": "system.diagnostics.performancecounter.5.0.0.nupkg.sha512"}, "System.Diagnostics.StackTrace/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiHg0vgtd35/DM9jvtaC1eKRpWZxr0gcQd643ABG7GnvSlf5pOkY2uyd42mMOJoOmKvnpNj0F4tuoS1pacTwYw==", "path": "system.diagnostics.stacktrace/4.3.0", "hashPath": "system.diagnostics.stacktrace.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tools/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-UUvkJfSYJMM6x527dJg2VyWPSRqIVB0Z7dbjHst1zmwTXz5CcXSYJFWRpuigfbO1Lf7yfZiIaEUesfnl/g5EyA==", "path": "system.diagnostics.tools/4.3.0", "hashPath": "system.diagnostics.tools.4.3.0.nupkg.sha512"}, "System.Diagnostics.TraceSource/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VnYp1NxGx8Ww731y2LJ1vpfb/DKVNKEZ8Jsh5SgQTZREL/YpWRArgh9pI8CDLmgHspZmLL697CaLvH85qQpRiw==", "path": "system.diagnostics.tracesource/4.3.0", "hashPath": "system.diagnostics.tracesource.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.Drawing.Common/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "path": "system.drawing.common/6.0.0", "hashPath": "system.drawing.common.6.0.0.nupkg.sha512"}, "System.Dynamic.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-SNVi1E/vfWUAs/WYKhE9+qlS6KqK0YVhnlT0HQtr8pMIA8YX3lwy3uPMownDwdYISBdmAF/2holEIldVp85Wag==", "path": "system.dynamic.runtime/4.3.0", "hashPath": "system.dynamic.runtime.4.3.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "path": "system.globalization.calendars/4.3.0", "hashPath": "system.globalization.calendars.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/6.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-C+Q5ORsFycRkRuvy/Xd0Pv5xVpmWSAvQYZAGs7VQogmkqlLhvfZXTgBIlHqC3cxkstSoLJAYx6xZB7foQ2y5eg==", "path": "system.identitymodel.tokens.jwt/6.10.0", "hashPath": "system.identitymodel.tokens.jwt.6.10.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "path": "system.io.compression/4.3.0", "hashPath": "system.io.compression.4.3.0.nupkg.sha512"}, "System.IO.Compression.ZipFile/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-G4HwjEsgIwy3JFBduZ9quBkAu+eUwjIdJleuNSgmUojbH6O3mlvEIme+GHx/cLlTAPcrnnL7GqvB9pTlWRfhOg==", "path": "system.io.compression.zipfile/4.3.0", "hashPath": "system.io.compression.zipfile.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.IO.Pipelines/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-qEePWsaq9LoEEIqhbGe6D5J8c9IqQOUuTzzV6wn1POlfdLkJliZY3OlB0j0f17uMWlqZYjH7txj+2YbyrIA8Yg==", "path": "system.io.pipelines/5.0.1", "hashPath": "system.io.pipelines.5.0.1.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Linq.Queryable/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-In1Bmmvl/j52yPu3xgakQSI0YIckPUr870w4K5+Lak3JCCa8hl+my65lABOuKfYs4ugmZy25ScFerC4nz8+b6g==", "path": "system.linq.queryable/4.3.0", "hashPath": "system.linq.queryable.4.3.0.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-sYg+FtILtRQuYWSIAuNOELwVuVsxVyJGWQyOnlAzhV4xvhyFnON1bAzYYC+jjRW8JREM45R0R5Dgi8MTC5sEwA==", "path": "system.net.http/4.3.0", "hashPath": "system.net.http.4.3.0.nupkg.sha512"}, "System.Net.Http.Json/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GbIV4y344kGOKjshAKCIDCMUTTW/hyUC42wV0Y5SXEdIbaKBIHBUxZ2MOe4/ZiV2svUAGfQ0c8LGtUExpOI8tg==", "path": "system.net.http.json/6.0.0", "hashPath": "system.net.http.json.6.0.0.nupkg.sha512"}, "System.Net.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "path": "system.net.primitives/4.3.0", "hashPath": "system.net.primitives.4.3.0.nupkg.sha512"}, "System.Net.Sockets/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-m6icV6TqQOAdgt5N/9I5KNpjom/5NFtkmGseEH+AK/hny8XrytLH3+b5M8zL/Ycg3fhIocFpUMyl/wpFnVRvdw==", "path": "system.net.sockets/4.3.0", "hashPath": "system.net.sockets.4.3.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-VR4kk8XLKebQ4MZuKuIni/7oh+QGFmZW3qORd1GvBq/8026OpW501SzT/oypwiQl4TvT8ErnReh/NzY9u+C6wQ==", "path": "system.reflection.emit/4.7.0", "hashPath": "system.reflection.emit.4.7.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Metadata/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-COC1aiAJjCoA5GBF+QKL2uLqEBew4JsCkQmoHKbN3TlOZKa2fKLz5CpiRQKDz0RsAOEGsVKqOD5bomsXq/4STQ==", "path": "system.reflection.metadata/1.6.0", "hashPath": "system.reflection.metadata.1.6.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-VybpaOQQhqE6siHppMktjfGBw1GCwvCqiufqmP8F1nj7fTUNtW35LOEt3UZTEsECfo+ELAl/9o9nJx3U91i7vA==", "path": "system.reflection.typeextensions/4.7.0", "hashPath": "system.reflection.typeextensions.4.7.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "path": "system.runtime.interopservices.runtimeinformation/4.3.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wz+0KOukJGAlXjtKr+5Xpuxf8+c8739RI1C+A2BoQZT+wMCCoMDDdO8/4IRHfaVINqL78GO8dW8G2lW/e45Mcw==", "path": "system.runtime.serialization.primitives/4.3.0", "hashPath": "system.runtime.serialization.primitives.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "path": "system.security.accesscontrol/6.0.0", "hashPath": "system.security.accesscontrol.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-WG3r7EyjUe9CMPFSs6bty5doUqT+q9pbI80hlNzo2SkPkZ4VTuZkGWjpp77JB8+uaL4DFPRdBsAY+DX3dBK92A==", "path": "system.security.cryptography.cng/4.5.0", "hashPath": "system.security.cryptography.cng.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "path": "system.security.cryptography.openssl/4.3.0", "hashPath": "system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rp1gMNEZpvx9vP0JW0oHLxlf8oSiQgtno77Y4PLUBjSiDYoD77Y8uXHr1Ea5XG4/pIKhqAdxZ8v8OTUtqo9PeQ==", "path": "system.security.cryptography.protecteddata/6.0.0", "hashPath": "system.security.cryptography.protecteddata.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "path": "system.security.cryptography.x509certificates/4.3.0", "hashPath": "system.security.cryptography.x509certificates.4.3.0.nupkg.sha512"}, "System.Security.Permissions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "path": "system.security.permissions/6.0.0", "hashPath": "system.security.permissions.6.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LSyCblMpvOe0N3E+8e0skHcrIhgV2huaNcjUUEa8hRtgEAm36aGkRoC8Jxlb6Ra6GSfF29ftduPNywin8XolzQ==", "path": "system.text.encoding.codepages/7.0.0", "hashPath": "system.text.encoding.codepages.7.0.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vg8eB5Tawm1IFqj4TVK1czJX89rhFxJo9ELqc/Eiq0eXy13RK00eubyU6TJE6y+GQXjyV5gSfiewDUZjQgSE0w==", "path": "system.text.encodings.web/6.0.0", "hashPath": "system.text.encodings.web.6.0.0.nupkg.sha512"}, "System.Text.Json/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zaJsHfESQvJ11vbXnNlkrR46IaMULk/gHxYsJphzSF+07kTjPHv+Oc14w6QEOfo3Q4hqLJgStUaYB9DBl0TmWg==", "path": "system.text.json/6.0.0", "hashPath": "system.text.json.6.0.0.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RpT2DA+L660cBt1FssIE9CAGpLFdFPuheB7pLpKpn6ZXNby7jDERe8Ua/Ne2xGiwLVG2JOqziiaVCGDon5sKFA==", "path": "system.text.regularexpressions/4.3.0", "hashPath": "system.text.regularexpressions.4.3.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Channels/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qmeeYNROMsONF6ndEZcIQ+VxR4Q/TX/7uIVLJqtwIWL7dDWeh0l1UIqgo4wYyjG//5lUNhwkLDSFl+pAWO6oiA==", "path": "system.threading.channels/7.0.0", "hashPath": "system.threading.channels.7.0.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-SPKfFGbpQsK5Srz2Kq3URgvC90yoOyBE8H1quDA2+MAJ2HAzFmV3biOgPv2Ck3mPAvdKngo3QHi2BNwUQDRVvA==", "path": "system.threading.tasks.extensions/4.4.0", "hashPath": "system.threading.tasks.extensions.4.4.0.nupkg.sha512"}, "System.Threading.Tasks.Parallel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbjBNZHf/vQCfcdhzx7knsiygoCKgxL8mZOeocXZn5gWhCdzHIq6bYNKWX0LAJCWYP7bds4yBK8p06YkP0oa0g==", "path": "system.threading.tasks.parallel/4.3.0", "hashPath": "system.threading.tasks.parallel.4.3.0.nupkg.sha512"}, "System.Threading.Timer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Z6YfyYTCg7lOZjJzBjONJTFKGN9/NIYKSxhU5GRd+DTwHSZyvWp1xuI5aR+dLg+ayyC5Xv57KiY4oJ0tMO89fQ==", "path": "system.threading.timer/4.3.0", "hashPath": "system.threading.timer.4.3.0.nupkg.sha512"}, "System.ValueTuple/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "path": "system.valuetuple/4.5.0", "hashPath": "system.valuetuple.4.5.0.nupkg.sha512"}, "System.Windows.Extensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "path": "system.windows.extensions/6.0.0", "hashPath": "system.windows.extensions.6.0.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5zJ0XDxAIg8iy+t4aMnQAu0MqVbqyvfoUVl1yDV61xdo3Vth45oA2FoY4pPkxYAH5f8ixpmTqXeEIya95x0aCQ==", "path": "system.xml.xdocument/4.3.0", "hashPath": "system.xml.xdocument.4.3.0.nupkg.sha512"}, "System.Xml.XmlDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lJ8AxvkX7GQxpC6GFCeBj8ThYVyQczx2+f/cWHJU8tjS7YfI6Cv6bon70jVEgs2CiFbmmM8b9j1oZVx0dSI2Ww==", "path": "system.xml.xmldocument/4.3.0", "hashPath": "system.xml.xmldocument.4.3.0.nupkg.sha512"}, "System.Xml.XPath/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-v1JQ5SETnQusqmS3RwStF7vwQ3L02imIzl++sewmt23VGygix04pEH+FCj1yWb+z4GDzKiljr1W7Wfvrx0YwgA==", "path": "system.xml.xpath/4.3.0", "hashPath": "system.xml.xpath.4.3.0.nupkg.sha512"}, "Yitter.IdGenerator/1.0.14": {"type": "package", "serviceable": true, "sha512": "sha512-F4nOJ7Geq41vgNWX9E6/vkxRzFInACGpDp4Kad2mA2WIKhEwgPyE9FpulBAuEmDByrfHHz6mOII3IIeLJAh91g==", "path": "yitter.idgenerator/1.0.14", "hashPath": "yitter.idgenerator.1.0.14.nupkg.sha512"}, "Z.EntityFramework.Extensions.EFCore/6.14.2": {"type": "package", "serviceable": true, "sha512": "sha512-wPx3yk3wkd+v1C4Iq7XLTbyWpfeAlpNWPmkK4axlPtH03MJjrCPcNPj64tV6pfD9u8iPvvSi0c5H5s/F1GPbtw==", "path": "z.entityframework.extensions.efcore/6.14.2", "hashPath": "z.entityframework.extensions.efcore.6.14.2.nupkg.sha512"}, "Z.EntityFramework.Plus.EFCore/6.14.2": {"type": "package", "serviceable": true, "sha512": "sha512-6uv6aJgQP6rrDWp04ZmOfrYVqZ0SLG9HLSdTzchqHeEBI5F77pWx1hXEjsNxQAtxMsQgsZupqjI1ieWY/1MkyA==", "path": "z.entityframework.plus.efcore/6.14.2", "hashPath": "z.entityframework.plus.efcore.6.14.2.nupkg.sha512"}, "Z.Expressions.Eval/4.0.85": {"type": "package", "serviceable": true, "sha512": "sha512-6XbyQRtd+V+eTuu8nHOlZ4ciryIr/mmUlHZIXIdOWfaXcwfZAsNCmhAHr+7FS0f0C4F/smvNJ+6pamhJbZ9yow==", "path": "z.expressions.eval/4.0.85", "hashPath": "z.expressions.eval.4.0.85.nupkg.sha512"}, "Adnc.Demo.Shared.Const/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Demo.Shared.Rpc.Grpc/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Demo.Shared.Rpc.Http/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Demo.Usr.Application/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Demo.Usr.Application.Contracts/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Demo.Usr.Repository/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Infra.Consul/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Infra.Core/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Infra.EventBus/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Infra.Helper/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Infra.IdGenerater/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Infra.Mapper/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Infra.Redis/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Infra.Redis.Caching/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Infra.Repository/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Infra.Repository.Dapper/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Infra.Repository.EfCore/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Infra.Repository.EfCore.MySql/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Infra.Repository.Mongo/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Shared/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Shared.Application/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Shared.Application.Contracts/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Shared.Repository/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Shared.Rpc/*******": {"type": "project", "serviceable": false, "sha512": ""}, "Adnc.Shared.WebApi/*******": {"type": "project", "serviceable": false, "sha512": ""}}}