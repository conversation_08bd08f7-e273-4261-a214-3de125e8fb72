﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Infra.Redis.Caching.Core.Interceptor;
using Adnc.Shared.Application.Contracts.ResultModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services
{
    public interface IBasStationService : IAppService
    {
        /// <summary>
        /// 获取所有工位信息
        /// </summary>
        /// <returns></returns>
        [OperateLog(LogName = "获取所有工位信息")]
        Task<List<BasStationDto>> GetAllAsync();

        /// <summary>
        /// 获取单个工位信息
        /// </summary>
        /// <returns></returns>
        [OperateLog(LogName = "获取指定工位信息")]
        Task<ResultJson> GetAppointAsync(long id);

        /// <summary>
        /// 新增工位
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [OperateLog(LogName = "新增工位")]
        Task<AppSrvResult<long>> CreateAsync(BasStationDto input);

        /// <summary>
        /// 修改工位
        /// </summary>
        /// <param name="id"></param>
        /// <param name="input"></param>
        /// <returns></returns>
        [OperateLog(LogName = "修改工位")]
        Task<AppSrvResult> UpdateAsync( BasStationDto input);


        /// <summary>
        /// 删除工位
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [OperateLog(LogName = "删除工位")]
        Task<AppSrvResult> DeleteAsync(long id);



        /// <summary>
        /// 修改工位状态
        /// </summary>
        /// <param name="id"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        [OperateLog(LogName = "修改工位状态")]
        Task<AppSrvResult> ChangeStatusAsync([CachingParam] long id, int status);

        /// <summary>
        /// 批量修改工位状态
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        [OperateLog(LogName = "批量修改工位状态")]
        Task<AppSrvResult> ChangeStatusAsync(UpdateStatusDto input);


        /// <summary>
        /// 获取用户列表
        /// </summary>
        /// <param name="search"></param>
        /// <returns></returns>
        Task<PageModelDto<BasStationDto>> GetPagedAsync(BasStationPagedDto search);

        Task<List<BasStationDto>> GetListAsync(BasStationSearchDto search);

        /// <summary>
        /// 获取所有工位信息
        /// </summary>
        /// <returns></returns>
        [OperateLog(LogName = "获取所有班次信息")]
        Task<List<ClassesDto>> GetClassesInfoAsync(long stationId);

        [OperateLog(LogName = "批量禁用启用工位信息")]
        Task<AppSrvResult> MakeEnableAsync(int status, List<long> ids);
    }
}
