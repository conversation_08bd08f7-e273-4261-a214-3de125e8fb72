﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasProductStepRelationAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductTechnologyRelationAggregate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class BasProductStepRelationConfig : AbstractEntityTypeConfiguration<BasProductStepRelation>
    {
        public override void Configure(EntityTypeBuilder<BasProductStepRelation> builder)
        {
            base.Configure(builder);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.ProCode).HasColumnName("procode");
            builder.Property(x => x.StepCode).HasColumnName("stepcode");
            builder.Property(x => x.TecCode).HasColumnName("tcode");
            builder.Property(x => x.Remark).HasColumnName("remark");
            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.CreateBy).HasColumnName("createby");
            builder.Property(x => x.CreateTime).HasColumnName("createtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
        }
    }
}
