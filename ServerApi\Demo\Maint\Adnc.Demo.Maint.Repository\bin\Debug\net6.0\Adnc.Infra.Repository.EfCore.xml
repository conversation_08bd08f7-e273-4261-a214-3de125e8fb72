<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Adnc.Infra.Repository.EfCore</name>
    </assembly>
    <members>
        <member name="T:Adnc.Infra.Repository.EfCore.Repositories.AbstractEfBaseRepository`2">
            <summary>
            Ef仓储的基类实现,抽象类
            </summary>
            <typeparam name="TDbContext"></typeparam>
            <typeparam name="TEntity"></typeparam>
        </member>
        <member name="T:Adnc.Infra.Repository.EfCore.Repositories.EfBasicRepository`1">
            <summary>
            Ef简单的、基础的，初级的仓储接口
            适合DDD开发模式,实体必须继承AggregateRoot
            </summary>
            <typeparam name="TEntity"></typeparam>
        </member>
        <member name="T:Adnc.Infra.Repository.EfCore.Repositories.EfRepository`1">
            <summary>
            Ef默认的、全功能的仓储实现
            </summary>
            <typeparam name="TEntity"></typeparam>
        </member>
    </members>
</doc>
