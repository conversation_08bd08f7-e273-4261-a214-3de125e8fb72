﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Infra.Redis.Caching.Core.Interceptor;
using Adnc.Shared.Application.Contracts.ResultModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services
{
    
    public interface IBasMaterialService : IAppService
    {
        /// <summary>
        /// 获取所有物料信息
        /// </summary>
        /// <returns></returns>
        [OperateLog(LogName = "获取所有物料信息")]
        Task<List<BasMaterialDto>> GetAllAsync();

        /// <summary>
        /// 获取单个物料信息
        /// </summary>
        /// <returns></returns>
        [OperateLog(LogName = "获取指定物料信息")]
        Task<ResultJson> GetAppointAsync(long id);

        /// <summary>
        /// 新增物料
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [OperateLog(LogName = "新增物料")]
        Task<AppSrvResult<long>> CreateAsync(BasMaterialDto input);

        /// <summary>
        /// 修改物料
        /// </summary>
        /// <param name="id"></param>
        /// <param name="input"></param>
        /// <returns></returns>
        [OperateLog(LogName = "修改物料")]
        Task<AppSrvResult> UpdateAsync(BasMaterialDto input);


        /// <summary>
        /// 删除物料
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [OperateLog(LogName = "删除物料")]
        Task<AppSrvResult> DeleteAsync(long id);



        /// <summary>
        /// 修改物料状态
        /// </summary>
        /// <param name="id"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        [OperateLog(LogName = "修改物料状态")]
        Task<AppSrvResult> ChangeStatusAsync([CachingParam] long id, int status);

        /// <summary>
        /// 批量修改物料状态
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        [OperateLog(LogName = "批量修改物料状态")]
        Task<AppSrvResult> ChangeStatusAsync(UpdateStatusDto input);


        /// <summary>
        /// 获取用户列表
        /// </summary>
        /// <param name="search"></param>
        /// <returns></returns>
        Task<PageModelDto<BasMaterialDto>> GetPagedAsync(BasMaterialPagedDto search);

        [OperateLog(LogName = "批量禁用启用物料")]
        Task<AppSrvResult> MakeEnableAsync(int status, List<long> ids);



        [OperateLog(LogName = "导入物料")]
        Task<ResultJson> ImportAsync(List<BasMaterialDto> dtos);

    }
}
