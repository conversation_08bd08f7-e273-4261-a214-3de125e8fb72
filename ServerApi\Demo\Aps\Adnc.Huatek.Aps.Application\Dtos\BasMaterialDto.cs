﻿using System.ComponentModel.DataAnnotations;

namespace Adnc.Huatek.Aps.Application.Dtos
{
    public class BasMaterialDto : Dto
    {
        /// <summary>
        /// ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool Isdeleted { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [Display(Name = "启用/禁用")]
        public string StatusDis { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        [Display(Name = "物料编码")]
        public string? Code { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        [Display(Name = "物料名称")]
        public string? Name { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Display(Name = "描述")]
        public string? Remark { get; set; }

        /// <summary>
        /// 物料类型
        /// </summary>
        public string? Mtype { get; set; }


        [Display(Name = "物料类型")]
        public string? MtypeDis { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        
        public string? Unit { get; set; }

        [Display(Name = "单位")]
        public string? UnitDis { get; set; }

    }


    public class BasMaterialPagedDto : SearchPagedDto
    {
        /// <summary>
        /// 工位名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 工位编码
        /// </summary>
        public string? Code { get; set; }


        /// <summary>
        /// 状态
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 物料类型
        /// </summary>
        public string? Mtype { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string? Unit { get; set; }
    }

   
}
