﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.BasMaterial;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductTechnologyRelationAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStation;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStepAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PartOrderAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PartOrderStepAggregate;
using Adnc.Huatek.Aps.Domain.AggregatesPartOrderMaterialAggregate;
using Adnc.Huatek.Aps.Domain.Services;
using Adnc.Infra.IdGenerater.Yitter;
using Adnc.Shared.Application.Contracts.Dtos;
using Adnc.Shared.Application.Contracts.ResultModels;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services.Implements
{
    public class PartOrderService : AbstractAppService, IPartOrderService
    {
        private readonly PartOrderManagement _partOrderMgr;
        private readonly IEfBasicRepository<PartOrder> _partOrderRepo;
        private readonly IEfBasicRepository<PartOrderMaterial> _partOrderMaterialRepo;
        private readonly IEfBasicRepository<PartOrderStep> _partOrderStepRepo;
        private readonly IEfBasicRepository<BasStep> _basStepRepo;
        private readonly IEfBasicRepository<BasMaterial> _basMaterialRepo;
        private readonly IEfBasicRepository<BasProductTechnologyRelation> _basProTecRepo;
        private readonly IEfBasicRepository<BasProduct> _basProductRepo;

        public PartOrderService(
            IEfBasicRepository<PartOrder> partOrderRepo,
            IEfBasicRepository<BasMaterial> basMaterialRepo,
            IEfBasicRepository<BasStep> basStepRepo,
            IEfBasicRepository<PartOrderMaterial> partOrderMaterialRepo,
            IEfBasicRepository<PartOrderStep> partOrderStepRepo,
            IEfBasicRepository<BasProductTechnologyRelation> basProTecRepo,
            IEfBasicRepository<BasProduct> basProductRepo,
        PartOrderManagement partOrderMgr)
        {
            _partOrderMgr = partOrderMgr;
            _partOrderRepo = partOrderRepo;
            _basStepRepo = basStepRepo;
            _basMaterialRepo = basMaterialRepo;
            _partOrderStepRepo = partOrderStepRepo;
            _basProTecRepo = basProTecRepo;
            _basProductRepo = basProductRepo;
            _partOrderMaterialRepo = partOrderMaterialRepo;
        }
        /// <summary>
        /// 获取分页列表
        /// </summary>
        /// <param name="search"></param>
        /// <returns></returns>
        public async Task<PageModelDto<PartOrderDto>> GetPagedAsync(PartOrderPagedDto search)
        {
            search.TrimStringFields();
            var whereExpression = ExpressionCreator
                                                .New<PartOrder>()
                                                .And(x=>!x.IsDeleted)
                                                .AndIf(search.OrderCode.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.OrderCode!, $"%{search.OrderCode}%"))
                                                .AndIf(search.IdCustorm!=0, x => EF.Functions.Like(x.IdCustorm!, $"%{search.IdCustorm}%"))
                                                .AndIf(search.IdProduct != 0, x => EF.Functions.Like(x.IdProduct!, $"%{search.IdProduct}%"))
                                                .AndIf(search.IdTec != 0, x => EF.Functions.Like(x.IdTec!, $"%{search.IdTec}%"))
                                                .AndIf(search.Status!=999, x => EF.Functions.Like(x.Status!, $"%{search.Status}%"));

            var total = await _partOrderRepo.CountAsync(whereExpression);
            if (total == 0)
                return new PageModelDto<PartOrderDto>(search);

            var entities = await _partOrderRepo
                                            .Where(whereExpression)
                                            .OrderByDescending(x => x.CreateTime)
                                            .Skip(search.SkipRows())
                                            .Take(search.PageSize)
                                            .ToListAsync();

            var productList = _basProductRepo.Where(x => !x.IsDeleted && entities.Select(x => x.IdProduct).Contains(x.Id));


           

            var orderDtos = Mapper.Map<List<PartOrderDto>>(entities);

            orderDtos.ForEach(x => {
                x.ProductCode = productList.FirstOrDefault(p => p.Id == x.IdProduct).Procode ?? "";
                x.ProductName = productList.FirstOrDefault(p => p.Id == x.IdProduct).Proname ?? "";
            });

            return new PageModelDto<PartOrderDto>(search, orderDtos, total);
        }


        /// <summary>
        /// 根据ID获取工艺数据
        /// </summary>
        /// <returns></returns>

        public async Task<PartOrderDto> GetByIdAsync(long id)
        {
            var order = await _partOrderRepo.GetAsync(id);

            var PartOrderDto = Mapper.Map<PartOrderDto>(order);

            var materials = await _partOrderMaterialRepo.Where(x => !x.IsDeleted && x.IdOrder == id).ToListAsync();
            PartOrderDto.Materials = Mapper.Map<List<PartOrderMaterialDto>>(materials);

            var materialLists = await _basMaterialRepo.Where(x => !x.IsDeleted && materials.Select(p => p.IdMaterial).Contains(x.Id)).ToListAsync();

            PartOrderDto.Materials.ForEach(x => { 
                x.MaterialName = materialLists.FirstOrDefault(p=>p.Id==x.IdMaterial).Name??""; 
                x.MaterialCode = materialLists.FirstOrDefault(p => p.Id == x.IdMaterial).Code??"";
            });


            var steps = await _partOrderStepRepo.Where(x => !x.IsDeleted && x.IdOrder == id).ToListAsync();
            PartOrderDto.Steps = Mapper.Map<List<PartOrderStepDto>>(steps);

            var stepList = await _basStepRepo.Where(x => !x.IsDeleted && steps.Select(p => p.IdStep).Contains(x.Id)).ToListAsync();

            PartOrderDto.Steps.ForEach(x => {
                x.StepName = stepList.FirstOrDefault(p => p.Id == x.IdStep).Name ?? "";
                x.StepCode = stepList.FirstOrDefault(p => p.Id == x.IdStep).Name ?? "";
                x.StepTime = stepList.FirstOrDefault(p => p.Id == x.IdStep).StepTime ?? 0;
            });

            return PartOrderDto;
        }


        /// <summary>
        /// 创建工艺
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult<long>> CreateAsync(PartOrderDto input)
        {
            input.TrimStringFields();
            var order = await _partOrderMgr.CreateAsync(input.OrderCode, input.ProductName, input.IdProduct, input.IdCustorm, input.Qty, input.IdTec, input.Status, input.DeliveryTime, input.Remark,input.Level);
            //order.IdTec = _basProTecRepo.Where(x => x.IdProduct == order.IdProduct && !x.IsDeleted && x.Status == 1)?.FirstOrDefault()?.IdTechnology??0;
            var materials = Mapper.Map<List<PartOrderMaterial>>(input.Materials);
            materials.ForEach(o =>
            {
                o.Id = IdGenerater.GetNextId();
                o.IdOrder = order.Id;
                o.SumQty = order.Qty * o.Qty;
            });
            var steps = Mapper.Map<List<PartOrderStep>>(input.Steps);
            steps.ForEach(o =>
            {
                o.Id = IdGenerater.GetNextId();
                o.IdOrder = order.Id;
            });

            await _partOrderRepo.InsertAsync(order);
            await _partOrderMaterialRepo.InsertRangeAsync(materials);
            await _partOrderStepRepo.InsertRangeAsync(steps);
            return order.Id;
        }


        /// <summary>
        /// 更新工艺
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult> UpdateAsync(PartOrderDto input)
        {
            bool isUpdete = await _partOrderMgr.UpdateAsync(input.OrderCode, input.ProductName, input.IdProduct, input.Id);
            if (isUpdete)
            {
                var order = await _partOrderRepo.GetAsync(input.Id??0);
                if (order != null)
                {
                    order.Status = input.Status;
                    order.OrderCode = input.OrderCode;
                    order.IdProduct = input.IdProduct;
                    order.IdTec = _basProTecRepo.Where(x => x.IdProduct == order.IdProduct && !x.IsDeleted && x.Status == 1)?.FirstOrDefault()?.IdTechnology ?? 0;
                    order.Qty = input.Qty;
                    order.IdCustorm = input.IdCustorm;
                    order.DeliveryTime = input.DeliveryTime;
                    order.Remark = input.Remark;
                    order.Level = input.Level;
                    await _partOrderRepo.UpdateAsync(order);

                    var materials = await _partOrderMaterialRepo.Where(x => !x.IsDeleted && x.IdOrder == input.Id, false, false).ToListAsync();
                    await _partOrderMaterialRepo.RemoveRangeAsync(materials);

                    //物料
                    var materialList = Mapper.Map<List<PartOrderMaterial>>(input.Materials);
                    materialList.ForEach(o =>
                    {
                        o.Id = IdGenerater.GetNextId();
                        o.IdOrder = order.Id;
                        o.SumQty = order.Qty * o.Qty;
                    });
                    await _partOrderMaterialRepo.InsertRangeAsync(materialList);
                    //工序
                    var steps = await _partOrderStepRepo.Where(x => !x.IsDeleted && x.IdOrder == input.Id, false, false).ToListAsync();
                    await _partOrderStepRepo.RemoveRangeAsync(steps);

                    var stepList = Mapper.Map<List<PartOrderStep>>(input.Steps);
                    stepList.ForEach(o =>
                    {
                        o.Id = IdGenerater.GetNextId();
                        o.IdOrder = order.Id;
                    });
                    await _partOrderStepRepo.InsertRangeAsync(stepList);

                }
            }
            return AppSrvResult();
        }

        /// <summary>
        /// 删除工艺
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult> DeleteAsync(long Id)
        {
            var step = await _partOrderRepo.GetAsync(Id);
            if (step != null)
            {
                var steps = await _partOrderStepRepo.Where(x => !x.IsDeleted && x.IdOrder == Id, false, false).ToListAsync();
                steps.ForEach(o =>
                {
                    o.IsDeleted = true;
                });
                await _partOrderStepRepo.UpdateRangeAsync(steps);

                var materials = await _partOrderMaterialRepo.Where(x => !x.IsDeleted && x.IdOrder == Id, false, false).ToListAsync();
                materials.ForEach(o =>
                {
                    o.IsDeleted = true;
                });
                await _partOrderMaterialRepo.UpdateRangeAsync(materials);


                step.IsDeleted = true;
                await _partOrderRepo.UpdateAsync(step);
            }
            return AppSrvResult();


        }
    }
}
