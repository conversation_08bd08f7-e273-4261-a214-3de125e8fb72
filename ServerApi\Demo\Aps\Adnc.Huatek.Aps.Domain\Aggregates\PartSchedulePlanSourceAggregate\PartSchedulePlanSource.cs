﻿using Adnc.Infra.Entities;

namespace Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanSourceAggregate
{
    /// <summary>
    /// 排产计划设备
    /// </summary>
    public class PartSchedulePlanSource: EfFullAuditEntity
    {
        /// <summary>
        /// 
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 排产计划编码
        /// </summary>
        public string? ScheduleCode { get; set; }

        /// <summary>
        /// 工序编码
        /// </summary>
        public string? StepCode { get; set; }

        /// <summary>
        /// 资源大类
        /// </summary>
        public string? SourceType { get; set; }

        /// <summary>
        /// 资源类型
        /// </summary>
        public string? DevType { get; set; }

        /// <summary>
        /// 资源编码
        /// </summary>
        public string? SourceCode { get; set; }

        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime BeginDate { get; set; }

        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// 消耗工时
        /// </summary>
        public decimal Tat { get; set; }


        /// <summary>
        /// 单位工作时长
        /// </summary>
        public decimal UnitTat { get; set; }

        /// <summary>
        /// 单位产能
        /// </summary>
        public decimal UnitCapacity { get; set; }


        public decimal TotalCapacity { get; set; }
        
    }
}
