<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Adnc.Huatek.Aps.Domain</name>
    </assembly>
    <members>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasBomAggregate.BasBom.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasBomAggregate.BasBom.ProductCode">
            <summary>
            产品编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasBomAggregate.BasBom.MaterialCode">
            <summary>
            物料编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasBomAggregate.BasBom.MaterialName">
            <summary>
            物料名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasBomAggregate.BasBom.Qty">
            <summary>
            物料数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasBomAggregate.BasBom.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasBomListAggregate.BasBomList.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasBomListAggregate.BasBomList.IdBom">
            <summary>
            BomID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasBomListAggregate.BasBomList.MaterialName">
            <summary>
            物料名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasBomListAggregate.BasBomList.MaterialCode">
            <summary>
            物料编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasBomListAggregate.BasBomList.Qty">
            <summary>
            数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasClassesAggregate.BasClasses.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasClassesAggregate.BasClasses.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasClassesAggregate.BasClasses.ShortName">
            <summary>
            简称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasClassesAggregate.BasClasses.Cname">
            <summary>
            班次名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasClassesAggregate.BasClasses.Ccode">
            <summary>
            班次编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasClassesAggregate.BasClasses.BeginTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasClassesAggregate.BasClasses.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasClassesAggregate.BasClasses.Status">
            <summary>
            启用状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasDeviceClassAggregate.BasDeviceClass.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasDeviceClassAggregate.BasDeviceClass.DevCode">
            <summary>
            设备编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasDeviceClassAggregate.BasDeviceClass.ClassCode">
            <summary>
            班次时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasDeviceClassAggregate.BasDeviceClass.WorkingDate">
            <summary>
            工作日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasDevicePreserveAggregate.BasDevicePreserve.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasDevicePreserveAggregate.BasDevicePreserve.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasDevicePreserveAggregate.BasDevicePreserve.DevCode">
            <summary>
            设备编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasDevicePreserveAggregate.BasDevicePreserve.DevName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasDevicePreserveAggregate.BasDevicePreserve.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasDevicePreserveAggregate.BasDevicePreserve.BeginTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasDevicePreserveAggregate.BasDevicePreserve.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasDevicePreserveAggregate.BasDevicePreserve.CreatedName">
            <summary>
            创建人名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasDevicePreserveAggregate.BasDevicePreserve.ModifyName">
            <summary>
            修改人名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasDevice.BasDevice.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasDevice.BasDevice.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasDevice.BasDevice.Code">
            <summary>
            设备编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasDevice.BasDevice.Name">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasDevice.BasDevice.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasDevice.BasDevice.CreatedName">
            <summary>
            创建人名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasDevice.BasDevice.ModifyName">
            <summary>
            修改人名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasDevice.BasDevice.Capacity">
            <summary>
            产能
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasDevice.BasDevice.Dtype">
            <summary>
            设备类型
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasDevice.BasDevice.Newness">
            <summary>
            设备新度
            </summary>
        </member>
        <member name="T:Adnc.Huatek.Aps.Domain.Aggregates.BasHolidayAggregate.BasHoliday">
            <summary>
            假期时间管理
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasHolidayAggregate.BasHoliday.CurrentDate">
            <summary>
            日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasHolidayAggregate.BasHoliday.IsHoliday">
            <summary>
            是否工作日
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasHolidayAggregate.BasHoliday.IsPlaned">
            <summary>
            是否排产
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasHolidayAggregate.BasHoliday.WeekName">
            <summary>
            星期名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasLineAggregate.BasLine.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasLineAggregate.BasLine.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasLineAggregate.BasLine.Lname">
            <summary>
            产线名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasLineAggregate.BasLine.Lcode">
            <summary>
            产线编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasLineAggregate.BasLine.IdWorkshop">
            <summary>
            车间id
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasLineAggregate.BasLine.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasLineProductRelationAggregate.BasLineProductRelation.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasLineProductRelationAggregate.BasLineProductRelation.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasLineProductRelationAggregate.BasLineProductRelation.LineCode">
            <summary>
            产线ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasLineProductRelationAggregate.BasLineProductRelation.ProductCode">
            <summary>
            产品ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasLineProductRelationAggregate.BasLineProductRelation.Capacity">
            <summary>
            默认产能
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasLineProductRelationAggregate.BasLineProductRelation.Unit">
            <summary>
            产能单位
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasMaterial.BasMaterial.Mtype">
            <summary>
            物料类型
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasMaterial.BasMaterial.Unit">
            <summary>
            单位
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasMaterial.BasMaterial.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasMaterial.BasMaterial.Code">
            <summary>
            物料编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasMaterial.BasMaterial.Name">
            <summary>
            物料名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasMaterial.BasMaterial.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasMaterial.BasMaterial.CreatedName">
            <summary>
            创建人名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasMaterial.BasMaterial.ModifyName">
            <summary>
            修改人名称
            </summary>
        </member>
        <member name="T:Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate.BasOrder">
            <summary>
            客户订单
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate.BasOrder.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate.BasOrder.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate.BasOrder.Status">
            <summary>
            生产状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate.BasOrder.OrderNumber">
            <summary>
            订单编号
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate.BasOrder.UserName">
            <summary>
            客户
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate.BasOrder.DeliveryDate">
            <summary>
            交付日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate.BasOrder.SaleNumber">
            <summary>
            销售单号
            </summary>
        </member>
        <member name="T:Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate.BasOrderProduct">
            <summary>
            客户订单产品
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate.BasOrderProduct.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate.BasOrderProduct.OrderId">
            <summary>
            orderId
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate.BasOrderProduct.ProductId">
            <summary>
            产品id
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate.BasOrderProduct.ProductCode">
            <summary>
             产品编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate.BasOrderProduct.Status">
            <summary>
            生产状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate.BasOrderProduct.ProductiveTime">
            <summary>
            生产日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate.BasOrderProduct.Quantity">
            <summary>
            产品数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate.BasOrderProduct.PendingNum">
            <summary>
            待排产产品数量
            </summary>
        </member>
        <member name="T:Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate.PartPlanPending">
            <summary>
            待排产计划
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate.PartPlanPending.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate.PartPlanPending.PlanCode">
            <summary>
            计划编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate.PartPlanPending.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate.PartPlanPending.Status">
            <summary>
            生产状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate.PartPlanPending.OrderId">
            <summary>
            订单id
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate.PartPlanPending.IdDetail">
            <summary>
            订单明细id
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate.PartPlanPending.OrderNumber">
            <summary>
            订单编号
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate.PartPlanPending.UserName">
            <summary>
            客户
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate.PartPlanPending.DeliveryDate">
            <summary>
            交付日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate.PartPlanPending.Qty">
            <summary>
            计划排产数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate.PartPlanPending.ProductName">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate.PartPlanPending.ProductCode">
            <summary>
            产品编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductAggregate.BasProduct.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductAggregate.BasProduct.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductAggregate.BasProduct.Procode">
            <summary>
            产品编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductAggregate.BasProduct.Proname">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductAggregate.BasProduct.TCode">
            <summary>
            工艺编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductAggregate.BasProduct.Status">
            <summary>
            启用状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStandardCapacityAggregate.BasProductStandardCapacity.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStandardCapacityAggregate.BasProductStandardCapacity.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStandardCapacityAggregate.BasProductStandardCapacity.Status">
            <summary>
            启用状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStandardCapacityAggregate.BasProductStandardCapacity.SolutionName">
            <summary>
            产能方案
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStandardCapacityAggregate.BasProductStandardCapacity.ProCode">
            <summary>
            产品编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStandardCapacityAggregate.BasProductStandardCapacity.StepCode">
            <summary>
            工序编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStandardCapacityAggregate.BasProductStandardCapacity.StationCode">
            <summary>
            工位编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStandardCapacityAggregate.BasProductStandardCapacity.LineCode">
            <summary>
            产线编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStandardCapacityAggregate.BasProductStandardCapacity.DeviceCode">
            <summary>
            设备编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStandardCapacityAggregate.BasProductStandardCapacity.Capacity">
            <summary>
            产能
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStandardCapacityAggregate.BasProductStandardCapacity.WorkUnitTime">
            <summary>
            单位工作时长
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStandardCapacityAggregate.BasProductStandardCapacity.StandardWorkTime">
            <summary>
            标准工作时长
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStandardCapacityAggregate.PlanStepCapacity.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStandardCapacityAggregate.PlanStepCapacity.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStandardCapacityAggregate.PlanStepCapacity.TaskCode">
            <summary>
            任务编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStandardCapacityAggregate.PlanStepCapacity.SolutionName">
            <summary>
            产能方案
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStandardCapacityAggregate.PlanStepCapacity.ProCode">
            <summary>
            产品编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStandardCapacityAggregate.PlanStepCapacity.StepCode">
            <summary>
            工序编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStandardCapacityAggregate.PlanStepCapacity.StationCode">
            <summary>
            工位编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStandardCapacityAggregate.PlanStepCapacity.LineCode">
            <summary>
            产线编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStandardCapacityAggregate.PlanStepCapacity.DeviceCode">
            <summary>
            设备编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStandardCapacityAggregate.PlanStepCapacity.Capacity">
            <summary>
            产能
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStandardCapacityAggregate.PlanStepCapacity.WorkUnitTime">
            <summary>
            单位工作时长
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStandardCapacityAggregate.PlanStepCapacity.StandardWorkTime">
            <summary>
            标准工作时长
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStepRelationAggregate.BasProductStepRelation.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStepRelationAggregate.BasProductStepRelation.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStepRelationAggregate.BasProductStepRelation.TecCode">
            <summary>
            产品编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStepRelationAggregate.BasProductStepRelation.ProCode">
            <summary>
            产品编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStepRelationAggregate.BasProductStepRelation.StepCode">
            <summary>
            工序编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStepRelationAggregate.BasProductStepRelationMaterial.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStepRelationAggregate.BasProductStepRelationMaterial.MainId">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStepRelationAggregate.BasProductStepRelationMaterial.MaterialCode">
            <summary>
            物料编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStepRelationAggregate.BasProductStepRelationMaterial.Qty">
            <summary>
            数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStepRelationAggregate.BasProductStepRelationMaterial.BatchQty">
            <summary>
            结批量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStepRelationAggregate.BasProductStepRelationMaterial.MaterialType">
            <summary>
            物料类型IN OUT
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStepRelationDeviceAggregate.BasProductStepRelationSource.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStepRelationDeviceAggregate.BasProductStepRelationSource.MainId">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStepRelationDeviceAggregate.BasProductStepRelationSource.SourceType">
            <summary>
            资源大类
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStepRelationDeviceAggregate.BasProductStepRelationSource.DeviceType">
            <summary>
            资源小类
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStepRelationDeviceAggregate.BasProductStepRelationSource.Tat">
            <summary>
            单位工作时长
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStepRelationDeviceAggregate.BasProductStepRelationSource.Capacity">
            <summary>
            单位产能
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStepRelationDeviceAggregate.BasProductStepRelationSource.MainSource">
            <summary>
            是否考虑占用
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductStepRelationDeviceAggregate.BasProductStepRelationSource.IsCapacity">
            <summary>
            产能约束
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductTechnologyRelationAggregate.BasProductTechnologyRelation.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductTechnologyRelationAggregate.BasProductTechnologyRelation.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductTechnologyRelationAggregate.BasProductTechnologyRelation.IdProduct">
            <summary>
            产品id
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductTechnologyRelationAggregate.BasProductTechnologyRelation.IdTechnology">
            <summary>
            工艺id
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasProductTechnologyRelationAggregate.BasProductTechnologyRelation.Status">
            <summary>
            启用状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasRelationDeviceAggregate.BasRelationDevice.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasRelationDeviceAggregate.BasRelationDevice.IdRelation">
            <summary>
            关系id
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasRelation.BasRelation.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasRelation.BasRelation.IdStep">
            <summary>
            工序id
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasStation.BasStation.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasStation.BasStation.Code">
            <summary>
            工位编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasStation.BasStation.Name">
            <summary>
            工位名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasStation.BasStation.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasStation.BasStation.DeviceCode">
            <summary>
            设备编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasStation.BasStation.CreatedName">
            <summary>
            创建人名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasStation.BasStation.ModifyName">
            <summary>
            修改人名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasStation.BasStation.LineCode">
            <summary>
            所属产线
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasStation.BasStation.Duration">
            <summary>
            标准工作时长(h)
            </summary>
        </member>
        <member name="T:Adnc.Huatek.Aps.Domain.Aggregates.BasStation.BasStationClasses">
            <summary>
            工位与班次关系表
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasStation.BasStationClasses.StationId">
            <summary>
            工位id
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasStation.BasStationClasses.StationCode">
            <summary>
            工位编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasStation.BasStationClasses.ClassesId">
            <summary>
            班次id
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasStation.BasStationClasses.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasStation.BasStationClasses.Duration">
            <summary>
            工作时长(h)
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasStation.BasStationClasses.Qty">
            <summary>
            人员数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasStepAggregate.BasStep.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasStepAggregate.BasStep.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasStepAggregate.BasStep.Name">
            <summary>
            工序名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasStepAggregate.BasStep.Code">
            <summary>
            工序编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasStepAggregate.BasStep.StepTime">
            <summary>
            工序时间(小时)
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasStepAggregate.BasStep.Tat">
            <summary>
            工序时间(小时)
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasStepAggregate.BasStep.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasStepMaterialRelationAggregate.BasStepMaterialRelation.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasStepMaterialRelationAggregate.BasStepMaterialRelation.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasStepMaterialRelationAggregate.BasStepMaterialRelation.ProCode">
            <summary>
            产品编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasStepMaterialRelationAggregate.BasStepMaterialRelation.TCode">
            <summary>
            工艺编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasStepMaterialRelationAggregate.BasStepMaterialRelation.StepCode">
            <summary>
            工序编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasStepMaterialRelationAggregate.BasStepMaterialRelation.MaterialCode">
            <summary>
            物料编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasStepMaterialRelationAggregate.BasStepMaterialRelation.MaterialName">
            <summary>
            物料名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasStepMaterialRelationAggregate.BasStepMaterialRelation.Qty">
            <summary>
            数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasStepMaterialRelationAggregate.BasStepMaterialRelation.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasTechnologyAggregate.BasTechnology.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasTechnologyAggregate.BasTechnology.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasTechnologyAggregate.BasTechnology.Tcode">
            <summary>
            工艺路线编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasTechnologyAggregate.BasTechnology.Tname">
            <summary>
            工艺路线名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasTechnologyAggregate.BasTechnology.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasTechnologyStepRelationAggregate.BasTechnologyStepRelation.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasTechnologyStepRelationAggregate.BasTechnologyStepRelation.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasTechnologyStepRelationAggregate.BasTechnologyStepRelation.IdTechnology">
            <summary>
            工艺路线ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasTechnologyStepRelationAggregate.BasTechnologyStepRelation.IdStep">
            <summary>
            工序路线id
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasTechnologyStepRelationAggregate.BasTechnologyStepRelation.IsKey">
            <summary>
            是否关键工序
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasTechnologyStepRelationAggregate.BasTechnologyStepRelation.Sort">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasTechnologyStepRelationAggregate.BasTechnologyStepRelation.RelationType">
            <summary>
            与下一道工序的关系
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasTechnologyStepRelationAggregate.BasTechnologyStepRelation.IntervalTime">
            <summary>
            前置工序间隔时间
            </summary>
        </member>
        <member name="F:Adnc.Huatek.Aps.Domain.Aggregates.BasTechnologyStepRelationAggregate.BasTechnologyStepRelation._preStep">
            <summary>
            前置工序
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasWorksjop.BasWorksjop.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasWorksjop.BasWorksjop.Code">
            <summary>
            车间编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasWorksjop.BasWorksjop.Name">
            <summary>
            车间名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasWorksjop.BasWorksjop.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasWorksjop.BasWorksjop.CreatedName">
            <summary>
            创建人名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.BasWorksjop.BasWorksjop.ModifyName">
            <summary>
            修改人名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartDeviceStateAggregates.PartDeviceState.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartDeviceStateAggregates.PartDeviceState.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartDeviceStateAggregates.PartDeviceState.DevCode">
            <summary>
            设备编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartDeviceStateAggregates.PartDeviceState.DevName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartDeviceStateAggregates.PartDeviceState.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartDeviceStateAggregates.PartDeviceState.BeginTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartDeviceStateAggregates.PartDeviceState.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartOrderAggregate.PartOrder.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartOrderAggregate.PartOrder.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartOrderAggregate.PartOrder.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartOrderStepAggregate.PartOrderStep.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartOrderStepAggregate.PartOrderStep.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanAggregate.PartSchedulePlan.Id">
            <summary>
            排产计划编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanAggregate.PartSchedulePlan.Code">
            <summary>
            排产计划编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanAggregate.PartSchedulePlan.PlanCode">
            <summary>
            生产计划编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanAggregate.PartSchedulePlan.PlanBeginDate">
            <summary>
            排产计划编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanAggregate.PartSchedulePlan.PlanEndDate">
            <summary>
            生产计划编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanAggregate.PartSchedulePlan.BeginDate">
            <summary>
            生产计划编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanAggregate.PartSchedulePlan.ScheduleType">
            <summary>
            来源
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanAggregate.PartSchedulePlanInfo.Id">
            <summary>
            排产计划编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanAggregate.PartSchedulePlanInfo.PlanCode">
            <summary>
            生产计划编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanAggregate.PartSchedulePlanInfo.ScheduleCode">
            <summary>
            生产计划编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanAggregate.PartSchedulePlanInfo.ProductModule">
            <summary>
            排产计划编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanMaterialAggregate.PartSchedulePlanMaterial.Id">
            <summary>
            排产计划编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanMaterialAggregate.PartSchedulePlanMaterial.ScheduleCode">
            <summary>
            排产计划编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanMaterialAggregate.PartSchedulePlanMaterial.StepCode">
            <summary>
            生产计划编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanMaterialAggregate.PartSchedulePlanMaterial.Mcode">
            <summary>
            物料编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanMaterialAggregate.PartSchedulePlanMaterial.NeedDate">
            <summary>
            需求时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanMaterialAggregate.PartSchedulePlanMaterial.Qty">
            <summary>
            生产计划编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanRuleAggregate.PartSchedulePlanRule.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanRuleAggregate.PartSchedulePlanRule.RuleType">
            <summary>
            规则类型
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanRuleAggregate.PartSchedulePlanRule.RuleValue">
            <summary>
            规则值
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanRuleAggregate.PartSchedulePlanRule.ScheduleCode">
            <summary>
            排产计划编码
            </summary>
        </member>
        <member name="T:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanSourceAggregate.PartSchedulePlanSource">
            <summary>
            排产计划设备
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanSourceAggregate.PartSchedulePlanSource.Id">
            <summary>
            
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanSourceAggregate.PartSchedulePlanSource.ScheduleCode">
            <summary>
            排产计划编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanSourceAggregate.PartSchedulePlanSource.StepCode">
            <summary>
            工序编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanSourceAggregate.PartSchedulePlanSource.SourceType">
            <summary>
            资源大类
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanSourceAggregate.PartSchedulePlanSource.DevType">
            <summary>
            资源类型
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanSourceAggregate.PartSchedulePlanSource.SourceCode">
            <summary>
            资源编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanSourceAggregate.PartSchedulePlanSource.BeginDate">
            <summary>
            开始日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanSourceAggregate.PartSchedulePlanSource.EndDate">
            <summary>
            结束日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanSourceAggregate.PartSchedulePlanSource.Tat">
            <summary>
            消耗工时
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanSourceAggregate.PartSchedulePlanSource.UnitTat">
            <summary>
            单位工作时长
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanSourceAggregate.PartSchedulePlanSource.UnitCapacity">
            <summary>
            单位产能
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanStepAggregate.PartSchedulePlanStep.Id">
            <summary>
            
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanStepAggregate.PartSchedulePlanStep.ScheduleCode">
            <summary>
            排产计划Code
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanStepAggregate.PartSchedulePlanStep.PreCode">
            <summary>
            前置工序
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanStepAggregate.PartSchedulePlanStep.StepCode">
            <summary>
            工序编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanStepAggregate.PartSchedulePlanStep.BeginDate">
            <summary>
            开始日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanStepAggregate.PartSchedulePlanStep.EndDate">
            <summary>
            结束日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanStepAggregate.PartSchedulePlanStep.Tat">
            <summary>
            消耗工时;用产能模型中考虑产能的资源的单元工作时长计算消耗时长
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanStepAggregate.PartSchedulePlanStep.ScheduleType">
            <summary>
            来源
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanStepAggregate.PartSchedulePlanStep.ReportResult">
            <summary>
            报工结果
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanStepAggregate.PartSchedulePlanStep.ReportDate">
            <summary>
            报工时间
            </summary>
        </member>
        <member name="T:Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulAggregate.PlanMaterial">
            <summary>
            计划物料信息
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulAggregate.PlanMaterial.SchedulDate">
            <summary>
            排产日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulAggregate.PlanMaterial.OrderNumber">
            <summary>
            订单编号
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulAggregate.PlanMaterial.ProductName">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulAggregate.PlanMaterial.ProductQty">
            <summary>
            产品数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulAggregate.PlanMaterial.MaterialName">
            <summary>
            物料名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulAggregate.PlanMaterial.MaterialQty">
            <summary>
            物料数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulAggregate.PlanMaterial.ExeNum">
            <summary>
            执行次数
            </summary>
        </member>
        <member name="T:Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulAggregate.PlanProductionSchedule">
            <summary>
            排产计划表
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulAggregate.PlanProductionSchedule.SchedulCode">
            <summary>
            排产编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulAggregate.PlanProductionSchedule.PlanCode">
            <summary>
            计划编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulAggregate.PlanProductionSchedule.PlanStart">
            <summary>
            计划开始日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulAggregate.PlanProductionSchedule.PlanEnd">
            <summary>
            计划结束日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulAggregate.PlanProductionSchedule.Sorts">
            <summary>
            排序方式
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulAggregate.PlanProductionSchedule.PreRule">
            <summary>
            前置规则
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulAggregate.PlanProductionSchedule.LCode">
            <summary>
            产线编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulAggregate.PlanProductionSchedule.Status">
            <summary>
            计划状态 0=待生效;1=生效;2=作废
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulAggregate.PlanProductionSchedule.PlanType">
            <summary>
            计划类型
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.StockMaterial.StockMaterial.Mcode">
            <summary>
            物料编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.StockMaterial.StockMaterial.Qty">
            <summary>
            库存数量(可用库存）
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.Aggregates.StockMaterial.StockMaterial.Prelockqty">
            <summary>
            预锁数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Domain.AggregatesPartOrderMaterialAggregate.PartOrderMaterial.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="M:Adnc.Huatek.Aps.Domain.RepositoryExtension.GetMaterialsBySqlAsync``1(Adnc.Infra.IRepositories.IEfRepository{Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulStepDetailsAggregate.PlanProductionSchedulStepDetails},System.String)">
            <summary>
             通过sql查询
            </summary>
            <typeparam name="TResult"></typeparam>
            <returns></returns>
            <exception cref="T:System.NullReferenceException"></exception>
        </member>
        <member name="T:Adnc.Huatek.Aps.Domain.Comm.Rules.ProductionRule">
            <summary>
            创建规则引擎
            </summary>
        </member>
        <member name="T:Adnc.Huatek.Aps.Domain.Services.BasOrderManagement">
            <summary>
            客户订单
            </summary>
        </member>
        <member name="M:Adnc.Huatek.Aps.Domain.Services.BasOrderManagement.FinishedAsync(System.Collections.Generic.List{System.Int64})">
            <summary>
            校验订单是否可以更新为已完成
            </summary>
            <param name="Status"></param>
            <param name="Priority"></param>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="T:Adnc.Huatek.Aps.Domain.Services.PartPlanPendingManagement">
            <summary>
            待排产计划
            </summary>
        </member>
        <member name="T:Adnc.Demo.Ord.Domain.Aggregates.OrderAggregate.SourceTypeCodes">
            <summary>
            订单状态枚举
            </summary>
        </member>
    </members>
</doc>
