﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Cache
{
    public class CacheService : AbstractCacheService, ICachePreheatable
    {
        public CacheService(Lazy<ICacheProvider> cacheProvider, Lazy<IServiceProvider> serviceProvider)
    : base(cacheProvider, serviceProvider)
        {
        }
        public override async Task PreheatAsync() => await Task.CompletedTask;
    }
}
