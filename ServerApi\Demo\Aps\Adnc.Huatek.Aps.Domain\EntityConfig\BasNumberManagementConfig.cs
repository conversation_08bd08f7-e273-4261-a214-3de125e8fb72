﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasNumberManagementAggregate;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class BasNumberManagementConfig : AbstractEntityTypeConfiguration<BasNumberManagement>
    {
        public override void Configure(EntityTypeBuilder<BasNumberManagement> builder)
        {
            base.Configure(builder);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.Name).HasColumnName("name");
            builder.Property(x => x.SimpleName).HasColumnName("simplename");
            builder.Property(x => x.CurrentNumber).HasColumnName("currentnumber");
            builder.Property(x => x.StartNumber).HasColumnName("startnumber");
            builder.Property(x => x.IdentityNumber).HasColumnName("identitynumber");
            builder.Property(x => x.IsContainerDate).HasColumnName("iscontainerdate");
            builder.Property(x => x.TimeFormat).HasColumnName("timeformat");
            builder.Property(x => x.Description).HasColumnName("description");
            builder.Property(x => x.NumberFormat).HasColumnName("numberformat");
            builder.Property(x => x.StartTarget).HasColumnName("starttarget");
            builder.Property(x => x.IsRound).HasColumnName("isround");

            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.CreateBy).HasColumnName("createby");
            builder.Property(x => x.CreateTime).HasColumnName("createtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
        }
    }
}
