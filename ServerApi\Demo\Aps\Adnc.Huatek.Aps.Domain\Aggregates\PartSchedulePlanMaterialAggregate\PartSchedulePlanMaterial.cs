﻿using Adnc.Infra.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanMaterialAggregate
{
    public class PartSchedulePlanMaterial : EfFullAuditEntity
    {

        /// <summary>
        /// 排产计划编码
        /// </summary>
        public long Id { get; set; }
        /// <summary>
        /// 排产计划编码
        /// </summary>
        public string? ScheduleCode { get; set; }

        /// <summary>
        /// 生产计划编码
        /// </summary>
        public string? StepCode { get; set; }


        /// <summary>
        /// 物料编码
        /// </summary>
        public string? Mcode { get; set; }

        /// <summary>
        /// 需求时间
        /// </summary>
        public DateTime NeedDate { get; set; }

        /// <summary>
        /// 生产计划编码
        /// </summary>
        public decimal? Qty { get; set; }
    }

   
}
