<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Adnc.Demo.Maint.Api</name>
    </assembly>
    <members>
        <member name="T:Adnc.Demo.Maint.Api.Controllers.CfgController">
            <summary>
            配置管理
            </summary>
        </member>
        <member name="M:Adnc.Demo.Maint.Api.Controllers.CfgController.CreateAsync(Adnc.Demo.Maint.Application.Dtos.CfgCreationDto)">
            <summary>
            新增配置
            </summary>
            <param name="input"><see cref="T:Adnc.Demo.Maint.Application.Dtos.CfgCreationDto"/></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Demo.Maint.Api.Controllers.CfgController.UpdateAsync(System.Int64,Adnc.Demo.Maint.Application.Dtos.CfgUpdationDto)">
            <summary>
            更新配置
            </summary>
            <param name="id">id</param>
            <param name="input"><see cref="T:Adnc.Demo.Maint.Application.Dtos.CfgUpdationDto"/></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Demo.Maint.Api.Controllers.CfgController.DeleteAsync(System.Int64)">
            <summary>
            删除配置节点
            </summary>
            <param name="id">节点id</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Demo.Maint.Api.Controllers.CfgController.GetAsync(System.Int64)">
            <summary>
            获取单个配置节点
            </summary>
            <param name="id">节点id</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Demo.Maint.Api.Controllers.CfgController.GetPagedAsync(Adnc.Demo.Maint.Application.Dtos.CfgSearchPagedDto)">
            <summary>
            获取配置列表
            </summary>
            <param name="search"><see cref="T:Adnc.Demo.Maint.Application.Dtos.CfgSearchPagedDto"/></param>
            <returns><see cref="T:Adnc.Shared.Application.Contracts.Dtos.PageModelDto`1"/></returns>
        </member>
        <member name="T:Adnc.Demo.Maint.Api.Controllers.DictController">
            <summary>
            字典管理
            </summary>
        </member>
        <member name="M:Adnc.Demo.Maint.Api.Controllers.DictController.CreateAsync(Adnc.Demo.Maint.Application.Dtos.DictCreationDto)">
            <summary>
            新增字典
            </summary>
            <param name="input"><see cref="T:Adnc.Demo.Maint.Application.Dtos.DictCreationDto"/></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Demo.Maint.Api.Controllers.DictController.UpdateAsync(System.Int64,Adnc.Demo.Maint.Application.Dtos.DictUpdationDto)">
            <summary>
            修改字典
            </summary>
            <param name="id">id</param>
            <param name="input"><see cref="T:Adnc.Demo.Maint.Application.Dtos.DictUpdationDto"/></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Demo.Maint.Api.Controllers.DictController.DeleteAsync(System.Int64)">
            <summary>
            删除字典
            </summary>
            <param name="id">字典ID</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Demo.Maint.Api.Controllers.DictController.GetListAsync(Adnc.Demo.Maint.Application.Dtos.DictSearchDto)">
            <summary>
            获取字典列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Demo.Maint.Api.Controllers.DictController.GetAsync(System.Int64)">
            <summary>
            获取单个字典数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Demo.Maint.Api.Controllers.DictController.GetDetail(Adnc.Demo.Maint.Application.Dtos.DictSearchDetailDto)">
            <summary>
            查询单个字典详情
            </summary>
            <returns></returns>
        </member>
        <member name="T:Adnc.Demo.Maint.WebApi.Controllers.LogController">
            <summary>
            日志管理
            </summary>
        </member>
        <member name="M:Adnc.Demo.Maint.WebApi.Controllers.LogController.GetOpsLogsPaged(Adnc.Demo.Maint.Application.Dtos.LogSearchPagedDto)">
            <summary>
            查询操作日志
            </summary>
            <param name="searchDto">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Demo.Maint.WebApi.Controllers.LogController.GetUserOpsLogsPagedAsync(Adnc.Demo.Maint.Application.Dtos.LogSearchPagedDto)">
            <summary>
            查询用户操作日志
            </summary>
            <param name="searchDto">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Demo.Maint.WebApi.Controllers.LogController.GetLoginLogsPagedAsync(Adnc.Demo.Maint.Application.Dtos.LogSearchPagedDto)">
            <summary>
            查询登录日志
            </summary>
            <param name="searchDto">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Demo.Maint.WebApi.Controllers.LogController.GetNlogLogsPagedAsync(Adnc.Demo.Maint.Application.Dtos.LogSearchPagedDto)">
            <summary>
            查询Nlog日志
            </summary>
            <param name="searchDto">查询条件</param>
            <returns></returns>
        </member>
        <member name="T:Adnc.Demo.Maint.WebApi.Controllers.NoticeController">
            <summary>
            通知管理
            </summary>
        </member>
        <member name="M:Adnc.Demo.Maint.WebApi.Controllers.NoticeController.GetList(Adnc.Demo.Maint.Application.Dtos.NoticeSearchDto)">
            <summary>
            获取通知消息列表
            </summary>
            <param name="search"><see cref="T:Adnc.Demo.Maint.Application.Dtos.NoticeSearchDto"/></param>
            <returns></returns>
        </member>
        <member name="T:Adnc.Demo.Shared.Rpc.Grpc.Services.MaintgrpcReflection">
            <summary>Holder for reflection information generated from services/maintgrpc.proto</summary>
        </member>
        <member name="P:Adnc.Demo.Shared.Rpc.Grpc.Services.MaintgrpcReflection.Descriptor">
            <summary>File descriptor for services/maintgrpc.proto</summary>
        </member>
        <member name="P:Adnc.Demo.Shared.Rpc.Grpc.Services.MaintGrpc.Descriptor">
            <summary>Service descriptor</summary>
        </member>
        <member name="T:Adnc.Demo.Shared.Rpc.Grpc.Services.MaintGrpc.MaintGrpcBase">
            <summary>Base class for server-side implementations of MaintGrpc</summary>
        </member>
        <member name="M:Adnc.Demo.Shared.Rpc.Grpc.Services.MaintGrpc.BindService(Adnc.Demo.Shared.Rpc.Grpc.Services.MaintGrpc.MaintGrpcBase)">
            <summary>Creates service definition that can be registered with a server</summary>
            <param name="serviceImpl">An object implementing the server-side handling logic.</param>
        </member>
        <member name="M:Adnc.Demo.Shared.Rpc.Grpc.Services.MaintGrpc.BindService(Grpc.Core.ServiceBinderBase,Adnc.Demo.Shared.Rpc.Grpc.Services.MaintGrpc.MaintGrpcBase)">
            <summary>Register service method with a service binder with or without implementation. Useful when customizing the service binding logic.
            Note: this method is part of an experimental API that can change or be removed without any prior notice.</summary>
            <param name="serviceBinder">Service methods will be bound by calling <c>AddMethod</c> on this object.</param>
            <param name="serviceImpl">An object implementing the server-side handling logic.</param>
        </member>
    </members>
</doc>
