﻿using Adnc.Huatek.Aps.Application.Services;
using Adnc.Huatek.Aps.Application.Subscribers;
//using Adnc.Huatek.Aps.Application.Subscribers;
using Adnc.Huatek.Aps.Domain.EntityConfig;
using Adnc.Shared.Application.Registrar;
using Adnc.Shared.Rpc.Http.Services;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application
{
    public class ApsApplicationDependencyRegistrar : AbstractApplicationDependencyRegistrar
    {
       
        public override Assembly ApplicationLayerAssembly => Assembly.GetExecutingAssembly();

        public override Assembly ContractsLayerAssembly => typeof(ITestAppService).Assembly;

        public override Assembly RepositoryOrDomainLayerAssembly => typeof(EntityInfo).Assembly;

  
        public ApsApplicationDependencyRegistrar(IServiceCollection services) : base(services)
        {
           
        }
        public override void AddAdnc()
        {
            AddApplicaitonDefault();
            AddDomainSerivces<IDomainService>();
            
            //rpc-rest
            var restPolicies = PollyStrategyEnable ? this.GenerateDefaultRefitPolicies() : new();
            AddRestClient<IAuthRestClient>(ServiceAddressConsts.AdncDemoAuthService, restPolicies);
            AddRestClient<IUsrRestClient>(ServiceAddressConsts.AdncDemoUsrService, restPolicies);
            AddRestClient<IMaintRestClient>(ServiceAddressConsts.AdncDemoMaintService, restPolicies);
            //rpc-event
            //AddCapEventBus<CapEventSubscriber>();
        }
      // protected override void AddEfCoreContext() => Services.AddEntityFrameworkMySql();
    }
} 
