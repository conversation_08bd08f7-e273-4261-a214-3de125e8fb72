﻿using Adnc.Demo.Shared.Const;
using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Application.Services;
using Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate;
using Adnc.Shared.Application.Contracts.ResultModels;
using Adnc.Shared.WebApi.Authorization;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Net.Http.Headers;
using Npoi.Mapper;
using NPOI.OpenXml4Net.OPC.Internal;
using Z.Expressions;

namespace Adnc.Huatek.Aps.Api.Controllers
{
    /// <summary>
    /// 客户订单
    /// </summary>
    [Route($"{RouteConsts.ApsRoot}/Orders")]
    [ApiController]
    [AllowAnonymous]
    public class BasOrderController : AdncControllerBase
    {
        private readonly IBasOrderService _orderSrv;

        public BasOrderController(IBasOrderService orderSrv) => _orderSrv = orderSrv;


        /// <summary>
        /// 获取订单分页信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("page")]
        public async Task<ActionResult<PageModelDto<BasOrderDto>>> GetPagedAsync([FromBody] BasOrderPagedDto search)
            => await _orderSrv.GetPagedAsync(search);


        /// <summary>
        /// 获取订单分页信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("getall")]
        public async Task<ActionResult<ResultJson>> GetAllAsync([FromBody] BasOrderPagedDto search)
            => await _orderSrv.GetAllAsync(search);

        /// <summary>
        /// 新增订单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("add")]
        [AllowAnonymous]
        public async Task<ActionResult<long>> CreateAsync([FromBody] BasOrderDto input)
           => CreatedResult(await _orderSrv.CreateAsync(input));

        /// <summary>
        /// 更新订单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("edit")]
        public async Task<ActionResult> UpdateAsync([FromBody] BasOrderDto input)
           => Result(await _orderSrv.UpdateAsync(input));


        /// <summary>
        /// 删除订单
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpDelete("delete/{id}")]
        public async Task<ActionResult> DeleteAsync([FromRoute] long id)
          => Result(await _orderSrv.DeleteAsync(id));

        /// <summary>
        /// 根据ID获取订单
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getbyid/{id}")]
        public async Task<ActionResult<ResultJson>> GetByIdAsync([FromRoute] long id)
            => await _orderSrv.GetByIdAsync(id);

        /// <summary>
        /// 根据订单Id初始化新增生产计划页面
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getdetailsbyid/{id}")]
        public async Task<ActionResult<PartPlanPendingDto>> GetByOrderIdAsync([FromRoute] long id)
            => await _orderSrv.GetDetailsByIdAsync(id);

        /// <summary>
        /// 更新订单为完成
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("finish")]
        public async Task<ActionResult> FinishedAsync([FromBody] FinishedOrders input)
           => Result(await _orderSrv.FinishedAsync(input));

        /// <summary>
        /// 导出订单
        /// </summary>
        /// <param name="queryParamDto"></param>
        /// <returns></returns>
        [HttpPost("exportorders")]
        public IActionResult ExportOrdersAsync([FromBody] BasOrderPagedDto search)
        {
            var filename = $"Orders_{DateTime.Now.ToString("yyyyMMddHHmmss")}.xlsx";
            var stream = _orderSrv.ExportOrders(search);

            Response.Headers[HeaderNames.ContentDisposition] = new ContentDispositionHeaderValue("attachment") { FileName = filename }.ToString();
            return new FileStreamResult(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        }

        /// <summary>
        /// 下载导入模板
        /// </summary>
        /// <returns></returns>
        [HttpPost("downloadtemplate")]
        public ActionResult<ResultJson> Download()
        {
            var path=  AppDomain.CurrentDomain.BaseDirectory;
            var filePath = path + "Template\\订单导入模板.xlsx";
            if (!System.IO.File.Exists(filePath))
            {
                return new JsonResult("模板不存在");
            }
            return File(new FileStream(filePath, FileMode.Open), "application/octet-stream", "订单导入模板.xlsx");
        }
        /// <summary>
        /// 导入订单
        /// </summary>
        /// <param name="formFile"></param>
        /// <returns></returns>
        [HttpPost("importOrders")]
        public async Task<ActionResult<ResultJson>> ImportOrders(IFormFile formFile)
        {
            //通过上传文件流初始化Mapper
            var mapper = new Mapper(formFile.OpenReadStream());
            //读取sheet1的数据
            var orders = mapper.Take<ImportOrderDto>("Sheet1").Select(i => i.Value).ToList();
            var lists = orders.Where(x => x.SaleNumber != null).ToList();
            var result = await _orderSrv.ImportOrdersAsync(lists);
            return result;
        }

        /// <summary>
        /// 根据订单Id初始化订单详情页面
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getdetails/{id}")]
        public async Task<ActionResult<OrderDetailDto>> GetOrderDetailAsync([FromRoute] long id)
            => await _orderSrv.GetOrderDetailAsync(id);
    }
}
