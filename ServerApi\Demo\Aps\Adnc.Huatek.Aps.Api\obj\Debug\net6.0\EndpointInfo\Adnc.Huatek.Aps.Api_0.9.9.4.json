{"openapi": "3.0.1", "info": {"title": "aps-api", "version": "*******"}, "paths": {"/aps/api/bom/page": {"post": {"tags": ["BasBom"], "summary": "获取Bom分页信息", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BomPagedDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BomPagedDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BomPagedDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BasBomDtoPageModelDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BasBomDtoPageModelDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasBomDtoPageModelDto"}}}}}}}, "/aps/api/bom/add": {"post": {"tags": ["BasBom"], "summary": "新增Bom", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasBomDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasBomDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasBomDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/aps/api/bom/edit": {"post": {"tags": ["BasBom"], "summary": "更新Bom", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasBomDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasBomDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasBomDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/bom/delete/{productCode}": {"delete": {"tags": ["BasBom"], "summary": "删除Bom", "parameters": [{"name": "productCode", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/aps/api/bom/getbyproductcode/{productcode}": {"get": {"tags": ["BasBom"], "summary": "根据ID获取Bom", "parameters": [{"name": "productcode", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BasBomDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BasBomDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasBomDto"}}}}}}}, "/aps/api/bom/getmaterialbyproductcode/{productCode}": {"get": {"tags": ["BasBom"], "summary": "根据产品code获取bom中物料列表", "parameters": [{"name": "productCode", "in": "path", "description": "", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasBomListDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasBomListDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasBomListDto"}}}}}}}}, "/aps/api/classes/page": {"post": {"tags": ["BasClasses"], "summary": "获取班次分页信息", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClassesPagedDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ClassesPagedDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ClassesPagedDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BasClassesDtoPageModelDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BasClassesDtoPageModelDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasClassesDtoPageModelDto"}}}}}}}, "/aps/api/classes/getall": {"get": {"tags": ["BasClasses"], "summary": "获取班次信息", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasClassesDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasClassesDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasClassesDto"}}}}}}}}, "/aps/api/classes/add": {"post": {"tags": ["BasClasses"], "summary": "新增班次", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasClassesDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasClassesDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasClassesDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/aps/api/classes/edit": {"post": {"tags": ["BasClasses"], "summary": "更新班次", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasClassesDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasClassesDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasClassesDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/classes/delete/{id}": {"delete": {"tags": ["BasClasses"], "summary": "删除班次", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/aps/api/classes/getbyid/{id}": {"get": {"tags": ["BasClasses"], "summary": "根据ID获取班次", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BasClassesDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BasClassesDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasClassesDto"}}}}}}}, "/aps/api/classes/getclasses": {"get": {"tags": ["BasClasses"], "summary": "获取班次信息 工位模块弹框中选择班次", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ClassesDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ClassesDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ClassesDto"}}}}}}}}, "/aps/api/classes/changestatus/{status}": {"put": {"tags": ["BasClasses"], "summary": "批量启用", "parameters": [{"name": "status", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/device/getallasync": {"get": {"tags": ["BasDevice"], "summary": "获取所有信息", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasDeviceDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasDeviceDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasDeviceDto"}}}}}}}}, "/aps/api/device/createasync": {"post": {"tags": ["BasDevice"], "summary": "新增设备", "requestBody": {"description": "用户信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasDeviceDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasDeviceDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasDeviceDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/aps/api/device/deleteasync/{id}": {"delete": {"tags": ["BasDevice"], "summary": "删除设备", "parameters": [{"name": "id", "in": "path", "description": "设备ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/aps/api/device/updateasync": {"put": {"tags": ["BasDevice"], "summary": "修改设备", "requestBody": {"description": "用户信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasDeviceDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasDeviceDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasDeviceDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/device/batch/changestatus/{id}": {"put": {"tags": ["BasDevice"], "summary": "变更设备状态", "parameters": [{"name": "id", "in": "path", "description": "设备ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"description": "状态", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Int32SimpleDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32SimpleDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Int32SimpleDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/device/changestatus": {"put": {"tags": ["BasDevice"], "summary": "批量变更设备状态", "requestBody": {"description": "设备Ids与状态", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateStatusDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateStatusDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateStatusDto"}}}}, "responses": {"200": {"description": "Success"}}}, "post": {"tags": ["BasDevice"], "summary": "更新启用/禁用状态", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangeStatusDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangeStatusDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangeStatusDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/device/page": {"post": {"tags": ["BasDevice"], "summary": "获取设备列表分页", "requestBody": {"description": "查询条件", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasDevicePagedDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasDevicePagedDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasDevicePagedDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BasDeviceDtoPageModelDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BasDeviceDtoPageModelDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasDeviceDtoPageModelDto"}}}}}}}, "/aps/api/device/current/{id}": {"get": {"tags": ["BasDevice"], "summary": "获取单个设备详情信息", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}}}}}}, "/aps/api/device/calendar": {"post": {"tags": ["BasDevice"], "summary": "设备日历-获取单个设备日历", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasDeviceCalendarDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasDeviceCalendarDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasDeviceCalendarDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasDeviceCalendarResultDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasDeviceCalendarResultDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasDeviceCalendarResultDto"}}}}}}}}, "/aps/api/device/calendar/setpreserve": {"put": {"tags": ["BasDevice"], "summary": "设备日历-设置设备保养状态", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasDeviceCalendarDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasDeviceCalendarDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasDeviceCalendarDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/device/calendar/delpreserve": {"put": {"tags": ["BasDevice"], "summary": "设备日历-删除设备保养状态", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasDeviceCalendarDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasDeviceCalendarDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasDeviceCalendarDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/device/getdevicepreserve/{id}": {"post": {"tags": ["BasDevice"], "summary": "获取设备保养列表", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}}}}}}, "/aps/api/device/importasync": {"post": {"tags": ["BasDevice"], "summary": "新增物料", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"formFile": {"type": "string", "format": "binary"}}}, "encoding": {"formFile": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}}}}}}, "/aps/api/device/getallType": {"post": {"tags": ["BasDevice"], "summary": "获取设备列表分页", "requestBody": {"description": "查询条件", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasDevicePagedDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasDevicePagedDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasDevicePagedDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PartDeviceStateDtoPageModelDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PartDeviceStateDtoPageModelDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PartDeviceStateDtoPageModelDto"}}}}}}}, "/aps/api/device/getdevicestate": {"post": {"tags": ["BasDevice"], "summary": "获取设备列表分页", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/QueryDevStateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/QueryDevStateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/QueryDevStateDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeviceStateDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeviceStateDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeviceStateDto"}}}}}}}}, "/aps/api/device/currentdevicestate/{id}": {"get": {"tags": ["BasDevice"], "summary": "获取单个设备状态详情信息", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}}}}}}, "/aps/api/device/editdevicestate": {"put": {"tags": ["BasDevice"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PartDeviceStateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PartDeviceStateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PartDeviceStateDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/device/deleteDeviceState/{id}": {"delete": {"tags": ["BasDevice"], "summary": "删除设备", "parameters": [{"name": "id", "in": "path", "description": "设备ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/aps/api/device/importdevicetstateasync": {"post": {"tags": ["BasDevice"], "summary": "新增物料", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"formFile": {"type": "string", "format": "binary"}}}, "encoding": {"formFile": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}}}}}}, "/aps/api/deviceclass/getallasync": {"get": {"tags": ["BasDeviceClass"], "summary": "获取所有设备班次信息", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasDeviceClassDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasDeviceClassDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasDeviceClassDto"}}}}}}}}, "/aps/api/deviceclass/createasync": {"post": {"tags": ["BasDeviceClass"], "summary": "新增设备班次", "requestBody": {"description": "用户信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasDeviceClassDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasDeviceClassDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasDeviceClassDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/aps/api/deviceclass/deleteasync/{id}": {"delete": {"tags": ["BasDeviceClass"], "summary": "删除设备班次", "parameters": [{"name": "id", "in": "path", "description": "设备ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/aps/api/deviceclass/updateasync": {"put": {"tags": ["BasDeviceClass"], "summary": "修改设备班次", "requestBody": {"description": "用户信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasDeviceClassDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasDeviceClassDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasDeviceClassDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/deviceclass/page": {"post": {"tags": ["BasDeviceClass"], "summary": "获取设备班次列表分页", "requestBody": {"description": "查询条件", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasDeviceClassPagedDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasDeviceClassPagedDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasDeviceClassPagedDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DeviceWorkingDateDtoPageModelDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DeviceWorkingDateDtoPageModelDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeviceWorkingDateDtoPageModelDto"}}}}}}}, "/aps/api/deviceclass/current/{id}": {"get": {"tags": ["BasDeviceClass"], "summary": "获取单个设备班次详情信息", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}}}}}}, "/aps/api/holiday/page": {"post": {"tags": ["BasHoliday"], "summary": "获取分页信息", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HolidayPagedDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/HolidayPagedDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/HolidayPagedDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BasHolidayDtoPageModelDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BasHolidayDtoPageModelDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasHolidayDtoPageModelDto"}}}}}}}, "/aps/api/holiday/getallasync": {"get": {"tags": ["BasHoliday"], "summary": "获取所有信息", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasHolidayDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasHolidayDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasHolidayDto"}}}}}}}}, "/aps/api/holiday/add": {"post": {"tags": ["BasHoliday"], "summary": "新增", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasHolidayDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasHolidayDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasHolidayDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/aps/api/holiday/edit": {"post": {"tags": ["BasHoliday"], "summary": "更新", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasHolidayDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasHolidayDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasHolidayDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/holiday/delete/{id}": {"delete": {"tags": ["BasHoliday"], "summary": "删除", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/aps/api/holiday/getbyid/{id}": {"get": {"tags": ["BasHoliday"], "summary": "根据ID获取", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BasHolidayDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BasHolidayDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasHolidayDto"}}}}}}}, "/aps/api/holiday/importholidays": {"post": {"tags": ["BasHoliday"], "summary": "导入", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"formFile": {"type": "string", "format": "binary"}}}, "encoding": {"formFile": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}}}}}}, "/aps/api/holiday/deleteall": {"delete": {"tags": ["BasHoliday"], "summary": "删除全部假期", "responses": {"200": {"description": "Success"}}}}, "/aps/api/holiday/getunplan": {"post": {"tags": ["BasHoliday"], "summary": "获取时间段内的非排产时间", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HolidayPagedDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/HolidayPagedDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/HolidayPagedDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/aps/api/line/page": {"post": {"tags": ["BasLine"], "summary": "获取产线分页信息", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LinePagedDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LinePagedDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LinePagedDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BasLineDtoPageModelDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BasLineDtoPageModelDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasLineDtoPageModelDto"}}}}}}}, "/aps/api/line/getallasync": {"get": {"tags": ["BasLine"], "summary": "获取所有信息", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasLineDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasLineDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasLineDto"}}}}}}}}, "/aps/api/line/add": {"post": {"tags": ["BasLine"], "summary": "新增产线", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasLineDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasLineDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasLineDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/aps/api/line/edit": {"post": {"tags": ["BasLine"], "summary": "更新产线", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasLineDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasLineDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasLineDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/line/changestatus/{status}": {"put": {"tags": ["BasLine"], "summary": "批量启用", "parameters": [{"name": "status", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/line/delete/{id}": {"delete": {"tags": ["BasLine"], "summary": "删除产线", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/aps/api/line/getbyid/{id}": {"get": {"tags": ["BasLine"], "summary": "根据ID获取产线", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BasLineDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BasLineDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasLineDto"}}}}}}}, "/aps/api/line/getlinebyproductcode/{productCode}": {"get": {"tags": ["BasLine"], "summary": "根据产品code获取产线列表", "parameters": [{"name": "productCode", "in": "path", "description": "", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductLineDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductLineDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductLineDto"}}}}}}}}, "/aps/api/material/getallasync": {"get": {"tags": ["BasMaterial"], "summary": "获取所有信息", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasMaterialDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasMaterialDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasMaterialDto"}}}}}}}}, "/aps/api/material/createasync": {"post": {"tags": ["BasMaterial"], "summary": "新增物料", "requestBody": {"description": "物料信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasMaterialDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasMaterialDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasMaterialDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/aps/api/material/deleteasync/{id}": {"delete": {"tags": ["BasMaterial"], "summary": "删除物料", "parameters": [{"name": "id", "in": "path", "description": "物料ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/aps/api/material/updateasync": {"put": {"tags": ["BasMaterial"], "summary": "修改物料", "requestBody": {"description": "物料信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasMaterialDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasMaterialDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasMaterialDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/material/batch/changestatus/{id}": {"put": {"tags": ["BasMaterial"], "summary": "变更物料状态", "parameters": [{"name": "id", "in": "path", "description": "物料ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"description": "状态", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Int32SimpleDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32SimpleDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Int32SimpleDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/material/changestatus": {"put": {"tags": ["BasMaterial"], "summary": "批量变更物料状态", "requestBody": {"description": "物料Ids与状态", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateStatusDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateStatusDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateStatusDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/material/page": {"post": {"tags": ["BasMaterial"], "summary": "获取物料列表分页", "requestBody": {"description": "查询条件", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasMaterialPagedDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasMaterialPagedDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasMaterialPagedDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BasMaterialDtoPageModelDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BasMaterialDtoPageModelDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasMaterialDtoPageModelDto"}}}}}}}, "/aps/api/material/current/{id}": {"get": {"tags": ["BasMaterial"], "summary": "获取单个物料详情信息", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}}}}}}, "/aps/api/material/changestatus/{status}": {"put": {"tags": ["BasMaterial"], "summary": "批量启用", "parameters": [{"name": "status", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/material/importasync": {"post": {"tags": ["BasMaterial"], "summary": "新增物料", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"formFile": {"type": "string", "format": "binary"}}}, "encoding": {"formFile": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}}}}}}, "/aps/api/number/page": {"post": {"tags": ["BasNumberManagement"], "summary": "获取编码分页数据", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasNumberManagementPagedDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasNumberManagementPagedDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasNumberManagementPagedDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BasNumberManagementDtoPageModelDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BasNumberManagementDtoPageModelDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasNumberManagementDtoPageModelDto"}}}}}}}, "/aps/api/number/add": {"post": {"tags": ["BasNumberManagement"], "summary": "新增", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasNumberManagementDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasNumberManagementDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasNumberManagementDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/aps/api/number/getbyid/{id}": {"get": {"tags": ["BasNumberManagement"], "summary": "通过id 获取单个编码信息", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BasNumberManagement"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BasNumberManagement"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasNumberManagement"}}}}}}}, "/aps/api/number/edit": {"post": {"tags": ["BasNumberManagement"], "summary": "修改", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasNumberManagementDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasNumberManagementDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasNumberManagementDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/number/delete/{id}": {"delete": {"tags": ["BasNumberManagement"], "summary": "删除", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/aps/api/number/getNumberBySimpleName/{simplename}": {"get": {"tags": ["BasNumberManagement"], "summary": "通过 名称简写 获取最新编码", "parameters": [{"name": "simplename", "in": "path", "description": "", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/aps/api/Orders/page": {"post": {"tags": ["BasOrder"], "summary": "获取订单分页信息", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasOrderPagedDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasOrderPagedDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasOrderPagedDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BasOrderDtoPageModelDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BasOrderDtoPageModelDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasOrderDtoPageModelDto"}}}}}}}, "/aps/api/Orders/getall": {"post": {"tags": ["BasOrder"], "summary": "获取订单分页信息", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasOrderPagedDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasOrderPagedDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasOrderPagedDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}}}}}}, "/aps/api/Orders/add": {"post": {"tags": ["BasOrder"], "summary": "新增订单", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasOrderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasOrderDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasOrderDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/aps/api/Orders/edit": {"post": {"tags": ["BasOrder"], "summary": "更新订单", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasOrderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasOrderDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasOrderDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/Orders/delete/{id}": {"delete": {"tags": ["BasOrder"], "summary": "删除订单", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/aps/api/Orders/getbyid/{id}": {"get": {"tags": ["BasOrder"], "summary": "根据ID获取订单", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}}}}}}, "/aps/api/Orders/getdetailsbyid/{id}": {"get": {"tags": ["BasOrder"], "summary": "根据订单Id初始化新增生产计划页面", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PartPlanPendingDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PartPlanPendingDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PartPlanPendingDto"}}}}}}}, "/aps/api/Orders/finish": {"post": {"tags": ["BasOrder"], "summary": "更新订单为完成", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FinishedOrders"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FinishedOrders"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FinishedOrders"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/Orders/exportorders": {"post": {"tags": ["BasOrder"], "summary": "导出订单", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasOrderPagedDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasOrderPagedDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasOrderPagedDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/Orders/downloadtemplate": {"post": {"tags": ["BasOrder"], "summary": "下载导入模板", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}}}}}}, "/aps/api/Orders/importOrders": {"post": {"tags": ["BasOrder"], "summary": "导入订单", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"formFile": {"type": "string", "format": "binary"}}}, "encoding": {"formFile": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}}}}}}, "/aps/api/Orders/getdetails/{id}": {"get": {"tags": ["BasOrder"], "summary": "根据订单Id初始化订单详情页面", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OrderDetailDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/OrderDetailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrderDetailDto"}}}}}}}, "/aps/api/product/page": {"post": {"tags": ["BasProduct"], "summary": "获取产品分页信息", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductPagedDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductPagedDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductPagedDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BasProductDtoPageModelDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BasProductDtoPageModelDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasProductDtoPageModelDto"}}}}}}}, "/aps/api/product/getallasync": {"get": {"tags": ["BasProduct"], "summary": "获取所有信息", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasProductDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasProductDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasProductDto"}}}}}}}}, "/aps/api/product/add": {"post": {"tags": ["BasProduct"], "summary": "新增产品", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasProductBomDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasProductBomDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasProductBomDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/aps/api/product/edit": {"post": {"tags": ["BasProduct"], "summary": "更新产品", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasProductBomDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasProductBomDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasProductBomDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/product/edit/stepmaterial": {"post": {"tags": ["BasProduct"], "summary": "更新产品工序物料", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasProductDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasProductDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasProductDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/product/delete/{id}": {"delete": {"tags": ["BasProduct"], "summary": "删除产品", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/aps/api/product/getbyid/{id}": {"get": {"tags": ["BasProduct"], "summary": "获取产品详细信息", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BasProductDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BasProductDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasProductDto"}}}}}}}, "/aps/api/product/edit/standardcapacity": {"post": {"tags": ["BasProduct"], "summary": "更新产品产能", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasProductDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasProductDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasProductDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/product/changestatus": {"post": {"tags": ["BasProduct"], "summary": "更新启用/禁用状态", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangeStatusDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangeStatusDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangeStatusDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/product/getmodulebyid/{id}": {"get": {"tags": ["BasProduct"], "summary": "获取产品详细信息", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BasProductDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BasProductDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasProductDto"}}}}}}}, "/aps/api/product/importasync": {"post": {"tags": ["BasProduct"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"formFile": {"type": "string", "format": "binary"}}}, "encoding": {"formFile": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}}}}}}, "/aps/api/relationship/createasync": {"post": {"tags": ["BasRelation"], "summary": "新增资源关系搭建", "requestBody": {"description": "资源关系搭建信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasRelationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasRelationDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasRelationDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/aps/api/relationship/deleteasync/{id}": {"delete": {"tags": ["BasRelation"], "summary": "删除资源关系搭建", "parameters": [{"name": "id", "in": "path", "description": "资源关系搭建ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/aps/api/relationship/updateasync": {"put": {"tags": ["BasRelation"], "summary": "修改资源关系搭建", "requestBody": {"description": "资源关系搭建信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasRelationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasRelationDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasRelationDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/relationship/page": {"post": {"tags": ["BasRelation"], "summary": "获取资源关系搭建列表分页", "requestBody": {"description": "查询条件", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RelationPagedDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RelationPagedDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RelationPagedDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BasRelationDtoPageModelDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BasRelationDtoPageModelDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasRelationDtoPageModelDto"}}}}}}}, "/aps/api/relationship/current/{id}": {"get": {"tags": ["BasRelation"], "summary": "获取单个资源关系搭建详情信息", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BasRelationDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BasRelationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasRelationDto"}}}}}}}, "/aps/api/station/getallasync": {"get": {"tags": ["BasStation"], "summary": "获取所有信息", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasStationDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasStationDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasStationDto"}}}}}}}}, "/aps/api/station/createasync": {"post": {"tags": ["BasStation"], "summary": "新增工位", "requestBody": {"description": "用户信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasStationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasStationDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasStationDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/aps/api/station/deleteasync/{id}": {"delete": {"tags": ["BasStation"], "summary": "删除工位", "parameters": [{"name": "id", "in": "path", "description": "工位ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/aps/api/station/updateasync": {"put": {"tags": ["BasStation"], "summary": "修改工位", "requestBody": {"description": "用户信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasStationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasStationDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasStationDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/station/batch/changestatus/{id}": {"put": {"tags": ["BasStation"], "summary": "变更工位状态", "parameters": [{"name": "id", "in": "path", "description": "工位ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"description": "状态", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Int32SimpleDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32SimpleDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Int32SimpleDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/station/changestatus": {"put": {"tags": ["BasStation"], "summary": "批量变更工位状态", "requestBody": {"description": "工位Ids与状态", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateStatusDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateStatusDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateStatusDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/station/page": {"post": {"tags": ["BasStation"], "summary": "获取工位列表分页", "requestBody": {"description": "查询条件", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasStationPagedDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasStationPagedDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasStationPagedDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BasStationDtoPageModelDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BasStationDtoPageModelDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasStationDtoPageModelDto"}}}}}}}, "/aps/api/station/list": {"post": {"tags": ["BasStation"], "summary": "按产线编码获取工位列表", "requestBody": {"description": "查询条件", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasStationSearchDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasStationSearchDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasStationSearchDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasStationDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasStationDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasStationDto"}}}}}}}}, "/aps/api/station/current/{id}": {"get": {"tags": ["BasStation"], "summary": "获取单个工位详情信息", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}}}}}}, "/aps/api/station/classes/{id}": {"get": {"tags": ["BasStation"], "summary": "获取工位中班次信息", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ClassesDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ClassesDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ClassesDto"}}}}}}}}, "/aps/api/station/changestatus/{status}": {"put": {"tags": ["BasStation"], "summary": "批量启用", "parameters": [{"name": "status", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/Steps/page": {"post": {"tags": ["BasStep"], "summary": "获取工序分页信息", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StepPagedDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StepPagedDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StepPagedDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BasStepDtoPageModelDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BasStepDtoPageModelDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasStepDtoPageModelDto"}}}}}}}, "/aps/api/Steps/getall": {"get": {"tags": ["BasStep"], "summary": "获取工序分页信息", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}}}}}}, "/aps/api/Steps/add": {"post": {"tags": ["BasStep"], "summary": "新增工序", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasStepDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasStepDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasStepDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/aps/api/Steps/edit": {"post": {"tags": ["BasStep"], "summary": "更新工序", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasStepDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasStepDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasStepDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/Steps/delete/{id}": {"delete": {"tags": ["BasStep"], "summary": "删除工序", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/aps/api/Steps/getbyid/{id}": {"get": {"tags": ["BasStep"], "summary": "根据ID获取班次", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}}}}}}, "/aps/api/Steps/changestatus": {"post": {"tags": ["BasStep"], "summary": "更新启用/禁用状态", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangeStatusDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangeStatusDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangeStatusDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/Steps/importasync": {"post": {"tags": ["BasStep"], "summary": "新增物料", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"formFile": {"type": "string", "format": "binary"}}}, "encoding": {"formFile": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}}}}}}, "/aps/api/Technologys/getall": {"get": {"tags": ["BasTechnology"], "summary": "获取工艺信息", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasTechnologyDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasTechnologyDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasTechnologyDto"}}}}}}}}, "/aps/api/Technologys/page": {"post": {"tags": ["BasTechnology"], "summary": "获取工序分页信息", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TechnologyPagedDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TechnologyPagedDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TechnologyPagedDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BasTechnologyDtoPageModelDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BasTechnologyDtoPageModelDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasTechnologyDtoPageModelDto"}}}}}}}, "/aps/api/Technologys/add": {"post": {"tags": ["BasTechnology"], "summary": "新增工艺", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasTechnologyDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasTechnologyDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasTechnologyDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/aps/api/Technologys/edit": {"post": {"tags": ["BasTechnology"], "summary": "更新工序", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasTechnologyDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasTechnologyDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasTechnologyDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/Technologys/delete/{id}": {"delete": {"tags": ["BasTechnology"], "summary": "删除工序", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/aps/api/Technologys/getbyid/{id}": {"get": {"tags": ["BasTechnology"], "summary": "获取工序详细信息", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BasTechnologyDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BasTechnologyDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasTechnologyDto"}}}}}}}, "/aps/api/Technologys/detailsbytcode/{tCode}": {"get": {"tags": ["BasTechnology"], "summary": "通过工艺编码获取工序详细信息", "parameters": [{"name": "tCode", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BasTechnologyDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BasTechnologyDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasTechnologyDto"}}}}}}}, "/aps/api/Technologys/changestatus": {"post": {"tags": ["BasTechnology"], "summary": "更新启用/禁用状态", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangeStatusDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangeStatusDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangeStatusDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/worksjop/getallasync": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "获取所有信息", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasWorksjopDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasWorksjopDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BasWorksjopDto"}}}}}}}}, "/aps/api/worksjop/getworkshop": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "获取所有信息", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {}}}, "application/json": {"schema": {"type": "array", "items": {}}}, "text/json": {"schema": {"type": "array", "items": {}}}}}}}}, "/aps/api/worksjop/createasync": {"post": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "新增车间", "requestBody": {"description": "车间信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasWorksjopDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasWorksjopDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasWorksjopDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/aps/api/worksjop/deleteasync/{id}": {"delete": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "删除车间", "parameters": [{"name": "id", "in": "path", "description": "车间ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/aps/api/worksjop/updateasync": {"put": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "修改车间", "requestBody": {"description": "车间信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasWorksjopDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasWorksjopDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasWorksjopDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/worksjop/batch/changestatus/{id}": {"put": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "变更车间状态", "parameters": [{"name": "id", "in": "path", "description": "车间ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"description": "状态", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Int32SimpleDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32SimpleDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Int32SimpleDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/worksjop/changestatus": {"put": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "批量变更车间状态", "requestBody": {"description": "车间Ids与状态", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateStatusDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateStatusDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateStatusDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/worksjop/page": {"post": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "获取车间列表分页", "requestBody": {"description": "查询条件", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasStationPagedDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasStationPagedDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BasStationPagedDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BasWorksjopDtoPageModelDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BasWorksjopDtoPageModelDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BasWorksjopDtoPageModelDto"}}}}}}}, "/aps/api/worksjop/current/{id}": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "获取单个车间详情信息", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}}}}}}, "/aps/api/home/<USER>/{status}": {"get": {"tags": ["HomePage"], "summary": "获取订单数量", "parameters": [{"name": "status", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int32"}}, "application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/aps/api/home/<USER>": {"get": {"tags": ["HomePage"], "summary": "首页计划数量统计", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PlanNumByDateDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PlanNumByDateDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PlanNumByDateDto"}}}}}}}}, "/aps/api/outerSystem/mescall": {"post": {"tags": ["OuterSystem"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InputParamDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InputParamDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/InputParamDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/order/page": {"post": {"tags": ["PartOrder"], "summary": "获取订单分页信息", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PartOrderPagedDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PartOrderPagedDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PartOrderPagedDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PartOrderDtoPageModelDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PartOrderDtoPageModelDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PartOrderDtoPageModelDto"}}}}}}}, "/aps/api/order/add": {"post": {"tags": ["PartOrder"], "summary": "新增订单", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PartOrderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PartOrderDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PartOrderDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/aps/api/order/edit": {"post": {"tags": ["PartOrder"], "summary": "更新订单", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PartOrderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PartOrderDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PartOrderDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/order/delete/{id}": {"delete": {"tags": ["PartOrder"], "summary": "删除订单", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/aps/api/order/getbyid/{id}": {"get": {"tags": ["PartOrder"], "summary": "根据ID获取订单", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PartOrderDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PartOrderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PartOrderDto"}}}}}}}, "/aps/api/PendingPlans/page": {"post": {"tags": ["PartPlanPending"], "summary": "获取待排产计划分页信息", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PartPlanPendingPagedDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PartPlanPendingPagedDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PartPlanPendingPagedDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PartPlanPendingResultDtoPageModelDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PartPlanPendingResultDtoPageModelDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PartPlanPendingResultDtoPageModelDto"}}}}}}}, "/aps/api/PendingPlans/getall": {"get": {"tags": ["PartPlanPending"], "summary": "获取待排产计划分页信息", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}}}}}}, "/aps/api/PendingPlans/add": {"post": {"tags": ["PartPlanPending"], "summary": "新增待排产计划", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PartPlanPendingDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PartPlanPendingDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PartPlanPendingDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/aps/api/PendingPlans/edit": {"post": {"tags": ["PartPlanPending"], "summary": "更新待排产计划", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PartPlanPendingDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PartPlanPendingDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PartPlanPendingDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/PendingPlans/delete/{id}": {"delete": {"tags": ["PartPlanPending"], "summary": "删除待排产计划", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/aps/api/PendingPlans/getbyid/{id}": {"get": {"tags": ["PartPlanPending"], "summary": "根据ID获取待排产计划", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}}}}}}, "/aps/api/PendingPlans/getPlanPendingById/{id}": {"get": {"tags": ["PartPlanPending"], "summary": "根据ID获取待排产计划", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}}}}}}, "/aps/api/SchedulePlan/CreateSchedule": {"post": {"tags": ["PartSchedulePlan"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PartSchedulePlanDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PartSchedulePlanDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PartSchedulePlanDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}}}}}}, "/aps/api/SchedulePlan/TrialSchedule": {"post": {"tags": ["PartSchedulePlan"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PartSchedulePlanDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PartSchedulePlanDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PartSchedulePlanDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PlanGanttDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PlanGanttDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PlanGanttDto"}}}}}}}, "/aps/api/SchedulePlan/changeplan": {"post": {"tags": ["PartSchedulePlan"], "summary": "拖拉拽后保存接口", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PartSchedulePlanDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PartSchedulePlanDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PartSchedulePlanDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}}}}}}, "/aps/api/SchedulePlan/page": {"post": {"tags": ["PartSchedulePlan"], "summary": "列表分页", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PartSchedulPlanPageDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PartSchedulPlanPageDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PartSchedulPlanPageDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PartSchedulePlanDtoPageModelDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PartSchedulePlanDtoPageModelDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PartSchedulePlanDtoPageModelDto"}}}}}}}, "/aps/api/SchedulePlan/changeState": {"post": {"tags": ["PartSchedulePlan"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SchedulStatusNew"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SchedulStatusNew"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SchedulStatusNew"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}}}}}}, "/aps/api/SchedulePlan/CheckSourceConfilct": {"post": {"tags": ["PartSchedulePlan"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SchedulStatusNew"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SchedulStatusNew"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SchedulStatusNew"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringAppSrvResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringAppSrvResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringAppSrvResult"}}}}}}}, "/aps/api/SchedulePlan/CheckSource": {"post": {"tags": ["PartSchedulePlan"], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PartSchedulePlanStepDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PartSchedulePlanStepDto"}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PartSchedulePlanStepDto"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}}}}}}, "/aps/api/SchedulePlan/deviceGantt": {"post": {"tags": ["PartSchedulePlan"], "summary": "设备甘特图\r\n设备甘特图", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PartSchedulPlanSourcePageDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PartSchedulPlanSourcePageDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PartSchedulPlanSourcePageDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeviceGanttDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeviceGanttDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeviceGanttDto"}}}}}}}}, "/aps/api/SchedulePlan/ChangeProgress": {"post": {"tags": ["PartSchedulePlan"], "summary": "报工保存接口", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PartSchedulePlanStepReportDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PartSchedulePlanStepReportDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PartSchedulePlanStepReportDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AppSrvResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AppSrvResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AppSrvResult"}}}}}}}, "/aps/api/SchedulePlan/QuerySourceConflict": {"post": {"tags": ["PartSchedulePlan"], "summary": "分析资源冲突列表", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QuerySourceConflict"}}, "text/json": {"schema": {"$ref": "#/components/schemas/QuerySourceConflict"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/QuerySourceConflict"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}}}}}}, "/aps/api/SchedulePlan/batchDel": {"post": {"tags": ["PartSchedulePlan"], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/aps/api/Schedul/page": {"post": {"tags": ["PlanProductionSchedul"], "summary": "获取生产计划分页信息", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PlanProductionSchedulPageDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PlanProductionSchedulPageDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PlanProductionSchedulPageDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PlanProductionSchedulResultDtoPageModelDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PlanProductionSchedulResultDtoPageModelDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PlanProductionSchedulResultDtoPageModelDto"}}}}}}}, "/aps/api/Schedul/getdetails/{id}": {"get": {"tags": ["PlanProductionSchedul"], "summary": "获取生产计划详情信息", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PartSchedulePlanDtoAppSrvResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PartSchedulePlanDtoAppSrvResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PartSchedulePlanDtoAppSrvResult"}}}}}}}, "/aps/api/Schedul/gantt/device/{scheduleCode}": {"get": {"tags": ["PlanProductionSchedul"], "summary": "按排产编码获取设备甘特图", "parameters": [{"name": "scheduleCode", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ScheduleDeviceGanttItem"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ScheduleDeviceGanttItem"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ScheduleDeviceGanttItem"}}}}}}}}, "/aps/api/Schedul/gantt/productplan/{id}": {"get": {"tags": ["PlanProductionSchedul"], "summary": "获取产品排产计划甘特图", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PlanGanttDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PlanGanttDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PlanGanttDto"}}}}}}}, "/aps/api/Schedul/gantt/orderplan": {"post": {"tags": ["PlanProductionSchedul"], "summary": "获取产品订单排产计划甘特图", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductionSchedulGanttDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductionSchedulGanttDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductionSchedulGanttDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PlanProductionSchedulGanttForOrderDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PlanProductionSchedulGanttForOrderDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PlanProductionSchedulGanttForOrderDto"}}}}}}}}, "/aps/api/Schedul/gantt/orderproductplan": {"post": {"tags": ["PlanProductionSchedul"], "summary": "获取订单产品已排产计划甘特图", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderProdcutGanttDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrderProdcutGanttDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OrderProdcutGanttDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PlanProductionSchedulGanttNewResultDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PlanProductionSchedulGanttNewResultDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PlanProductionSchedulGanttNewResultDto"}}}}}}}}, "/aps/api/Schedul/gantt/plan": {"get": {"tags": ["PlanProductionSchedul"], "summary": "按计划编码获取生产计划甘特图", "parameters": [{"name": "planCode", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PlanProductionSchedulGanttNewResultForPlanDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PlanProductionSchedulGanttNewResultForPlanDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PlanProductionSchedulGanttNewResultForPlanDto"}}}}}}}}, "/aps/api/Schedul/getmaterials": {"post": {"tags": ["PlanProductionSchedul"], "summary": "获取物料计划信息", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PlanMaterialDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PlanMaterialDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PlanMaterialDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PlanMaterialListDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PlanMaterialListDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PlanMaterialListDto"}}}}}}}, "/aps/api/Schedul/getpersonnels": {"post": {"tags": ["PlanProductionSchedul"], "summary": "获取人员计划信息", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PersonnelDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PersonnelDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PersonnelDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PersonnelResultDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PersonnelResultDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PersonnelResultDto"}}}}}}}}, "/aps/api/Schedul/add": {"post": {"tags": ["PlanProductionSchedul"], "summary": "新增排产计划", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PlanProductionSchedulDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PlanProductionSchedulDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PlanProductionSchedulDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/aps/api/Schedul/batchDel": {"post": {"tags": ["PlanProductionSchedul"], "summary": "批量删除排产计划", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/aps/api/Schedul/reschedulingproduction": {"post": {"tags": ["PlanProductionSchedul"], "summary": "重排接口", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReloadBasProcuct"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ReloadBasProcuct"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ReloadBasProcuct"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {}}}, "application/json": {"schema": {"type": "array", "items": {}}}, "text/json": {"schema": {"type": "array", "items": {}}}}}}}}, "/aps/api/Schedul/saveSchedulingProduction": {"post": {"tags": ["PlanProductionSchedul"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReloadBasProcuct"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ReloadBasProcuct"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ReloadBasProcuct"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/aps/api/Schedul/setScheduleStatus": {"post": {"tags": ["PlanProductionSchedul"], "summary": "修改排产状态", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SchedulStatus"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SchedulStatus"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SchedulStatus"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/Schedul/stepsolution/{code}": {"get": {"tags": ["PlanProductionSchedul"], "summary": "查询排产工序方案", "parameters": [{"name": "code", "in": "path", "description": "排产编码", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SchduleStepSolutionItem"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SchduleStepSolutionItem"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SchduleStepSolutionItem"}}}}}}}}, "/aps/api/Schedul/material": {"post": {"tags": ["PlanProductionSchedul"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MaterialWarningPagedDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MaterialWarningPagedDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MaterialWarningPagedDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/MaterialWarningReturnDtoPageModelDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MaterialWarningReturnDtoPageModelDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MaterialWarningReturnDtoPageModelDto"}}}}}}}, "/aps/api/Schedul/materialchart": {"post": {"tags": ["PlanProductionSchedul"], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}, "application/*+json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/MaterialWarningChartDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MaterialWarningChartDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MaterialWarningChartDto"}}}}}}}, "/aps/api/stockmaterial/getallasync": {"get": {"tags": ["StockMaterial"], "summary": "获取所有信息", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}}}}}}, "/aps/api/stockmaterial/page": {"post": {"tags": ["StockMaterial"], "summary": "获取物料列表分页", "requestBody": {"description": "查询条件", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StockMaterialPagedDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockMaterialPagedDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StockMaterialPagedDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StockMaterialDtoPageModelDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StockMaterialDtoPageModelDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockMaterialDtoPageModelDto"}}}}}}}, "/aps/api/stockmaterial/getbyid/{id}": {"get": {"tags": ["StockMaterial"], "summary": "根据ID获取数据", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StockMaterialDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StockMaterialDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockMaterialDto"}}}}}}}, "/aps/api/stockmaterial/add": {"post": {"tags": ["StockMaterial"], "summary": "新增产线", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StockMaterialDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockMaterialDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StockMaterialDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/stockmaterial/edit": {"post": {"tags": ["StockMaterial"], "summary": "更新产线", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StockMaterialDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockMaterialDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StockMaterialDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/aps/api/stockmaterial/importasync": {"post": {"tags": ["StockMaterial"], "summary": "新增物料", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"formFile": {"type": "string", "format": "binary"}}}, "encoding": {"formFile": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultJson"}}}}}}}, "/aps/api/tests/GetAllAsync": {"get": {"tags": ["Test"], "summary": "获取所有信息", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SysTestDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SysTestDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SysTestDto"}}}}}}}}, "/aps/api/tests/getTotalTime": {"get": {"tags": ["Test"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SysTestDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SysTestDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SysTestDto"}}}}}}}}}, "components": {"schemas": {"AppSrvResult": {"type": "object", "properties": {"isSuccess": {"type": "boolean", "readOnly": true}, "problemDetails": {"$ref": "#/components/schemas/ProblemDetails"}}, "additionalProperties": false}, "BasBomDto": {"type": "object", "properties": {"creatName": {"type": "string", "nullable": true}, "modifyName": {"type": "string", "nullable": true}, "createBy": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "modifyBy": {"type": "integer", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "format": "date-time", "nullable": true}, "id": {"type": "integer", "description": "ID", "format": "int64", "nullable": true}, "productCode": {"type": "string", "description": "产品编码", "nullable": true}, "productName": {"type": "string", "description": "产品名称", "nullable": true}, "materialName": {"type": "string", "description": "物料名称", "nullable": true}, "materialCode": {"type": "string", "description": "物料编码", "nullable": true}, "qty": {"type": "number", "description": "物料数量", "format": "double", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}}, "additionalProperties": false}, "BasBomDtoPageModelDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/BasBomDto"}, "nullable": true}, "rowsCount": {"type": "integer", "format": "int32", "readOnly": true}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32", "readOnly": true}, "xData": {"nullable": true}}, "additionalProperties": false}, "BasBomListDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID", "format": "int64"}, "idBom": {"type": "integer", "description": "BomID", "format": "int64"}, "materialName": {"type": "string", "description": "物料名称", "nullable": true}, "materialCode": {"type": "string", "description": "物料编码", "nullable": true}, "qty": {"type": "number", "description": "数量", "format": "double", "nullable": true}}, "additionalProperties": false}, "BasClassesDto": {"type": "object", "properties": {"creatName": {"type": "string", "nullable": true}, "modifyName": {"type": "string", "nullable": true}, "createBy": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "modifyBy": {"type": "integer", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "format": "date-time", "nullable": true}, "id": {"type": "integer", "description": "ID", "format": "int64", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}, "shortName": {"type": "string", "description": "简称", "nullable": true}, "cname": {"type": "string", "description": "班次名称", "nullable": true}, "ccode": {"type": "string", "description": "班次编码", "nullable": true}, "beginTime": {"type": "string", "description": "开始时间", "nullable": true}, "endTime": {"type": "string", "description": "结束时间", "nullable": true}, "status": {"type": "integer", "description": "启用状态", "format": "int32", "nullable": true}, "color": {"type": "string", "nullable": true}, "duration": {"type": "integer", "description": "工作时长(h)", "format": "int32", "nullable": true}, "qty": {"type": "integer", "description": "人员数量", "format": "int32", "nullable": true}}, "additionalProperties": false}, "BasClassesDtoPageModelDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/BasClassesDto"}, "nullable": true}, "rowsCount": {"type": "integer", "format": "int32", "readOnly": true}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32", "readOnly": true}, "xData": {"nullable": true}}, "additionalProperties": false}, "BasDeviceCalendarDto": {"type": "object", "properties": {"beginTime": {"type": "string", "description": "开始时间", "format": "date-time"}, "endTime": {"type": "string", "description": "结束时间", "format": "date-time"}, "deviceCode": {"type": "string", "description": "设备编码", "nullable": true}, "deviceName": {"type": "string", "description": "设备名称", "nullable": true}, "productName": {"type": "string", "description": "产品名称", "nullable": true}, "productCode": {"type": "string", "description": "产品名称", "nullable": true}, "stationName": {"type": "string", "description": "所属工位名称", "nullable": true}, "stationCode": {"type": "string", "description": "所属工位编码", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}}, "additionalProperties": false}, "BasDeviceCalendarResultDto": {"type": "object", "properties": {"beginTime": {"type": "string", "description": "开始时间", "format": "date-time"}, "endTime": {"type": "string", "description": "结束时间", "format": "date-time"}, "deviceCode": {"type": "string", "description": "设备编码", "nullable": true}, "deviceName": {"type": "string", "description": "设备名称", "nullable": true}, "deviceWorkStatus": {"type": "string", "description": "设备工作状态", "nullable": true}, "orderList": {"type": "array", "items": {"$ref": "#/components/schemas/OrderInfoItem"}, "description": "订单产品信息列表", "nullable": true}, "stationName": {"type": "string", "description": "所属工位名称", "nullable": true}, "stationCode": {"type": "string", "description": "所属工位编码", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}, "standardWorkTime": {"type": "number", "description": "标准工作时长", "format": "double", "nullable": true}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "nullable": true}, "creater": {"type": "string", "description": "创建人", "nullable": true}}, "additionalProperties": false}, "BasDeviceClassDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID", "format": "int64", "nullable": true}, "isdeleted": {"type": "boolean", "description": "是否删除"}, "createBy": {"type": "integer", "description": "创建人", "format": "int64", "nullable": true}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "nullable": true}, "modifyBy": {"type": "integer", "description": "更新人", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "description": "更新时间", "format": "date-time", "nullable": true}, "createdName": {"type": "string", "description": "创建人名称", "nullable": true}, "modifyName": {"type": "string", "description": "修改人名称", "nullable": true}, "devCode": {"type": "string", "description": "设备编号", "nullable": true}, "devName": {"type": "string", "description": "设备名称", "nullable": true}, "classCode": {"type": "string", "description": "班次编码", "nullable": true}, "className": {"type": "string", "description": "班次名称", "nullable": true}, "classColor": {"type": "string", "description": "班次颜色", "nullable": true}, "beginTime": {"type": "string", "description": "开始时间", "nullable": true}, "endTime": {"type": "string", "description": "结束时间", "nullable": true}, "workingDate": {"type": "string", "description": "工作时间", "format": "date-time"}}, "additionalProperties": false}, "BasDeviceClassPagedDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "devCode": {"type": "string", "description": "设备编码", "nullable": true}, "dtype": {"type": "string", "description": "设备类型", "nullable": true}, "classCode": {"type": "string", "description": "班次", "nullable": true}, "currentMonth": {"type": "string", "description": "当前月", "format": "date-time"}}, "additionalProperties": false}, "BasDeviceDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID", "format": "int64", "nullable": true}, "isdeleted": {"type": "boolean", "description": "是否删除"}, "createBy": {"type": "integer", "description": "创建人", "format": "int64", "nullable": true}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "nullable": true}, "modifyBy": {"type": "integer", "description": "更新人", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "description": "更新时间", "format": "date-time", "nullable": true}, "status": {"type": "integer", "description": "状态", "format": "int32", "nullable": true}, "code": {"type": "string", "description": "设备编码", "nullable": true}, "name": {"type": "string", "description": "设备名称", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}, "createdName": {"type": "string", "description": "创建人名称", "nullable": true}, "modifyName": {"type": "string", "description": "修改人名称", "nullable": true}, "capacity": {"type": "integer", "description": "产能", "format": "int32", "nullable": true}, "dtype": {"type": "string", "nullable": true}, "dtypeDis": {"type": "string", "description": "设备类型", "nullable": true}, "newness": {"type": "number", "description": "设备新度", "format": "double", "nullable": true}}, "additionalProperties": false}, "BasDeviceDtoPageModelDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/BasDeviceDto"}, "nullable": true}, "rowsCount": {"type": "integer", "format": "int32", "readOnly": true}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32", "readOnly": true}, "xData": {"nullable": true}}, "additionalProperties": false}, "BasDevicePagedDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "name": {"type": "string", "description": "设备名称", "nullable": true}, "code": {"type": "string", "description": "设备编码", "nullable": true}, "dtype": {"type": "string", "description": "设备编码", "nullable": true}, "status": {"type": "integer", "description": "设备编码", "format": "int32", "nullable": true}}, "additionalProperties": false}, "BasHolidayDto": {"type": "object", "properties": {"creatName": {"type": "string", "nullable": true}, "modifyName": {"type": "string", "nullable": true}, "createBy": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "modifyBy": {"type": "integer", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "format": "date-time", "nullable": true}, "id": {"type": "integer", "format": "int64", "nullable": true}, "currentDate": {"type": "string", "description": "日期", "format": "date-time", "nullable": true}, "isHoliday": {"type": "integer", "description": "是否工作日", "format": "int32"}, "isPlaned": {"type": "integer", "description": "是否排产", "format": "int32"}, "weekName": {"type": "string", "description": "星期名称", "nullable": true}}, "additionalProperties": false}, "BasHolidayDtoPageModelDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/BasHolidayDto"}, "nullable": true}, "rowsCount": {"type": "integer", "format": "int32", "readOnly": true}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32", "readOnly": true}, "xData": {"nullable": true}}, "additionalProperties": false}, "BasLineDto": {"type": "object", "properties": {"creatName": {"type": "string", "nullable": true}, "modifyName": {"type": "string", "nullable": true}, "createBy": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "modifyBy": {"type": "integer", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "format": "date-time", "nullable": true}, "id": {"type": "integer", "description": "ID", "format": "int64", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}, "lname": {"type": "string", "description": "产线名称", "nullable": true}, "lcode": {"type": "string", "description": "产线编码", "nullable": true}, "workshopCode": {"type": "string", "nullable": true}, "workshopName": {"type": "string", "nullable": true}, "idWorkshop": {"type": "integer", "format": "int64", "nullable": true}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/BasLineProductRelationDto"}, "nullable": true}}, "additionalProperties": false}, "BasLineDtoPageModelDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/BasLineDto"}, "nullable": true}, "rowsCount": {"type": "integer", "format": "int32", "readOnly": true}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32", "readOnly": true}, "xData": {"nullable": true}}, "additionalProperties": false}, "BasLineProductRelationDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID", "format": "int64", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}, "idLine": {"type": "integer", "description": "产线ID", "format": "int64", "nullable": true}, "idProduct": {"type": "integer", "description": "产品id", "format": "int64", "nullable": true}, "productCode": {"type": "string", "nullable": true}, "productName": {"type": "string", "nullable": true}, "capacity": {"type": "number", "format": "double", "nullable": true}, "unit": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BasMaterialDto": {"type": "object", "properties": {"creatName": {"type": "string", "nullable": true}, "modifyName": {"type": "string", "nullable": true}, "createBy": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "modifyBy": {"type": "integer", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "format": "date-time", "nullable": true}, "id": {"type": "integer", "description": "ID", "format": "int64"}, "isdeleted": {"type": "boolean", "description": "是否删除"}, "status": {"type": "integer", "description": "状态", "format": "int32"}, "statusDis": {"type": "string", "description": "状态", "nullable": true}, "code": {"type": "string", "description": "物料编码", "nullable": true}, "name": {"type": "string", "description": "物料名称", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}, "mtype": {"type": "string", "description": "物料类型", "nullable": true}, "mtypeDis": {"type": "string", "nullable": true}, "unit": {"type": "string", "description": "单位", "nullable": true}, "unitDis": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BasMaterialDtoPageModelDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/BasMaterialDto"}, "nullable": true}, "rowsCount": {"type": "integer", "format": "int32", "readOnly": true}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32", "readOnly": true}, "xData": {"nullable": true}}, "additionalProperties": false}, "BasMaterialPagedDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "name": {"type": "string", "description": "工位名称", "nullable": true}, "code": {"type": "string", "description": "工位编码", "nullable": true}, "status": {"type": "integer", "description": "状态", "format": "int32"}, "mtype": {"type": "string", "description": "物料类型", "nullable": true}, "unit": {"type": "string", "description": "单位", "nullable": true}}, "additionalProperties": false}, "BasNumberManagement": {"type": "object", "properties": {"createBy": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "modifyBy": {"type": "integer", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "format": "date-time", "nullable": true}, "isDeleted": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string", "nullable": true}, "simpleName": {"type": "string", "nullable": true}, "currentNumber": {"type": "string", "nullable": true}, "startNumber": {"type": "string", "nullable": true}, "identityNumber": {"type": "string", "nullable": true}, "isContainerDate": {"type": "string", "nullable": true}, "timeFormat": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "numberFormat": {"type": "integer", "format": "int64"}, "startTarget": {"type": "string", "nullable": true}, "isRound": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BasNumberManagementDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string", "nullable": true}, "simpleName": {"type": "string", "nullable": true}, "currentNumber": {"type": "string", "nullable": true}, "startNumber": {"type": "string", "nullable": true}, "identityNumber": {"type": "string", "nullable": true}, "isContainerDate": {"type": "string", "nullable": true}, "timeFormat": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "numberFormat": {"type": "integer", "format": "int64"}, "startTarget": {"type": "string", "nullable": true}, "isRound": {"type": "string", "nullable": true}, "createBy": {"type": "integer", "description": "创建人", "format": "int64", "nullable": true}, "createName": {"type": "string", "nullable": true}, "createTime": {"type": "string", "description": "创建时间/注册时间", "format": "date-time", "nullable": true}, "modifyBy": {"type": "integer", "description": "最后更新人", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "description": "最后更新时间", "format": "date-time", "nullable": true}, "isDeleted": {"type": "boolean", "description": "是否删除标识", "nullable": true}}, "additionalProperties": false}, "BasNumberManagementDtoPageModelDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/BasNumberManagementDto"}, "nullable": true}, "rowsCount": {"type": "integer", "format": "int32", "readOnly": true}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32", "readOnly": true}, "xData": {"nullable": true}}, "additionalProperties": false}, "BasNumberManagementPagedDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "simpleName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BasOrderDto": {"type": "object", "properties": {"creatName": {"type": "string", "nullable": true}, "modifyName": {"type": "string", "nullable": true}, "createBy": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "modifyBy": {"type": "integer", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "format": "date-time", "nullable": true}, "id": {"type": "integer", "description": "ID", "format": "int64", "nullable": true}, "idDetail": {"type": "integer", "description": "ID", "format": "int64", "nullable": true}, "priority": {"type": "integer", "description": "优先级", "format": "int32"}, "status": {"type": "integer", "description": "生产状态", "format": "int32"}, "orderNumber": {"type": "string", "description": "订单编号", "nullable": true}, "userName": {"type": "string", "description": "客户", "nullable": true}, "deliveryDate": {"type": "string", "description": "交付日期", "format": "date-time", "nullable": true}, "productName": {"type": "string", "description": "产品名称", "nullable": true}, "productCode": {"type": "string", "description": "产品编码", "nullable": true}, "quantity": {"type": "number", "description": "产品数量", "format": "double"}, "saleNumber": {"type": "string", "description": "销售单号", "nullable": true}, "pendingNum": {"type": "number", "description": "待排产产品数量", "format": "double"}}, "additionalProperties": false, "description": "客户订单"}, "BasOrderDtoPageModelDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/BasOrderDto"}, "nullable": true}, "rowsCount": {"type": "integer", "format": "int32", "readOnly": true}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32", "readOnly": true}, "xData": {"nullable": true}}, "additionalProperties": false}, "BasOrderPagedDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "priority": {"type": "integer", "description": "优先级", "format": "int32"}, "status": {"type": "integer", "description": "生产状态", "format": "int32"}, "saleNumber": {"type": "string", "description": "销售单号", "nullable": true}, "orderNumber": {"type": "string", "description": "订单编号", "nullable": true}, "userName": {"type": "string", "description": "客户", "nullable": true}, "productCode": {"type": "string", "description": "产品编码", "nullable": true}, "productName": {"type": "string", "description": "产品名称", "nullable": true}}, "additionalProperties": false}, "BasOrderProductDto": {"type": "object", "properties": {"productName": {"type": "string", "description": "产品名称", "nullable": true}, "productCode": {"type": "string", "description": "产品编码", "nullable": true}, "qty": {"type": "number", "description": "订单产品待排产数量", "format": "double"}}, "additionalProperties": false}, "BasProductBomDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID", "format": "int64", "nullable": true}, "isEdit": {"type": "boolean"}, "remark": {"type": "string", "description": "备注", "nullable": true}, "proname": {"type": "string", "description": "产品名称", "nullable": true}, "procode": {"type": "string", "description": "产品编码", "nullable": true}, "tCode": {"type": "string", "description": "工艺编码", "nullable": true}, "tName": {"type": "string", "description": "工艺编码", "nullable": true}, "status": {"type": "integer", "description": "状态", "format": "int32", "nullable": true}, "creatName": {"type": "string", "description": "创建名", "nullable": true}, "modifyName": {"type": "string", "description": "更新人", "nullable": true}, "createBy": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间/注册时间", "format": "date-time"}, "modifyBy": {"type": "integer", "description": "最后更新人", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "description": "最后更新时间", "format": "date-time", "nullable": true}, "bomItems": {"type": "array", "items": {"$ref": "#/components/schemas/BasBomListDto"}, "nullable": true}}, "additionalProperties": false}, "BasProductDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID", "format": "int64"}, "isEdit": {"type": "boolean"}, "remark": {"type": "string", "description": "备注", "nullable": true}, "proname": {"type": "string", "description": "产品名称", "nullable": true}, "procode": {"type": "string", "description": "产品编码", "nullable": true}, "tCode": {"type": "string", "description": "工艺编码", "nullable": true}, "tName": {"type": "string", "description": "工艺编码", "nullable": true}, "status": {"type": "integer", "description": "状态", "format": "int32", "nullable": true}, "statusDis": {"type": "string", "nullable": true}, "creatName": {"type": "string", "description": "创建名", "nullable": true}, "modifyName": {"type": "string", "description": "更新人", "nullable": true}, "createBy": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间/注册时间", "format": "date-time"}, "modifyBy": {"type": "integer", "description": "最后更新人", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "description": "最后更新时间", "format": "date-time", "nullable": true}, "stepItems": {"type": "array", "items": {"$ref": "#/components/schemas/BasProductStepRelationDto"}, "nullable": true}, "bomItems": {"type": "array", "items": {"$ref": "#/components/schemas/BasBomListDto"}, "nullable": true}}, "additionalProperties": false}, "BasProductDtoPageModelDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/BasProductDto"}, "nullable": true}, "rowsCount": {"type": "integer", "format": "int32", "readOnly": true}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32", "readOnly": true}, "xData": {"nullable": true}}, "additionalProperties": false}, "BasProductMaterialDto": {"type": "object", "properties": {"productName": {"type": "string", "description": "产品名称", "nullable": true}, "materials": {"type": "array", "items": {"$ref": "#/components/schemas/MaterialDto"}, "description": "物料BOM", "nullable": true}}, "additionalProperties": false, "description": "订单物料BOM"}, "BasProductStandardCapacityDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID", "format": "int64", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}, "status": {"type": "integer", "description": "启用状态", "format": "int32", "nullable": true}, "solutionName": {"type": "string", "description": "产能方案", "nullable": true}, "proCode": {"type": "string", "description": "产品编码", "nullable": true}, "stepCode": {"type": "string", "description": "工序编码", "nullable": true}, "stepName": {"type": "string", "description": "工序名称", "nullable": true}, "stationCode": {"type": "string", "description": "工位编码", "nullable": true}, "stationName": {"type": "string", "description": "工位名称", "nullable": true}, "lineCode": {"type": "string", "description": "产线编码", "nullable": true}, "lineName": {"type": "string", "description": "产线名称", "nullable": true}, "deviceCode": {"type": "string", "description": "设备编码", "nullable": true}, "deviceName": {"type": "string", "description": "设备名称", "nullable": true}, "capacity": {"type": "number", "description": "产能", "format": "double", "nullable": true}, "workUnitTime": {"type": "number", "description": "单位工作时长", "format": "double", "nullable": true}, "standardWorkTime": {"type": "number", "description": "标准工作时长", "format": "double", "nullable": true}, "minWorkingDuration": {"type": "number", "description": "工序最短工作时长", "format": "double", "nullable": true}, "createBy": {"type": "integer", "description": "创建人ID", "format": "int64", "nullable": true}, "createTime": {"type": "string", "description": "创建时间/注册时间", "format": "date-time", "nullable": true}, "modifyBy": {"type": "integer", "description": "最后更新人", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "description": "最后更新时间", "format": "date-time", "nullable": true}, "sort": {"type": "integer", "description": "排序", "format": "int32", "nullable": true, "writeOnly": true}}, "additionalProperties": false}, "BasProductStepRelationDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID", "format": "int64", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}, "proCode": {"type": "string", "description": "产品编码", "nullable": true}, "tecCode": {"type": "string", "description": "工艺编码", "nullable": true}, "stepCode": {"type": "string", "description": "工序编码", "nullable": true}, "stepName": {"type": "string", "description": "工序名称", "nullable": true}, "sort": {"type": "integer", "description": "排序", "format": "int32", "nullable": true}, "preStep": {"type": "string", "description": "前置工序", "nullable": true}, "preStepName": {"type": "string", "description": "前置工序名称", "nullable": true}, "intervalTime": {"type": "number", "description": "前置工序间隔时间", "format": "double", "nullable": true}, "stepTime": {"type": "number", "description": "单位工作时长", "format": "double", "nullable": true}, "tat": {"type": "number", "description": "单位工作时长", "format": "double", "nullable": true}, "isKey": {"type": "integer", "description": "是否关键工序", "format": "int32", "nullable": true}, "createBy": {"type": "integer", "description": "创建人ID", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间/注册时间", "format": "date-time"}, "modifyBy": {"type": "integer", "description": "最后更新人", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "description": "最后更新时间", "format": "date-time", "nullable": true}, "materialsIn": {"type": "array", "items": {"$ref": "#/components/schemas/BasProductStepRelationMaterialDto"}, "nullable": true}, "materialsOut": {"type": "array", "items": {"$ref": "#/components/schemas/BasProductStepRelationMaterialDto"}, "nullable": true}, "mainItems": {"type": "array", "items": {"$ref": "#/components/schemas/BasProductStepRelationSourceDto"}, "nullable": true}, "assistItems": {"type": "array", "items": {"$ref": "#/components/schemas/BasProductStepRelationSourceDto"}, "nullable": true}}, "additionalProperties": false}, "BasProductStepRelationMaterialDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID", "format": "int64", "nullable": true}, "isEdit": {"type": "boolean", "description": "备注"}, "mainId": {"type": "integer", "description": "备注", "format": "int64", "nullable": true}, "materialType": {"type": "string", "description": "物料类型 in out", "nullable": true}, "materialCode": {"type": "string", "description": "物料编码", "nullable": true}, "materialName": {"type": "string", "description": "物料名称", "nullable": true}, "qty": {"type": "number", "description": "数量", "format": "double", "nullable": true}, "batchQty": {"type": "number", "description": "结批量", "format": "double", "nullable": true}, "createBy": {"type": "integer", "description": "创建人ID", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间/注册时间", "format": "date-time"}, "modifyBy": {"type": "integer", "description": "最后更新人", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "description": "最后更新时间", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "BasProductStepRelationSourceDto": {"type": "object", "properties": {"isEdit": {"type": "boolean", "description": "是否编辑"}, "id": {"type": "integer", "description": "ID", "format": "int64", "nullable": true}, "mainId": {"type": "integer", "description": "ID", "format": "int64", "nullable": true}, "sourceType": {"type": "string", "description": "资源大类", "nullable": true}, "sourceTypeName": {"type": "string", "description": "资源大类名称", "nullable": true}, "deviceType": {"type": "string", "description": "资源小类", "nullable": true}, "deviceTypeName": {"type": "string", "description": "物料类型设备类型", "nullable": true}, "mainSource": {"type": "boolean", "description": "是否主资源"}, "isCapacity": {"type": "boolean", "description": "是否考虑产能"}, "tat": {"type": "number", "description": "单位工作时长", "format": "double", "nullable": true}, "capacity": {"type": "number", "description": "单位产能", "format": "double", "nullable": true}, "createBy": {"type": "integer", "description": "创建人ID", "format": "int64", "nullable": true}, "createTime": {"type": "string", "description": "创建时间/注册时间", "format": "date-time", "nullable": true}, "modifyBy": {"type": "integer", "description": "最后更新人", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "description": "最后更新时间", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "BasRelationDeviceDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID", "format": "int64"}, "remark": {"type": "string", "description": "备注", "nullable": true}, "idRelation": {"type": "integer", "description": "工序id", "format": "int64", "nullable": true}, "idDevice": {"type": "integer", "description": "工序编码", "format": "int64", "nullable": true}, "deviceCode": {"type": "string", "nullable": true}, "deviceName": {"type": "string", "nullable": true}, "capacity": {"type": "number", "description": "创建人姓名", "format": "double", "nullable": true}, "isDeleted": {"type": "boolean"}, "creatName": {"type": "string", "nullable": true}, "modifyName": {"type": "string", "description": "更新人", "nullable": true}, "createBy": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间/注册时间", "format": "date-time"}, "modifyBy": {"type": "integer", "description": "最后更新人", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "description": "最后更新时间", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "BasRelationDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID", "format": "int64"}, "isDeleted": {"type": "boolean", "description": "是否删除"}, "creatName": {"type": "string", "nullable": true}, "modifyName": {"type": "string", "description": "更新人", "nullable": true}, "createBy": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间/注册时间", "format": "date-time"}, "modifyBy": {"type": "integer", "description": "最后更新人", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "description": "最后更新时间", "format": "date-time", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}, "idStep": {"type": "integer", "description": "工序id", "format": "int64", "nullable": true}, "stepCode": {"type": "string", "description": "工序编码", "nullable": true}, "stepName": {"type": "string", "nullable": true}, "devices": {"type": "array", "items": {"$ref": "#/components/schemas/BasRelationDeviceDto"}, "nullable": true}}, "additionalProperties": false}, "BasRelationDtoPageModelDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/BasRelationDto"}, "nullable": true}, "rowsCount": {"type": "integer", "format": "int32", "readOnly": true}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32", "readOnly": true}, "xData": {"nullable": true}}, "additionalProperties": false}, "BasStationDto": {"type": "object", "properties": {"creatName": {"type": "string", "nullable": true}, "modifyName": {"type": "string", "nullable": true}, "createBy": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "modifyBy": {"type": "integer", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "format": "date-time", "nullable": true}, "id": {"type": "integer", "description": "ID", "format": "int64", "nullable": true}, "isdeleted": {"type": "boolean", "description": "是否删除"}, "status": {"type": "integer", "description": "状态", "format": "int32"}, "code": {"type": "string", "description": "工位编码", "nullable": true}, "name": {"type": "string", "description": "工位名称", "nullable": true}, "worksjopid": {"type": "integer", "description": "所属车间", "format": "int64", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}, "worksjopName": {"type": "string", "description": "车间名称", "nullable": true}, "deviceCode": {"type": "string", "description": "设备编码", "nullable": true}, "linecode": {"type": "string", "description": "产线code", "nullable": true}, "deviceName": {"type": "string", "description": "设备名称", "nullable": true}, "lineName": {"type": "string", "description": "产线名称", "nullable": true}, "duration": {"type": "integer", "description": "标准工作时长(h)", "format": "int32", "nullable": true}, "classes": {"type": "array", "items": {"$ref": "#/components/schemas/ClassesDto"}, "description": "工位中设置的班次列表", "nullable": true}, "isEdit": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "BasStationDtoPageModelDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/BasStationDto"}, "nullable": true}, "rowsCount": {"type": "integer", "format": "int32", "readOnly": true}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32", "readOnly": true}, "xData": {"nullable": true}}, "additionalProperties": false}, "BasStationPagedDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "name": {"type": "string", "description": "工位名称", "nullable": true}, "code": {"type": "string", "description": "工位编码", "nullable": true}, "devName": {"type": "string", "description": "设备名称", "nullable": true}, "status": {"type": "integer", "description": "状态", "format": "int32"}, "lineCode": {"type": "string", "description": "产线编码", "nullable": true}}, "additionalProperties": false}, "BasStationSearchDto": {"type": "object", "properties": {"lineCode": {"type": "string", "description": "产线编码", "nullable": true}}, "additionalProperties": false}, "BasStepDto": {"type": "object", "properties": {"creatName": {"type": "string", "nullable": true}, "modifyName": {"type": "string", "nullable": true}, "createBy": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "modifyBy": {"type": "integer", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "format": "date-time", "nullable": true}, "id": {"type": "integer", "description": "ID", "format": "int64", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}, "name": {"type": "string", "description": "工序名称", "nullable": true}, "code": {"type": "string", "description": "工序编码S", "nullable": true}, "stepTime": {"type": "number", "description": "工序最短时长(小时)", "format": "double", "nullable": true}, "tat": {"type": "number", "description": "工序标准时长(小时)", "format": "double", "nullable": true}, "status": {"type": "integer", "description": "状态", "format": "int32", "nullable": true}, "statusDis": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BasStepDtoPageModelDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/BasStepDto"}, "nullable": true}, "rowsCount": {"type": "integer", "format": "int32", "readOnly": true}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32", "readOnly": true}, "xData": {"nullable": true}}, "additionalProperties": false}, "BasTechnologyDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID", "format": "int64", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}, "tname": {"type": "string", "description": "工序名称", "nullable": true}, "tcode": {"type": "string", "description": "工序编码", "nullable": true}, "status": {"type": "integer", "description": "状态", "format": "int32", "nullable": true}, "creatName": {"type": "string", "description": "创建名", "nullable": true}, "modifyName": {"type": "string", "description": "更新人", "nullable": true}, "createBy": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间/注册时间", "format": "date-time"}, "modifyBy": {"type": "integer", "description": "最后更新人", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "description": "最后更新时间", "format": "date-time", "nullable": true}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/BasTechnologyStepRelationDto"}, "nullable": true}}, "additionalProperties": false}, "BasTechnologyDtoPageModelDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/BasTechnologyDto"}, "nullable": true}, "rowsCount": {"type": "integer", "format": "int32", "readOnly": true}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32", "readOnly": true}, "xData": {"nullable": true}}, "additionalProperties": false}, "BasTechnologyStepRelationDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID", "format": "int64", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}, "idTechnology": {"type": "integer", "description": "工艺路线ID", "format": "int64", "nullable": true}, "idStep": {"type": "integer", "description": "工序路线id", "format": "int64", "nullable": true}, "stepCode": {"type": "string", "nullable": true}, "stepName": {"type": "string", "nullable": true}, "stepTime": {"type": "number", "format": "double", "nullable": true}, "waitTime": {"type": "number", "format": "double", "nullable": true}, "tCode": {"type": "string", "nullable": true}, "preStep": {"type": "string", "description": "前置工序编码", "nullable": true}, "preStepName": {"type": "string", "description": "前置工序名称", "nullable": true}, "intervalTime": {"type": "number", "description": "前置工序间隔时间", "format": "double", "nullable": true}, "isKey": {"type": "integer", "description": "是否关键工序", "format": "int32", "nullable": true}, "sort": {"type": "integer", "description": "排序", "format": "int32", "nullable": true}, "relationType": {"type": "integer", "description": "与下一道工序的关系", "format": "int32", "nullable": true}, "createBy": {"type": "integer", "description": "创建人ID", "format": "int64", "nullable": true}, "createTime": {"type": "string", "description": "创建时间/注册时间", "format": "date-time"}, "modifyBy": {"type": "integer", "description": "最后更新人", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "description": "最后更新时间", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "BasWorksjopDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID", "format": "int64", "nullable": true}, "isdeleted": {"type": "boolean", "description": "是否删除"}, "createdby": {"type": "integer", "description": "创建人", "format": "int64"}, "createdtime": {"type": "string", "description": "创建时间", "format": "date-time"}, "createdname": {"type": "string", "description": "创建人姓名", "nullable": true}, "modifyby": {"type": "integer", "description": "更新人", "format": "int64"}, "modifytime": {"type": "string", "description": "更新时间", "format": "date-time"}, "modifyname": {"type": "string", "description": "更新人姓名", "nullable": true}, "status": {"type": "integer", "description": "状态", "format": "int32"}, "remark": {"type": "string", "description": "备注", "nullable": true}, "code": {"type": "string", "description": "车间编码", "nullable": true}, "name": {"type": "string", "description": "车间名称", "nullable": true}}, "additionalProperties": false}, "BasWorksjopDtoPageModelDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/BasWorksjopDto"}, "nullable": true}, "rowsCount": {"type": "integer", "format": "int32", "readOnly": true}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32", "readOnly": true}, "xData": {"nullable": true}}, "additionalProperties": false}, "BomPagedDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "productCode": {"type": "string", "description": "产品编码", "nullable": true}, "productName": {"type": "string", "description": "产品名称", "nullable": true}, "materialCode": {"type": "string", "description": "物料编码", "nullable": true}}, "additionalProperties": false}, "ChangeStatusDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "状态", "format": "int32"}, "codeList": {"type": "array", "items": {"type": "string"}, "description": "编码列表", "nullable": true}}, "additionalProperties": false}, "ClassesDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID", "format": "int64", "nullable": true}, "cname": {"type": "string", "description": "班次名称", "nullable": true}, "beginTime": {"type": "string", "description": "开始时间", "nullable": true}, "endTime": {"type": "string", "description": "结束时间", "nullable": true}, "duration": {"type": "integer", "description": "班次时长(h)", "format": "int32", "nullable": true}, "status": {"type": "integer", "description": "状态", "format": "int32", "nullable": true}, "qty": {"type": "integer", "description": "人员数量", "format": "int32", "nullable": true}}, "additionalProperties": false}, "ClassesPagedDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "cname": {"type": "string", "description": "班次名称", "nullable": true}, "ccode": {"type": "string", "description": "班次编码", "nullable": true}, "shortName": {"type": "string", "description": "班次简称", "nullable": true}, "status": {"type": "integer", "description": "状态", "format": "int32"}}, "additionalProperties": false}, "DeviceGanttDto": {"type": "object", "properties": {"devCode": {"type": "string", "nullable": true}, "devName": {"type": "string", "nullable": true}, "shifts": {"type": "array", "items": {"$ref": "#/components/schemas/DevicePlanDto"}, "nullable": true}}, "additionalProperties": false}, "DevicePlanDto": {"type": "object", "properties": {"creatName": {"type": "string", "nullable": true}, "modifyName": {"type": "string", "nullable": true}, "createBy": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "modifyBy": {"type": "integer", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "format": "date-time", "nullable": true}, "scheduleCode": {"type": "string", "nullable": true}, "stepCode": {"type": "string", "nullable": true}, "devCode": {"type": "string", "nullable": true}, "beginDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}, "tat": {"type": "number", "format": "double"}, "status": {"type": "integer", "format": "int32"}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DeviceStateDto": {"type": "object", "properties": {"targetDate": {"type": "string", "format": "date-time"}, "status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DeviceWorkingDateDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID", "format": "int64", "nullable": true}, "isdeleted": {"type": "boolean", "description": "是否删除"}, "createBy": {"type": "integer", "description": "创建人", "format": "int64", "nullable": true}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "nullable": true}, "modifyBy": {"type": "integer", "description": "更新人", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "description": "更新时间", "format": "date-time", "nullable": true}, "status": {"type": "integer", "description": "状态", "format": "int32", "nullable": true}, "code": {"type": "string", "description": "设备编码", "nullable": true}, "name": {"type": "string", "description": "设备名称", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}, "createdName": {"type": "string", "description": "创建人名称", "nullable": true}, "modifyName": {"type": "string", "description": "修改人名称", "nullable": true}, "capacity": {"type": "integer", "description": "产能", "format": "int32", "nullable": true}, "dtype": {"type": "string", "nullable": true}, "dtypeDis": {"type": "string", "description": "设备类型", "nullable": true}, "newness": {"type": "number", "description": "设备新度", "format": "double", "nullable": true}, "shifts": {"type": "array", "items": {"$ref": "#/components/schemas/BasDeviceClassDto"}, "nullable": true}}, "additionalProperties": false}, "DeviceWorkingDateDtoPageModelDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/DeviceWorkingDateDto"}, "nullable": true}, "rowsCount": {"type": "integer", "format": "int32", "readOnly": true}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32", "readOnly": true}, "xData": {"nullable": true}}, "additionalProperties": false}, "DictEnumTreeRto": {"type": "object", "properties": {"label": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}, "childs": {"type": "array", "items": {"$ref": "#/components/schemas/DictEnumTreeRto"}, "nullable": true}}, "additionalProperties": false}, "FinishedOrders": {"type": "object", "properties": {"orders": {"type": "array", "items": {"$ref": "#/components/schemas/BasOrderDto"}, "nullable": true}}, "additionalProperties": false, "description": "批量更新订单状态为完成"}, "GanttDate": {"type": "object", "properties": {"stepCode": {"type": "string", "description": "工序编码", "nullable": true}, "beginTime": {"type": "string", "description": "开始日期", "format": "date-time", "nullable": true}, "endTime": {"type": "string", "description": "结束日期", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "HolidayPagedDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "start": {"type": "string", "description": "开始时间", "format": "date-time", "nullable": true}, "end": {"type": "string", "description": "结束时间", "format": "date-time", "nullable": true}, "isHoliday": {"type": "integer", "description": "是否工作日", "format": "int32", "nullable": true}, "isPlaned": {"type": "integer", "description": "是否排产", "format": "int32", "nullable": true}}, "additionalProperties": false}, "HomeDeviceNumDto": {"type": "object", "properties": {"status": {"type": "string", "description": "设备状态", "nullable": true}, "num": {"type": "number", "description": "数量", "format": "double", "nullable": true}}, "additionalProperties": false}, "HomeOrderNumByDateDto": {"type": "object", "properties": {"orderdate": {"type": "string", "description": "订单创建时间(月份)", "nullable": true}, "num": {"type": "integer", "description": "数量", "format": "int32", "nullable": true}}, "additionalProperties": false}, "HttpStatusCode": {"enum": [100, 101, 102, 103, 200, 201, 202, 203, 204, 205, 206, 207, 208, 226, 300, 301, 302, 303, 304, 305, 306, 307, 308, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 421, 422, 423, 424, 426, 428, 429, 431, 451, 500, 501, 502, 503, 504, 505, 506, 507, 508, 510, 511], "type": "integer", "format": "int32"}, "InputParamDto": {"type": "object", "properties": {"serviceUrl": {"type": "string", "description": "接口路径", "nullable": true}, "param": {"type": "string", "description": "参数", "nullable": true}, "httpMethod": {"type": "string", "description": "HTTP请求方法(GET/POST/PUT/DELETE等)，默认为POST", "nullable": true}}, "additionalProperties": false}, "Int32SimpleDto": {"type": "object", "properties": {"value": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "LinePagedDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "lname": {"type": "string", "description": "产线名称", "nullable": true}, "lcode": {"type": "string", "description": "产线编码", "nullable": true}}, "additionalProperties": false}, "MaterialDto": {"type": "object", "properties": {"materialName": {"type": "string", "description": "物料编码", "nullable": true}, "qty": {"type": "number", "description": "数量", "format": "double"}}, "additionalProperties": false}, "MaterialWarningChartDto": {"type": "object", "properties": {"mcode": {"type": "string", "nullable": true}, "qty": {"type": "number", "description": "产品名称", "format": "double", "nullable": true}, "linepoints": {"type": "array", "items": {"$ref": "#/components/schemas/MaterialWarningLineDto"}, "description": "剩余QTY", "nullable": true}}, "additionalProperties": false}, "MaterialWarningLineDto": {"type": "object", "properties": {"schedule": {"type": "string", "nullable": true}, "lessQty": {"type": "number", "description": "剩余QTY", "format": "double", "nullable": true}}, "additionalProperties": false}, "MaterialWarningPagedDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "mcode": {"type": "string", "description": "产品编码", "nullable": true}, "mname": {"type": "string", "description": "产品名称", "nullable": true}, "dtBegin": {"type": "string", "description": "产品编码", "format": "date-time", "nullable": true}, "dtEnd": {"type": "string", "description": "产品名称", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "MaterialWarningReturnDto": {"type": "object", "properties": {"mcode": {"type": "string", "description": "物料数量", "nullable": true}, "mname": {"type": "string", "nullable": true}, "qty": {"type": "number", "format": "double", "nullable": true}, "planQty": {"type": "number", "format": "double", "nullable": true}, "missQty": {"type": "number", "format": "double", "nullable": true}, "orders": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MaterialWarningReturnDtoPageModelDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/MaterialWarningReturnDto"}, "nullable": true}, "rowsCount": {"type": "integer", "format": "int32", "readOnly": true}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32", "readOnly": true}, "xData": {"nullable": true}}, "additionalProperties": false}, "OrderDetailDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "生产状态", "format": "int32", "nullable": true}, "statusString": {"type": "string", "nullable": true}, "orderNumber": {"type": "string", "description": "订单编号", "nullable": true}, "deliveryDate": {"type": "string", "description": "交付日期", "format": "date-time", "nullable": true}, "userName": {"type": "string", "description": "客户", "nullable": true}, "priority": {"type": "integer", "description": "优先级", "format": "int32"}, "priorityString": {"type": "string", "nullable": true}, "planStartDate": {"type": "string", "description": "计划开始日期", "format": "date-time", "nullable": true}, "planEndDate": {"type": "string", "description": "计划结束日期", "format": "date-time", "nullable": true}, "materialList": {"type": "array", "items": {"$ref": "#/components/schemas/BasProductMaterialDto"}, "description": "物料列表", "nullable": true}}, "additionalProperties": false}, "OrderInfoItem": {"type": "object", "properties": {"orderNumber": {"type": "string", "description": "订单号", "nullable": true}, "productName": {"type": "string", "description": "产品名称", "nullable": true}}, "additionalProperties": false}, "OrderProdcutGanttDto": {"type": "object", "properties": {"orderNumber": {"type": "string", "description": "订单编码", "nullable": true}, "productCode": {"type": "string", "description": "产品编码", "nullable": true}}, "additionalProperties": false}, "PartDeviceStateDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID", "format": "int64", "nullable": true}, "isdeleted": {"type": "boolean", "description": "是否删除"}, "createBy": {"type": "integer", "description": "创建人", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time"}, "modifyBy": {"type": "integer", "description": "更新人", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "description": "更新时间", "format": "date-time", "nullable": true}, "status": {"type": "integer", "description": "状态", "format": "int32", "nullable": true}, "statusDis": {"type": "string", "description": "状态", "nullable": true}, "dtype": {"type": "string", "description": "设备类型", "nullable": true}, "dtypeDis": {"type": "string", "description": "设备类型编码", "nullable": true}, "devCode": {"type": "string", "description": "设备编码", "nullable": true}, "devName": {"type": "string", "description": "设备名称", "nullable": true}, "beginTime": {"type": "string", "description": "开始时间", "format": "date-time", "nullable": true}, "endTime": {"type": "string", "description": "结束时间", "format": "date-time", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}, "createdName": {"type": "string", "description": "创建人名称", "nullable": true}, "modifyName": {"type": "string", "description": "修改人名称", "nullable": true}, "tat": {"type": "number", "format": "double", "nullable": true}}, "additionalProperties": false}, "PartDeviceStateDtoPageModelDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/PartDeviceStateDto"}, "nullable": true}, "rowsCount": {"type": "integer", "format": "int32", "readOnly": true}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32", "readOnly": true}, "xData": {"nullable": true}}, "additionalProperties": false}, "PartOrderDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID", "format": "int64", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}, "level": {"type": "integer", "format": "int32", "nullable": true}, "orderCode": {"type": "string", "nullable": true}, "status": {"type": "integer", "description": "状态", "format": "int32", "nullable": true}, "technologyCode": {"type": "string", "nullable": true}, "technologyName": {"type": "string", "nullable": true}, "idTec": {"type": "integer", "format": "int64", "nullable": true}, "qty": {"type": "number", "format": "double", "nullable": true}, "custormCode": {"type": "string", "nullable": true}, "custormName": {"type": "string", "nullable": true}, "idCustorm": {"type": "integer", "format": "int64", "nullable": true}, "deliveryTime": {"type": "string", "format": "date-time", "nullable": true}, "productCode": {"type": "string", "nullable": true}, "productName": {"type": "string", "nullable": true}, "idProduct": {"type": "integer", "format": "int64", "nullable": true}, "creatName": {"type": "string", "description": "创建名", "nullable": true}, "modifyName": {"type": "string", "description": "更新人", "nullable": true}, "createBy": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间/注册时间", "format": "date-time"}, "modifyBy": {"type": "integer", "description": "最后更新人", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "description": "最后更新时间", "format": "date-time", "nullable": true}, "materials": {"type": "array", "items": {"$ref": "#/components/schemas/PartOrderMaterialDto"}, "nullable": true}, "steps": {"type": "array", "items": {"$ref": "#/components/schemas/PartOrderStepDto"}, "nullable": true}}, "additionalProperties": false}, "PartOrderDtoPageModelDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/PartOrderDto"}, "nullable": true}, "rowsCount": {"type": "integer", "format": "int32", "readOnly": true}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32", "readOnly": true}, "xData": {"nullable": true}}, "additionalProperties": false}, "PartOrderMaterialDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID", "format": "int64", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}, "idMaterial": {"type": "integer", "description": "BomID", "format": "int64", "nullable": true}, "materialCode": {"type": "string", "nullable": true}, "materialName": {"type": "string", "nullable": true}, "idOrder": {"type": "integer", "format": "int64", "nullable": true}, "qty": {"type": "number", "format": "double", "nullable": true}, "sumQty": {"type": "number", "format": "double", "nullable": true}, "createBy": {"type": "integer", "description": "创建人ID", "format": "int64", "nullable": true}, "createTime": {"type": "string", "description": "创建时间/注册时间", "format": "date-time"}, "modifyBy": {"type": "integer", "description": "最后更新人", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "description": "最后更新时间", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "PartOrderPagedDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "orderCode": {"type": "string", "nullable": true}, "idProduct": {"type": "integer", "format": "int64", "nullable": true}, "idCustorm": {"type": "integer", "format": "int64", "nullable": true}, "idTec": {"type": "integer", "format": "int64", "nullable": true}, "status": {"type": "integer", "format": "int64", "nullable": true}}, "additionalProperties": false}, "PartOrderStepDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID", "format": "int64", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}, "idStep": {"type": "integer", "description": "BomID", "format": "int64", "nullable": true}, "stepCode": {"type": "string", "nullable": true}, "stepName": {"type": "string", "nullable": true}, "stepTime": {"type": "number", "format": "double", "nullable": true}, "waitTime": {"type": "number", "format": "double", "nullable": true}, "isKey": {"type": "integer", "format": "int32", "nullable": true}, "relationType": {"type": "integer", "format": "int32", "nullable": true}, "idOrder": {"type": "integer", "format": "int64", "nullable": true}, "createBy": {"type": "integer", "description": "创建人ID", "format": "int64", "nullable": true}, "createTime": {"type": "string", "description": "创建时间/注册时间", "format": "date-time"}, "modifyBy": {"type": "integer", "description": "最后更新人", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "description": "最后更新时间", "format": "date-time", "nullable": true}, "sort": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "PartPlanPendingDto": {"type": "object", "properties": {"creatName": {"type": "string", "nullable": true}, "modifyName": {"type": "string", "nullable": true}, "createBy": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "modifyBy": {"type": "integer", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "format": "date-time", "nullable": true}, "id": {"type": "integer", "description": "ID", "format": "int64"}, "idDetail": {"type": "integer", "description": "ID", "format": "int64"}, "planCode": {"type": "string", "description": "计划编码", "nullable": true}, "priority": {"type": "integer", "description": "优先级", "format": "int32"}, "priorityDis": {"type": "string", "description": "优先级", "nullable": true}, "status": {"type": "integer", "description": "生产状态", "format": "int32", "nullable": true}, "orderId": {"type": "integer", "description": "订单id", "format": "int64", "nullable": true}, "orderNumber": {"type": "string", "description": "订单编号", "nullable": true}, "userName": {"type": "string", "description": "客户", "nullable": true}, "deliveryDate": {"type": "string", "description": "交付日期", "format": "date-time", "nullable": true}, "qty": {"type": "number", "description": "计划排产数量", "format": "double"}, "productQty": {"type": "number", "description": "产品实际数量", "format": "double", "nullable": true}, "productName": {"type": "string", "description": "产品名称", "nullable": true}, "productCode": {"type": "string", "description": "产品编码", "nullable": true}, "tName": {"type": "string", "description": "工艺名称", "nullable": true}, "productList": {"type": "array", "items": {"$ref": "#/components/schemas/BasOrderProductDto"}, "description": "产品列表", "nullable": true}, "rules": {"type": "array", "items": {"$ref": "#/components/schemas/PartSchedulePlanRuleDto"}, "nullable": true}}, "additionalProperties": false, "description": "待排产计划"}, "PartPlanPendingPagedDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "priority": {"type": "string", "description": "优先级", "nullable": true}, "status": {"type": "string", "description": "生产状态", "nullable": true}, "orderNumber": {"type": "string", "description": "订单编号", "nullable": true}, "userName": {"type": "string", "description": "客户", "nullable": true}, "productCode": {"type": "string", "description": "产品编码", "nullable": true}, "productName": {"type": "string", "description": "产品名称", "nullable": true}}, "additionalProperties": false}, "PartPlanPendingResultDto": {"type": "object", "properties": {"creatName": {"type": "string", "nullable": true}, "modifyName": {"type": "string", "nullable": true}, "createBy": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "modifyBy": {"type": "integer", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "format": "date-time", "nullable": true}, "id": {"type": "integer", "description": "ID", "format": "int64"}, "planCode": {"type": "string", "description": "计划编码", "nullable": true}, "priority": {"type": "string", "description": "优先级", "nullable": true}, "status": {"type": "string", "description": "生产状态", "nullable": true}, "statusDis": {"type": "string", "description": "生产状态", "nullable": true}, "orderId": {"type": "integer", "description": "订单id", "format": "int64", "nullable": true}, "orderNumber": {"type": "string", "description": "订单编号", "nullable": true}, "userName": {"type": "string", "description": "客户", "nullable": true}, "deliveryDate": {"type": "string", "description": "交付日期", "nullable": true}, "qty": {"type": "number", "description": "计划排产数量", "format": "double"}, "productQty": {"type": "number", "description": "产品实际数量", "format": "double", "nullable": true}, "productName": {"type": "string", "description": "产品名称", "nullable": true}, "productCode": {"type": "string", "description": "产品编码", "nullable": true}, "tName": {"type": "string", "description": "工艺名称", "nullable": true}, "productList": {"type": "array", "items": {"$ref": "#/components/schemas/BasOrderProductDto"}, "description": "产品列表", "nullable": true}}, "additionalProperties": false}, "PartPlanPendingResultDtoPageModelDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/PartPlanPendingResultDto"}, "nullable": true}, "rowsCount": {"type": "integer", "format": "int32", "readOnly": true}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32", "readOnly": true}, "xData": {"nullable": true}}, "additionalProperties": false}, "PartSchedulPlanPageDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "state": {"type": "integer", "format": "int32"}, "sourceConflict": {"type": "string", "nullable": true}, "priority": {"type": "integer", "format": "int32"}, "orderNumber": {"type": "string", "description": "订单编号", "nullable": true}, "planCode": {"type": "string", "description": "计划编码", "nullable": true}, "scheduleCode": {"type": "string", "description": "排产编码", "nullable": true}, "productName": {"type": "string", "description": "产品名称", "nullable": true}}, "additionalProperties": false}, "PartSchedulPlanSourcePageDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "devCode": {"type": "string", "description": "设备编码", "nullable": true}, "devName": {"type": "string", "description": "设备名称", "nullable": true}, "dtype": {"type": "string", "description": "设备名称", "nullable": true}, "beginDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "PartSchedulePlanDto": {"type": "object", "properties": {"creatName": {"type": "string", "nullable": true}, "modifyName": {"type": "string", "nullable": true}, "createBy": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "modifyBy": {"type": "integer", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "format": "date-time", "nullable": true}, "id": {"type": "integer", "description": "排产计划编码", "format": "int64"}, "code": {"type": "string", "description": "排产计划编码", "nullable": true}, "planCode": {"type": "string", "description": "生产计划编码", "nullable": true}, "productId": {"type": "string", "description": "产品", "nullable": true}, "tcode": {"type": "string", "description": "产品", "nullable": true}, "tname": {"type": "string", "description": "产品", "nullable": true}, "proCode": {"type": "string", "description": "产品", "nullable": true}, "proName": {"type": "string", "description": "产品", "nullable": true}, "userName": {"type": "string", "description": "产品", "nullable": true}, "orderNum": {"type": "string", "description": "产品", "nullable": true}, "scheduleResult": {"type": "string", "nullable": true}, "sourceConflict": {"type": "string", "description": "资源冲突结果", "nullable": true}, "qty": {"type": "number", "description": "生产数量", "format": "double"}, "beginDate": {"type": "string", "description": "开始排产时间", "format": "date-time"}, "dtDelivery": {"type": "string", "description": "delivery time", "format": "date-time"}, "planBeginDate": {"type": "string", "description": "排产计划编码", "format": "date-time"}, "planEndDate": {"type": "string", "description": "生产计划编码", "format": "date-time"}, "state": {"type": "integer", "description": "计划状态", "format": "int32", "nullable": true}, "stateDis": {"type": "string", "description": "计划状态", "nullable": true}, "priority": {"type": "integer", "format": "int32", "nullable": true}, "priorityDis": {"type": "string", "nullable": true}, "scheduleType": {"type": "integer", "description": "来源", "format": "int32", "nullable": true}, "scheduleTypeDis": {"type": "string", "description": "来源", "nullable": true}, "rules": {"type": "array", "items": {"$ref": "#/components/schemas/PartSchedulePlanRuleDto"}, "description": "排产规则", "nullable": true}, "steps": {"type": "array", "items": {"$ref": "#/components/schemas/PartSchedulePlanStepDto"}, "nullable": true}, "productModule": {"$ref": "#/components/schemas/BasProductDto"}}, "additionalProperties": false, "description": "生产计划"}, "PartSchedulePlanDtoAppSrvResult": {"type": "object", "properties": {"isSuccess": {"type": "boolean", "readOnly": true}, "content": {"$ref": "#/components/schemas/PartSchedulePlanDto"}, "problemDetails": {"$ref": "#/components/schemas/ProblemDetails"}}, "additionalProperties": false}, "PartSchedulePlanDtoPageModelDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/PartSchedulePlanDto"}, "nullable": true}, "rowsCount": {"type": "integer", "format": "int32", "readOnly": true}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32", "readOnly": true}, "xData": {"nullable": true}}, "additionalProperties": false}, "PartSchedulePlanRuleDto": {"type": "object", "properties": {"creatName": {"type": "string", "nullable": true}, "modifyName": {"type": "string", "nullable": true}, "createBy": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "modifyBy": {"type": "integer", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "format": "date-time", "nullable": true}, "id": {"type": "integer", "description": "ID", "format": "int64"}, "ruleType": {"type": "string", "description": "规则类型", "nullable": true}, "ruleTypeName": {"type": "string", "description": "规则类型", "nullable": true}, "ruleValue": {"type": "string", "description": "规则值", "nullable": true}, "ruleValueName": {"type": "string", "description": "规则值", "nullable": true}, "scheduleCode": {"type": "string", "description": "排产计划编码", "nullable": true}, "nodes": {"type": "array", "items": {"$ref": "#/components/schemas/DictEnumTreeRto"}, "nullable": true}}, "additionalProperties": false, "description": "生产计划"}, "PartSchedulePlanStepDto": {"type": "object", "properties": {"creatName": {"type": "string", "nullable": true}, "modifyName": {"type": "string", "nullable": true}, "createBy": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "modifyBy": {"type": "integer", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "format": "date-time", "nullable": true}, "id": {"type": "integer", "description": "", "format": "int64"}, "scheduleCode": {"type": "string", "description": "排产计划Code", "nullable": true}, "stepCode": {"type": "string", "description": "工序编码", "nullable": true}, "reportResult": {"type": "number", "format": "double", "nullable": true}, "preCode": {"type": "string", "description": "前置工序", "nullable": true}, "reportDate": {"type": "string", "description": "报工时间", "format": "date-time", "nullable": true}, "beginDate": {"type": "string", "description": "开始日期", "format": "date-time"}, "endDate": {"type": "string", "description": "结束日期", "format": "date-time"}, "tat": {"type": "number", "description": "消耗工时;用产能模型中考虑产能的资源的单元工作时长计算消耗时长", "format": "double"}, "scheduleType": {"type": "integer", "description": "来源", "format": "int32", "nullable": true}, "scheduleTypeDis": {"type": "string", "description": "来源", "nullable": true}}, "additionalProperties": false, "description": "生产计划"}, "PartSchedulePlanStepReportDto": {"type": "object", "properties": {"scheduleCode": {"type": "string", "description": "排产计划Code", "nullable": true}, "stepCode": {"type": "string", "description": "工序编码", "nullable": true}, "reportResult": {"type": "number", "format": "double", "nullable": true}}, "additionalProperties": false}, "Personnel": {"type": "object", "properties": {"classesName": {"type": "string", "description": "班次名称", "nullable": true}, "color": {"type": "string", "description": "班次颜色", "nullable": true}, "qty": {"type": "integer", "description": "人员数量", "format": "int32"}}, "additionalProperties": false}, "PersonnelDto": {"type": "object", "properties": {"start": {"type": "string", "format": "date-time", "nullable": true}, "end": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false, "description": "人员计划查询条件"}, "PersonnelResultDto": {"type": "object", "properties": {"planDates": {"type": "string", "description": "计划时间", "nullable": true}, "classes": {"type": "array", "items": {"$ref": "#/components/schemas/Personnel"}, "nullable": true}}, "additionalProperties": false, "description": "人员计划返回结果"}, "PlanGanttDto": {"type": "object", "properties": {"tasks": {"type": "array", "items": {"$ref": "#/components/schemas/PlanProductionSchedulGanttChildItem"}, "nullable": true}, "links": {"type": "array", "items": {"$ref": "#/components/schemas/ScheduleGanttLink"}, "nullable": true}}, "additionalProperties": false}, "PlanMaterialChartDto": {"type": "object", "properties": {"day": {"type": "string", "nullable": true}, "needQty": {"type": "number", "description": "所需物料数量", "format": "double", "nullable": true}, "totalQty": {"type": "number", "description": "累计物料数量", "format": "double", "nullable": true}}, "additionalProperties": false}, "PlanMaterialDetailsDto": {"type": "object", "properties": {"orderNumber": {"type": "string", "description": "订单编号", "nullable": true}, "materialName": {"type": "string", "description": "物料名称", "nullable": true}, "materialCode": {"type": "string", "description": "物料编码", "nullable": true}, "getDays": {"type": "integer", "description": "默认获取7天的物料数据", "format": "int32", "nullable": true}, "day": {"type": "string", "nullable": true}, "productName": {"type": "string", "description": "产品名称", "nullable": true}, "productQty": {"type": "number", "description": "产品数量", "format": "double", "nullable": true}, "materialQty": {"type": "number", "description": "物料数量", "format": "double", "nullable": true}}, "additionalProperties": false}, "PlanMaterialDto": {"type": "object", "properties": {"orderNumber": {"type": "string", "description": "订单编号", "nullable": true}, "materialName": {"type": "string", "description": "物料名称", "nullable": true}, "materialCode": {"type": "string", "description": "物料编码", "nullable": true}, "getDays": {"type": "integer", "description": "默认获取7天的物料数据", "format": "int32", "nullable": true}}, "additionalProperties": false}, "PlanMaterialListDto": {"type": "object", "properties": {"materials": {"type": "array", "items": {"$ref": "#/components/schemas/PlanMaterialChartDto"}, "nullable": true}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/PlanMaterialDetailsDto"}, "description": "物料清单", "nullable": true}}, "additionalProperties": false}, "PlanNumByDateDto": {"type": "object", "properties": {"planDate": {"type": "string", "description": "计划创建时间", "nullable": true}, "planNum": {"type": "integer", "description": "生产计划数量", "format": "int32", "nullable": true}, "schedulingNum": {"type": "integer", "description": "已排产数量", "format": "int32", "nullable": true}}, "additionalProperties": false}, "PlanProductionSchedulDto": {"type": "object", "properties": {"creatName": {"type": "string", "nullable": true}, "modifyName": {"type": "string", "nullable": true}, "createBy": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "modifyBy": {"type": "integer", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "format": "date-time", "nullable": true}, "id": {"type": "integer", "description": "ID", "format": "int64"}, "planCode": {"type": "string", "description": "计划编码", "nullable": true}, "schedulCode": {"type": "string", "description": "排产编码", "nullable": true}, "planStart": {"type": "string", "description": "计划开始日期", "format": "date-time"}, "planEnd": {"type": "string", "description": "计划结束日期", "format": "date-time", "nullable": true}, "sorts": {"type": "integer", "description": "排序方式", "format": "int32"}, "preRule": {"type": "integer", "description": "前置规则", "format": "int32"}, "lCode": {"type": "string", "description": "产线编码", "nullable": true}, "lName": {"type": "string", "description": "产线名称", "nullable": true}, "status": {"type": "integer", "description": "计划状态", "format": "int32"}, "remark": {"type": "string", "nullable": true}, "planType": {"type": "integer", "description": "计划类型", "format": "int32", "nullable": true}, "priority": {"type": "integer", "description": "优先级", "format": "int32"}, "orderNumber": {"type": "string", "description": "订单编号", "nullable": true}, "userName": {"type": "string", "description": "客户", "nullable": true}, "deliveryDate": {"type": "string", "description": "交付日期", "format": "date-time", "nullable": true}, "productCode": {"type": "string", "description": "产品编码", "nullable": true}, "productName": {"type": "string", "description": "产品名称", "nullable": true}, "qty": {"type": "number", "description": "排产数量", "format": "double"}, "schedulTime": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false, "description": "生产计划"}, "PlanProductionSchedulGanttChildItem": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "productName": {"type": "string", "nullable": true}, "stepCode": {"type": "string", "nullable": true}, "stepName": {"type": "string", "nullable": true}, "startTime": {"type": "string", "format": "date-time", "nullable": true}, "endTime": {"type": "string", "format": "date-time", "nullable": true}, "duration": {"type": "integer", "format": "int32", "nullable": true}, "stepSort": {"type": "integer", "description": "工序排序码", "format": "int32"}, "parent": {"type": "string", "nullable": true}, "scheduleCode": {"type": "string", "nullable": true}, "progress": {"type": "number", "format": "double", "nullable": true}}, "additionalProperties": false}, "PlanProductionSchedulGanttForOrderDto": {"type": "object", "properties": {"stepName": {"type": "string", "description": "工序名称", "nullable": true, "writeOnly": true}, "stepCode": {"type": "string", "description": "工序编码", "nullable": true, "writeOnly": true}, "dateList": {"type": "array", "items": {"$ref": "#/components/schemas/GanttDate"}, "description": "日期", "nullable": true, "writeOnly": true}, "parent": {"type": "string", "nullable": true}, "orderNumber": {"type": "string", "description": "订单编号", "nullable": true}, "productName": {"type": "string", "description": "产品名称", "nullable": true}, "deliveryDate": {"type": "string", "description": "交付日期", "format": "date-time", "nullable": true}, "beginTime": {"type": "string", "description": "开始时间", "format": "date-time", "nullable": true}, "endTime": {"type": "string", "description": "结束时间", "format": "date-time", "nullable": true}, "duration": {"type": "integer", "format": "int32", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "PlanProductionSchedulGanttNewResultDto": {"type": "object", "properties": {"stepName": {"type": "string", "description": "工序名称", "nullable": true, "writeOnly": true}, "stepCode": {"type": "string", "description": "工序编码", "nullable": true, "writeOnly": true}, "dateList": {"type": "array", "items": {"$ref": "#/components/schemas/GanttDate"}, "description": "日期", "nullable": true, "writeOnly": true}, "orderNumber": {"type": "string", "description": "订单编号", "nullable": true}, "productName": {"type": "string", "description": "产品名称", "nullable": true, "writeOnly": true}, "productCount": {"type": "string", "description": "产品数量", "nullable": true}, "scheduleCount": {"type": "string", "description": "已排产数量", "nullable": true}, "scheduleCode": {"type": "string", "description": "排产编码", "nullable": true}, "planCode": {"type": "string", "description": "计划编码", "nullable": true}, "scheduleStatus": {"type": "string", "description": "排产状态", "nullable": true}, "deliveryDate": {"type": "string", "description": "交付日期", "format": "date-time", "nullable": true}, "beginTime": {"type": "string", "description": "开始时间", "format": "date-time", "nullable": true}, "endTime": {"type": "string", "description": "结束时间", "format": "date-time", "nullable": true}, "duration": {"type": "integer", "format": "int32", "nullable": true, "readOnly": true}, "ganttData": {"$ref": "#/components/schemas/PlanGanttDto"}}, "additionalProperties": false}, "PlanProductionSchedulGanttNewResultForPlanDto": {"type": "object", "properties": {"stepName": {"type": "string", "description": "工序名称", "nullable": true, "writeOnly": true}, "stepCode": {"type": "string", "description": "工序编码", "nullable": true, "writeOnly": true}, "dateList": {"type": "array", "items": {"$ref": "#/components/schemas/GanttDate"}, "description": "日期", "nullable": true, "writeOnly": true}, "orderNumber": {"type": "string", "description": "订单编号", "nullable": true}, "productName": {"type": "string", "description": "产品名称", "nullable": true, "writeOnly": true}, "productCount": {"type": "string", "description": "产品数量", "nullable": true}, "scheduleCount": {"type": "string", "description": "已排产数量", "nullable": true}, "scheduleCode": {"type": "string", "description": "排产编码", "nullable": true}, "planCode": {"type": "string", "description": "计划编码", "nullable": true}, "scheduleStatus": {"type": "string", "description": "排产状态", "nullable": true}, "deliveryDate": {"type": "string", "description": "交付日期", "format": "date-time", "nullable": true}, "beginTime": {"type": "string", "description": "开始时间", "format": "date-time", "nullable": true}, "endTime": {"type": "string", "description": "结束时间", "format": "date-time", "nullable": true}, "duration": {"type": "integer", "format": "int32", "nullable": true, "readOnly": true}, "ganttData": {"$ref": "#/components/schemas/PlanGanttDto"}, "scheduleType": {"type": "string", "nullable": true}, "scheduleDate": {"type": "string", "format": "date-time", "nullable": true}, "createTime": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "PlanProductionSchedulPageDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "description": "计划状态", "format": "int32"}, "orderNumber": {"type": "string", "description": "订单编号", "nullable": true}, "planCode": {"type": "string", "description": "计划编码", "nullable": true}, "scheduleCode": {"type": "string", "description": "排产编码", "nullable": true}, "productName": {"type": "string", "description": "产品名称", "nullable": true}}, "additionalProperties": false}, "PlanProductionSchedulResultDto": {"type": "object", "properties": {"creatName": {"type": "string", "nullable": true}, "modifyName": {"type": "string", "nullable": true}, "createBy": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "modifyBy": {"type": "integer", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "format": "date-time", "nullable": true}, "id": {"type": "integer", "description": "ID", "format": "int64"}, "planCode": {"type": "string", "description": "计划编码", "nullable": true}, "schedulCode": {"type": "string", "description": "排产编码", "nullable": true}, "planStart": {"type": "string", "description": "计划开始日期", "nullable": true}, "planEnd": {"type": "string", "description": "计划结束日期", "nullable": true}, "sorts": {"type": "integer", "description": "排序方式", "format": "int32"}, "preRule": {"type": "integer", "description": "前置规则", "format": "int32"}, "lCode": {"type": "string", "description": "产线编码", "nullable": true}, "lName": {"type": "string", "description": "产线名称", "nullable": true}, "status": {"type": "integer", "description": "计划状态", "format": "int32"}, "remark": {"type": "string", "nullable": true}, "planType": {"type": "integer", "description": "计划类型", "format": "int32", "nullable": true}, "priority": {"type": "integer", "description": "优先级", "format": "int32"}, "orderNumber": {"type": "string", "description": "订单编号", "nullable": true}, "userName": {"type": "string", "description": "客户", "nullable": true}, "deliveryDate": {"type": "string", "description": "交付日期", "nullable": true}, "productCode": {"type": "string", "description": "产品编码", "nullable": true}, "productName": {"type": "string", "description": "产品名称", "nullable": true}, "qty": {"type": "number", "description": "排产数量", "format": "double"}}, "additionalProperties": false}, "PlanProductionSchedulResultDtoPageModelDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/PlanProductionSchedulResultDto"}, "nullable": true}, "rowsCount": {"type": "integer", "format": "int32", "readOnly": true}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32", "readOnly": true}, "xData": {"nullable": true}}, "additionalProperties": false}, "ProblemDetails": {"type": "object", "properties": {"detail": {"type": "string", "nullable": true}, "instance": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32"}, "title": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}}, "additionalProperties": {}}, "ProductLineDto": {"type": "object", "properties": {"lname": {"type": "string", "description": "产线名称", "nullable": true}, "lcode": {"type": "string", "description": "产线编码", "nullable": true}}, "additionalProperties": false, "description": "查找产品对应的产线"}, "ProductPagedDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "proname": {"type": "string", "description": "产品名称", "nullable": true}, "procode": {"type": "string", "description": "产品编码", "nullable": true}, "status": {"type": "integer", "description": "状态", "format": "int32"}}, "additionalProperties": false}, "ProductionSchedulGanttDto": {"type": "object", "properties": {"orderNumber": {"type": "string", "description": "订单编码", "nullable": true}, "productCode": {"type": "string", "description": "产品编码", "nullable": true}, "scheduleCode": {"type": "string", "description": "排产编码", "nullable": true}, "scheduleStatus": {"type": "string", "description": "排产状态", "nullable": true}, "beginTime": {"type": "string", "description": "开始时间", "format": "date-time", "nullable": true}, "endTime": {"type": "string", "description": "截止时间", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "QueryDevStateDto": {"type": "object", "properties": {"devCode": {"type": "string", "nullable": true}, "targetDate": {"type": "string", "nullable": true}}, "additionalProperties": false}, "QuerySourceConflict": {"type": "object", "properties": {"scheduleCode": {"type": "string", "description": "排产编码", "nullable": true}, "planCode": {"type": "string", "description": "计划编码", "nullable": true}}, "additionalProperties": false}, "RelationPagedDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "idStep": {"type": "integer", "description": "产品名称", "format": "int64", "nullable": true}}, "additionalProperties": false}, "ReloadBasProcuct": {"type": "object", "properties": {"planCode": {"type": "string", "description": "计划编码", "nullable": true}, "schedulCode": {"type": "string", "description": "排产编码", "nullable": true}, "lists": {"type": "array", "items": {"$ref": "#/components/schemas/BasProductStandardCapacityDto"}, "nullable": true}}, "additionalProperties": false}, "ResultJson": {"type": "object", "properties": {"code": {"$ref": "#/components/schemas/HttpStatusCode"}, "msg": {"type": "string", "nullable": true}, "data": {"nullable": true}}, "additionalProperties": false}, "SchduleStepSolutionItem": {"type": "object", "properties": {"stepCode": {"type": "string", "nullable": true}, "stepName": {"type": "string", "nullable": true}, "solutionName": {"type": "string", "nullable": true}, "stationCode": {"type": "string", "nullable": true}, "stationName": {"type": "string", "nullable": true}, "lineCode": {"type": "string", "nullable": true}, "lineName": {"type": "string", "nullable": true}, "deviceCode": {"type": "string", "nullable": true}, "deviceName": {"type": "string", "nullable": true}, "workUnitTime": {"type": "number", "format": "double", "nullable": true}, "standardWorkTime": {"type": "number", "format": "double", "nullable": true}, "capacity": {"type": "number", "format": "double", "nullable": true}}, "additionalProperties": false}, "SchedulStatus": {"type": "object", "properties": {"status": {"type": "integer", "description": "排产状态", "format": "int32"}, "schedulCode": {"type": "array", "items": {"type": "string"}, "description": "排产编码", "nullable": true}}, "additionalProperties": false}, "SchedulStatusNew": {"type": "object", "properties": {"status": {"type": "integer", "description": "排产状态", "format": "int32"}, "schedulCode": {"type": "string", "description": "排产编码", "nullable": true}}, "additionalProperties": false}, "ScheduleDeviceGanttItem": {"type": "object", "properties": {"deviceName": {"type": "string", "description": "设备名称", "nullable": true}, "stepName": {"type": "string", "description": "工序名称", "nullable": true}, "beginTime": {"type": "string", "description": "开始时间", "format": "date-time", "nullable": true}, "endTime": {"type": "string", "description": "结束时间", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "ScheduleGanttLink": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "targetCode": {"type": "string", "nullable": true}, "sourceCode": {"type": "string", "nullable": true}, "type": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "StepPagedDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "name": {"type": "string", "description": "工序名称", "nullable": true}, "code": {"type": "string", "description": "工序编码", "nullable": true}, "status": {"type": "integer", "description": "状态", "format": "int32"}}, "additionalProperties": false}, "StockMaterialDto": {"type": "object", "properties": {"creatName": {"type": "string", "nullable": true}, "modifyName": {"type": "string", "nullable": true}, "createBy": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "modifyBy": {"type": "integer", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "format": "date-time", "nullable": true}, "id": {"type": "integer", "format": "int64", "nullable": true}, "mcode": {"type": "string", "description": "物料编码", "nullable": true}, "unit": {"type": "string", "description": "单位枚举", "nullable": true}, "unit_dis": {"type": "string", "description": "单位显示值", "nullable": true}, "mname": {"type": "string", "description": "物料名称", "nullable": true}, "qty": {"type": "number", "description": "物料库存", "format": "double", "nullable": true}, "preLockqty": {"type": "number", "description": "预锁库存", "format": "double", "nullable": true}}, "additionalProperties": false}, "StockMaterialDtoPageModelDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/StockMaterialDto"}, "nullable": true}, "rowsCount": {"type": "integer", "format": "int32", "readOnly": true}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32", "readOnly": true}, "xData": {"nullable": true}}, "additionalProperties": false}, "StockMaterialPagedDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "mcode": {"type": "string", "description": "物料编码", "nullable": true}, "mname": {"type": "string", "description": "物料名称", "nullable": true}}, "additionalProperties": false}, "StringAppSrvResult": {"type": "object", "properties": {"isSuccess": {"type": "boolean", "readOnly": true}, "content": {"type": "string", "nullable": true}, "problemDetails": {"$ref": "#/components/schemas/ProblemDetails"}}, "additionalProperties": false}, "SysTestDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "htName": {"type": "string", "nullable": true}, "htDescription": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TechnologyPagedDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "tname": {"type": "string", "description": "工序名称", "nullable": true}, "tcode": {"type": "string", "description": "工序编码", "nullable": true}, "status": {"type": "integer", "description": "状态", "format": "int32", "nullable": true}}, "additionalProperties": false}, "UpdateStatusDto": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int64"}, "description": "IDS", "nullable": true}, "status": {"type": "integer", "description": "状态", "format": "int32"}, "type": {"type": "integer", "description": "类型", "format": "int32"}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the Bear<PERSON> scheme.", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}], "tags": [{"name": "BasNumberManagement", "description": "编码管理"}, {"name": "BasOrder", "description": "客户订单"}, {"name": "HomePage", "description": "首页"}, {"name": "PartPlanPending", "description": "待排产计划"}, {"name": "PartSchedulePlan", "description": "排产计划"}, {"name": "PlanProductionSchedul", "description": "排产计划"}]}