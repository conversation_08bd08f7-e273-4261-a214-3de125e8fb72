﻿namespace Adnc.Demo.Maint.Application.Dtos;

[Serializable]
public class DictDto : OutputDto
{
    public string Name { get; set; } = string.Empty;

    public bool status { get; set; }

    public long? Pid { get; set; }

    /// <summary>
    /// 创建时间/注册时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    private string _value;

    public string Value
    {
        get => _value is not null ? _value : string.Empty;
        set => _value = value;
    }

    private IList<DictDto> _data = Array.Empty<DictDto>();

    public IList<DictDto> Children
    {
        get => _data;
        set
        {
            if (value != null)
            {
                _data = value;
            }
        }
    }
}

public class EnumDto
{
    public string label { get; set; }
    public dynamic value { get; set; }
}


public class EnumTreeDto
{
    public string label { get; set; }
    public dynamic value { get; set; }
    public List<EnumTreeDto> childs { get; set; }

}