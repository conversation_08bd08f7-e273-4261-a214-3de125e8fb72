﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Dtos
{
    public class BasStationPagedDto : SearchPagedDto
    {  
        /// <summary>
        /// 工位名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 工位编码
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        public string? DevName { get; set; }


        /// <summary>
        /// 状态
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 产线编码
        /// </summary>
        public string? LineCode {  get; set; }
    }

    public class BasStationSearchDto : IDto
    {
        /// <summary>
        /// 产线编码
        /// </summary>
        public string? LineCode { get; set; }
    }
}
