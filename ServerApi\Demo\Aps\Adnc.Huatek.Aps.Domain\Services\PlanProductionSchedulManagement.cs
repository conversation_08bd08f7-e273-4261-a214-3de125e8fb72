﻿using Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.SysTestAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.SysUserAggregate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Services
{
    public class PlanProductionSchedulManagement : IDomainService
    {
        private readonly IEfBasicRepository<PlanProductionSchedule> _planProductionSchedulManagementRepo;

        public PlanProductionSchedulManagement(IEfBasicRepository<PlanProductionSchedule> planProductionSchedulRepo)
        {
            _planProductionSchedulManagementRepo = planProductionSchedulRepo;
        }
    }
}
