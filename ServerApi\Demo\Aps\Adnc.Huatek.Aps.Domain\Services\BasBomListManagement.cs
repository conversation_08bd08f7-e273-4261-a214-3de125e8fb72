﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasBomListAggregate;
using Adnc.Infra.Helper;
using Adnc.Shared.Rpc.Event;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using static StackExchange.Redis.Role;

namespace Adnc.Huatek.Aps.Domain.Services
{
    public class BasBomListManagement : IDomainService
    {
        private readonly IEfBasicRepository<BasBomList> _basBomListRepo;

        public BasBomListManagement(IEfBasicRepository<BasBomList> basBomListRepo) 
        {
            _basBomListRepo = basBomListRepo;
        }

    }
}
