﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasBomAggregate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class BasBomConfig : AbstractEntityTypeConfiguration<BasBom>
    {
        public override void Configure(EntityTypeBuilder<BasBom> builder)
        {
            base.Configure(builder);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.ProductCode).HasColumnName("productcode");
            builder.Property(x => x.MaterialCode).HasColumnName("materialcode");
            builder.Property(x => x.MaterialName).HasColumnName("materialname");
            builder.Property(x => x.Qty).HasColumnName("qty");
            builder.Property(x => x.Remark).HasColumnName("remark");
            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.CreateBy).HasColumnName("createby");
            builder.Property(x => x.CreateTime).HasColumnName("createtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
        }
    }
}
