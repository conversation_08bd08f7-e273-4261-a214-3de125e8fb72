﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Shared.Application.Contracts.ResultModels;

namespace Adnc.Huatek.Aps.Application.Services
{
    public interface IPartSchedulePlanService : IAppService
    {

        [OperateLog(LogName = "一键排产")]
        Task<ResultJson> CreateSchedulePlan(PartSchedulePlanDto input);
        [OperateLog(LogName = "获取排产计划列表")]
        Task<PageModelDto<PartSchedulePlanDto>> GetPagedAsync(PartSchedulPlanPageDto search);
        [OperateLog(LogName = "排产计划改变状态")]
        Task<ResultJson> SetScheduleStatusAsync(SchedulStatusNew input);


        [OperateLog(LogName = "排产计划工序修改")]
        Task<ResultJson> ModuleSchedulePlan(PartSchedulePlanDto input);
        [OperateLog(LogName = "试算")]
        Task<PlanGanttDto> TrialSchedulePlan(PartSchedulePlanDto input);

        [OperateLog(LogName = "调整排产计划时核验资源情况")]
        Task<ResultJson> CheckSourceEffiveAsync(List<PartSchedulePlanStepDto> dtos);

        [OperateLog(LogName = "判断资源是否存在冲突")]
        Task<AppSrvResult<string>> CheckSourceConfilct(SchedulStatusNew input);

        //[OperateLog(LogName = "测试分批")]
        //Task<AppSrvResult<string>> testBatch();

        [OperateLog(LogName = "保存工序进度")]
        Task<AppSrvResult> SaveStepProgress(PartSchedulePlanStepReportDto input);

        [OperateLog(LogName = "获取资源甘特图")]
        Task<List<DeviceGanttDto>> GetDeviceGanttPlan(PartSchedulPlanSourcePageDto search);

        [OperateLog(LogName = "分析资源冲突情况")]
        Task<ResultJson> CheckSourceConflict(QuerySourceConflict input);
        [OperateLog(LogName = "删除数据")]
        Task<AppSrvResult<long>> BatchDelAsync(List<string> codes);
    }
}
