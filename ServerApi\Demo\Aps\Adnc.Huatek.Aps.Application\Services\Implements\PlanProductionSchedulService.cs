﻿using Adnc.Huatek.Aps.Application.Adnc.Huatek.Aps.Application.Services.Implements;
using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.BasClassesAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasDevice;
using Adnc.Huatek.Aps.Domain.Aggregates.BasHolidayAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasLineAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasMaterial;
using Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductStandardCapacityAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductStepRelationAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductTechnologyRelationAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStation;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStepAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStepMaterialRelationAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasTechnologyAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasTechnologyStepRelationAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanMaterialAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanRuleAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanSourceAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanStepAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulStepDetailsAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PlanSchedulStepSolutionAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.SourceSchedulDeviceStationAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.StockMaterial;
using Adnc.Huatek.Aps.Domain.Aggregates.SysUserAggregate;
using Adnc.Huatek.Aps.Domain.Services;
using Adnc.Infra.IdGenerater.Yitter;
using Adnc.Shared;
using Adnc.Shared.Application.Contracts.ResultModels;
using MongoDB.Driver.Linq;
using Newtonsoft.Json;
using System.Net;
using System.Runtime.Serialization.Formatters.Binary;
using System.Text;

namespace Adnc.Huatek.Aps.Application.Services.Implements
{
    public class PlanProductionSchedulService : AbstractAppService, IPlanProductionSchedulService
    {
        private readonly PlanProductionSchedulManagement _planProductionSchedulMgr;
        private readonly IEfBasicRepository<PlanProductionSchedule> _planProductionSchedulRepo;
        private readonly IEfBasicRepository<SysUser> _sysUserRepo;
        private readonly IEfBasicRepository<PartPlanPending> _pendingPlanRepo;
        private readonly IEfBasicRepository<BasProduct> _basProductRepo;
        private readonly IEfBasicRepository<BasTechnology> _basTecRepo;
        private readonly IEfBasicRepository<BasProductTechnologyRelation> _basProTecRelRepo;
        private readonly IEfBasicRepository<BasOrderProduct> _basOrderProductRepo;
        private readonly IEfBasicRepository<BasLine> _basLineRepo;
        private readonly IEfBasicRepository<BasOrder> _basOrderRepo;
        private readonly IEfBasicRepository<BasDevice> _basDeviceRepo;
        private readonly IEfBasicRepository<BasStep> _basStepRepo;
        private readonly IEfBasicRepository<BasMaterial> _basMatRepo;
        private readonly IEfRepository<PlanProductionSchedulStepDetails> _plandetailsRepo;
        private readonly BasNumberManagementService _basNumberManagementService;
        private readonly IEfBasicRepository<PartPlanPending> _basplanPendingRepo;
        private readonly IEfBasicRepository<BasProductStandardCapacity> _basProductStandardCapacityRepo;
        private readonly IEfBasicRepository<BasProductStepRelation> _basProStepRelRepo;
        private readonly IEfBasicRepository<BasTechnologyStepRelation> _basTechnologyStepRelationRepo;
        private readonly IEfBasicRepository<PlanProductionSchedulStepDetails> _planProductionSchedulStepDetailsRepo;
        private readonly IEfBasicRepository<SourceSchedulDeviceStation> _planSolutionRepo;
        private readonly IEfBasicRepository<BasClasses> _classRepo;
        private readonly IEfBasicRepository<BasStationClasses> _basStationClassRepo;
        private readonly IEfBasicRepository<PlanStepCapacity> _planStepCapacityRepo;
        private readonly IEfBasicRepository<BasStepMaterialRelation> _basStepMatRepo;
        private readonly IEfBasicRepository<BasProductStepRelationMaterial> _proStepMatRepo;
        private readonly IEfBasicRepository<BasHoliday> _basHolidayRepo;
        private readonly IEfBasicRepository<BasStation> _basStationRepo;
        private readonly IEfBasicRepository<StockMaterial> _stockMRepo;
        private readonly IEfBasicRepository<PartSchedulePlan> _spRepo;
        private readonly IEfBasicRepository<PartSchedulePlanStep> _spStepRepo;
        private readonly IEfBasicRepository<PartSchedulePlanRule> _ruleRepo;
        private readonly IEfBasicRepository<PartSchedulePlanSource> _spSourceRepo;
        private readonly IEfBasicRepository<PartSchedulePlanMaterial> _spMatRepo;
        private readonly IEfBasicRepository<PartSchedulePlanInfo> _spInfoRepo;
        public IMaintRestClient _maintRestClient;

        private readonly IEfBasicRepository<PlanSchedulStepSolution> _planschedulStepSolutionRepo;

        public PlanProductionSchedulService(PlanProductionSchedulManagement planProductionSchedulMgr, IMaintRestClient maintRestClient,
            IEfBasicRepository<PlanProductionSchedule> planProductionSchedulRepo,
            IEfBasicRepository<SysUser> sysUserRepo,
             IEfBasicRepository<PartSchedulePlanRule> ruleRepo,
            IEfBasicRepository<BasOrder> basOrderRepo,
            IEfBasicRepository<BasStep> basStepRepo,
            IEfBasicRepository<BasDevice> basDeviceRepo,
            IEfBasicRepository<BasOrderProduct> basOrderProductRepo,
            IEfBasicRepository<PartPlanPending> pendingPlanRepo,
            IEfBasicRepository<BasProduct> basProductRepo,
            IEfBasicRepository<BasProductTechnologyRelation> basProTecRelRepo,
            IEfBasicRepository<BasTechnology> basTecRepo,
            IEfBasicRepository<BasLine> basLineRepo,
            IEfBasicRepository<BasProductStepRelationMaterial> proStepMatRepo,
            IEfBasicRepository<PartSchedulePlan> spRepo,
            IEfBasicRepository<PartSchedulePlanMaterial> spMatRepo,
            IEfBasicRepository<PartSchedulePlanStep> spStepRepo,
            IEfRepository<PlanProductionSchedulStepDetails> plandetailsRepo,
            BasNumberManagementService basNumberManagementService,
            IEfBasicRepository<BasMaterial> basMatRepo,
            IEfBasicRepository<PartPlanPending> basplanPendingRepo,
            IEfBasicRepository<BasProductStandardCapacity> basProductStandardCapacityRepo,
            IEfBasicRepository<BasProductStepRelation> basProStepRelRepo,
            IEfBasicRepository<BasTechnologyStepRelation> basTechnologyStepRelationRepo,
            IEfBasicRepository<PlanProductionSchedulStepDetails> planProductionSchedulStepDetailsRepo,
            IEfBasicRepository<SourceSchedulDeviceStation> planSolutionRepo,
            IEfBasicRepository<BasClasses> classRepo,
            IEfBasicRepository<BasStationClasses> basStationClassRepo,
            IEfBasicRepository<PlanStepCapacity> planStepCapacityRepo,
            IEfBasicRepository<BasHoliday> basHolidayRepo,
            IEfBasicRepository<BasStation> basStationRepo,
            IEfBasicRepository<PlanSchedulStepSolution> planschedulStepSolutionRepo,
            IEfBasicRepository<StockMaterial> stockMRepo,
            IEfBasicRepository<BasStepMaterialRelation> basStepMatRepo,
            IEfBasicRepository<PartSchedulePlanSource> spSourceRepo, IEfBasicRepository<PartSchedulePlanInfo> spInfoRepo
               )
        {
            _planProductionSchedulMgr = planProductionSchedulMgr;
            _planProductionSchedulRepo = planProductionSchedulRepo;
            _sysUserRepo = sysUserRepo;
            _basOrderRepo = basOrderRepo;
            _basDeviceRepo = basDeviceRepo;
            _pendingPlanRepo = pendingPlanRepo;
            _basOrderProductRepo = basOrderProductRepo;
            _basLineRepo = basLineRepo;
            _proStepMatRepo = proStepMatRepo;
            _ruleRepo = ruleRepo;
            _maintRestClient = maintRestClient;
            _basProductRepo = basProductRepo;
            _basTecRepo = basTecRepo;
            _basStepRepo = basStepRepo;
            _basProTecRelRepo = basProTecRelRepo;
            _plandetailsRepo = plandetailsRepo;
            _basNumberManagementService = basNumberManagementService;
            _basplanPendingRepo = basplanPendingRepo;
            _basProductStandardCapacityRepo = basProductStandardCapacityRepo;
            _basProStepRelRepo = basProStepRelRepo;
            _basTechnologyStepRelationRepo = basTechnologyStepRelationRepo;
            _planProductionSchedulStepDetailsRepo = planProductionSchedulStepDetailsRepo;
            _planSolutionRepo = planSolutionRepo;
            _classRepo = classRepo;
            _spRepo = spRepo;
            _basMatRepo = basMatRepo;
            _spStepRepo = spStepRepo;
            _basStationClassRepo = basStationClassRepo;
            _planStepCapacityRepo = planStepCapacityRepo;
            _basHolidayRepo = basHolidayRepo;
            _basStationRepo = basStationRepo;
            _planschedulStepSolutionRepo = planschedulStepSolutionRepo;
            _stockMRepo = stockMRepo;
            _basStepMatRepo = basStepMatRepo;
            _spSourceRepo = spSourceRepo;
            _spMatRepo = spMatRepo;
            _spInfoRepo = spInfoRepo;
        }


        public async Task<PartSchedulePlanDto> GetDetailsNewAsync(long id)
        {

            var result = new PartSchedulePlanDto();
            //排产计划数据
            var schedulDto = await _spRepo.GetAsync(id);
            var ruleEntites = await _ruleRepo.Where(x => !x.IsDeleted && x.ScheduleCode == schedulDto.Code).ToListAsync();
            var userDto= _sysUserRepo.Where(x => x.Id == schedulDto.CreateBy).FirstOrDefault();
            //生产计划数据
            var planDto =  _pendingPlanRepo.Where(x => !x.IsDeleted && x.PlanCode == schedulDto.PlanCode).FirstOrDefault();
            //产品信息
            var basProductDto = _basProductRepo.Where(x => !x.IsDeleted && x.Procode == planDto.ProductCode).FirstOrDefault();
            //工艺路线
            var basTecDto = _basTecRepo.Where(x => !x.IsDeleted && x.Tcode == basProductDto.TCode).FirstOrDefault();

            var ruleDtos = Mapper.Map<List<PartSchedulePlanRuleDto>>(ruleEntites);


            var restRpcResultNew = await _maintRestClient.GetChildByCodeAsync("排产规则");
            if (restRpcResultNew.IsSuccessStatusCode)
            {
                var ruleEnum = restRpcResultNew.Content;
                if (ruleEnum is not null)
                {
                    ruleDtos.ForEach(d =>
                    {
                        var obj = ruleEnum.childs.FirstOrDefault(x => x.value == d.RuleType);
                        if (obj != null)
                        {
                            d.RuleTypeName = obj.label ?? "";
                            d.Nodes = obj.childs.ToList();
                            if(d.Nodes.Any())
                            d.RuleValueName = d.Nodes.FirstOrDefault(x => x.value == d.RuleValue).label;
                        }

                    });
                }
            }

            // 添加反序列化验证
            var ProductModule = await _spInfoRepo.Where(x => !x.IsDeleted && x.ScheduleCode == schedulDto.Code).FirstOrDefaultAsync();
            var json = ProductModule.ProductModule
                ?? throw new InvalidOperationException("找不到产能模型数据");

            var productDto = JsonConvert.DeserializeObject<BasProductDto>(json)
                ?? throw new InvalidDataException("产能模型数据格式错误");

            result = new PartSchedulePlanDto
            {
                ProductId = basProductDto.Id.ToString(),
                OrderNum = planDto.OrderNumber,
                Priority = planDto.Priority,
                Qty = planDto.Qty,
                State = schedulDto.State,
                ProName = planDto.ProductName,
                ProCode = planDto.ProductCode,
                DtDelivery = planDto.DeliveryDate,
                UserName = planDto.UserName,
                Tname = basTecDto.Tname,
                Tcode = basTecDto.Tcode,
                PlanCode = schedulDto.PlanCode,
                Code = schedulDto.Code,
                BeginDate = schedulDto.BeginDate,
                PlanBeginDate = schedulDto.PlanBeginDate,
                PlanEndDate = schedulDto.PlanEndDate,
                CreatName = userDto.Name,
                Rules = ruleDtos,
                ProductModule = productDto


            };
            return result;
        }

        public async Task<PlanProductionSchedulDetailsResultDto> GetDetailsAsync(string schCode)
        {
            schCode.TrimStringFields();

            var result = new PlanProductionSchedulDetailsResultDto();

            var schedulEntities = _planProductionSchedulRepo.Where(x => true);

            var basProductEntities = _basProductRepo.Where(x => true);
            var basTecEntities = _basTecRepo.Where(x => true);

            var basOrderProductEntities = _basOrderProductRepo.Where(x => true);
            var basOrderEntities = _basOrderRepo.Where(x => true);
            var basPendingPlanEntities = _pendingPlanRepo.Where(x => true);

            var schedules = from s in schedulEntities
                            join p in basPendingPlanEntities on s.PlanCode equals p.PlanCode into t1
                            from ss in t1.DefaultIfEmpty()
                            join product in basProductEntities on ss.ProductCode equals product.Procode into t2
                            from t2s in t2.DefaultIfEmpty()
                            join t in basTecEntities on t2s.TCode equals t.Tcode into t3
                            from t3s in t3.DefaultIfEmpty()
                            join ts in basOrderProductEntities on t2s.Procode equals ts.ProductCode into t4
                            from t4s in t4.DefaultIfEmpty()
                            join o in basOrderEntities on t4s.OrderId equals o.Id into productOrders
                            from x in productOrders.DefaultIfEmpty()
                            where s.SchedulCode == schCode && ss.OrderNumber == x.OrderNumber
                            select new BasOrderInfo
                            {
                                ProductId = t2s.Id,
                                OrderNumber = x.OrderNumber,
                                Priority = ss.Priority,
                                Qty = ss.Qty,
                                Status = s.Status,
                                ProductName = t2s.Proname,
                                ProductCode = t2s.Procode,
                                DeliveryDate = x.DeliveryDate,
                                UserName = x.UserName,
                                TName = t3s.Tname,
                                TCode = t3s.Tcode,
                                Remark = s.Remark,
                                PlanCode = s.PlanCode,
                                SchedulCode = s.SchedulCode,
                                Quantity = t4s.Quantity,
                                ScheduleTime = s.SchedulTime

                            };
            if (schedules.Count() > 0)
            {
                result.BasOrderInfo = schedules.FirstOrDefault();
            }

            var stepSolutionList = await GetStepSolution(schCode);
            result.BasOrderInfo.Lines = stepSolutionList.Select(x => x.LineName).Distinct().ToList() ?? new List<string>();

            return result;
        }

        /// <summary>
        /// 获取分页列表
        /// </summary>
        /// <param name="search"></param>
        /// <returns></returns>
        public async Task<PageModelDto<PlanProductionSchedulResultDto>> GetPagedAsync(PlanProductionSchedulPageDto search)
        {
            search.TrimStringFields();
            var whereExpression = ExpressionCreator
                                                .New<PlanProductionSchedule>()
                                                .And(x => !x.IsDeleted)
                                                // .AndIf(search.OrderNumber.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.OrderNumber!, $"%{search.OrderNumber}%"))
                                                .AndIf(search.PlanCode.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.PlanCode!, $"%{search.PlanCode}%"))
                                                .AndIf(search.ScheduleCode.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.SchedulCode!, $"%{search.ScheduleCode}%"))
                                                .AndIf(search.Status > -1, x => x.Status == search.Status);

            var total = await _planProductionSchedulRepo.CountAsync(whereExpression);
            if (total == 0)
                return new PageModelDto<PlanProductionSchedulResultDto>(search);

            var plans = _planProductionSchedulRepo.Where(whereExpression)
                                            .OrderByDescending(x => x.ModifyTime)
                                            .ThenByDescending(x => x.CreateTime)
                                            .Skip(search.SkipRows())
                                            .Take(search.PageSize);
            var wherepending = ExpressionCreator
                                                .New<PartPlanPending>()
                                                .And(x => !x.IsDeleted)
                                                .AndIf(search.OrderNumber.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.OrderNumber!, $"%{search.OrderNumber}%"))
                                                .AndIf(search.ProductName.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.ProductName!, $"%{search.ProductName}%"));
            var pendingPlans = _pendingPlanRepo.Where(wherepending);
            var lines = _basLineRepo.Where(x => !x.IsDeleted);
            var users = _sysUserRepo.Where(x => true);
            var entities = await (from p in plans
                                  join s in pendingPlans on p.PlanCode equals s.PlanCode
                                  join l in lines on p.LCode equals l.Lcode into g
                                  from xx in g.DefaultIfEmpty()
                                  join u in users on p.CreateBy equals u.Id
                                  select new PlanProductionSchedulResultDto
                                  {
                                      Id = p.Id,
                                      PlanCode = p.PlanCode,
                                      SchedulCode = p.SchedulCode,
                                      OrderNumber = s.OrderNumber,
                                      Status = p.Status,
                                      PlanType = p.PlanType,
                                      Priority = s.Priority,
                                      DeliveryDate = $"{s.DeliveryDate:yyyy-MM-dd}",
                                      ProductCode = s.ProductCode,
                                      ProductName = s.ProductName,
                                      Qty = s.Qty,
                                      UserName = s.UserName,
                                      PlanStart = $"{p.PlanStart:yyyy-MM-dd}",
                                      PlanEnd = $"{p.PlanEnd:yyyy-MM-dd}",
                                      Sorts = p.Sorts,
                                      PreRule = p.PreRule,
                                      LCode = p.LCode,
                                      LName = xx.Lname,
                                      CreateBy = p.CreateBy,
                                      CreateTime = p.CreateTime,
                                      CreatName = u.Name
                                  }

                       ).ToListAsync();

            return new PageModelDto<PlanProductionSchedulResultDto>(search, entities, total);
        }

        /// <summary>
        /// 获取物料计划
        /// </summary>
        /// <param name="search"></param>
        /// <returns></returns>
        public async Task<PlanMaterialListDto> GetMaterials(PlanMaterialDto search)
        {
            search.TrimStringFields();

            var sql = new StringBuilder();
            var where = new StringBuilder("");
            if (search.GetDays > 0)
            {
                where = where.AppendFormat(@" where procode = productcode and scheduldate>=CURDATE() and scheduldate<=DATE_ADD(CURDATE(), INTERVAL {0} DAY) ", search.GetDays);
            }
            else
            {
                where = where.Append(" where procode = productcode and scheduldate>=CURDATE() and scheduldate<=DATE_ADD(CURDATE(), INTERVAL 7 DAY) ");
            }
            sql = sql.Append(@"select scheduldate,p.exenum ,
                ppp.ordernumber,ppp.productname ,ppp.qty productQty ,sm.materialname ,sm.qty materialQty
                from plan_production_schedul_step_details p
                join bas_step_material_relation sm on p.stepcode = sm.stepcode
                join plan_production_schedule sc  on p.schedulcode=sc.schedulcode and sc.status = 1
                join part_plan_pending ppp on ppp.plancode =sc.plancode 
                ");
            if (!string.IsNullOrWhiteSpace(search.OrderNumber))
            {
                where = where.AppendFormat(@" and  ppp.ordernumber like '%{0}%'", search.OrderNumber);
            }

            if (!string.IsNullOrWhiteSpace(search.MaterialCode))
            {
                where = where.AppendFormat(@" and sm.materialcode like '%{0}%'", search.MaterialCode);
            }
            if (!string.IsNullOrWhiteSpace(search.MaterialName))
            {
                where = where.AppendFormat(@" and sm.materialname like '%{0}%'", search.MaterialName);
            }
            sql = sql.Append(where);//.Append(" group by  scheduldate,p.exenum,ppp.productname ,ppp.qty,sm.materialname ,sm.qty ");
            var result = _plandetailsRepo.AdoQuerier.QueryAsync<PlanMaterial>(sql.ToString());
            var dto = new PlanMaterialListDto();
            if (result.Result != null)
            {
                var s = result.Result.OrderBy(x => x.SchedulDate).ToList();
                if (s.Any())
                {
                    var now = DateTime.Now;

                    for (var dateTime = now.Date; dateTime < now.AddDays(7); dateTime = dateTime.AddDays(1))
                    {
                        if (!s.Exists(x => x.SchedulDate.Value.Date == dateTime.Date))
                        {
                            s.Add(new PlanMaterial()
                            {
                                SchedulDate = dateTime.Date,
                                MaterialQty = 0
                            });
                        }
                    }

                    s = s.OrderBy(x => x.SchedulDate).ToList();

                    var minDay = s.Min(x => x.SchedulDate).Value;
                    var mm = s.GroupBy(x => x.SchedulDate.Value.ToString("yyyy-MM-dd")).ToList();
                    decimal total = 0;
                    foreach (var item in mm)
                    {
                        var d = Convert.ToDateTime(item.Key);
                        var m = new PlanMaterialChartDto()
                        {
                            Day = "P" + (d - minDay).Days,
                            NeedQty = item.Sum(x => x.ProductQty * x.MaterialQty * x.ExeNum)
                        };
                        total = total + m.NeedQty ?? 0;
                        m.TotalQty = total;
                        dto.Materials.Add(m);
                    }
                    foreach (var detail in s)
                    {
                        if (detail.OrderNumber is null) continue;
                        var date = Convert.ToDateTime(detail.SchedulDate.Value);
                        var d = new PlanMaterialDetailsDto()
                        {
                            Day = "P" + (date - minDay).Days,
                            OrderNumber = detail.OrderNumber,
                            ProductName = detail.ProductName,
                            ProductQty = detail.ProductQty,
                            MaterialName = detail.MaterialName,
                            MaterialQty = detail.MaterialQty
                        };
                        dto.Items.Add(d);
                    }
                }
            }


            return dto;
        }


        /// <summary>
        /// 获取物料计划
        /// </summary>
        /// <param name="search"></param>
        /// <returns></returns>
        public async Task<PlanMaterialListDto> GetMaterialsNew(PlanMaterialDto search)
        {
            search.TrimStringFields();

            var sql = new StringBuilder();
            var where = new StringBuilder("");
            if (search.GetDays > 0)
            {
                where = where.AppendFormat(@" and  ppp.productcode  = productcode and a.needDate >=CURDATE() and a.needDate <=DATE_ADD(CURDATE(), INTERVAL {0} DAY)", search.GetDays);
            }
            else
            {
                where = where.Append("  and  ppp.productcode = productcode and a.needDate>=CURDATE() and a.needDate<=DATE_ADD(CURDATE(), INTERVAL 7 DAY) ");
            }
            sql = sql.Append(@"  select a.needDate  scheduldate,1 exenum,ppp.ordernumber,ppp.productname ,ppp.qty productQty,bm.name materialname,(a.Qty/ppp.qty) materialQty
                from part_schedule_plan_material a
                left join part_schedule_plan b on b.Code  = a.ScheduleCode  and b.state  = 1
                left join part_plan_pending ppp on ppp.plancode  = b.PlanCode 
                left join bas_material bm on bm.code  = a.Mcode 
                where ppp.ordernumber is not null 
                ");
            if (!string.IsNullOrWhiteSpace(search.OrderNumber))
            {
                where = where.AppendFormat(@" and  ppp.ordernumber like '%{0}%'", search.OrderNumber);
            }

            if (!string.IsNullOrWhiteSpace(search.MaterialCode))
            {
                where = where.AppendFormat(@" and bm.code like '%{0}%'", search.MaterialCode);
            }
            if (!string.IsNullOrWhiteSpace(search.MaterialName))
            {
                where = where.AppendFormat(@" and bm.name like '%{0}%'", search.MaterialName);
            }
            sql = sql.Append(where);//.Append(" group by  scheduldate,p.exenum,ppp.productname ,ppp.qty,sm.materialname ,sm.qty ");
            var result = _plandetailsRepo.AdoQuerier.QueryAsync<PlanMaterial>(sql.ToString());
            var dto = new PlanMaterialListDto();
            if (result.Result != null)
            {
                var s = result.Result.OrderBy(x => x.SchedulDate).ToList();
                if (s.Any())
                {
                    var now = DateTime.Now;

                    for (var dateTime = now.Date; dateTime < now.AddDays(7); dateTime = dateTime.AddDays(1))
                    {
                        if (!s.Exists(x => x.SchedulDate.Value.Date == dateTime.Date))
                        {
                            s.Add(new PlanMaterial()
                            {
                                SchedulDate = dateTime.Date,
                                MaterialQty = 0
                            });
                        }
                    }

                    s = s.OrderBy(x => x.SchedulDate).ToList();

                    var minDay = s.Min(x => x.SchedulDate).Value;
                    var mm = s.GroupBy(x => x.SchedulDate.Value.ToString("yyyy-MM-dd")).ToList();
                    decimal total = 0;
                    foreach (var item in mm)
                    {
                        var d = Convert.ToDateTime(item.Key);
                        var m = new PlanMaterialChartDto()
                        {
                            Day = "P" + (d - minDay).Days,
                            NeedQty = item.Sum(x => x.ProductQty * x.MaterialQty * x.ExeNum)
                        };
                        total = total + m.NeedQty ?? 0;
                        m.TotalQty = total;
                        dto.Materials.Add(m);
                    }
                    foreach (var detail in s)
                    {
                        if (detail.OrderNumber is null) continue;
                        var date = Convert.ToDateTime(detail.SchedulDate.Value);
                        var d = new PlanMaterialDetailsDto()
                        {
                            Day = "P" + (date - minDay).Days,
                            OrderNumber = detail.OrderNumber,
                            ProductName = detail.ProductName,
                            ProductQty = detail.ProductQty,
                            MaterialName = detail.MaterialName,
                            MaterialQty = detail.MaterialQty
                        };
                        dto.Items.Add(d);
                    }
                }
            }
            dto.Items = dto.Items.OrderByDescending(x=>x.Day).ToList();

            return dto;
        }



        /// <summary>
        /// 计算物料预警
        /// </summary>
        /// <param name="search"></param>
        /// <returns></returns>
        public async Task<PlanMaterialListDto> GetMaterialsWarning(PlanMaterialDto search)
        {
            search.TrimStringFields();

            var sql = new StringBuilder();
            var where = new StringBuilder("");
            if (search.GetDays > 0)
            {
                where = where.AppendFormat(@" where procode = productcode and scheduldate>=CURDATE() and scheduldate<=DATE_ADD(CURDATE(), INTERVAL {0} DAY) ", search.GetDays);
            }
            else
            {
                where = where.Append(" where procode = productcode and scheduldate>=CURDATE() and scheduldate<=DATE_ADD(CURDATE(), INTERVAL 7 DAY) ");
            }
            sql = sql.Append(@"select scheduldate,p.exenum ,
                ppp.ordernumber,ppp.productname ,ppp.qty productQty ,sm.materialname ,sm.qty materialQty
                from plan_production_schedul_step_details p
                join bas_step_material_relation sm on p.stepcode = sm.stepcode
                join plan_production_schedule sc  on p.schedulcode=sc.schedulcode and sc.status = 1
                join part_plan_pending ppp on ppp.plancode =sc.plancode 
                ");
            if (!string.IsNullOrWhiteSpace(search.OrderNumber))
            {
                where = where.AppendFormat(@" and  ppp.ordernumber like '%{0}%'", search.OrderNumber);
            }

            if (!string.IsNullOrWhiteSpace(search.MaterialCode))
            {
                where = where.AppendFormat(@" and sm.materialcode like '%{0}%'", search.MaterialCode);
            }
            if (!string.IsNullOrWhiteSpace(search.MaterialName))
            {
                where = where.AppendFormat(@" and sm.materialname like '%{0}%'", search.MaterialName);
            }
            sql = sql.Append(where);//.Append(" group by  scheduldate,p.exenum,ppp.productname ,ppp.qty,sm.materialname ,sm.qty ");
            var result = _plandetailsRepo.AdoQuerier.QueryAsync<PlanMaterial>(sql.ToString());
            var dto = new PlanMaterialListDto();
            if (result.Result != null)
            {
                var s = result.Result.OrderBy(x => x.SchedulDate).ToList();
                if (s.Any())
                {
                    var now = DateTime.Now;

                    for (var dateTime = now.Date; dateTime < now.AddDays(7); dateTime = dateTime.AddDays(1))
                    {
                        if (!s.Exists(x => x.SchedulDate.Value.Date == dateTime.Date))
                        {
                            s.Add(new PlanMaterial()
                            {
                                SchedulDate = dateTime.Date,
                                MaterialQty = 0
                            });
                        }
                    }

                    s = s.OrderBy(x => x.SchedulDate).ToList();

                    var minDay = s.Min(x => x.SchedulDate).Value;
                    var mm = s.GroupBy(x => x.SchedulDate.Value.ToString("yyyy-MM-dd")).ToList();
                    decimal total = 0;
                    foreach (var item in mm)
                    {
                        var d = Convert.ToDateTime(item.Key);
                        var m = new PlanMaterialChartDto()
                        {
                            Day = "P" + (d - minDay).Days,
                            NeedQty = item.Sum(x => x.ProductQty * x.MaterialQty * x.ExeNum)
                        };
                        total = total + m.NeedQty ?? 0;
                        m.TotalQty = total;
                        dto.Materials.Add(m);
                    }
                    foreach (var detail in s)
                    {
                        if (detail.OrderNumber is null) continue;
                        var date = Convert.ToDateTime(detail.SchedulDate.Value);
                        var d = new PlanMaterialDetailsDto()
                        {
                            Day = "P" + (date - minDay).Days,
                            OrderNumber = detail.OrderNumber,
                            ProductName = detail.ProductName,
                            ProductQty = detail.ProductQty,
                            MaterialName = detail.MaterialName,
                            MaterialQty = detail.MaterialQty
                        };
                        dto.Items.Add(d);
                    }
                }
            }


            return dto;
        }
        /// <summary>
        /// 获取人员计划
        /// </summary>
        /// <param name="schCode"></param>
        /// <returns></returns>
        public async Task<List<PersonnelResultDto>> GetPersonnels(PersonnelDto search)
        {
            var list = new List<PersonnelResultDto>();
            search.TrimStringFields();
            var whereExpression = ExpressionCreator
                                                .New<SourceSchedulDeviceStation>()
                                                .And(x => !x.IsDeleted)
                                                .And(x => x.Status == 1)
                                                .AndIf(search.Start.HasValue, x => x.UseDate.Value.Date >= search.Start.Value.Date)
                                                 .AndIf(search.End.HasValue, x => x.UseDate.Value.Date <= search.End.Value.Date);

            //获取start-end时间段中的生效计划数据 -工位数据
            var solutionsData = _planSolutionRepo.Where(whereExpression);

            if (solutionsData.Any())
            {
                int days = search.End.Value.Date.Subtract(search.Start.Value.Date).Days;
                var solutions = (from s in solutionsData
                                 group s by new { UseDate = s.UseDate.Value.Date, s.StationCode } into k
                                 select new
                                 {
                                     PlanDate = k.Key.UseDate,
                                     StationCode = k.Key.StationCode
                                 }).ToList();

                //获取工位配置的班次和所需人员数量
                var basstionCalss = _basStationClassRepo.Where(x => x.Status == 1);

                //获取班次信息
                var classes = _classRepo.Where(x => true);

                //获取获取start-end时间段中的生效计划数据 中工位数据对应的班次信息的所需人员信息
                var results = (from s in solutions
                               join sc in basstionCalss on s.StationCode equals sc.StationCode
                               join c in classes on sc.ClassesId equals c.Id
                               select new
                               {
                                   Date = s.PlanDate,
                                   StationCode = s.StationCode,
                                   ClassesName = c.Cname,
                                   Color = c.Color,
                                   Qty = sc.Qty
                               }).ToList();


                if (results.Any())
                {

                    var groupedSum = from t in results
                                     group t by new { t.Date, t.ClassesName, t.Color } into g
                                     select new
                                     {
                                         Date = g.Key.Date,
                                         ClassesName = g.Key.ClassesName,
                                         Color = g.Key.Color,
                                         Qty = g.Sum(t => t.Qty)
                                     };


                    if (groupedSum.Any())
                    {
                        for (int i = 0; i <= days; i++)
                        {
                            List<Personnel> Personnels = new List<Personnel>();
                            string planDate = search.Start.Value.Date.AddDays(i).ToString("yyyy-MM-dd");
                            Personnels = groupedSum.Where(x => x.Date.ToString("yyyy-MM-dd") == planDate)?
                                                    .Select(t => new Personnel
                                                    {
                                                        ClassesName = t.ClassesName,
                                                        Color = t.Color,
                                                        Qty = t.Qty ?? 0
                                                    }).ToList();


                            var mainObject = new PersonnelResultDto();
                            mainObject.PlanDates = planDate;
                            mainObject.Classes = Personnels;

                            list.Add(mainObject);
                        }
                    }
                }
            }
            return list;
        }

        #region =================================================== 排产========================================================

        public async Task<AppSrvResult<long>> CreateAsync(PlanProductionSchedulDto input)
        {
            input.TrimStringFields();
            //待排产数据
            var basplanPending = _basplanPendingRepo.Where(o => o.PlanCode == input.PlanCode, true, false).FirstOrDefault();
            if (basplanPending.Status == PendingPlanStatusConst.Finished)
            {
                return Problem(HttpStatusCode.BadRequest, "当前计划已完成，无法再次排产！");
            }

            //产品工序关系表
            var basProStepRel = _basProStepRelRepo.Where(o => o.ProCode == basplanPending!.ProductCode).ToList();

            //前置工序
            var basTechnologyStep = _basTechnologyStepRelationRepo.Where(o => true).ToList();

            var lcodes = _basProductStandardCapacityRepo.Where(o => o.ProCode == basplanPending!.ProductCode && o.LineCode == input.LCode).ToList();

            if (lcodes == null || lcodes.Count == 0)
            {
                return Problem(HttpStatusCode.BadRequest, "当前所选产线，没有配置产能方案，请配置后再操作!!!");
            }


            var basData = (from i in basProStepRel
                           join j in lcodes on new { i.StepCode, i.ProCode } equals new { j.StepCode, j.ProCode }
                           join h in basTechnologyStep on new { tcode = i.TecCode, stepcode = i.StepCode } equals new { tcode = h.TCode, stepcode = h.StepCode }

                           select new ProcessSchedulDto
                           {
                               Name = i.StepCode,
                               PreStep = h.PreStep
                           }
                          ).ToList();

            foreach (var o in basData)
            {
                if (o.PreStep.IsNotNullOrEmpty())
                {
                    var preData = basData.Where(x => x.Name == o.PreStep);
                    if (preData == null || preData.Count() == 0)
                    {
                        return Problem(HttpStatusCode.BadRequest, "方案配置不正确，有前置工序的，前置工序需配到方案当中!!!");
                    }
                }
            }

            var capacitys = RemoveDuplicateData(lcodes, input);
            //return AppSrvResult<long>(0);

            //工序表
            var basStepRepo = _basStepRepo.Where(o => true).ToList();

            var planProductionSchedul = Mapper.Map<PlanProductionSchedule>(input);

            DateTime maxDateTime = DateTime.MinValue;
            DateTime minDateTime = DateTime.MinValue;
            if (input.PreRule.ToString().Equals(SchedulTypeConst.Prerule_2, StringComparison.OrdinalIgnoreCase))
            {
                return await SavePartialProductionScheduling(capacitys, planProductionSchedul, basplanPending);
            }
            else if (input.PreRule.ToString().Equals(SchedulTypeConst.Prerule_1, StringComparison.OrdinalIgnoreCase))
            {
                return await SaveCompleteProductionScheduling(capacitys, planProductionSchedul, basplanPending);
            }
            return AppSrvResult<long>(0);
        }



        /// <summary>
        /// 根据工序剩余产能排产日期，及生产量
        /// </summary>
        /// <param name="process"></param>
        /// <param name="processes"></param>
        /// <param name="sort"></param>
        /// <param name="currentProcess"></param>
        /// <param name="productQuantity"></param>
        /// <returns></returns>
        private (int, int) GetLeaveDate(ProductionProcess process,ProductionProcess currentProcess = null, int productQuantity = 0)
        {
            //天数
            int i = 0;
            //消耗当前工序的产量
            int useDurion = 0;

            ProductionProcess preProcess = currentProcess;

            //最大生产次数
            var maxProductCount = (int)Math.Floor(process.LimitHours / process.Duration);
            //每天最大单位产量
            var maxProductQ = (int)Math.Ceiling(maxProductCount * process.Quantity);
            //总需求量
            var totalProductQ = (int)Math.Ceiling(productQuantity * process.Demand);

            if (totalProductQ < maxProductQ)
            {
                process.RemainingTime = CalculateRemainingTime(totalProductQ, process.Quantity, process.Duration, process.LimitHours);

                if (process.RemainingTime == 0)
                {
                    return (1, 0);
                }
            }
            else
            {
                var remainingQ = (int)Math.Ceiling((double)(totalProductQ % maxProductQ));
                if (remainingQ > 0)
                {
                    process.RemainingTime =  CalculateRemainingTime(remainingQ, process.Quantity, process.Duration, process.LimitHours); 
                }
                else if (remainingQ == 0)
                {
                    if (process.LimitHours - process.Duration == 0)
                    {
                        return (1, 0);
                    }
                    else
                    {
                        process.RemainingTime = process.LimitHours - process.Duration;
                    }
                }
            }
            //前置工序班次信息
           var presStations = GetStationClasses(process.StationCode);

            //当前工序班次
            var stations = GetStationClasses(preProcess.StationCode);


            if (process.StationCode == currentProcess.StationCode)
            {
                if (process.RemainingTime >= currentProcess.Duration)
                {
                    int excuteNum = (int)Math.Floor((double)process.RemainingTime % currentProcess.Duration);
                    useDurion = (int)Math.Ceiling(excuteNum * currentProcess.Quantity);
                    i = 0;
                }
                else
                {
                    i = 1;
                }
            }
            else
            {
                //计算前置工序结束时间

                int premaxend = GetMaxTime(presStations);

                //剩余开始时间点
                double remainmaxend = (double)(premaxend - process.RemainingTime);

                //计算当前工序结束时间
                int currmaxend = GetMaxTime(stations);
                //开始时间
                int currminstart = GetMinTime(stations);

                if (remainmaxend < currmaxend)
                {
                    double useQualtiy = 0;
                    if (currminstart <= remainmaxend && remainmaxend <= currmaxend)
                    {
                        useQualtiy = currmaxend - remainmaxend;
                    }
                    else if (remainmaxend < currminstart)
                    {
                        useQualtiy = currmaxend - currminstart;
                    }
                    if (useQualtiy >= currentProcess.Duration)
                    {
                        i = 0;

                        var totalTime = (int)Math.Floor(totalProductQ / process.Quantity) * currentProcess.Duration;

                        if (useQualtiy > totalTime)
                        {
                            useDurion = (int)((int)Math.Floor((double)(totalTime / currentProcess.Duration)) * currentProcess.Quantity);
                        }
                        else
                        {
                            useDurion = (int)((int)Math.Floor((double)(useQualtiy / currentProcess.Duration)) * currentProcess.Quantity);
                        }

                    }
                    else
                    {
                        i = 1;
                    }
                }
                else
                {
                    i = 1;
                }

            }
            return (i, useDurion);

        }

        /// <summary>
        ///  计算剩余时间的方法
        /// </summary>
        /// <param name="productQ"></param>
        /// <param name="quantity"></param>
        /// <param name="duration"></param>
        /// <param name="limitHours"></param>
        /// <returns></returns>
        private double CalculateRemainingTime(int productQ, double quantity, double duration, double limitHours)
        {
            return limitHours - (int)Math.Ceiling(productQ / quantity) * duration;
        }

        /// <summary>
        /// 获取工序班次信息的方法
        /// </summary>
        /// <param name="stationCode"></param>
        /// <returns></returns>
        private List<StationClass> GetStationClasses(string stationCode)
        {
            return (from f in _basStationClassRepo.Where(o => o.StationCode == stationCode)
                    join j in _classRepo.Where(o => true) on f.ClassesId equals j.Id
                    orderby j.BeginTime
                    select new StationClass
                    {
                        StationCode = f.StationCode,
                        BeginTime = j.BeginTime,
                        EndTime = j.EndTime,
                        Duration = f.Duration
                    }).ToList();
        }


        /// <summary>
        ///根据传入工位获取最大时间点
        /// </summary>
        /// <param name="presStations"></param>
        /// <returns></returns>
        private static int GetMaxTime(List<StationClass> presStations)
        {
            int currmaxend = 0;
            int startTime = 0;
            int endTime = 0;
            foreach (var item in presStations)
            {
                startTime = Convert.ToInt32(item.BeginTime?.Split(':')[0]); ;
                endTime = Convert.ToInt32(item.EndTime?.Split(':')[0]);

                if (startTime > endTime)
                {
                    endTime += 24;
                }
                if (endTime > currmaxend)
                {
                    currmaxend = endTime;
                }

            }

            return currmaxend;
        }


        /// <summary>
        /// 根据传入工位获取最小时间点
        /// </summary>
        /// <param name="presStations"></param>
        /// <returns></returns>
        private static int GetMinTime(List<StationClass> presStations)
        {
            int currminstart = int.MaxValue;
            int startTime = 0;
            int endTime = 0;
            foreach (var item in presStations)
            {
                startTime = Convert.ToInt32(item.BeginTime?.Split(':')[0]); ;
                endTime = Convert.ToInt32(item.EndTime?.Split(':')[0]);
                if (startTime < currminstart)
                {
                    currminstart = startTime;
                }
            }
            return currminstart;
        }

        /// <summary>
        /// 获取可执行的最优方案方法
        /// </summary>
        /// <param name="dataItems"></param>
        /// <returns></returns>
        private List<ProcessSchedulDto> RemoveDuplicateData(List<BasProductStandardCapacity> dataItems, PlanProductionSchedulDto input)
        {
            var holidayS = _basHolidayRepo.Where(o => o.IsDeleted == false
                                               && o.IsPlaned == 1).ToList();
            List<ProcessSchedulDto> result = new List<ProcessSchedulDto>();

            if (!dataItems.Any()) return result;

            input.TrimStringFields();

            //  var autoCode = _basNumberManagementService.GetNumberBySimpleName(CommonConst.SCHEDULNUMBER).Result;

            long id = IdGenerater.GetNextId();

            //待排产数据
            var basplanPending = _basplanPendingRepo.Where(o => o.PlanCode == input.PlanCode, true, false).FirstOrDefault();

            //产品工序关系表
            var basProStepRel = _basProStepRelRepo.Where(o => o.ProCode == basplanPending!.ProductCode).ToList();

            //产品基础产能表
            var basProductStandardCapacitys = dataItems;

            var planStepCapacityRepo = _planStepCapacityRepo.Where(o => true);
            var basStationClassRepo = _basStationClassRepo.Where(o => true);

            var planProductionSchedulStepDetailsRepo = _planProductionSchedulStepDetailsRepo.Where(o => o.PlanCode != input.PlanCode);

            var planProductionSchedulRepo = _planProductionSchedulRepo.Where(o => o.Status != SchedulStatusConst.Cancel);

            var planschedulStepSolutionRepo = _planschedulStepSolutionRepo.Where(o => true);

            var classRepo = _classRepo.Where(o => true);
            //前置工序
            var basTechnologyStep = _basTechnologyStepRelationRepo.Where(o => true).ToList();


            var basStepRepo = _basStepRepo.Where(o => true).ToList();

            //排序方式
            int sort = input.Sorts;


            var capacitys = (from i in basProStepRel
                             join j in basProductStandardCapacitys on new { i.StepCode, i.ProCode } equals new { j.StepCode, j.ProCode }
                             join h in basTechnologyStep on new { tcode = i.TecCode, stepcode = i.StepCode } equals new { tcode = h.TCode, stepcode = h.StepCode }
                             join k in basStepRepo on i.StepCode equals k.Code
                             orderby h.Sort ascending
                             select new ProcessSchedulDto
                             {
                                 Name = i.StepCode,
                                 LimitHours = j.WorkUnitTime == null ? 0 : (double)j.WorkUnitTime,
                                 ProcessingTime = j.StandardWorkTime == null ? 0 : (double)j.StandardWorkTime,
                                 //RequiredQuantity = i.BatchQty == null ? 0 : (double)i.BatchQty,
                                 SingleProduction = j.Capacity == null ? 0 : (double)j.Capacity,
                                 IntervalTime = h.IntervalTime == null ? 0 : (double)h.IntervalTime,
                                 PreStep = h.PreStep,
                                 StationCode = j.StationCode!,
                                 StepName = k.Name,
                                 DeviceCode = j.DeviceCode,
                                 SolutionName = j.SolutionName,
                                 sort = h.Sort,
                                 LineCode = j.LineCode,
                                 ProCode = j.ProCode
                             }
                          ).ToList();

            var result1 = (from i in planProductionSchedulStepDetailsRepo
                           join j in planProductionSchedulRepo on i.SchedulCode equals j.SchedulCode
                           join f in planschedulStepSolutionRepo on i.TaskCode equals f.TaskCode
                           join h in planStepCapacityRepo on f.TandardCapacityId equals h.Id
                           where i.PlanCode != input.PlanCode
                           orderby i.SchedulDate ascending
                           select new PlanStepCapacitySource
                           {
                               SchedulCode = i.SchedulCode!,
                               StepCode = i.StepCode!,
                               TaskCode = i.TaskCode!,
                               RemainingTime = i.RemainingTime,
                               DeviceCode = h.DeviceCode!,
                               StationCode = h.StationCode!,
                               SchedulDate = i.SchedulDate,
                           }
            ).ToList();


            if (input.PreRule.ToString().Equals(SchedulTypeConst.Prerule_2, StringComparison.OrdinalIgnoreCase))
            {
                ProductionScheduler scheduler = new ProductionScheduler();
                scheduler.TargetQuantity = (int)basplanPending!.Qty;
                scheduler.StartTime = input.PlanStart!;
                scheduler.Sorts = sort;
                foreach (var item in capacitys)
                {
                    ProcessSchedul process = new ProcessSchedul(item.Name, item.ProcessingTime, item.RequiredQuantity, item.SingleProduction, item.LimitHours, item.StepName, item.SolutionName);
                    if (!string.IsNullOrEmpty(item.PreStep))
                    {
                        process.PrecedingProcesses.Add(item.PreStep);
                    }
                    scheduler.AddProcess(process);
                }

                scheduler.Schedule();

                foreach (var process in scheduler.Processes)
                {
                    DateTime maxDateTime = DateTime.MinValue;
                    DateTime minDateTime = DateTime.MinValue;
                    //产能占用
                    var capacity = capacitys.FirstOrDefault(o => o.Name == process.Name);
                    int i = 0;
                    var autoCode1 = _basNumberManagementService.GetNumberBySimpleName(CommonConst.TASKRWBM).Result;
                    foreach (var executionCount in process.DailyExecutionCount)
                    {
                        int j = isVacation(executionCount.Key.AddDays(i));
                        i = i + j;
                        var model = new PlanProductionSchedulStepDetails
                        {
                            Id = IdGenerater.GetNextId(),
                            IsDeleted = false,
                            SchedulDate = executionCount.Key.AddDays(i),
                            StepCode = process.Name,
                            PlanCode = input.PlanCode,
                            TaskCode = autoCode1,
                            Exenum = executionCount.Value,
                            // SchedulCode = autoCode
                        };
                    }
                }
            }
            else if (input.PreRule.ToString().Equals(SchedulTypeConst.Prerule_1, StringComparison.OrdinalIgnoreCase))
            {
                // 定义工序列表
                var processes = new List<ProductionProcess>();

                if (sort == SchedulSortsConst.DESC)
                {
                    capacitys = capacitys.OrderByDescending(o => o.sort).ToList();
                }

                foreach (var item in capacitys)
                {
                    ProductionProcess process = new ProductionProcess(item.Name, item.ProcessingTime, item.SingleProduction,
                        item.RequiredQuantity, item.IntervalTime, item.LimitHours, item.sort, item.StepName, item.SolutionName, item.StationCode, item.StartTime);
                    if (!string.IsNullOrEmpty(item.PreStep))
                    {
                        var xxx = capacitys.Where(o => o.Name == item.PreStep).ToList();
                        foreach (var item1 in xxx)
                        {
                            ProductionProcess process1 = new ProductionProcess(item1.Name, item1.ProcessingTime, item1.SingleProduction,
                            item1.RequiredQuantity, item1.IntervalTime, item1.LimitHours, item.sort, item1.StepName, item1.SolutionName, item1.StationCode, item.StartTime);

                            process.PreviousProcesses.Add(process1);
                        }

                    }

                    processes.Add(process);
                }
                // 定义产品数量
                int productQuantity = (int)basplanPending!.Qty;

                // 计算每个工序的执行日期和每天执行次数
                DateTime currentDateTime = (DateTime)input.SchedulTime;
                var aaa = GetCombinations(processes);

                List<List<ProductionProcess>> combinations = DeepCloneBinary<List<List<ProductionProcess>>>(aaa);


                var PlanSolutions = new List<List<PlanSolution>>();

                foreach (var item2 in combinations)
                {
                    var lopEntity = DeepCloneBinary(item2);
                    List<PlanSolution> planSolutionList = new List<PlanSolution>();
                    DateTime maxDateTime = DateTime.MinValue;
                    DateTime minDateTime = DateTime.MinValue;
                    var dayCondition = (0, 0);
                    var dayConditionList = new List<dynamic>();// 每个工序节时间
                    foreach (var item3 in lopEntity)
                    {
                        bool isUp = false;
                        var process = DeepCloneBinary(item3);
                        DateTime startTime = DateTime.MinValue;
                        double workHoursPerDay = process.LimitHours;

                        int i = 0;
                        List<List<DateTime>> dateTimes = new List<List<DateTime>>();

                        double expendCount = 0;
                        var sourceResult = result1.Where(o => o.StationCode == process.StationCode).ToList();

                        if (sourceResult != null && sourceResult.Count > 0 && sort == SchedulSortsConst.ASC)
                        {
                            var capacity = capacitys.FirstOrDefault(o => o.Name == process.Name);

                            int dayCount = (int)Math.Floor(workHoursPerDay / process.Duration);

                            double dayProcuct = dayCount * process.Quantity;

                            double totalDemand = productQuantity * process.Demand;

                            int processDay = (int)Math.Floor(totalDemand / dayProcuct);// 工序总工时

                            if (process.PreviousProcesses == null || process.PreviousProcesses.Count == 0)
                            {
                                var preProcess = lopEntity.FirstOrDefault(o => o.Sorts == process.Sorts + 1);
                                if (preProcess != null && process.StationCode == preProcess.StationCode)
                                {
                                    var sameStationP= planSolutionList.FirstOrDefault(o => o.StepName == preProcess.Name);
                                    if (sameStationP != null)
                                    {
                                        startTime = sameStationP.ExecuteTime.AddDays(1);
                                    }
                                    else 
                                    {
                                        startTime = (DateTime)input.SchedulTime;
                                    }
                                   
                                }
                                else
                                {
                                    startTime = (DateTime)input.SchedulTime;
                                }
                            }
                            else
                            {
                                startTime = planSolutionList.Where(x => x.StepName == (process.PreviousProcesses.FirstOrDefault()?.Name??"")).Max(x => x.ExecuteTime).AddDays(1); ;
                            }

                            if (startTime.AddDays(processDay) < sourceResult.Min(x => x.SchedulDate))
                            {
                                
                                currentDateTime = startTime ;
                            }
                            else if (startTime > sourceResult.Max(x => x.SchedulDate))
                            {
                                currentDateTime = startTime ;
                            }
                            else 
                            {
                                var minSchedulDate = sourceResult.Min(x => x.SchedulDate).Value;
                                var maxSchedulDate = sourceResult.Max(x => x.SchedulDate).Value;
                                TimeSpan difference = maxSchedulDate.Subtract(minSchedulDate);
                                int daday_spanys = (int)difference.TotalDays;
                                for (int day_i = 1; day_i <= daday_spanys; day_i++)
                                {
                                    var dt_start = minSchedulDate.AddDays(day_i);
                                    if (day_i == daday_spanys)
                                    {
                                        currentDateTime = dt_start.AddDays(1);
                                        startTime= dt_start.AddDays(1);
                                    }
                                    else if (sourceResult.Count(x => x.SchedulDate == dt_start) > 0)
                                    {
                                        continue;
                                    }
                                    else
                                    {
                                        int holiy_t = 0;
                                        bool is_can = true;// 
                                        for (int total_i = 1; total_i < processDay; total_i++)
                                        {
                                            var dt_start_new = dt_start.AddDays(total_i);
                                            if (holidayS.Any(x => x.CurrentDate == dt_start_new))
                                            {
                                                holiy_t += 1; // 判断节假日
                                            }
                                            if (sourceResult.Count(x => x.SchedulDate == dt_start_new) > 0)
                                            {
                                                is_can = false;
                                                break;
                                            }
                                            if (total_i == processDay)
                                            {
                                                is_can = true;
                                                //currentDateTime = dt_start;

                                            }
                                        }
                                        if (is_can)
                                        {
                                            if (holiy_t > 0)
                                            {
                                                for (int mm_d = 1; mm_d < holiy_t; mm_d++)
                                                {
                                                    var dt_start_new_h = dt_start.AddDays(mm_d + processDay);
                                                    if (sourceResult.Count(x => x.SchedulDate == dt_start_new_h) > 0)
                                                    {
                                                        break;
                                                    }
                                                    if (mm_d == processDay)
                                                    {
                                                        currentDateTime = dt_start_new_h;
                                                        startTime = dt_start_new_h;
                                                    }

                                                }
                                            }
                                            else
                                            {
                                                currentDateTime = dt_start.AddDays(processDay).AddDays(1);
                                                startTime = dt_start.AddDays(processDay).AddDays(1);
                                            }

                                        }

                                    }
                                }

                            }
                        }
                        else
                        {
                            DateTime inputDate = (DateTime)input.SchedulTime;
                            if (process.PreviousProcesses == null || process.PreviousProcesses.Count == 0)
                            {
                                if (sort == SchedulSortsConst.DESC)
                                {
                                    var preProcess = lopEntity.FirstOrDefault(o => o.Sorts == process.Sorts + 1);
                                    if (preProcess != null && process.StationCode == preProcess.StationCode)
                                    {
                                        startTime = minDateTime.AddDays(-dayCondition.Item1);
                                        currentDateTime = startTime;
                                        isUp = true;
                                    }
                                    else
                                    {
                                        var upProcess = planSolutionList.Where(o => o.StationCode == process.StationCode && o.Sorts > process.Sorts).OrderByDescending(o => o.ExecuteTime).FirstOrDefault();
                                        if (upProcess != null)
                                        {
                                            CalculateExecutionDates(process, process.PreviousProcesses.FirstOrDefault(), productQuantity);
                                            var maxJieYu = GetMaxJieYu(process.PreviousProcesses.FirstOrDefault()?.Name);
                                            startTime = upProcess.ExecuteTime.AddDays(-maxJieYu);
                                            currentDateTime = startTime;
                                            isUp = true;
                                        }
                                        else
                                        {
                                            var preUpProcess = lopEntity.FirstOrDefault(o => o.PreviousProcesses.Count > 0
                                                && o.PreviousProcesses.FirstOrDefault(x => x.Name == process.Name) != null
                                                && o.Sorts > process.Sorts);

                                            if (preUpProcess != null)
                                            {
                                                var upProcess1 = planSolutionList.Where(o => o.StepName == preUpProcess.Name).OrderByDescending(o => o.ExecuteTime).FirstOrDefault();
                                                CalculateExecutionDates(process, process.PreviousProcesses.FirstOrDefault(), productQuantity);
                                                var maxJieYu = GetMaxJieYu(process.PreviousProcesses.FirstOrDefault()?.Name);
                                                startTime = upProcess1.ExecuteTime.AddDays(-maxJieYu);
                                                currentDateTime = startTime;
                                                isUp = true;
                                            }
                                            else
                                            {
                                                dayCondition = (0, 0);
                                                startTime = inputDate;
                                                currentDateTime = startTime;
                                            }
                                        }
                                    }
                                }
                                else
                                {
                                    var preProcess = lopEntity.FirstOrDefault(o => o.Sorts == process.Sorts - 1);
                                    if (preProcess != null && process.StationCode == preProcess.StationCode)
                                    {
                                        startTime = maxDateTime.AddDays(dayCondition.Item1);
                                        currentDateTime = startTime;
                                        isUp = true;
                                    }
                                    else
                                    {
                                        foreach (var item in lopEntity.Where(o => o.PreviousProcesses.FirstOrDefault()?.Name == process.Name))
                                        {
                                            CalculateExecutionDates(process, item, productQuantity);
                                        }
                                        startTime = inputDate;
                                        currentDateTime = startTime;
                                    }
                                }
                            }
                            else
                            {
                                if (sort == SchedulSortsConst.DESC)
                                {
                                    var preProcess = lopEntity.Where(o => o.PreviousProcesses.Count > 0
                                        && o.PreviousProcesses.FirstOrDefault(x => x.Name == process.Name && x.SolutionName == process.SolutionName) != null
                                        && o.Sorts > process.Sorts).Select(o => o.Name).ToList();

                                    if (preProcess.Count > 0)
                                    {
                                        var upProcess = planSolutionList.Where(o => preProcess.Contains(o.StepName)).OrderBy(o => o.ExecuteTime).FirstOrDefault();

                                        if (upProcess != null)
                                        {
                                            CalculateExecutionDates(process, process.PreviousProcesses.FirstOrDefault(), productQuantity);
                                            var maxJieYu = GetMaxJieYu(process.Name);
                                            startTime = upProcess.ExecuteTime.AddDays(-maxJieYu);
                                            currentDateTime = startTime;
                                            isUp = true;
                                        }
                                        else
                                        {
                                            CalculateExecutionDates(process, process.PreviousProcesses.FirstOrDefault(), productQuantity);
                                            startTime = inputDate;
                                            currentDateTime = startTime;
                                        }
                                    }
                                    else
                                    {
                                        CalculateExecutionDates(process, process.PreviousProcesses.FirstOrDefault(), productQuantity);
                                        startTime = inputDate;
                                        currentDateTime = startTime;
                                    }
                                }
                                else
                                {
                                    var upProcess = planSolutionList.Where(o => o.StepName == process.PreviousProcesses.FirstOrDefault()?.Name && o.Name == process.PreviousProcesses.FirstOrDefault()?.SolutionName && o.Sorts < process.Sorts).OrderByDescending(o => o.ExecuteTime).FirstOrDefault();

                                    if (upProcess != null)
                                    {
                                        foreach (var item in lopEntity.Where(o => o.PreviousProcesses.FirstOrDefault()?.Name == process.Name))
                                        {
                                            CalculateExecutionDates(process, item, productQuantity);
                                        }
                                        var maxJieYu = GetQMaxJieYu(process.PreviousProcesses.FirstOrDefault()?.Name, process.Name);
                                        startTime = upProcess.ExecuteTime.AddDays(maxJieYu);
                                        currentDateTime = startTime;
                                        isUp = true;
                                    }
                                    else
                                    {
                                        dayCondition = (0, 0);
                                    }
                                }
                            }
                        }


                        // 计算执行日期并添加到 dayConditionList 的方法
                        void CalculateExecutionDates(ProductionProcess process, ProductionProcess previousProcess, int productQuantity)
                        {
                            var dayCondition = GetLeaveDate(process, previousProcess, productQuantity);
                            dayConditionList.Add(new { Name = process.Name, BeforName = previousProcess?.Name, JieYu = dayCondition.Item1 });
                        }

                        // 获取最大的 JieYu 值
                        int GetMaxJieYu(string processName)
                        {
                            return dayConditionList.Where(x => x.BeforName == processName)?.Max(x => x.JieYu)??0;
                        }

                        // 获取前置工序最大的 JieYu 值
                        int GetQMaxJieYu(string previousProcessName, string processName)
                        {
                            return dayConditionList.Where(x => x.BeforName == previousProcessName && x.Name == processName)?.Max(x => x.JieYu)??0;
                        }

                        var leaveQuality = (0, 0);
                        if (sort == SchedulSortsConst.DESC)
                        {
                            minDateTime = currentDateTime;
                            if (!isUp)
                            {
                                currentDateTime = currentDateTime.AddDays(-dayCondition.Item1);
                            }

                            var preProcesses = lopEntity.Where(o => o.PreviousProcesses.Count > 0
                               && o.PreviousProcesses.FirstOrDefault(x => x.Name == process.Name && x.SolutionName == process.SolutionName) != null
                               && o.Sorts > process.Sorts).Select(o => o.Name).ToList();

                            var upProcess = planSolutionList.Where(o => preProcesses.Contains(o.StepName)).OrderBy(o => o.ExecuteTime).FirstOrDefault();
                            if (upProcess != null)
                            {
                                leaveQuality = GetLeaveDate(lopEntity.FirstOrDefault(o => o.Name == upProcess.StepName), process, productQuantity);
                            }
                            else
                            {
                                leaveQuality = (0, 0);
                            }
                            process.CalculateExecutionDatesDesc(productQuantity, currentDateTime, workHoursPerDay, leaveQuality.Item2);
                        }
                        else
                        {
                            maxDateTime = currentDateTime;
                            if (!isUp)
                            {
                                currentDateTime = currentDateTime.AddDays(dayCondition.Item1);
                            }
                            if (process.PreviousProcesses != null && process.PreviousProcesses.Count > 0)
                            {
                                var solutionNames = lopEntity.Select(o => o.SolutionName).ToList();
                                leaveQuality = GetLeaveDate(process.PreviousProcesses.FirstOrDefault(o => solutionNames.Contains(o.SolutionName)), process, productQuantity);
                            }
                            else
                            {
                                leaveQuality = (0, 0);
                            }
                            process.CalculateExecutionDates(productQuantity, currentDateTime, workHoursPerDay, leaveQuality.Item2);

                        }


                        foreach (var execution in process.DailyExecutionCounts)
                        {
                            if (sort == SchedulSortsConst.DESC)
                            {
                                int j = isVacationDesc(execution.Key.AddDays(i));
                                i = i + j;
                            }
                            else
                            {
                                int j = isVacation(execution.Key.AddDays(i));
                                i = i + j;
                            }
                            var model = new PlanProductionSchedulStepDetails
                            {
                                Id = IdGenerater.GetNextId(),
                                IsDeleted = false,
                                SchedulDate = execution.Key.AddDays(i),
                                StepCode = process.Name,
                                PlanCode = input.PlanCode,
                                //TaskCode = autoCode1,
                                Exenum = execution.Value,
                                //  SchedulCode = autoCode
                            };
                            if (sort == SchedulSortsConst.ASC)
                            {
                                if (model.SchedulDate >= maxDateTime)
                                {
                                    maxDateTime = (DateTime)model.SchedulDate;
                                }
                                currentDateTime = maxDateTime;
                            }
                            else if (sort == SchedulSortsConst.DESC)
                            {
                                if (model.SchedulDate <= minDateTime)
                                {
                                    minDateTime = (DateTime)model.SchedulDate;
                                }
                                currentDateTime = minDateTime;
                            }
                        }
                        planSolutionList.Add(new PlanSolution { Sorts = process.Sorts, Name = process.SolutionName!, StationCode = process.StationCode, StepName = process.Name, StartTime = startTime, ExecuteTime = (sort == SchedulSortsConst.ASC ? maxDateTime : minDateTime) });
                    }
                    PlanSolutions.Add(planSolutionList);
                }

                int minDaysDifference = int.MaxValue;
                List<PlanSolution> minDifferencePair = null;

                foreach (var pair in PlanSolutions)
                {
                    int daysDifference = CalculateDaysDifference(pair, sort);

                    if (daysDifference < minDaysDifference)
                    {
                        minDaysDifference = daysDifference;
                        minDifferencePair = pair;
                    }
                }

                var resultSelects = minDifferencePair;

                if (sort == SchedulSortsConst.ASC)
                {
                    foreach (var item in resultSelects)
                    {
                        var model = capacitys.Where(o => o.SolutionName == item.Name! && o.Name == item.StepName!).FirstOrDefault();
                        model.StartTime = item.StartTime;
                        model.EndTime = item.ExecuteTime;
                        result.Add(model);
                    }
                }
                else if (sort == SchedulSortsConst.DESC)
                {
                    foreach (var item in resultSelects)
                    {
                        var model = capacitys.Where(o => o.SolutionName == item.Name && o.Name == item.StepName).FirstOrDefault();
                        model.StartTime = item.StartTime;
                        model.EndTime = item.ExecuteTime;
                        result.Add(model);
                    }
                }
            }
            return result;
        }
        /// <summary>
        /// 重排功能
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult<List<dynamic>>> RealoadSchedulAsync(ReloadBasProcuct input)
        {

            var result = new List<dynamic>();

            var planDetailEntities = new List<PlanProductionSchedulStepDetails>();

            var autoCode = _basNumberManagementService.GetNumberBySimpleName(CommonConst.SCHEDULNUMBER).Result;
            //待排产数据
            var basplanPending = _basplanPendingRepo.Where(o => o.PlanCode == input.PlanCode).FirstOrDefault();
            //产品工序关系表
            var basProStepRel = _basProStepRelRepo.Where(o => o.ProCode == basplanPending!.ProductCode).ToList();

            var basProductStandardCapacitys = input.lists;
            //前置工序
            var basTechnologyStep = _basTechnologyStepRelationRepo.Where(o => true).ToList();

            var stepEntities = _basStepRepo.Where(x => true).ToList();

            var planProductionSchedul = _planProductionSchedulRepo.Where(o => o.SchedulCode == input.SchedulCode).FirstOrDefault();


            var planProductionSchedulDto = Mapper.Map<PlanProductionSchedulDto>(planProductionSchedul);


            var basPendingPlanEntities = _pendingPlanRepo.Where(x => true);

            var productEntities = _basProductRepo.Where(x => true);


            var basCapacitys = Mapper.Map<List<BasProductStandardCapacity>>(basProductStandardCapacitys);
            //排序方式
            int sort = planProductionSchedul!.Sorts;

            var capacitys = RemoveDuplicateData(basCapacitys, planProductionSchedulDto);

            DateTime maxDateTime = DateTime.MinValue;
            DateTime minDateTime = DateTime.MinValue;

            if (planProductionSchedul.PreRule.ToString().Equals(SchedulTypeConst.Prerule_2, StringComparison.OrdinalIgnoreCase))
            {
                ProductionScheduler scheduler = new ProductionScheduler();
                scheduler.TargetQuantity = (int)basplanPending!.Qty;
                scheduler.StartTime = planProductionSchedul.PlanStart!;
                scheduler.Sorts = sort;
                foreach (var item in capacitys)
                {
                    ProcessSchedul process = new ProcessSchedul(item.Name, item.ProcessingTime, item.RequiredQuantity, item.SingleProduction, item.LimitHours, item.StepName, item.SolutionName);
                    if (!string.IsNullOrEmpty(item.PreStep) && !item.PreStep.Equals("无"))
                    {
                        process.PrecedingProcesses.Add(item.PreStep);
                    }
                    scheduler.AddProcess(process);
                }

                scheduler.Schedule();

                foreach (var process in scheduler.Processes)
                {
                    int i = 0;
                    var autoCode1 = _basNumberManagementService.GetNumberBySimpleName(CommonConst.TASKRWBM).Result;
                    foreach (var executionCount in process.DailyExecutionCount)
                    {
                        if (sort == SchedulSortsConst.DESC)
                        {
                            int j = isVacationDesc(executionCount.Key);
                            i = i + j;
                        }
                        else
                        {
                            int j = isVacation(executionCount.Key);
                            i = i + j;
                        }
                        var model = new PlanProductionSchedulStepDetails
                        {
                            Id = IdGenerater.GetNextId(),
                            IsDeleted = false,
                            SchedulDate = executionCount.Key.AddDays(i),
                            StepCode = process.Name,
                            PlanCode = input.PlanCode,
                            TaskCode = autoCode1,
                            Exenum = executionCount.Value,
                            SchedulCode = autoCode
                        };
                        planDetailEntities.Add(model);

                        if (sort == SchedulSortsConst.ASC)
                        {
                            if (model.SchedulDate >= maxDateTime)
                            {
                                maxDateTime = (DateTime)model.SchedulDate;
                            }
                        }
                        else if (sort == SchedulSortsConst.DESC)
                        {
                            if (model.SchedulDate <= minDateTime)
                            {
                                minDateTime = (DateTime)model.SchedulDate;
                            }
                        }
                    }
                }
            }
            else if (planProductionSchedul.PreRule.ToString().Equals(SchedulTypeConst.Prerule_1, StringComparison.OrdinalIgnoreCase))
            {
                // 定义工序列表
                var processes = new List<ProductionProcess>();
                if (sort == SchedulSortsConst.DESC)
                {
                    capacitys = capacitys.OrderByDescending(o => o.sort).ToList();
                }

                foreach (var item in capacitys)
                {
                    ProductionProcess process = new ProductionProcess(item.Name, item.ProcessingTime, item.SingleProduction,
                         item.RequiredQuantity, item.IntervalTime, item.LimitHours, item.sort, item.StepName,
                         item.SolutionName, item.StationCode, item.StartTime);
                    if (!string.IsNullOrEmpty(item.PreStep))
                    {
                        var xxx = capacitys.Where(o => o.Name == item.PreStep).ToList();
                        foreach (var item1 in xxx)
                        {
                            ProductionProcess process1 = new ProductionProcess(item1.Name, item1.ProcessingTime, item1.SingleProduction,
                         item1.RequiredQuantity, item1.IntervalTime, item1.LimitHours, item.sort, item1.StepName,
                          item1.SolutionName, item1.StationCode, item.StartTime);

                            process.PreviousProcesses.Add(process1);
                        }

                    }

                    processes.Add(process);
                }
                // 定义产品数量
                int productQuantity = (int)basplanPending!.Qty;

                // 计算每个工序的执行日期和每天执行次数
                DateTime currentDateTime = (sort == SchedulSortsConst.DESC ? (DateTime)planProductionSchedul.PlanEnd! : planProductionSchedul.PlanStart!);

                var dayCondition = (0, 0);
                foreach (var process in processes)
                {
                    currentDateTime = process.StartTime;
                    int i = 0;
                    double workHoursPerDay = process.LimitHours;

                    if (sort == SchedulSortsConst.DESC)
                    {
                        minDateTime = currentDateTime;
                        var preProcesses = processes
                            .Where(o => o.PreviousProcesses.Count > 0
                               && o.PreviousProcesses.FirstOrDefault(x => x.Name == process.Name) != null
                               && o.Sorts > process.Sorts)
                            .Select(o => o.Name);

                        var downProcess = processes
                            .Where(o => preProcesses.Contains(o.Name))
                            .OrderBy(o => o.EndTime)
                            .FirstOrDefault();

                        if (downProcess != null)
                        {
                            dayCondition = GetLeaveDate(downProcess, process, productQuantity);
                        }
                        process.CalculateExecutionDatesDesc(productQuantity, currentDateTime, workHoursPerDay, dayCondition.Item2);
                    }
                    else
                    {
                        maxDateTime = currentDateTime;
                        if (process.PreviousProcesses != null && process.PreviousProcesses.Count > 0)
                        {
                            var solutionNames = processes
                                .Select(o => o.SolutionName)
                                .ToList();
                            dayCondition = GetLeaveDate(process.PreviousProcesses.FirstOrDefault(o => solutionNames.Contains(o.SolutionName)), process, productQuantity);
                        }
                        process.CalculateExecutionDates(productQuantity, currentDateTime, workHoursPerDay, dayCondition.Item2);
                    }

                    var autoCode1 = _basNumberManagementService.GetNumberBySimpleName(CommonConst.TASKRWBM).Result;

                    foreach (var execution in process.DailyExecutionCounts)
                    {
                        if (sort == SchedulSortsConst.DESC)
                        {
                            int j = isVacationDesc(execution.Key.AddDays(i));
                            i = i + j;
                        }
                        else
                        {
                            int j = isVacation(execution.Key.AddDays(i));
                            i = i + j;
                        }
                        var model = new PlanProductionSchedulStepDetails
                        {
                            Id = IdGenerater.GetNextId(),
                            IsDeleted = false,
                            SchedulDate = execution.Key.AddDays(i),
                            StepCode = process.Name,
                            PlanCode = input.PlanCode,
                            TaskCode = autoCode1,
                            Exenum = execution.Value,
                            SchedulCode = autoCode
                        };
                        planDetailEntities.Add(model);
                        if (sort == SchedulSortsConst.ASC)
                        {
                            if (model.SchedulDate >= maxDateTime)
                            {
                                maxDateTime = (DateTime)model.SchedulDate;
                            }
                            currentDateTime = maxDateTime;
                        }
                        else if (sort == SchedulSortsConst.DESC)
                        {
                            if (model.SchedulDate <= minDateTime)
                            {
                                minDateTime = (DateTime)model.SchedulDate;
                            }
                            currentDateTime = minDateTime;
                        }
                    }
                }

            }

            //排产计划新增数据
            var entity = new List<PlanProductionSchedule>{ new PlanProductionSchedule
            {
                LCode = string.Join(",", capacitys.Select(o => o.LineCode).Distinct().ToList()),
                PlanCode = planProductionSchedul.PlanCode,
                PlanStart = (sort == SchedulSortsConst.ASC ? (DateTime)planProductionSchedul.SchedulTime.Value : minDateTime),
                PlanEnd = (sort == SchedulSortsConst.ASC ? maxDateTime : planProductionSchedul.SchedulTime.Value),
                IsDeleted = false,
                PlanType = SchedulOperateConst.System,
                SchedulCode = autoCode,
                Sorts = planProductionSchedul.Sorts,
                PreRule = planProductionSchedul.PreRule,
                Status = SchedulStatusConst.Waiting,
                SchedulTime = (DateTime)planProductionSchedul.SchedulTime!
            } };

            var res = (from a in entity
                       join b in basPendingPlanEntities on a.PlanCode equals b.PlanCode into _1
                       from t_1 in _1.DefaultIfEmpty()

                       join c in planDetailEntities on a.SchedulCode equals c.SchedulCode into _2
                       from t_2 in _2.DefaultIfEmpty()
                       join step in stepEntities on t_2.StepCode equals step.Code into _3
                       from t_3 in _3.DefaultIfEmpty()

                       join pros in productEntities on t_1.ProductCode equals pros.Procode into ptemp
                       from products in ptemp.DefaultIfEmpty()
                       join techs in basTechnologyStep on products.TCode equals techs.TCode into tes
                       from techSteps in tes.DefaultIfEmpty()

                       orderby t_2.SchedulDate
                       select new PlanProductionSchedulData()
                       {
                           OrderNumber = basplanPending!.OrderNumber,
                           ProductName = basplanPending.ProductName,
                           PlanCode = basplanPending.PlanCode,
                           ProductCode = basplanPending.ProductCode,
                           SchedulDate = t_2.SchedulDate,
                           PlanStart = a.PlanStart,
                           PlanEnd = a.PlanEnd,
                           ScheduleStatus = a.Status,
                           StepName = t_3.Name,
                           StepCode = t_3.Code,
                           StepSort = techSteps.Sort ?? 0,
                           TecStepCode = techSteps.StepCode
                       }).Where(x => x.StepCode == x.TecStepCode).ToList();

            res.GroupBy(s => s.StepCode).ForEach(x =>
            {
                var first = x.First();
                var parent = new PlanProductionSchedulGanttChildItem
                {
                    Id = first?.StepCode,
                    ProductName = first?.ProductName,
                    StepCode = first?.StepCode,
                    StepName = first?.StepName,
                    StartTime = first?.SchedulDate,
                    EndTime = first?.SchedulDate,
                    Duration = 0,
                    StepSort = first?.StepSort ?? 0,
                    Parent = null
                };

                result.Add(parent);

                var list = x.ToList();

                for (int i = 0, k = 1; i < list.Count(); i++, k++)
                {
                    var item = new PlanProductionSchedulGanttChildItem
                    {
                        Id = $"{first?.StepCode}_{k}",
                        ProductName = list[i]?.ProductName,
                        StepName = list[i]?.StepName,
                        StepCode = list[i]?.StepCode,
                        StartTime = list[i]?.SchedulDate,
                        EndTime = list[i]?.SchedulDate,
                        Duration = 0,
                        Parent = list[0].StepCode,
                        StepSort = first?.StepSort ?? 0
                    };

                    result.Add(item);

                    for (int j = i + 1, d = 1; j <= list.Count() - 1; j++, d++)
                    {
                        if ((list[j].SchedulDate - item.StartTime).Value.Days == d)
                        {
                            i++;
                            item.Duration++;
                            item.EndTime = list[j].SchedulDate;
                            continue;
                        }
                        else
                        {
                            i = j - 1;
                            break;
                        }
                    }
                    item.Duration++;
                    //parent.Duration += item.Duration;
                }
                parent.EndTime = result.Last<dynamic>().EndTime;
                parent.Duration = (parent.EndTime - parent.StartTime)?.Days + 1;
            });
            var returnModel = new List<dynamic>();
            var gresult = result.Distinct().GroupBy(x => x.StartTime).ToList();
            foreach (var item in gresult)
            {
                var plans = item.OrderBy(x => x.StepSort);
                foreach (var s in plans)
                {
                    if (returnModel.Any(x => x.StartTime == s.StartTime && x.EndTime == s.EndTime && x.Parent == s.Parent && x.StepCode == s.StepCode))
                    {
                        continue;
                    }
                    returnModel.Add(s);
                }
            }

            return returnModel;
        }




        /// <summary>
        /// 重排并保存
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult<long>> SaveSchedulingProduction(ReloadBasProcuct input)
        {
            //待排产数据
            var basplanPending = _basplanPendingRepo.Where(o => o.PlanCode == input.PlanCode, true, false).FirstOrDefault();
            //产品工序关系表
            var basProStepRel = _basProStepRelRepo.Where(o => o.ProCode == basplanPending!.ProductCode).ToList();

            var basProductStandardCapacitys = input.lists;
            //前置工序
            var basTechnologyStep = _basTechnologyStepRelationRepo.Where(o => true).ToList();

            var stepEntities = _basStepRepo.Where(x => true).ToList();

            var planProductionSchedul = _planProductionSchedulRepo.Where(o => o.SchedulCode == input.SchedulCode).FirstOrDefault();


            var planProductionSchedulDto = Mapper.Map<PlanProductionSchedulDto>(planProductionSchedul);

            var basCapacitys = Mapper.Map<List<BasProductStandardCapacity>>(basProductStandardCapacitys);
            //排序方式
            int sort = planProductionSchedul!.Sorts;

            var capacitys = RemoveDuplicateData(basCapacitys, planProductionSchedulDto);

            if (planProductionSchedul.PreRule.ToString().Equals(SchedulTypeConst.Prerule_2, StringComparison.OrdinalIgnoreCase))
            {
                return await SavePartialProductionScheduling(capacitys, planProductionSchedul, basplanPending);
            }
            else if (planProductionSchedul.PreRule.ToString().Equals(SchedulTypeConst.Prerule_1, StringComparison.OrdinalIgnoreCase))
            {
                return await SaveCompleteProductionScheduling(capacitys, planProductionSchedul, basplanPending);
            }
            return AppSrvResult<long>(0);
        }

        /// <summary>
        /// 部分排产
        /// </summary>
        private async Task<AppSrvResult<long>> SavePartialProductionScheduling(List<ProcessSchedulDto> capacitys,
            PlanProductionSchedule planProductionSchedul, PartPlanPending basplanPending)
        {

            var result = new List<dynamic>();
            long id = IdGenerater.GetNextId();

            var autoCode = _basNumberManagementService.GetNumberBySimpleName(CommonConst.SCHEDULNUMBER).Result;

            DateTime maxDateTime = DateTime.MinValue;
            DateTime minDateTime = DateTime.MinValue;
            //排序方式
            int sort = planProductionSchedul!.Sorts;
            // 定义工序列表
            var processes = new List<ProductionProcess>();
            if (sort == SchedulSortsConst.DESC)
            {
                capacitys = capacitys.OrderByDescending(o => o.sort).ToList();
            }
            ProductionScheduler scheduler = new ProductionScheduler();
            scheduler.TargetQuantity = (int)basplanPending!.Qty;
            scheduler.StartTime = planProductionSchedul.PlanStart!;
            scheduler.Sorts = sort;
            foreach (var item in capacitys)
            {
                ProcessSchedul process = new ProcessSchedul(item.Name, item.ProcessingTime, item.RequiredQuantity, item.SingleProduction, item.LimitHours, item.StepName, item.SolutionName);
                if (!string.IsNullOrEmpty(item.PreStep) && !item.PreStep.Equals("无"))
                {
                    process.PrecedingProcesses.Add(item.PreStep);
                }
                scheduler.AddProcess(process);
            }
            scheduler.Schedule();

            foreach (var process in scheduler.Processes)
            {
                //产能占用
                var capacity = capacitys.FirstOrDefault(o => o.Name == process.Name);
                int i = 0;
                var autoCode1 = _basNumberManagementService.GetNumberBySimpleName(CommonConst.TASKRWBM).Result;
                foreach (var executionCount in process.DailyExecutionCount)
                {
                    if (sort == SchedulSortsConst.DESC)
                    {
                        int j = isVacationDesc(executionCount.Key.AddDays(i));
                        i = i + j;
                    }
                    else
                    {
                        int j = isVacation(executionCount.Key.AddDays(i));
                        i = i + j;
                    }
                    var model = new PlanProductionSchedulStepDetails
                    {
                        Id = IdGenerater.GetNextId(),
                        IsDeleted = false,
                        SchedulDate = executionCount.Key.AddDays(i),
                        StepCode = process.Name,
                        PlanCode = planProductionSchedul.PlanCode,
                        TaskCode = autoCode1,
                        Exenum = executionCount.Value,
                        SchedulCode = autoCode,
                        RemainingTime = process.RemainingTime
                    };
                    await _planProductionSchedulStepDetailsRepo.InsertAsync(model);
                    //工位占用
                    var sourceSchedulDeviceStation = new SourceSchedulDeviceStation
                    {
                        DiviceCode = capacity.DeviceCode,
                        Id = IdGenerater.GetNextId(),
                        OrderNumber = basplanPending.OrderNumber,
                        StationCode = capacity.StationCode,
                        IsDeleted = false,
                        Status = SchedulStatusConst.Waiting,
                        UseDate = model.SchedulDate,
                        StepCode = process.Name,
                        SchedulCode = autoCode,
                        UseendDate = model.SchedulDate.Value.AddSeconds(86399),
                        StepName = process.StepName
                    };
                    await _planSolutionRepo.InsertAsync(sourceSchedulDeviceStation);
                    if (sort == SchedulSortsConst.ASC)
                    {
                        if (model.SchedulDate >= maxDateTime)
                        {
                            maxDateTime = (DateTime)model.SchedulDate;
                        }
                    }
                    else if (sort == SchedulSortsConst.DESC)
                    {
                        if (model.SchedulDate <= minDateTime)
                        {
                            minDateTime = (DateTime)model.SchedulDate;
                        }
                    }
                }

                var planStepCapId = IdGenerater.GetNextId();
                var model1 = new PlanStepCapacity
                {
                    Id = IdGenerater.GetNextId(),
                    IsDeleted = false,
                    TaskCode = autoCode,
                    Capacity = (decimal)capacity!.SingleProduction,
                    DeviceCode = capacity.DeviceCode,
                    LineCode = planProductionSchedul.LCode,
                    SolutionName = capacity.SolutionName,
                    ProCode = capacity.ProCode,
                    StepCode = capacity.Name,
                    StandardWorkTime = (decimal)capacity.ProcessingTime,
                    WorkUnitTime = (decimal)capacity.LimitHours,
                    StepName = capacity.StepName,
                    StationCode = capacity.StationCode
                };
                await _planStepCapacityRepo.InsertAsync(model1);

                var planSchedulStepSolution = new PlanSchedulStepSolution
                {
                    Id = IdGenerater.GetNextId(),
                    IsDeleted = false,
                    PlanCode = planProductionSchedul.PlanCode,
                    StepCode = capacity.Name,
                    TaskCode = autoCode,
                    TandardCapacityId = planStepCapId
                };
                await _planschedulStepSolutionRepo.InsertAsync(planSchedulStepSolution);
            }

            //更新排产计划状态 
            basplanPending.Status = PendingPlanStatusConst.Planed;
            await _basplanPendingRepo.UpdateAsync(basplanPending!);

            //排产计划新增数据
            var entity = new PlanProductionSchedule
            {
                Id = id,
                LCode = planProductionSchedul.LCode,
                PlanCode = planProductionSchedul.PlanCode,
                PlanStart = (sort == SchedulSortsConst.ASC ? planProductionSchedul.PlanStart : minDateTime),
                PlanEnd = (sort == SchedulSortsConst.ASC ? maxDateTime : planProductionSchedul.PlanEnd),
                IsDeleted = false,
                PlanType = SchedulOperateConst.System,
                SchedulCode = autoCode,
                Sorts = planProductionSchedul.Sorts,
                PreRule = planProductionSchedul.PreRule,
                Status = SchedulStatusConst.Waiting
            };
            return await _planProductionSchedulRepo.InsertAsync(entity);

        }

        /// <summary>
        /// 整排
        /// </summary>
        /// <param name="capacitys"></param>
        /// <param name="planProductionSchedul"></param>
        /// <param name="basplanPending"></param>
        /// <param name="input"></param>
        /// <returns></returns>
        private async Task<AppSrvResult<long>> SaveCompleteProductionScheduling(List<ProcessSchedulDto> capacitys,
            PlanProductionSchedule planProductionSchedul, PartPlanPending basplanPending)
        {
            var result = new List<dynamic>();
            long id = IdGenerater.GetNextId();

            var autoCode = _basNumberManagementService.GetNumberBySimpleName(CommonConst.SCHEDULNUMBER).Result;

            DateTime maxDateTime = DateTime.MinValue;
            DateTime minDateTime = DateTime.MinValue;
            //排序方式
            int sort = planProductionSchedul!.Sorts;
            // 定义工序列表
            var processes = new List<ProductionProcess>();
            if (sort == SchedulSortsConst.DESC)
            {
                capacitys = capacitys.OrderByDescending(o => o.sort).ToList();
            }

            foreach (var item in capacitys)
            {
                ProductionProcess process = new ProductionProcess(item.Name, item.ProcessingTime, item.SingleProduction,
                    item.RequiredQuantity, item.IntervalTime, item.LimitHours, item.sort, item.StepName,
                    item.SolutionName, item.StationCode, item.StartTime);

                if (!string.IsNullOrEmpty(item.PreStep))
                {
                    var item1 = capacitys.FirstOrDefault(o => o.Name == item.PreStep);
                    ProductionProcess process1 = new ProductionProcess(item1.Name, item1.ProcessingTime, item1.SingleProduction,
                   item1.RequiredQuantity, item1.IntervalTime, item1.LimitHours, item.sort, item1.StepName,
                   item1.SolutionName, item1.StationCode, item.StartTime);

                    process.PreviousProcesses.Add(process1);
                }
                processes.Add(process);
            }
            // 定义产品数量
            int productQuantity = (int)basplanPending!.Qty;

            // 计算每个工序的执行日期和每天执行次数
            DateTime currentDateTime = DateTime.MinValue;

            DateTime endAscDateTime = DateTime.MinValue;
            DateTime endDescDateTime = DateTime.MaxValue;

            var dayCondition = (0, 0);
            foreach (var process in processes)
            {
                currentDateTime = process.StartTime;
                //产能占用
                var capacity = capacitys.FirstOrDefault(o => o.Name == process.Name);
                int i = 0;
                double workHoursPerDay = process.LimitHours;

                if (sort == SchedulSortsConst.DESC)
                {
                    minDateTime = currentDateTime;
                    var preProcesses = processes
                        .Where(o => o.PreviousProcesses.Count > 0
                                && o.PreviousProcesses.FirstOrDefault(x => x.Name == process.Name) != null
                                && o.Sorts > process.Sorts)
                        .Select(o => o.Name);

                    var downProcess = processes
                        .Where(o => preProcesses.Contains(o.Name))
                        .OrderBy(o => o.EndTime)
                        .FirstOrDefault();


                    if (downProcess != null)
                    {
                        dayCondition = GetLeaveDate(downProcess, process, productQuantity);
                    }
                    process.CalculateExecutionDatesDesc(productQuantity, currentDateTime, workHoursPerDay, dayCondition.Item2);
                }
                else
                {
                    maxDateTime = currentDateTime;
                    if (process.PreviousProcesses != null && process.PreviousProcesses.Count > 0)
                    {
                        dayCondition = GetLeaveDate(process.PreviousProcesses.FirstOrDefault(), process, productQuantity);
                    }
                    process.CalculateExecutionDates(productQuantity, currentDateTime, workHoursPerDay, dayCondition.Item2);

                }


                var autoCode1 = _basNumberManagementService.GetNumberBySimpleName(CommonConst.TASKRWBM).Result;
                foreach (var execution in process.DailyExecutionCounts)
                {
                    if (sort == SchedulSortsConst.DESC)
                    {
                        int j = isVacationDesc(execution.Key.AddDays(i));
                        i = i + j;
                    }
                    else
                    {
                        int j = isVacation(execution.Key.AddDays(i));
                        i = i + j;
                    }
                    var model = new PlanProductionSchedulStepDetails
                    {
                        Id = IdGenerater.GetNextId(),
                        IsDeleted = false,
                        SchedulDate = execution.Key.AddDays(i),
                        StepCode = process.Name,
                        PlanCode = planProductionSchedul.PlanCode,
                        TaskCode = autoCode1,
                        Exenum = execution.Value,
                        SchedulCode = autoCode,
                        RemainingTime = process.RemainingTime
                    };
                    await _planProductionSchedulStepDetailsRepo.InsertAsync(model);

                    //工位占用
                    var sourceSchedulDeviceStation = new SourceSchedulDeviceStation
                    {
                        DiviceCode = capacity.DeviceCode,
                        Id = IdGenerater.GetNextId(),
                        OrderNumber = basplanPending.OrderNumber,
                        StationCode = capacity.StationCode,
                        IsDeleted = false,
                        Status = SchedulStatusConst.Waiting,
                        UseDate = model.SchedulDate,
                        StepCode = process.Name,
                        SchedulCode = autoCode,
                        UseendDate = model.SchedulDate.Value.AddSeconds(86399),
                        StepName = process.StepName
                    };
                    await _planSolutionRepo.InsertAsync(sourceSchedulDeviceStation);

                    if (sort == SchedulSortsConst.ASC)
                    {
                        if (model.SchedulDate >= maxDateTime)
                        {
                            maxDateTime = (DateTime)model.SchedulDate;
                            if (model.SchedulDate >= endAscDateTime)
                            {
                                endAscDateTime = (DateTime)model.SchedulDate;
                            }
                        }
                        currentDateTime = maxDateTime;
                    }
                    else if (sort == SchedulSortsConst.DESC)
                    {
                        if (model.SchedulDate <= minDateTime)
                        {
                            minDateTime = (DateTime)model.SchedulDate;
                            if (model.SchedulDate <= endDescDateTime)
                            {
                                endDescDateTime = (DateTime)model.SchedulDate;
                            }
                        }
                        currentDateTime = minDateTime;
                    }
                }

                var planStepCapId = IdGenerater.GetNextId();
                var model1 = new PlanStepCapacity
                {
                    Id = planStepCapId,
                    IsDeleted = false,
                    TaskCode = autoCode,
                    Capacity = (decimal)capacity!.SingleProduction,
                    DeviceCode = capacity.DeviceCode,
                    LineCode = capacity.LineCode,
                    SolutionName = capacity.SolutionName,
                    ProCode = capacity.ProCode,
                    StepCode = capacity.Name,
                    StandardWorkTime = (decimal)capacity.ProcessingTime,
                    WorkUnitTime = (decimal)capacity.LimitHours,
                    StationCode = process.StationCode,
                    StepName = capacity.StepName
                };
                await _planStepCapacityRepo.InsertAsync(model1);

                var planSchedulStepSolution = new PlanSchedulStepSolution
                {
                    Id = IdGenerater.GetNextId(),
                    IsDeleted = false,
                    PlanCode = planProductionSchedul.PlanCode,
                    StepCode = capacity.Name,
                    TaskCode = autoCode1,
                    TandardCapacityId = planStepCapId
                };
                await _planschedulStepSolutionRepo.InsertAsync(planSchedulStepSolution);
            }

            //更新排产计划状态 
            basplanPending.Status = PendingPlanStatusConst.Planed;
            await _basplanPendingRepo.UpdateAsync(basplanPending!);

            //排产计划新增数据
            var entity = new PlanProductionSchedule
            {
                Id = id,
                LCode = string.Join(",", capacitys.Select(o => o.LineCode).Distinct().ToList()),
                PlanCode = planProductionSchedul.PlanCode,
                PlanStart = (sort == SchedulSortsConst.ASC ? (DateTime)planProductionSchedul.SchedulTime : endDescDateTime),
                PlanEnd = (sort == SchedulSortsConst.ASC ? endAscDateTime : planProductionSchedul.SchedulTime),
                IsDeleted = false,
                PlanType = SchedulOperateConst.System,
                SchedulCode = autoCode,
                Sorts = planProductionSchedul.Sorts,
                PreRule = planProductionSchedul.PreRule,
                Status = SchedulStatusConst.Waiting,
                SchedulTime = (DateTime)planProductionSchedul.SchedulTime!
            };
            return await _planProductionSchedulRepo.InsertAsync(entity);

        }
        static T DeepCloneBinary<T>(T original)
        {
            using (var stream = new MemoryStream())
            {
                var formatter = new BinaryFormatter();
                formatter.Serialize(stream, original);
                stream.Seek(0, SeekOrigin.Begin);
                return (T)formatter.Deserialize(stream);
            }
        }

        private int CalculateDaysDifference(List<PlanSolution> planSolutions, int sort)
        {
            long minStartTime = long.MinValue;
            long maxExecuteTime = long.MinValue;
            if (sort == SchedulSortsConst.ASC)
            {
                minStartTime = (long)planSolutions.Min(p => p.StartTime).Ticks;
                maxExecuteTime = (long)planSolutions.Max(p => p.ExecuteTime).Ticks;
            }
            else
            {
                minStartTime = (long)planSolutions.Min(p => p.ExecuteTime.Date).Ticks;
                maxExecuteTime = (long)planSolutions.Max(p => p.StartTime.Date).Ticks;
            }

            TimeSpan difference = TimeSpan.FromTicks(maxExecuteTime - minStartTime);
            int daysDifference = difference.Days;

            return daysDifference;
        }


        private List<List<ProductionProcess>> GetCombinations(List<ProductionProcess> planSolutions)
        {
            List<List<ProductionProcess>> combinations = new List<List<ProductionProcess>>();

            // 根据名称分组
            var groups = planSolutions.GroupBy(p => p.Name).ToList();

            // 递归生成组合
            GenerateCombinations(groups, new List<ProductionProcess>(), combinations);

            return combinations;
        }

        private void GenerateCombinations(List<IGrouping<string, ProductionProcess>> groups, List<ProductionProcess> currentCombination, List<List<ProductionProcess>> combinations)
        {
            if (currentCombination.Count == groups.Count())
            {
                combinations.Add(currentCombination.ToList());
                return;
            }

            var currentGroup = groups.ElementAt(currentCombination.Count);

            foreach (var item in currentGroup)
            {
                var item1 = DeepCloneBinary<ProductionProcess>(item);
                currentCombination.Add(item1);
                GenerateCombinations(groups, currentCombination, combinations);
                currentCombination.RemoveAt(currentCombination.Count - 1);
            }
        }


        private List<List<DateTime>> GetDateGroups(List<PlanStepCapacitySource> list)
        {
            List<DateTime?> allDates = new List<DateTime?>();

            if (list.Count <= 0)
            {
                return null;
            }
            DateTime startDate = list.Min(o => o.SchedulDate).Value;
            DateTime endDate = list.Max(o => o.SchedulDate).Value;

            var existingDates = list.Select(o => o.SchedulDate).Distinct();
            for (DateTime date = startDate; date <= endDate; date = date.AddDays(1))
            {
                allDates.Add(date);
            }

            List<DateTime?> missingDates = allDates.Except(existingDates).ToList();

            List<List<DateTime>> groups = new List<List<DateTime>>();
            List<DateTime> currentGroup = new List<DateTime>();

            missingDates.Sort();

            foreach (DateTime date in missingDates)
            {
                if (currentGroup.Count == 0 || (date - currentGroup.Last()).Days == 1)
                {
                    currentGroup.Add(date);
                }
                else
                {
                    if (currentGroup.Count > 1)
                    {
                        groups.Add(currentGroup);
                    }
                    currentGroup = new List<DateTime> { date };
                }
            }

            if (currentGroup.Count > 1)
            {
                groups.Add(currentGroup);
            }

            return groups;
        }


        private (int, int) GetDate(List<StationClass> stationClasses)
        {
            List<StationTime> aaList = new List<StationTime>();
            foreach (var item in stationClasses)
            {
                aaList.Add(new StationTime
                {
                    begintime = new TimeSpan(item.BeginTime.Split(':')[0].Cast<int>().First(), 0, 0),
                    endtime = new TimeSpan(item.EndTime.Split(':')[0].Cast<int>().First(), 0, 0)
                });
            }
            TimeSpan minBeginTime = TimeSpan.MaxValue;
            TimeSpan maxEndTime = TimeSpan.MinValue;

            foreach (StationTime aa in aaList)
            {
                if (aa.begintime < minBeginTime)
                {
                    minBeginTime = aa.begintime;
                }

                if (aa.endtime > maxEndTime)
                {
                    maxEndTime = aa.endtime;
                }
                else if (aa.endtime < aa.begintime && aa.endtime > maxEndTime)
                {
                    maxEndTime = aa.endtime;
                }
            }

            return (minBeginTime.Hours, maxEndTime.Hours);
        }

        private bool GetisUnion(List<CurrentTime> list)
        {
            bool hasIntersection = false;

            for (int i = 0; i < list.Count - 1; i++)
            {
                for (int j = i + 1; j < list.Count; j++)
                {
                    int begin1 = list[i].begin;
                    int end1 = list[i].end;
                    int begin2 = list[j].begin;
                    int end2 = list[j].end;

                    // 处理跨天情况
                    if (end1 < begin1)
                    {
                        end1 += 24;
                    }
                    if (end2 < begin2)
                    {
                        end2 += 24;
                    }

                    if (begin1 <= end2 && begin2 <= end1)
                    {
                        hasIntersection = true;
                        break;
                    }
                }

                if (hasIntersection)
                {
                    break;
                }
            }

            return hasIntersection;
        }

        /// <summary>
        /// 正排判断是否是工作日
        /// </summary>
        /// <param name="dt"></param>
        /// <returns></returns>
        private int isVacation(DateTime dt)
        {
            int i = 0;
            bool flag = true;
            while (flag)
            {
                var holiday = _basHolidayRepo.Where(o => o.CurrentDate!.Value.Date == dt.Date
                                                && o.IsDeleted == false
                                                && o.IsPlaned == 1).FirstOrDefault();
                if (null != holiday)
                {
                    flag = false;
                }
                else
                {
                    flag = true;
                    dt = dt.AddDays(1);
                    i++;
                }
            }
            return i;
        }

        /// <summary>
        /// 倒排判断是否工作日
        /// </summary>
        /// <param name="dt"></param>
        /// <returns></returns>
        private int isVacationDesc(DateTime dt)
        {
            int i = 0;
            bool flag = true;
            while (flag)
            {
                var holiday = _basHolidayRepo.Where(o => o.CurrentDate!.Value.Date == dt.Date
                                                && o.IsDeleted == false
                                                && o.IsPlaned == 1).FirstOrDefault();
                if (null != holiday)
                {
                    flag = false;
                }
                else
                {
                    flag = true;
                    dt = dt.AddDays(-1);
                    i--;
                }
            }
            return i;
        }

        private bool isValidate(DateTime dt)
        {
            bool flag = true;
            var holiday = _basHolidayRepo.Where(o => o.CurrentDate!.Value.Date == dt.Date
                                                && o.IsDeleted == false
                                                && o.IsPlaned == 1).FirstOrDefault();

            if (null != holiday)
            {
                flag = false;
            }
            return flag;
        }

        #endregion

        /// <summary>
        /// 获取产品甘特图
        /// </summary>
        /// <param name="productCode"></param>
        /// <returns></returns>
        public async Task<List<PlanProductionSchedulGanttChildItem>> GetGanttByProductAsync(ProductionSchedulGanttDto dto)
        {
            dto.TrimStringFields();

            var result = new List<PlanProductionSchedulGanttChildItem>();

            Func<string?, string?, Task<List<PlanProductionSchedulData>>> SearchGanttItems
                = new(async (scheduleCode, scheduleStatus) =>
                {
                    var result = new PlanProductionSchedulGanttResultDto();
                    var whereExpression = ExpressionCreator
                                                    .New<PlanProductionSchedule>()
                                                    .And(x => !x.IsDeleted)
                                                    .AndIf(scheduleCode.IsNotNullOrWhiteSpace(), x => x.SchedulCode == scheduleCode)
                                                    .AndIf(scheduleStatus.IsNotNullOrWhiteSpace(), x => x.Status.ToString() == scheduleStatus);

                    var schedulEntities = _planProductionSchedulRepo.Where(whereExpression);

                    var basPendingPlanEntities = _pendingPlanRepo.Where(x => true);

                    var planDetailEntities = _plandetailsRepo.Where(x => true);

                    var stepEntities = await _basStepRepo.Where(x => true).ToListAsync();

                    var productEntities = _basProductRepo.Where(x => true);

                    var techStepEntities = _basTechnologyStepRelationRepo.Where(x => true);

                    var list = await (from a in schedulEntities
                                      join b in basPendingPlanEntities on a.PlanCode equals b.PlanCode into _1
                                      from t_1 in _1.DefaultIfEmpty()
                                      join c in planDetailEntities on a.SchedulCode equals c.SchedulCode into _2
                                      from t_2 in _2.DefaultIfEmpty()
                                      join pros in productEntities on t_1.ProductCode equals pros.Procode into ptemp
                                      from products in ptemp.DefaultIfEmpty()
                                      join techs in techStepEntities on products.TCode equals techs.TCode into tes
                                      from techSteps in tes.DefaultIfEmpty()
                                          //join techStep in techStepEntities on t_2.StepCode equals techStep.StepCode into tbl
                                          //from x in tbl.DefaultIfEmpty()
                                          //join step in stepEntities on t_2.StepCode equals step.Code into _3
                                          //from t_3 in _3.DefaultIfEmpty()
                                      orderby t_2.SchedulDate
                                      select new PlanProductionSchedulData()
                                      {
                                          OrderNumber = t_1.OrderNumber,
                                          ProductName = t_1.ProductName,
                                          PlanCode = t_1.PlanCode,
                                          ProductCode = t_1.ProductCode,
                                          SchedulDate = t_2.SchedulDate,
                                          PlanStart = a.PlanStart,
                                          PlanEnd = a.PlanEnd,
                                          ScheduleStatus = a.Status,
                                          //StepName = t_3.Name,
                                          StepCode = t_2.StepCode,
                                          StepSort = techSteps.Sort ?? 0,
                                          TecStepCode = techSteps.StepCode
                                      }).Where(x => x.StepCode == x.TecStepCode).ToListAsync();
                    list.ForEach(x =>
                    {
                        var step = stepEntities.Find(s => s.Code == x.StepCode);
                        x.StepName = step?.Name;
                    });
                    return list;
                });

            if (dto.OrderNumber.IsNotNullOrWhiteSpace())
            {
                var whereExpression = ExpressionCreator
                                                .New<PlanProductionSchedule>()
                                                .And(x => !x.IsDeleted)
                                                .AndIf(dto.BeginTime is DateTime, x => x.PlanStart >= dto.BeginTime)
                                                .AndIf(dto.EndTime is DateTime, x => x.PlanEnd <= dto.EndTime);

                var orderEntities = _basOrderRepo.Where(x => !x.IsDeleted && x.OrderNumber == dto.OrderNumber);

                var orderProductEntities = _basOrderProductRepo.Where(x => !x.IsDeleted);
                var basPendingPlanEntities = _pendingPlanRepo.Where(x => !x.IsDeleted);
                var schedulEntities = _planProductionSchedulRepo.Where(whereExpression);

                var list = await (from a in schedulEntities
                                  join b in basPendingPlanEntities on a.PlanCode equals b.PlanCode into t1
                                  from x in t1.DefaultIfEmpty()
                                  orderby x.ProductCode
                                  select new
                                  {
                                      BeginTime = a.PlanStart,
                                      EndTime = a.PlanEnd,
                                      ScheduleCode = a.SchedulCode,
                                      x.OrderNumber
                                  }).Where(x => x.OrderNumber == dto.OrderNumber).ToListAsync();

                if (list.Any())
                {
                    list.Where(x => x.ScheduleCode is not null).ForEach(l =>
                    {
                        var res = SearchGanttItems(l.ScheduleCode, dto.ScheduleStatus).Result;
                        res.GroupBy(s => s.OrderNumber).ForEach(x =>
                        {

                        });
                    });
                }

                return result;
            }
            else if (dto.ScheduleCode.IsNotNullOrWhiteSpace())
            {
                var res = await SearchGanttItems(dto.ScheduleCode, dto.ScheduleStatus);
                var plans = res.Distinct().GroupBy(s => s.StepCode);
                foreach (var x in plans)
                {
                    {
                        var first = x.First();
                        var parent = new PlanProductionSchedulGanttChildItem
                        {
                            Id = first?.StepCode,
                            ProductName = first?.ProductName,
                            StepCode = first?.StepCode,
                            StepName = first?.StepName,
                            StartTime = first?.SchedulDate,
                            EndTime = first?.SchedulDate,
                            Duration = 0,
                            StepSort = first?.StepSort ?? 0,
                            Parent = null
                        };

                        result.Add(parent);

                        var list = x.ToList();

                        for (int i = 0, k = 1; i < list.Count(); i++, k++)
                        {
                            var s = list[i];
                            if (s != null)
                            {
                                if (result.Any(x => x.StartTime == s.SchedulDate && x.EndTime == s.SchedulDate && x.Parent == list[0].StepCode && x.StepCode == s.StepCode))
                                {
                                    continue;
                                }
                            }
                            var item = new PlanProductionSchedulGanttChildItem
                            {
                                Id = $"{first?.StepCode}_{k}",
                                ProductName = list[i]?.ProductName,
                                StepName = list[i]?.StepName,
                                StepCode = list[i]?.StepCode,
                                StartTime = list[i]?.SchedulDate,
                                EndTime = list[i]?.SchedulDate,
                                Duration = 0,
                                Parent = list[0].StepCode,
                                StepSort = first?.StepSort ?? 0
                            };

                            result.Add(item);

                            for (int j = i + 1, d = 1; j <= list.Count() - 1; j++, d++)
                            {
                                if ((list[j].SchedulDate - item.StartTime).Value.Days == d)
                                {
                                    i++;
                                    item.Duration++;
                                    item.EndTime = list[j].SchedulDate;
                                    continue;
                                }
                                else
                                {
                                    i = j - 1;
                                    break;
                                }
                            }
                            item.Duration++;
                            //parent.Duration += item.Duration;
                        }
                        parent.EndTime = result.Last<dynamic>().EndTime;
                        parent.Duration = (parent.EndTime - parent.StartTime)?.Days + 1;
                    }
                }
            }
            else
            {
                var whereExpression = ExpressionCreator
                                                .New<PlanProductionSchedule>()
                                                .And(x => !x.IsDeleted)
                                                .AndIf(dto.BeginTime is DateTime, x => x.PlanStart >= dto.BeginTime)
                                                .AndIf(dto.EndTime is DateTime, x => x.PlanEnd <= dto.EndTime);

                var schedulEntities = await _planProductionSchedulRepo.Where(whereExpression).ToListAsync();
            }
            var returnModel = new List<PlanProductionSchedulGanttChildItem>();
            var gresult = result.Distinct().GroupBy(x => x.StartTime).ToList();
            foreach (var item in gresult)
            {
                var plans = item.OrderBy(x => x.StepSort);
                foreach (var s in plans)
                {
                    if (returnModel.Any(x => x.StartTime == s.StartTime && x.EndTime == s.EndTime && x.Parent == s.Parent && x.StepCode == s.StepCode))
                    {
                        continue;
                    }
                    returnModel.Add(s);
                }
            }
            //  result = result.OrderBy(x => x.StartTime).ThenBy(x => x.StepSort).ToList();
            return returnModel;
        }



        public async Task<PlanGanttDto> GetGanttByProductNewAsync(long id)
        {
            var result = new PlanGanttDto();
            var scheduleDto = await _spRepo.GetAsync(id);
            var stepDtos = await _spStepRepo.Where(x => !x.IsDeleted && x.ScheduleCode == scheduleDto.Code).ToListAsync();
            var planDtos = await _pendingPlanRepo.Where(x => !x.IsDeleted && x.PlanCode == scheduleDto.PlanCode).ToListAsync();
            var plan = planDtos.FirstOrDefault();
            //var productDtos = await _basProductRepo.Where(x => !x.IsDeleted && x.Procode == plan.ProductCode).ToListAsync();
            //var product = productDtos.FirstOrDefault();
            
            var basStepDtos = await _basStepRepo.Where(x => !x.IsDeleted && stepDtos.Select(x => x.StepCode).Contains(x.Code)).ToListAsync();
            //var tecStepDtos = await _basTechnologyStepRelationRepo.Where(x => !x.IsDeleted && x.TCode == product.TCode).ToListAsync();

            result.Tasks = (from a in stepDtos
                          join b in basStepDtos on a.StepCode equals b.Code
                          //join c in tecStepDtos on b.Code equals c.StepCode 
                          select new PlanProductionSchedulGanttChildItem
                          {
                              Progress = a.ReportResult,
                              ScheduleCode = a.ScheduleCode,
                              Duration = Convert.ToInt32(a.Tat),
                              EndTime = a.EndDate,
                              StartTime = a.BeginDate,
                              StepCode = a.StepCode,
                              StepName = b.Name,
                              Parent = a.PreCode,
                              StepSort = 0,
                              Id = a.StepCode,
                              ProductName = plan.ProductName,
                          }).ToList();

            result.Links = (from a in stepDtos
                            select new ScheduleGanttLink
                            {
                                id = IdGenerater.GetNextId(),
                                TargetCode = a.StepCode,
                                SourceCode = a.PreCode,
                                Type = 0
                            }).ToList();

            return result;
        }


        public async Task<ProductionSchedulDeviceGanttResultDto> GetGanttForDeviceAsync(ProductionSchedulGanttDto dto)
        {
            dto.TrimStringFields();
            var result = new ProductionSchedulDeviceGanttResultDto();

            var whereExpression = ExpressionCreator
                                                .New<PlanProductionSchedule>()
                                                .And(x => !x.IsDeleted)
                                                .AndIf(dto.ScheduleCode.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.SchedulCode, dto.ScheduleCode))
                                                .AndIf(dto.ScheduleStatus.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Status, dto.ScheduleStatus));

            var schedulEntities = _planProductionSchedulRepo.Where(whereExpression);

            var basPendingPlanEntities = _pendingPlanRepo.Where(x => true);

            var sourceSchedulDeviceEntities = _planSolutionRepo.Where(x => true);

            var deviceEntities = _basDeviceRepo.Where(x => true);

            var productEntities = _basProductRepo.Where(x => true);

            var orderEntities = _basOrderRepo.Where(x => true);

            var list = await (from a in schedulEntities
                              join b in basPendingPlanEntities on a.PlanCode equals b.PlanCode into t1
                              from t1s in t1.DefaultIfEmpty()
                              join c in sourceSchedulDeviceEntities on t1s.OrderNumber equals c.OrderNumber into t2
                              from t2s in t2.DefaultIfEmpty()
                              join d in deviceEntities on t2s.DiviceCode equals d.Code into t3
                              from t3s in t3.DefaultIfEmpty()
                              join e in productEntities on t1s.ProductCode equals e.Procode into tbl
                              from x in tbl.DefaultIfEmpty()
                              select new
                              {
                                  OrderNumber = t1s.OrderNumber,
                                  ProductName = t1s.ProductName,
                                  PlanCode = t1s.PlanCode,
                                  ProductCode = t1s.ProductCode,
                                  ScheduleBeginTime = a.PlanStart,
                                  ScheduleEndTime = a.PlanEnd,
                                  ScheduleStatus = a.Status,
                                  DeviceName = t3s.Name,
                                  DeviceCode = t3s.Code,
                                  BeginTime = t2s.UseDate,
                                  EndTime = t2s.UseDate,
                              }).ToListAsync();

            if (list.Any())
            {
                result.ScheduleCode = dto.ScheduleCode;
                result.OrderNumber = list.First().OrderNumber;
                result.ProductName = list.First().ProductName;
                result.BeginTime = list.First().ScheduleBeginTime;
                result.EndTime = list.First().ScheduleEndTime;

                list.GroupBy(x => x.BeginTime).ForEach(x =>
                {
                    var dateList = x.Select(x => new DeviceGanttDate()
                    {
                        //StepCode = x.,
                        BeginTime = x.BeginTime,
                        EndTime = x.EndTime
                    });

                    result.DeviceItems?.Add(new()
                    {
                        DeviceCode = x.First().DeviceCode,
                        DeviceName = x.First().DeviceName,
                        DateList = dateList?.ToList()
                    });
                });
            }

            return result;
        }

        public async Task<List<PlanProductionSchedulGanttForOrderDto>> GetGanttByOrderAsync(ProductionSchedulGanttDto dto)
        {
            dto.TrimStringFields();

            var result = new List<PlanProductionSchedulGanttForOrderDto>();

            Func<string?, string?, Task<PlanProductionSchedulGanttResultDto>> SearchGanttItems
                = new(async (scheduleCode, scheduleStatus) =>
                {
                    var result = new PlanProductionSchedulGanttResultDto();
                    var whereExpression = ExpressionCreator
                                                    .New<PlanProductionSchedule>()
                                                    .And(x => !x.IsDeleted)
                                                    .AndIf(scheduleCode.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.SchedulCode, scheduleCode))
                                                    .AndIf(scheduleStatus.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Status, scheduleStatus));

                    var schedulEntities = _planProductionSchedulRepo.Where(whereExpression);

                    var basPendingPlanEntities = _pendingPlanRepo.Where(x => true);

                    var planDetailEntities = _plandetailsRepo.Where(x => true);

                    var stepEntities = _basStepRepo.Where(x => true);

                    var productEntities = _basProductRepo.Where(x => true);

                    var techStepEntities = _basTechnologyStepRelationRepo.Where(x => true);

                    var list = await (from a in schedulEntities
                                      join b in basPendingPlanEntities on a.PlanCode equals b.PlanCode into _1
                                      from t_1 in _1.DefaultIfEmpty()
                                      join c in planDetailEntities on a.SchedulCode equals c.SchedulCode into _2
                                      from t_2 in _2.DefaultIfEmpty()
                                      join step in stepEntities on t_2.StepCode equals step.Code into _3
                                      from t_3 in _3.DefaultIfEmpty()
                                      join product in productEntities on t_1.ProductCode equals product.Procode into _4
                                      from t_4 in _4.DefaultIfEmpty()
                                      join techStep in techStepEntities on new { t_4.TCode, StepCode = t_3.Code } equals new { techStep.TCode, techStep.StepCode }
                                      into tbl
                                      from x in tbl.DefaultIfEmpty()
                                      orderby x.Sort, t_2.SchedulDate
                                      select new
                                      {
                                          OrderNumber = t_1.OrderNumber,
                                          ProductName = t_1.ProductName,
                                          PlanCode = t_1.PlanCode,
                                          ProductCode = t_1.ProductCode,
                                          SchedulDate = t_2.SchedulDate,
                                          ScheduleStatus = a.Status,
                                          StepName = t_3.Name,
                                          StepCode = t_3.Code,
                                      }).ToListAsync();

                    if (list.Any())
                    {
                        ProductScheduleProductItem productItem = new()
                        {
                            ScheduleCode = scheduleCode,
                            ScheduleStatus = scheduleStatus,
                            ProductName = list.First().ProductName,
                        };

                        result.ScheduleItems?.Add(productItem);

                        result.OrderNumber = list.First().OrderNumber;

                        list.GroupBy(x => x.StepCode).ForEach(x =>
                        {
                            var dateList = x.Select(x => new GanttDate()
                            {
                                StepCode = x.StepCode,
                                BeginTime = x.SchedulDate,
                                EndTime = x.SchedulDate
                            });

                            result.ScheduleItems?.Last().GanttItems?.Add(new()
                            {
                                StepName = x.First().StepName,
                                StepCode = x.First().StepCode,
                                BeginTime = x.First().SchedulDate,
                                EndTime = x.Last().SchedulDate,
                                DateList = dateList?.ToList()
                            });
                        });
                    }

                    return result;
                });

            var whereExpression = ExpressionCreator
                                                .New<PlanProductionSchedule>()
                                                .And(x => !x.IsDeleted)
                                                .And(x => x.Status == 1);
            //.AndIf(dto.BeginTime is DateTime, x => x.PlanStart >= dto.BeginTime)
            //.AndIf(dto.EndTime is DateTime, x => x.PlanEnd <= dto.EndTime);

            var basPendingPlanEntities = _pendingPlanRepo.Where(x => !x.IsDeleted);

            var schedulEntities = _planProductionSchedulRepo.Where(whereExpression);

            var list = await (from a in schedulEntities
                              join b in basPendingPlanEntities on a.PlanCode equals b.PlanCode into t1
                              from x in t1.DefaultIfEmpty()
                              where x.PlanCode != null
                              orderby x.OrderNumber
                              select new
                              {
                                  x.PlanCode,
                                  x.OrderNumber,
                                  ScheduleStatus = a.Status,
                                  BeginTime = a.PlanStart,
                                  EndTime = a.PlanEnd,
                                  x.ProductCode,
                                  x.ProductName,
                                  x.DeliveryDate,
                                  ScheduleCode = a.SchedulCode
                              }).ToListAsync();

            if (list.Any())
            {
                if (dto.OrderNumber.IsNotNullOrWhiteSpace())
                {
                    var l = list?.Find(y => y.OrderNumber == dto.OrderNumber);
                    result.Add(new PlanProductionSchedulGanttForOrderDto
                    {
                        OrderNumber = l.OrderNumber,
                        BeginTime = l.BeginTime.Date,
                        EndTime = l.EndTime.HasValue ? l.EndTime.Value.Date : null,
                        ProductName = l.ProductName,
                        DeliveryDate = l.DeliveryDate,
                    });
                    return result;
                }

                list.Where(x => x.ScheduleCode is not null).ForEach(l =>
                {
                    result.Add(new PlanProductionSchedulGanttForOrderDto
                    {
                        OrderNumber = l.OrderNumber,
                        BeginTime = l.BeginTime.Date,
                        EndTime = l.EndTime.HasValue ? l.EndTime.Value.Date : null,
                        ProductName = l.ProductName,
                        DeliveryDate = l.DeliveryDate,
                    });
                });
            }

            return result;
        }

        public async Task<List<PlanProductionSchedulGanttForOrderDto>> GetGanttByOrderNewAsync(ProductionSchedulGanttDto dto)
        {
            dto.TrimStringFields();
            var whereExpression = ExpressionCreator
                                                .New<PartSchedulePlan>()
                                                .And(x => !x.IsDeleted)
                                                .And(x => x.State == SchedulStatusConst.Effect)
                                                .AndIf(dto.OrderNumber.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.PlanCode!, $"%{dto.OrderNumber}%"));

            var scheduleDtos = await _spRepo.Where(whereExpression).ToListAsync();
            var planDtos = await _pendingPlanRepo.Where(x => scheduleDtos.Select(x => x.PlanCode).Contains(x.PlanCode)).ToListAsync();
            var result = (from a in scheduleDtos
                          join b in planDtos on a.PlanCode equals b.PlanCode
                          select new PlanProductionSchedulGanttForOrderDto
                          {
                              Parent = "",
                              OrderNumber = a.PlanCode,
                              BeginTime = a.PlanBeginDate,
                              EndTime = a.PlanEndDate,
                              ProductName = b.ProductName,
                              DeliveryDate = b.DeliveryDate
                          }).ToList();



            //var stepDtos = await _spStepRepo.Where(x => !x.IsDeleted && scheduleDtos.Select(x => x.PlanCode).Contains(x.ScheduleCode)).ToListAsync();
            //var productDtos = await _basProductRepo.Where(x => !x.IsDeleted && planDtos.Select(x=>x.ProductCode).Contains(x.Procode)).ToListAsync();
            
            //var basStepDtos = await _basStepRepo.Where(x => !x.IsDeleted && stepDtos.Select(x => x.StepCode).Contains(x.Code)).ToListAsync();
            //var tecStepDtos = await _basTechnologyStepRelationRepo.Where(x => !x.IsDeleted && productDtos.Select(x=>x.TCode).Contains(x.TCode)).ToListAsync();

            //var dtos = (from a in stepDtos
            //               join b in basStepDtos on a.StepCode equals b.Code
            //               join c in tecStepDtos on b.Code equals c.StepCode
            //               select new PlanProductionSchedulGanttForOrderDto
            //               {
            //                   EndTime = a.EndDate,
            //                   BeginTime = a.BeginDate,
            //                   ProductName = b.Name,
            //                   Parent = c.PreStep,
            //                   OrderNumber = "",
            //               }).ToList();

            //result.AddRange(dtos);




            return result;
        }
        /*
         * 
        public async Task<List<PlanProductionSchedulGanttNewResultDto>> GetGanttForOrderProducePlanAsync(ProductionSchedulGanttDto dto)
        {
            dto.TrimStringFields();

            var result = new List<PlanProductionSchedulGanttNewResultDto>();

            Func<string?, string?, Task<PlanProductionSchedulGanttResultDto>> SearchGanttItems
                = new(async (scheduleCode, scheduleStatus) =>
                {
                    var result = new PlanProductionSchedulGanttResultDto();
                    var whereExpression = ExpressionCreator
                                                    .New<PlanProductionSchedule>()
                                                    .And(x => !x.IsDeleted)
                                                    .AndIf(scheduleCode.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.SchedulCode, scheduleCode))
                                                    .AndIf(scheduleStatus.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Status, scheduleStatus));

                    var schedulEntities = _planProductionSchedulRepo.Where(whereExpression);

                    var basPendingPlanEntities = _pendingPlanRepo.Where(x => true);

                    var planDetailEntities = _plandetailsRepo.Where(x => true);

                    var stepEntities = _basStepRepo.Where(x => true);

                    var productEntities = _basProductRepo.Where(x => true);

                    var techStepEntities = _basTechnologyStepRelationRepo.Where(x => true);

                    var list = await (from a in schedulEntities
                                      join b in basPendingPlanEntities on a.PlanCode equals b.PlanCode into _1
                                      from t_1 in _1.DefaultIfEmpty()
                                      join c in planDetailEntities on a.SchedulCode equals c.SchedulCode into _2
                                      from t_2 in _2.DefaultIfEmpty()
                                      join step in stepEntities on t_2.StepCode equals step.Code into _3
                                      from t_3 in _3.DefaultIfEmpty()
                                      join product in productEntities on t_1.ProductCode equals product.Procode into _4
                                      from t_4 in _4.DefaultIfEmpty()
                                      join techStep in techStepEntities on new { t_4.TCode, StepCode = t_3.Code } equals new { techStep.TCode, techStep.StepCode }
                                      into tbl
                                      from x in tbl.DefaultIfEmpty()
                                      orderby x.Sort, t_2.SchedulDate
                                      select new
                                      {
                                          OrderNumber = t_1.OrderNumber,
                                          ProductName = t_1.ProductName,
                                          PlanCode = t_1.PlanCode,
                                          ProductCode = t_1.ProductCode,
                                          SchedulDate = t_2.SchedulDate,
                                          ScheduleStatus = a.Status,
                                          StepName = t_3.Name,
                                          StepCode = t_3.Code,
                                      }).ToListAsync();

                    if (list.Any())
                    {
                        ProductScheduleProductItem productItem = new()
                        {
                            ScheduleCode = scheduleCode,
                            ScheduleStatus = scheduleStatus,
                            ProductName = list.First().ProductName,
                        };

                        result.ScheduleItems?.Add(productItem);

                        result.OrderNumber = list.First().OrderNumber;

                        list.GroupBy(x => x.StepCode).ForEach(x =>
                        {
                            var dateList = x.Select(x => new GanttDate()
                            {
                                StepCode = x.StepCode,
                                BeginTime = x.SchedulDate,
                                EndTime = x.SchedulDate
                            });

                            result.ScheduleItems?.Last().GanttItems?.Add(new()
                            {
                                StepName = x.First().StepName,
                                StepCode = x.First().StepCode,
                                BeginTime = x.First().SchedulDate,
                                EndTime = x.Last().SchedulDate,
                                DateList = dateList?.ToList()
                            });
                        });
                    }

                    return result;
                });

            if (dto.OrderNumber.IsNotNullOrWhiteSpace())
            {
                var whereExpression = ExpressionCreator
                                                .New<PlanProductionSchedule>()
                                                .And(x => !x.IsDeleted)
                                                .And(x => x.Status == 1)
                                                .AndIf(dto.BeginTime is DateTime, x => x.PlanStart >= dto.BeginTime)
                                                .AndIf(dto.EndTime is DateTime, x => x.PlanEnd <= dto.EndTime);

                var orderEntities = _basOrderRepo.Where(x => !x.IsDeleted && x.OrderNumber == dto.OrderNumber);
                var orderProductEntities = _basOrderProductRepo.Where(x => !x.IsDeleted);
                var basPendingPlanEntities = _pendingPlanRepo.Where(x => !x.IsDeleted && x.OrderNumber == dto.OrderNumber);
                var schedulEntities = _planProductionSchedulRepo.Where(whereExpression);

                var list = await (from a in schedulEntities
                                  join b in basPendingPlanEntities on a.PlanCode equals b.PlanCode into t1
                                  from x in t1.DefaultIfEmpty()
                                  orderby x.ProductCode
                                  select new
                                  {
                                      x.Qty,
                                      ScheduleStatus = a.Status,
                                      BeginTime = a.PlanStart,
                                      EndTime = a.PlanEnd,
                                      ScheduleCode = a.SchedulCode
                                  }).ToListAsync();

                if (list.Any())
                {
                    list.Where(x => x.ScheduleCode is not null).ForEach(l =>
                    {
                        var res = SearchGanttItems(l.ScheduleCode, dto.ScheduleStatus).Result;
                        res.ScheduleItems.ForEach(x =>
                        {
                            x.GanttItems.ForEach(step =>
                            {
                                result.Add(new PlanProductionSchedulGanttNewResultDto
                                {
                                    OrderNumber = dto.OrderNumber,
                                    ScheduleCount = l.Qty.ToString(),
                                    ProductCount = "100",
                                    ProductName = x.ProductName,
                                    ScheduleCode = x.ScheduleCode,
                                    ScheduleStatus = x.ScheduleStatus,
                                    BeginTime = l.BeginTime,
                                    EndTime = l.EndTime,
                                    StepName = step.StepName,
                                    StepCode = step.StepCode,
                                    DateList = step.DateList,
                                });
                            });
                        });
                    });
                }

                return result;
            }

            return result;
        }*/

        public async Task<List<PlanProductionSchedulGanttNewResultForPlanDto>> GetGanttByPlanCodeAsync(string planCode)
        {
            var result = new List<PlanProductionSchedulGanttNewResultForPlanDto>();
            if (planCode.IsNotNullOrWhiteSpace())
            {
                var whereExpression = ExpressionCreator
                                                .New<PartSchedulePlan>()
                                                .And(x => !x.IsDeleted && x.PlanCode == planCode);
                //.AndIf(dto.BeginTime is DateTime, x => x.PlanStart >= dto.BeginTime)
                //.AndIf(dto.EndTime is DateTime, x => x.PlanEnd <= dto.EndTime);

                //2. 规则解析
                var schedulDtos = await  _spRepo.Where(whereExpression).ToListAsync();

                var scheduleRules = await _ruleRepo.Where(x => schedulDtos.Select(x => x.Code).Contains(x.ScheduleCode)).ToListAsync();
                 


                var planDto = _pendingPlanRepo.Where(x => !x.IsDeleted && x.PlanCode == planCode).FirstOrDefault();
                
                if (schedulDtos.Any())
                {
                    schedulDtos.ForEach(l =>
                    {
                        var res = GetGanttByProductNewAsync(l.Id).Result;
                        result.Add(new PlanProductionSchedulGanttNewResultForPlanDto
                        {
                            ScheduleCount = planDto.Qty.ToString(),
                            //ProductCount = l.Quantity.ToString(),
                            PlanCode = l.PlanCode,
                            OrderNumber = planDto.OrderNumber,
                            ScheduleType = scheduleRules?.FirstOrDefault(x=>x.ScheduleCode == l.Code && x.RuleType == ScheduleRuleEnum.ScheduleFun)?.RuleValue??"0",
                            ScheduleDate = l.BeginDate,
                            BeginTime = l.PlanBeginDate,
                            EndTime = l.PlanEndDate,
                            CreateTime = l.CreateTime,
                            ScheduleStatus = l.State.ToString(),
                            ScheduleCode = l.Code,
                            GanttData = res
                        });
                    });
                }


            }
            return result;
        }


        private (bool forWardSchedule, bool batchOperation, bool kittingSchedule, bool SourceRange) ScheduleRule(List<PartSchedulePlanRuleDto> Rules)
        {
            var scheduleRules = Rules.ToDictionary(r => r.RuleType, r => r.RuleValue);
            var forWardSchedule = scheduleRules.GetValueOrDefault(ScheduleRuleEnum.ScheduleFun) == ScheduleRuleScheduleFun.ForWard;
            var batchOperation = scheduleRules.GetValueOrDefault(ScheduleRuleEnum.BatchSchedule) == ScheduleRuleBatchSchedule.Batch;
            var kittingSchedule = scheduleRules.GetValueOrDefault(ScheduleRuleEnum.KittingShcdule) == ScheduleRuleKittingShcdule.Kitting;
            var SourceRange = scheduleRules.GetValueOrDefault(ScheduleRuleEnum.SourceRange) == ScheduleRuleSourceRange.No;
            return (forWardSchedule, batchOperation, kittingSchedule, SourceRange);
        }
        public async Task<List<PlanProductionSchedulGanttNewResultDto>> GetGanttByOrderProductAsync(OrderProdcutGanttDto dto)
        {
            dto.TrimStringFields();

            var result = new List<PlanProductionSchedulGanttNewResultDto>();

            Func<string?, string?, Task<PlanProductionSchedulGanttResultDto>> SearchGanttItems
                = new(async (scheduleCode, scheduleStatus) =>
                {
                    var result = new PlanProductionSchedulGanttResultDto();
                    var whereExpression = ExpressionCreator
                                                    .New<PlanProductionSchedule>()
                                                    .And(x => !x.IsDeleted)
                                                    .AndIf(scheduleCode.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.SchedulCode, scheduleCode))
                                                    .AndIf(scheduleStatus.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Status, scheduleStatus));

                    var schedulEntities = _planProductionSchedulRepo.Where(whereExpression);

                    var basPendingPlanEntities = _pendingPlanRepo.Where(x => true);

                    var planDetailEntities = _plandetailsRepo.Where(x => true);

                    var stepEntities = _basStepRepo.Where(x => true);

                    var productEntities = _basProductRepo.Where(x => true);

                    var techStepEntities = _basTechnologyStepRelationRepo.Where(x => true);

                    var list = await (from a in schedulEntities
                                      join b in basPendingPlanEntities on a.PlanCode equals b.PlanCode into _1
                                      from t_1 in _1.DefaultIfEmpty()
                                      join c in planDetailEntities on a.SchedulCode equals c.SchedulCode into _2
                                      from t_2 in _2.DefaultIfEmpty()
                                      join step in stepEntities on t_2.StepCode equals step.Code into _3
                                      from t_3 in _3.DefaultIfEmpty()
                                      join product in productEntities on t_1.ProductCode equals product.Procode into _4
                                      from t_4 in _4.DefaultIfEmpty()
                                      join techStep in techStepEntities on new { t_4.TCode, StepCode = t_3.Code } equals new { techStep.TCode, techStep.StepCode }
                                      into tbl
                                      from x in tbl.DefaultIfEmpty()
                                      orderby x.Sort, t_2.SchedulDate
                                      select new
                                      {
                                          OrderNumber = t_1.OrderNumber,
                                          ProductName = t_1.ProductName,
                                          PlanCode = t_1.PlanCode,
                                          ProductCode = t_1.ProductCode,
                                          SchedulDate = t_2.SchedulDate,
                                          ScheduleStatus = a.Status,
                                          StepName = t_3.Name,
                                          StepCode = t_3.Code,
                                      }).ToListAsync();

                    if (list.Any())
                    {
                        ProductScheduleProductItem productItem = new()
                        {
                            ScheduleCode = scheduleCode,
                            ScheduleStatus = scheduleStatus,
                            ProductName = list.First().ProductName,
                        };

                        result.ScheduleItems?.Add(productItem);

                        result.OrderNumber = list.First().OrderNumber;

                        list.GroupBy(x => x.StepCode).ForEach(x =>
                        {
                            var dateList = x.Select(x => new GanttDate()
                            {
                                StepCode = x.StepCode,
                                BeginTime = x.SchedulDate,
                                EndTime = x.SchedulDate
                            });

                            result.ScheduleItems?.Last().GanttItems?.Add(new()
                            {
                                StepName = x.First().StepName,
                                StepCode = x.First().StepCode,
                                BeginTime = x.First().SchedulDate,
                                EndTime = x.Last().SchedulDate,
                                DateList = dateList?.ToList()
                            });
                        });
                    }

                    return result;
                });

            if (dto.OrderNumber.IsNotNullOrWhiteSpace())
            {
                var whereExpression = ExpressionCreator
                                                .New<PlanProductionSchedule>()
                                                .And(x => !x.IsDeleted)
                                                .And(x => x.Status == SchedulStatusConst.Effect);
                //.AndIf(dto.BeginTime is DateTime, x => x.PlanStart >= dto.BeginTime)
                //.AndIf(dto.EndTime is DateTime, x => x.PlanEnd <= dto.EndTime);

                var orderEntities = await _basOrderRepo.Where(x => !x.IsDeleted && x.OrderNumber == dto.OrderNumber).FirstOrDefaultAsync();
                var orderProductEntities = _basOrderProductRepo.Where(x => !x.IsDeleted && x.OrderId == orderEntities.Id);
                var basPendingPlanEntities = _pendingPlanRepo.Where(x => !x.IsDeleted && x.OrderNumber == dto.OrderNumber);
                var schedulEntities = _planProductionSchedulRepo.Where(whereExpression);

                var list = await (from a in schedulEntities
                                  join b in basPendingPlanEntities on a.PlanCode equals b.PlanCode into t1
                                  from x in t1.DefaultIfEmpty()
                                  join c in orderProductEntities on x.ProductCode equals c.ProductCode into t2
                                  from y in t2.DefaultIfEmpty()
                                  where x.PlanCode != null && x.ProductCode == dto.ProductCode
                                  orderby x.ProductCode
                                  select new
                                  {
                                      x.Id,
                                      x.Qty,
                                      y.Quantity,
                                      x.PlanCode,
                                      ScheduleStatus = a.Status,
                                      BeginTime = a.PlanStart,
                                      EndTime = a.PlanEnd,
                                      ScheduleCode = a.SchedulCode
                                  }).ToListAsync();

                if (list.Any())
                {
                    list.Where(x => x.ScheduleCode is not null).ForEach(l =>
                    {
                        var res = GetGanttByProductNewAsync(l.Id).Result;
                        result.Add(new PlanProductionSchedulGanttNewResultDto
                        {
                            ScheduleCount = l.Qty.ToString(),
                            ProductCount = l.Quantity.ToString(),
                            PlanCode = l.PlanCode,
                            OrderNumber = dto.OrderNumber,
                            BeginTime = l.BeginTime,
                            EndTime = l.EndTime,
                            ScheduleStatus = l.ScheduleStatus.ToString(),
                            GanttData = res
                        });
                    });
                }

                return result;
            }
            return result;
        }

        public async Task<AppSrvResult> setScheduleStatusAsync(SchedulStatus input)
        {
            if (null == input.SchedulCode || input.SchedulCode.Count == 0)
            {
                return Problem(HttpStatusCode.BadRequest, $"请选择数据！");
            }
            var schedulEntities = await _planProductionSchedulRepo.Where(x => input.SchedulCode.Contains(x.SchedulCode), true, false).ToListAsync();
            //待生效=》已生效
            if (input.Status == SchedulStatusConst.Effect)
            {
                var calcles = await _planProductionSchedulRepo.Where(x => input.SchedulCode.Contains(x.SchedulCode) && x.Status == SchedulStatusConst.Cancel).ToListAsync();
                if (calcles.Any())
                {
                    string[] codes = calcles.AsEnumerable().Select(a => a.SchedulCode.ToString()).ToArray();
                    string codeWarning = string.Join(",", codes);
                    return Problem(HttpStatusCode.BadRequest, $"排产编码：{codeWarning} 已作废，无法生效");
                }

                if (await _planProductionSchedulRepo
                    .Where(x => x.PlanCode == schedulEntities.First().PlanCode && x.Status == SchedulStatusConst.Effect)
                    .AnyAsync())
                {
                    return Problem(HttpStatusCode.BadRequest, $"该订单计划已生效，无法重复生效计划");
                }

                //更新排产记录、计划记录的状态数据
                if (schedulEntities.Any())
                {
                    foreach (var o in schedulEntities)
                    {
                        o.Status = input.Status;
                        await _planProductionSchedulRepo.UpdateAsync(o);

                        //更新资源占用状态
                        var planSolutions = _planSolutionRepo.Where(x => x.SchedulCode == o.SchedulCode, true, false).ToList();
                        if (planSolutions.Count > 0)
                        {
                            planSolutions.ForEach(x => { x.Status = SchedulStatusConst.Effect; });
                            await _planSolutionRepo.UpdateRangeAsync(planSolutions);
                        }

                        //将该计划下的其它排产记录更新为作废状态
                        var plan = _pendingPlanRepo.Where(x => x.PlanCode == o.PlanCode, true, false).FirstOrDefault();
                        if (plan != null)
                        {
                            plan.Status = PendingPlanStatusConst.Finished;
                            await _pendingPlanRepo.UpdateAsync(plan);

                            var schdules = _planProductionSchedulRepo.Where(x => x.SchedulCode != o.SchedulCode && x.PlanCode == plan.PlanCode, true, false).ToList();
                            if (schdules.Count > 0)
                            {
                                var param = new SchedulStatus { Status = SchedulStatusConst.Cancel, SchedulCode = schdules.Where(x => x.SchedulCode != null).Select(x => x.SchedulCode).ToList() };
                                return await setScheduleStatusAsync(param);
                            }
                        }
                    }
                }
            }
            //排产作废
            else if (input.Status == SchedulStatusConst.Cancel)
            {
                var calcles = await _planProductionSchedulRepo.Where(x => input.SchedulCode.Contains(x.SchedulCode) && x.Status == SchedulStatusConst.Effect).ToListAsync();
                if (calcles.Any())
                {
                    string[] codes = calcles.AsEnumerable().Select(a => a.SchedulCode.ToString()).ToArray();
                    string codeWarning = string.Join(",", codes);
                    return Problem(HttpStatusCode.BadRequest, $"排产编码：{codeWarning} 已生效，无法作废");
                }

                //更新相关资源信息
                if (schedulEntities.Any())
                {
                    foreach (var o in schedulEntities)
                    {
                        o.Status = input.Status;
                        await _planProductionSchedulRepo.UpdateAsync(o);

                        //更新资源占用状态
                        var planSolutions = _planSolutionRepo.Where(x => x.SchedulCode == o.SchedulCode, true, false).ToList();
                        if (planSolutions.Count > 0)
                        {
                            planSolutions.ForEach(x => { x.Status = SchedulStatusConst.Cancel; });
                            await _planSolutionRepo.UpdateRangeAsync(planSolutions);
                        }
                    }
                }
            }

            return AppSrvResult();
        }

        public async Task<List<ScheduleDeviceGanttItem>> GetDeviceGanttAsync(string code)
        {
            var result = new List<ScheduleDeviceGanttItem>();
            if (code.IsNotNullOrWhiteSpace())
            {
                var sourceDevices = await _spSourceRepo.Where(x => x.ScheduleCode == code && x.SourceType == "1").ToListAsync();
                var devices = await _basDeviceRepo.Where(x => sourceDevices.Select(x=>x.SourceCode).Contains(x.Code)).ToArrayAsync();
                var steps = await _basStepRepo.Where(x => sourceDevices.Select(x => x.StepCode).Contains(x.Code)).ToArrayAsync();

                result = (from a in sourceDevices
                            join b in devices on a.SourceCode equals b.Code
                            join s in steps on a.StepCode equals s.Code
                            orderby a.StepCode, a.BeginDate
                            select new ScheduleDeviceGanttItem
                            {
                                DeviceName = b.Name,
                                StepName = s.Name,
                                BeginTime = a.BeginDate,
                                EndTime = a.EndDate
                            }).ToList();
                
            }

            return result;
        }

        public async Task<List<SchduleStepSolutionItem>> GetStepSolution(string code)
        {
            var result = new List<SchduleStepSolutionItem>();

            if (code.IsNotNullOrWhiteSpace())
            {
                var solutions = await _planStepCapacityRepo.Where(x => x.TaskCode == code && x.StepCode != null && x.SolutionName != null).ToListAsync();
                var devices = await _basDeviceRepo.Where(x => true).ToListAsync();
                var stations = await _basStationRepo.Where(x => true).ToListAsync();
                var lines = await _basLineRepo.Where(x => true).ToListAsync();
                var steps = await _basStepRepo.Where(x => true).ToListAsync();

                solutions.ForEach(x =>
                {
                    var stepName = steps.Find(s => s.Code == x.StepCode)?.Name;
                    var devName = devices.Find(s => s.Code == x.DeviceCode)?.Name;
                    var stationName = stations.Find(s => s.Code == x.StationCode)?.Name;
                    var lineName = lines.Find(s => s.Lcode == x.LineCode)?.Lname;

                    result.Add(new SchduleStepSolutionItem()
                    {
                        StepCode = x.StepCode,
                        StepName = stepName,
                        SolutionName = x.SolutionName,
                        DeviceCode = x.DeviceCode,
                        DeviceName = devName,
                        LineCode = x.LineCode,
                        LineName = lineName,
                        StationCode = x.StationCode,
                        StationName = stationName,
                        WorkUnitTime = x.WorkUnitTime,
                        StandardWorkTime = x.StandardWorkTime,
                        Capacity = x.Capacity,
                    });
                });
            }

            return result;
        }

        public async Task<AppSrvResult<long>> BatchDelAsync(List<string> codes)
        {
            if (codes?.Count == 0)
            {
                return Problem(HttpStatusCode.BadRequest, $"请选择数据！");
            }

            var schedulEntities = await _planProductionSchedulRepo.Where(x => codes.Contains(x.SchedulCode) && x.Status == SchedulStatusConst.Cancel, true, false).ToListAsync();

            if (schedulEntities.Count > 0)
            {
                schedulEntities.ForEach(x => x.IsDeleted = true);

                await _planProductionSchedulRepo.UpdateRangeAsync(schedulEntities);

                //同步删除 工位设备资源占用表
                var schDevs = await _planSolutionRepo.Where(x => codes.Contains(x.SchedulCode), true, false).ToListAsync();
                schDevs.ForEach(x => x.IsDeleted = true);
                await _planSolutionRepo.UpdateRangeAsync(schDevs);

                //同步删除 工序产能表
                var steps = await _planStepCapacityRepo.Where(x => codes.Contains(x.TaskCode), true, false).ToListAsync();
                steps.ForEach(x => x.IsDeleted = true);
                await _planStepCapacityRepo.UpdateRangeAsync(steps);

                //同步删除 排产详情表
                var details = await _planProductionSchedulStepDetailsRepo.Where(x => codes.Contains(x.SchedulCode), true, false).ToListAsync();
                details.ForEach(x => x.IsDeleted = true);
                await _planProductionSchedulStepDetailsRepo.UpdateRangeAsync(details);

                //同步删除 计划工序排产方案表
                var taskCodes = details.Select(x => x.TaskCode).Distinct().ToList();
                var tasks = await _planschedulStepSolutionRepo.Where(x => taskCodes.Contains(x.TaskCode), true, false).ToListAsync();
                tasks.ForEach(x => x.IsDeleted = true);
                await _planschedulStepSolutionRepo.UpdateRangeAsync(tasks);

            }

            return AppSrvResult();
        }

        /// <summary>
        /// 试算
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult<List<dynamic>>> RealoadSchedulYwgAsync(ReloadBasProcuct input)
        {

            var result = new List<dynamic>();

            var planDetailEntities = new List<PlanProductionSchedulStepDetails>();

            var autoCode = _basNumberManagementService.GetNumberBySimpleName(CommonConst.SCHEDULNUMBER).Result;
            //待排产数据
            var basplanPending = _basplanPendingRepo.Where(o => o.PlanCode == input.PlanCode).FirstOrDefault();
            //产品工序关系表
            var basProStepRel = _basProStepRelRepo.Where(o => o.ProCode == basplanPending!.ProductCode).ToList();

            var basProductStandardCapacitys = input.lists;
            //前置工序
            var basTechnologyStep = _basTechnologyStepRelationRepo.Where(o => true).ToList();

            var stepEntities = _basStepRepo.Where(x => true).ToList();

            var planProductionSchedul = _planProductionSchedulRepo.Where(o => o.SchedulCode == input.SchedulCode).FirstOrDefault();


            var planProductionSchedulDto = Mapper.Map<PlanProductionSchedulDto>(planProductionSchedul);


            var basPendingPlanEntities = _pendingPlanRepo.Where(x => true);

            var productEntities = _basProductRepo.Where(x => true);


            var basCapacitys = Mapper.Map<List<BasProductStandardCapacity>>(basProductStandardCapacitys);
            //排序方式
            int sort = planProductionSchedul!.Sorts;

            var capacitys = RemoveDuplicateData(basCapacitys, planProductionSchedulDto);

            DateTime maxDateTime = DateTime.MinValue;
            DateTime minDateTime = DateTime.MinValue;

            if (planProductionSchedul.PreRule.ToString().Equals(SchedulTypeConst.Prerule_2, StringComparison.OrdinalIgnoreCase))
            {
                ProductionScheduler scheduler = new ProductionScheduler();
                scheduler.TargetQuantity = (int)basplanPending!.Qty;
                scheduler.StartTime = planProductionSchedul.PlanStart!;
                scheduler.Sorts = sort;
                foreach (var item in capacitys)
                {
                    ProcessSchedul process = new ProcessSchedul(item.Name, item.ProcessingTime, item.RequiredQuantity, item.SingleProduction, item.LimitHours, item.StepName, item.SolutionName);
                    if (!string.IsNullOrEmpty(item.PreStep) && !item.PreStep.Equals("无"))
                    {
                        process.PrecedingProcesses.Add(item.PreStep);
                    }
                    scheduler.AddProcess(process);
                }

                scheduler.Schedule();

                foreach (var process in scheduler.Processes)
                {
                    int i = 0;
                    var autoCode1 = _basNumberManagementService.GetNumberBySimpleName(CommonConst.TASKRWBM).Result;
                    foreach (var executionCount in process.DailyExecutionCount)
                    {
                        if (sort == SchedulSortsConst.DESC)
                        {
                            int j = isVacationDesc(executionCount.Key);
                            i = i + j;
                        }
                        else
                        {
                            int j = isVacation(executionCount.Key);
                            i = i + j;
                        }
                        var model = new PlanProductionSchedulStepDetails
                        {
                            Id = IdGenerater.GetNextId(),
                            IsDeleted = false,
                            SchedulDate = executionCount.Key.AddDays(i),
                            StepCode = process.Name,
                            PlanCode = input.PlanCode,
                            TaskCode = autoCode1,
                            Exenum = executionCount.Value,
                            SchedulCode = autoCode
                        };
                        planDetailEntities.Add(model);

                        if (sort == SchedulSortsConst.ASC)
                        {
                            if (model.SchedulDate >= maxDateTime)
                            {
                                maxDateTime = (DateTime)model.SchedulDate;
                            }
                        }
                        else if (sort == SchedulSortsConst.DESC)
                        {
                            if (model.SchedulDate <= minDateTime)
                            {
                                minDateTime = (DateTime)model.SchedulDate;
                            }
                        }
                    }
                }
            }
            else if (planProductionSchedul.PreRule.ToString().Equals(SchedulTypeConst.Prerule_1, StringComparison.OrdinalIgnoreCase))
            {
                // 定义工序列表
                var processes = new List<ProductionProcess>();
                if (sort == SchedulSortsConst.DESC)
                {
                    capacitys = capacitys.OrderByDescending(o => o.sort).ToList();
                }

                foreach (var item in capacitys)
                {
                    ProductionProcess process = new ProductionProcess(item.Name, item.ProcessingTime, item.SingleProduction,
                         item.RequiredQuantity, item.IntervalTime, item.LimitHours, item.sort, item.StepName,
                         item.SolutionName, item.StationCode, item.StartTime);
                    if (!string.IsNullOrEmpty(item.PreStep))
                    {
                        var xxx = capacitys.Where(o => o.Name == item.PreStep).ToList();
                        foreach (var item1 in xxx)
                        {
                            ProductionProcess process1 = new ProductionProcess(item1.Name, item1.ProcessingTime, item1.SingleProduction,
                         item1.RequiredQuantity, item1.IntervalTime, item1.LimitHours, item.sort, item1.StepName,
                          item1.SolutionName, item1.StationCode, item.StartTime);

                            process.PreviousProcesses.Add(process1);
                        }

                    }

                    processes.Add(process);
                }
                // 定义产品数量
                int productQuantity = (int)basplanPending!.Qty;

                // 计算每个工序的执行日期和每天执行次数
                DateTime currentDateTime = (sort == SchedulSortsConst.DESC ? (DateTime)planProductionSchedul.PlanEnd! : planProductionSchedul.PlanStart!);

                var dayCondition = (0, 0);
                foreach (var process in processes)
                {
                    currentDateTime = process.StartTime;
                    int i = 0;
                    double workHoursPerDay = process.LimitHours;

                    if (sort == SchedulSortsConst.DESC)
                    {
                        minDateTime = currentDateTime;
                        var preProcesses = processes
                            .Where(o => o.PreviousProcesses.Count > 0
                               && o.PreviousProcesses.FirstOrDefault(x => x.Name == process.Name) != null
                               && o.Sorts > process.Sorts)
                            .Select(o => o.Name);

                        var downProcess = processes
                            .Where(o => preProcesses.Contains(o.Name))
                            .OrderBy(o => o.EndTime)
                            .FirstOrDefault();

                        if (downProcess != null)
                        {
                            dayCondition = GetLeaveDate(downProcess, process, productQuantity);
                        }
                        process.CalculateExecutionDatesDesc(productQuantity, currentDateTime, workHoursPerDay, dayCondition.Item2);
                    }
                    else
                    {
                        maxDateTime = currentDateTime;
                        if (process.PreviousProcesses != null && process.PreviousProcesses.Count > 0)
                        {
                            var solutionNames = processes
                                .Select(o => o.SolutionName)
                                .ToList();
                            dayCondition = GetLeaveDate(process.PreviousProcesses.FirstOrDefault(o => solutionNames.Contains(o.SolutionName)), process, productQuantity);
                        }
                        process.CalculateExecutionDates(productQuantity, currentDateTime, workHoursPerDay, dayCondition.Item2);
                    }

                    var autoCode1 = _basNumberManagementService.GetNumberBySimpleName(CommonConst.TASKRWBM).Result;

                    foreach (var execution in process.DailyExecutionCounts)
                    {
                        if (sort == SchedulSortsConst.DESC)
                        {
                            int j = isVacationDesc(execution.Key.AddDays(i));
                            i = i + j;
                        }
                        else
                        {
                            int j = isVacation(execution.Key.AddDays(i));
                            i = i + j;
                        }
                        var model = new PlanProductionSchedulStepDetails
                        {
                            Id = IdGenerater.GetNextId(),
                            IsDeleted = false,
                            SchedulDate = execution.Key.AddDays(i),
                            StepCode = process.Name,
                            PlanCode = input.PlanCode,
                            TaskCode = autoCode1,
                            Exenum = execution.Value,
                            SchedulCode = autoCode
                        };
                        planDetailEntities.Add(model);
                        if (sort == SchedulSortsConst.ASC)
                        {
                            if (model.SchedulDate >= maxDateTime)
                            {
                                maxDateTime = (DateTime)model.SchedulDate;
                            }
                            currentDateTime = maxDateTime;
                        }
                        else if (sort == SchedulSortsConst.DESC)
                        {
                            if (model.SchedulDate <= minDateTime)
                            {
                                minDateTime = (DateTime)model.SchedulDate;
                            }
                            currentDateTime = minDateTime;
                        }
                    }
                }

            }

            //排产计划新增数据
            var entity = new List<PlanProductionSchedule>{ new PlanProductionSchedule
            {
                LCode = string.Join(",", capacitys.Select(o => o.LineCode).Distinct().ToList()),
                PlanCode = planProductionSchedul.PlanCode,
                PlanStart = (sort == SchedulSortsConst.ASC ? (DateTime)planProductionSchedul.SchedulTime.Value : minDateTime),
                PlanEnd = (sort == SchedulSortsConst.ASC ? maxDateTime : planProductionSchedul.SchedulTime.Value),
                IsDeleted = false,
                PlanType = SchedulOperateConst.System,
                SchedulCode = autoCode,
                Sorts = planProductionSchedul.Sorts,
                PreRule = planProductionSchedul.PreRule,
                Status = SchedulStatusConst.Waiting,
                SchedulTime = (DateTime)planProductionSchedul.SchedulTime!
            } };

            var res = (from a in entity
                       join b in basPendingPlanEntities on a.PlanCode equals b.PlanCode into _1
                       from t_1 in _1.DefaultIfEmpty()

                       join c in planDetailEntities on a.SchedulCode equals c.SchedulCode into _2
                       from t_2 in _2.DefaultIfEmpty()
                       join step in stepEntities on t_2.StepCode equals step.Code into _3
                       from t_3 in _3.DefaultIfEmpty()

                       join pros in productEntities on t_1.ProductCode equals pros.Procode into ptemp
                       from products in ptemp.DefaultIfEmpty()
                       join techs in basTechnologyStep on products.TCode equals techs.TCode into tes
                       from techSteps in tes.DefaultIfEmpty()

                       orderby t_2.SchedulDate
                       select new PlanProductionSchedulData()
                       {
                           OrderNumber = basplanPending!.OrderNumber,
                           ProductName = basplanPending.ProductName,
                           PlanCode = basplanPending.PlanCode,
                           ProductCode = basplanPending.ProductCode,
                           SchedulDate = t_2.SchedulDate,
                           PlanStart = a.PlanStart,
                           PlanEnd = a.PlanEnd,
                           ScheduleStatus = a.Status,
                           StepName = t_3.Name,
                           StepCode = t_3.Code,
                           StepSort = techSteps.Sort ?? 0,
                           TecStepCode = techSteps.StepCode
                       }).Where(x => x.StepCode == x.TecStepCode).ToList();

            res.GroupBy(s => s.StepCode).ForEach(x =>
            {
                var first = x.First();
                var parent = new PlanProductionSchedulGanttChildItem
                {
                    Id = first?.StepCode,
                    ProductName = first?.ProductName,
                    StepCode = first?.StepCode,
                    StepName = first?.StepName,
                    StartTime = first?.SchedulDate,
                    EndTime = first?.SchedulDate,
                    Duration = 0,
                    StepSort = first?.StepSort ?? 0,
                    Parent = null
                };

                result.Add(parent);

                var list = x.ToList();

                for (int i = 0, k = 1; i < list.Count(); i++, k++)
                {
                    var item = new PlanProductionSchedulGanttChildItem
                    {
                        Id = $"{first?.StepCode}_{k}",
                        ProductName = list[i]?.ProductName,
                        StepName = list[i]?.StepName,
                        StepCode = list[i]?.StepCode,
                        StartTime = list[i]?.SchedulDate,
                        EndTime = list[i]?.SchedulDate,
                        Duration = 0,
                        Parent = list[0].StepCode,
                        StepSort = first?.StepSort ?? 0
                    };

                    result.Add(item);

                    for (int j = i + 1, d = 1; j <= list.Count() - 1; j++, d++)
                    {
                        if ((list[j].SchedulDate - item.StartTime).Value.Days == d)
                        {
                            i++;
                            item.Duration++;
                            item.EndTime = list[j].SchedulDate;
                            continue;
                        }
                        else
                        {
                            i = j - 1;
                            break;
                        }
                    }
                    item.Duration++;
                    //parent.Duration += item.Duration;
                }
                parent.EndTime = result.Last<dynamic>().EndTime;
                parent.Duration = (parent.EndTime - parent.StartTime)?.Days + 1;
            });
            var returnModel = new List<dynamic>();
            var gresult = result.Distinct().GroupBy(x => x.StartTime).ToList();
            foreach (var item in gresult)
            {
                var plans = item.OrderBy(x => x.StepSort);
                foreach (var s in plans)
                {
                    if (returnModel.Any(x => x.StartTime == s.StartTime && x.EndTime == s.EndTime && x.Parent == s.Parent && x.StepCode == s.StepCode))
                    {
                        continue;
                    }
                    returnModel.Add(s);
                }
            }

            return returnModel;
        }

        /// <summary>
        /// 物料预警
        /// </summary>
        /// <param name="search"></param>
        /// <returns></returns>
        public async Task<PageModelDto<MaterialWarningReturnDto>> ComputeMaterialWarning(MaterialWarningPagedDto search)
        {
            List<MaterialWarningReturnDto> groupDtos = new List<MaterialWarningReturnDto>();
            //List<MaterialWarningDto> reDtos = new List<MaterialWarningDto>();
            ////获取需要预警的物料
            //var warnIngMDtos = await _stockMRepo.Where(x => !x.IsDeleted && x.Prelockqty > x.Qty).ToListAsync();
            //if (warnIngMDtos.Any())
            //{
            //    var mCodes = warnIngMDtos.Select(x => x.Mcode);

            //    var stepMaterialEntities = await _spMatRepo.Where(x => !x.IsDeleted && mCodes.Contains(x.Mcode)).ToListAsync();



            //    if (stepMaterialEntities.Any())
            //    {
            //        var planStepDetailEntities = await _plandetailsRepo.Where(x => !x.IsDeleted
            //                                                                        // && x.SchedulDate >= search.dtBegin
            //                                                                        // && x.SchedulDate <= search.dtEnd

            //                                                                         && x.SchedulDate >= DateTime.Now.Date
            //                                                                         && x.SchedulDate <= DateTime.Now.AddDays(57).Date
            //                                                                        && stepMaterialEntities.Select(x => x.StepCode).Contains(x.StepCode))
            //                                                              .ToListAsync();


            //        var proScheduleEntities = await _planProductionSchedulRepo.Where(x => !x.IsDeleted
            //                                                                        && planStepDetailEntities.Select(x => x.SchedulCode).Contains(x.SchedulCode))
            //                                                              .ToListAsync();

            //        var planPendingEntites = await _pendingPlanRepo.Where(x => !x.IsDeleted
            //                                                                        && proScheduleEntities.Select(x => x.PlanCode).Contains(x.PlanCode))
            //                                                              .ToListAsync();

            //        reDtos = (from a in planStepDetailEntities
            //                  join mm in proScheduleEntities on a.SchedulCode equals mm.SchedulCode
            //                  join g in planPendingEntites on mm.PlanCode equals g.PlanCode
            //                  join b in stepMaterialEntities on a.StepCode equals b.StepCode
            //                  join c in warnIngMDtos on b.MaterialCode equals c.Mcode
            //                  select new MaterialWarningDto
            //                  {
            //                      OrderCode = g.OrderNumber,
            //                      PlanCode = a.PlanCode,
            //                      StepCode = a.StepCode,
            //                      Mcode = b.MaterialCode,
            //                      Mname = b.MaterialName,
            //                      Qty = c.Qty,
            //                      StepQty = b.Qty,
            //                      Schedule = a.SchedulDate
            //                  }).ToList();


            //        groupDtos = reDtos.GroupBy(p => new { p.Mcode, p.Mname })
            //                              .Select(productGroup => new MaterialWarningReturnDto
            //                              {
            //                                  Mcode = productGroup.Key.Mcode,
            //                                  Mname = productGroup.Key.Mname,
            //                                  PlanQty = productGroup?.Sum(r => r.StepQty),
            //                                  Qty = productGroup.First().Qty,
            //                                  MissQty = productGroup.First().Qty - productGroup?.Sum(r => r.StepQty),
            //                                  Orders = string.Join(",", productGroup.Select(x => x.OrderCode))
            //                              }).ToList();

            //    }
            //}

            //return new PageModelDto<MaterialWarningReturnDto>(search, groupDtos!, groupDtos.Count());

            return new PageModelDto<MaterialWarningReturnDto>(search, groupDtos, groupDtos.Count());
        }



        public async Task<PageModelDto<MaterialWarningReturnDto>> ComputeMaterialWarningNew(MaterialWarningPagedDto search)
        {
            List<MaterialWarningReturnDto> groupDtos = new List<MaterialWarningReturnDto>();
            List<MaterialWarningDto> reDtos = new List<MaterialWarningDto>();
            var warnIngMDtos = await _stockMRepo.Where(x => !x.IsDeleted).ToListAsync();

            var schedulePlan = await _spRepo.Where(x => !x.IsDeleted && x.State == SchedulStatusConst.Effect).ToListAsync();
            //获取物料需求计划
            var spMaterials = await _spMatRepo.Where(x => !x.IsDeleted 
                                                          && x.NeedDate >= DateTime.Now
                                                          && schedulePlan.Select(x => x.Code).Contains(x.ScheduleCode)).ToListAsync();


            var mCodes = (from a in warnIngMDtos
                          join b in spMaterials.GroupBy(x => x.Mcode) on a.Mcode equals b.Key
                          where a.Qty < b.Sum(x => x.Qty)
                          select a.Mcode).ToList();
            //获取需要预警的物料

            if (mCodes.Any())
            {
                //获取物料需求计划
                var stepMaterialEntities = spMaterials.Where(x => mCodes.Contains(x.Mcode)).ToList();

                if (stepMaterialEntities.Any())
                {
                    var planPendingEntites = await _pendingPlanRepo.Where(x => !x.IsDeleted
                                                                             && schedulePlan.Select(x => x.PlanCode).Contains(x.PlanCode))
                                                                   .ToListAsync();
                    var materialDtos = await _basMatRepo.Where(x => !x.IsDeleted && mCodes.Contains(x.Code)).ToListAsync();
                    reDtos = (from a in stepMaterialEntities
                              join b in schedulePlan on a.ScheduleCode equals b.Code
                              join g in planPendingEntites on b.PlanCode equals g.PlanCode
                              join c in warnIngMDtos on a.Mcode equals c.Mcode
                              join d in materialDtos on a.Mcode equals d.Code
                              select new MaterialWarningDto
                              {
                                  OrderCode = g.OrderNumber,
                                  PlanCode = b.PlanCode,
                                  StepCode = a.StepCode,
                                  Mcode = a.Mcode,
                                  Mname = d.Name,
                                  Qty = c.Qty,
                                  StepQty = a.Qty,
                                  Schedule = a.NeedDate
                              }).ToList();


                    groupDtos = reDtos.GroupBy(p => new { p.Mcode, p.Mname })
                                          .Select(productGroup => new MaterialWarningReturnDto
                                          {
                                              Mcode = productGroup.Key.Mcode,
                                              Mname = productGroup.Key.Mname,
                                              PlanQty = productGroup?.Sum(r => r.StepQty),
                                              Qty = productGroup.First().Qty,
                                              MissQty = productGroup.First().Qty - productGroup?.Sum(r => r.StepQty),
                                              Orders = string.Join(",", productGroup.Select(x => x.OrderCode))
                                          }).ToList();

                }
            }
            return new PageModelDto<MaterialWarningReturnDto>(search, groupDtos!, groupDtos.Count());
        }
        /// <summary>
        /// 物料预警图
        /// </summary>
        /// <param name="Mcode"></param>
        /// <returns></returns>
        public async Task<MaterialWarningChartDto> GetMaterialWarningChart(string Mcode)
        {
            //获取需要预警的物料
            MaterialWarningChartDto chartDto = new MaterialWarningChartDto();
            DateTime dtBegin = DateTime.Now.Date;
            var warnIngM  = await _stockMRepo.Where(x => !x.IsDeleted && x.Mcode == Mcode).FirstOrDefaultAsync();
            if (warnIngM != null)
            {
                chartDto.Mcode = warnIngM.Mcode;
                chartDto.Qty = warnIngM.Qty;

                //获取生效的排产计划
                var schedulePlan = await _spRepo.Where(x => !x.IsDeleted && x.State == SchedulStatusConst.Effect).ToListAsync();

                //获取物料需求计划
                var stepMaterialEntities = await _spMatRepo.Where(x => !x.IsDeleted 
                                                                        && x.NeedDate>=DateTime.Now
                                                                        && schedulePlan.Select(x => x.Code).Contains(x.ScheduleCode) 
                                                                        && x.Mcode == warnIngM.Mcode).ToListAsync();

                stepMaterialEntities.Add(new PartSchedulePlanMaterial
                {
                    NeedDate = DateTime.Now.AddDays(-1).Date,
                    Qty = 0
                });
                if (stepMaterialEntities.Any())
                {
                    chartDto.linepoints = CalculateDailyInventory(chartDto.Qty??0, stepMaterialEntities);


                    //var reDtos =stepMaterialEntities.GroupBy(p => new { Mcode = p.Mcode, Schedule = p.Schedule.Date })
                    //                                .Select(a=> new MaterialWarningDto
                    //          {
                    //              Mcode = a.Mcode,
                    //              StepQty = a.Qty,
                    //              Schedule = a.NeedDate
                    //          }).ToList().OrderBy(x => x.Schedule);

                    //var groupDtos = reDtos.GroupBy(p => new { Mcode = p.Mcode, Schedule = p.Schedule.Date });
                    //decimal sum = warnIngM.Qty??0;
                    //DateTime dtEnd = reDtos?.Max(x => x.Schedule) ??dtBegin;


                    //int loopNum = dtEnd.Subtract(dtBegin).Days;
                    //// 将日期范围转换为时间段列表
                    //var dates = Enumerable.Range(0, loopNum).Select(i => dtBegin.Date.AddDays(i)).ToList();
;
                }
            }

            return chartDto;
        }

        public List<MaterialWarningLineDto> CalculateDailyInventory(decimal MaterialQty,List<PartSchedulePlanMaterial> demands)
        {
            // 按天分组，并合并同一天的物料需求
            var groupedDemands = demands
                .GroupBy(d => d.NeedDate.Date)
                .Select(g => new
                {
                    Day = g.Key,
                    Qty = g.Sum(d => d.Qty)
                })
                .OrderBy(g => g.Day)
                .ToList();

            var currentInventory = new Dictionary<string, decimal>();
            var result = new List<MaterialWarningLineDto>();

            foreach (var group in groupedDemands)
            {
                var obj = new MaterialWarningLineDto();
                obj.Schedule = group.Day.ToString("MM-dd");
                MaterialQty -= group.Qty ?? 0;
                obj.LessQty = MaterialQty;
                result.Add(obj);
                
            }
            return result;
        }

    }
}
