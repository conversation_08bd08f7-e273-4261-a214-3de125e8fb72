﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Infra.Redis.Caching.Core.Interceptor;
using Adnc.Shared.Application.Contracts.ResultModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services
{
    
    public interface IStockMaterialService : IAppService
    {
        /// <summary>
        /// 获取所有物料信息
        /// </summary>
        /// <returns></returns>
        [OperateLog(LogName = "获取所有物料信息")]
        Task<ResultJson> GetAllAsync();

        /// <summary>
        /// 导入物料库存
        /// </summary>
        /// <param name="Dtos"></param>
        /// <returns></returns>
        [OperateLog(LogName = "导入物料库存")]
        Task<ResultJson> ImportAsync(List<StockMaterialDto> dtos);
        /// <summary>
        /// 
        /// </summary>
        /// <param name="search"></param>
        /// <returns></returns>
        [OperateLog(LogName = "获取分页数据")]
        Task<PageModelDto<StockMaterialDto>> GetPagedAsync(StockMaterialPagedDto search);
        /// <summary>
        /// 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>

        [OperateLog(LogName = "更新物料库存")]
        Task<AppSrvResult> UpdateAsync(StockMaterialDto input);


        /// <summary>
        /// 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>

        [OperateLog(LogName = "新增物料库存")]
        Task<AppSrvResult> CreateAsync(StockMaterialDto input);

        [OperateLog(LogName = "根据ID获取数据")]
        Task<StockMaterialDto> GetByIdAsync(long id);




    }
}
