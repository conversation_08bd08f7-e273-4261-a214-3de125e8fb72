﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Dtos
{
    public class BasNumberManagementDto : IDto
    {
        public long Id { get; set; }

        public string Name { get; set; }
        public string? SimpleName { get; set; }
        public string? CurrentNumber { get; set; }
        public string? StartNumber { get; set; }
        public string? IdentityNumber { get; set; }
        public string? IsContainerDate { get; set; }

        public string? TimeFormat { get; set; }
        public string? Description { get; set; }
        public long NumberFormat { get; set; }
        public string? StartTarget { get; set; }
        public string? IsRound { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public long? CreateBy { get; set; }

        public string? CreateName { get; set; }

        /// <summary>
        /// 创建时间/注册时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 最后更新人
        /// </summary>
        public long? ModifyBy { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 是否删除标识
        /// </summary>
        public bool? IsDeleted { get; set; }
    }

    public class BasNumberManagementPagedDto : SearchPagedDto
    {
        public string Name { get; set; }

        public string? SimpleName { get; set; }
    }
}
