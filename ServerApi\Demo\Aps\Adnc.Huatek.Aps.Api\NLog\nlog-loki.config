﻿<?xml version="1.0" encoding="utf-8" ?>

<!--internalLogLevel 记录Nlog自身日志级别，正式环境改为Error
    autoReload="true" nlog.config配置文件修改，程序将会重新读取配置文件，也就是自动再配置
-->
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	  autoReload="true"
      internalLogLevel="Error"
      internalLogFile="${basedir}/internal-nlog.txt">

	<extensions>
		<add assembly="NLog.Loki" />
	</extensions>

	<!-- Loki target is async, so there is no need to wrap it in an async target wrapper. -->
	<!--https://nlog-project.org/config/?tab=layout-renderers-->
	<!--https://grafana.com/docs/loki/latest/logql/metric_queries/-->
	<!--https://github.com/corentinaltepe/nlog.loki-->
	<!--https://www.cnblogs.com/turingguo/p/13847003.html [collect docker logs ]-->
	<!--https://www.cnblogs.com/Sunzz/p/15190702.html [collect pod logs]-->
	<targets>
		<target
		  name="loki"
		  xsi:type="loki"
		  batchSize="200"
		  taskDelayMilliseconds="500"
		  endpoint="${configsetting:item=Loki.Endpoint}"
		  orderWrites="true"
		  compressionLevel="noCompression"
		  layout="${sequenceid}|${level}|${message}${onexception:|${exception:format=type,message,method:maxInnerExceptionLevel=5:innerFormat=shortType,message,method}}|source=${logger}|env=${aspnet-environment}|requestid=${aspnet-TraceIdentifier}">
			<!-- Loki requires at least one label associated with the log stream. Make sure you specify at least one label here. -->
			<label name="instanceid" layout="${local-ip}"/>
			<label name="app" layout="${configsetting:item=ServiceName}" />
			<label name="server" layout="${hostname:lowercase=true}" />
		</target>
	</targets>

	<rules>
		<logger name="Adnc.*" minlevel="${configsetting:item=Logging.LogLevel.Adnc}" writeTo="loki" />
		<logger name="Microsoft.*" minlevel="${configsetting:item=Logging.LogLevel.Microsoft}" writeTo="loki" />
		<logger name="*" minlevel="${configsetting:item=Logging.LogLevel.Default}" writeTo="loki"  final="true"/>
	</rules>

</nlog>