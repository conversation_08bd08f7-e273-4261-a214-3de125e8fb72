[{"ContainingType": "Adnc.Demo.Maint.Api.Controllers.DictController", "Method": "GetAsync", "RelativePath": "getbyid/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Demo.Maint.Application.Dtos.DictDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Demo.Maint.Api.Controllers.DictController", "Method": "GetDetail", "RelativePath": "getDetail", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Demo.Maint.Application.Dtos.DictSearchDetailDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Demo.Maint.Application.Dtos.DictDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Demo.Maint.Api.Controllers.CfgController", "Method": "CreateAsync", "RelativePath": "maint/api/cfgs", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Demo.Maint.Application.Dtos.CfgCreationDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 201}]}, {"ContainingType": "Adnc.Demo.Maint.Api.Controllers.CfgController", "Method": "UpdateAsync", "RelativePath": "maint/api/cfgs/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "input", "Type": "Adnc.Demo.Maint.Application.Dtos.CfgUpdationDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}]}, {"ContainingType": "Adnc.Demo.Maint.Api.Controllers.CfgController", "Method": "DeleteAsync", "RelativePath": "maint/api/cfgs/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}]}, {"ContainingType": "Adnc.Demo.Maint.Api.Controllers.CfgController", "Method": "GetAsync", "RelativePath": "maint/api/cfgs/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Demo.Maint.Application.Dtos.CfgDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "Adnc.Demo.Maint.Api.Controllers.CfgController", "Method": "GetPagedAsync", "RelativePath": "maint/api/cfgs/page", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "Value", "Type": "System.String", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.PageModelDto`1[[Adnc.Demo.Maint.Application.Dtos.CfgDto, Adnc.Demo.Maint.Application, Version=0.9.9.4, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Demo.Maint.Api.Controllers.DictController", "Method": "CreateAsync", "RelativePath": "maint/api/dicts/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Demo.Maint.Application.Dtos.DictCreationDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 201}]}, {"ContainingType": "Adnc.Demo.Maint.Api.Controllers.DictController", "Method": "DeleteAsync", "RelativePath": "maint/api/dicts/delete/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}]}, {"ContainingType": "Adnc.Demo.Maint.Api.Controllers.DictController", "Method": "UpdateAsync", "RelativePath": "maint/api/dicts/edit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": false}, {"Name": "input", "Type": "Adnc.Demo.Maint.Application.Dtos.DictUpdationDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}]}, {"ContainingType": "Adnc.Demo.Maint.Api.Controllers.DictController", "Method": "GetByCodeAsync", "RelativePath": "maint/api/dicts/getbyname/{name}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "name", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Adnc.Demo.Maint.Application.Dtos.EnumDto, Adnc.Demo.Maint.Application, Version=0.9.9.4, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Demo.Maint.Api.Controllers.DictController", "Method": "GetChildByCodeAsync", "RelativePath": "maint/api/dicts/gettreebyname/{name}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "name", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Demo.Maint.Application.Dtos.EnumTreeDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Demo.Maint.Api.Controllers.DictController", "Method": "GetListAsync", "RelativePath": "maint/api/dicts/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "Adnc.Demo.Maint.Application.Dtos.DictSearchDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.PageModelDto`1[[Adnc.Demo.Maint.Application.Dtos.DictDto, Adnc.Demo.Maint.Application, Version=0.9.9.4, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Demo.Maint.WebApi.Controllers.LogController", "Method": "GetLoginLogsPagedAsync", "RelativePath": "maint/api/loginlogs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "BeginTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Account", "Type": "System.String", "IsRequired": false}, {"Name": "Method", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Level", "Type": "System.String", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.PageModelDto`1[[Adnc.Demo.Maint.Application.Dtos.LoginLogDto, Adnc.Demo.Maint.Application, Version=0.9.9.4, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Demo.Maint.WebApi.Controllers.LogController", "Method": "GetNlogLogsPagedAsync", "RelativePath": "maint/api/nloglogs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "BeginTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Account", "Type": "System.String", "IsRequired": false}, {"Name": "Method", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Level", "Type": "System.String", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.PageModelDto`1[[Adnc.Demo.Maint.Application.Dtos.NlogLogDto, Adnc.Demo.Maint.Application, Version=0.9.9.4, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Demo.Maint.WebApi.Controllers.NoticeController", "Method": "GetList", "RelativePath": "maint/api/notices", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Title", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Adnc.Demo.Maint.Application.Dtos.NoticeDto, Adnc.Demo.Maint.Application, Version=0.9.9.4, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Demo.Maint.WebApi.Controllers.LogController", "Method": "GetOpsLogsPaged", "RelativePath": "maint/api/opslogs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "BeginTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Account", "Type": "System.String", "IsRequired": false}, {"Name": "Method", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Level", "Type": "System.String", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.PageModelDto`1[[Adnc.Demo.Maint.Application.Dtos.OpsLogDto, Adnc.Demo.Maint.Application, Version=0.9.9.4, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Demo.Maint.WebApi.Controllers.LogController", "Method": "GetUserOpsLogsPagedAsync", "RelativePath": "maint/api/users/opslogs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "BeginTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Account", "Type": "System.String", "IsRequired": false}, {"Name": "Method", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Level", "Type": "System.String", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.PageModelDto`1[[Adnc.Demo.Maint.Application.Dtos.OpsLogDto, Adnc.Demo.Maint.Application, Version=0.9.9.4, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]