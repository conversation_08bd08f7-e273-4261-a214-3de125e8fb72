﻿using Adnc.Infra.Entities;

namespace Adnc.Huatek.Aps.Domain.Aggregates.BasClassesAggregate
{
    public class BasClasses : EfFullAuditEntity
    {
        /// <summary>
        /// ID
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
        /// <summary>
        /// 简称
        /// </summary>
        public string? ShortName { get; set; }

        /// <summary>
        /// 班次名称
        /// </summary>
        public string? Cname { get; set; }

        /// <summary>
        /// 班次编码
        /// </summary>
        public string? Ccode { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public string? BeginTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public string? EndTime { get; set; }
        /// <summary>
        /// 启用状态
        /// </summary>
        public int? Status { get; set; }

        public string? Color { get; set; }






    }

   
}
