﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStepAggregate;
using Adnc.Shared.Application.Contracts.ResultModels;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services
{
    public interface IBasOrderService : IAppService
    {
        [OperateLog(LogName = "获取订单列表信息")]
        Task<PageModelDto<BasOrderDto>> GetPagedAsync(BasOrderPagedDto search);
        [OperateLog(LogName = "获取所有订单信息")]
        Task<ResultJson> GetAllAsync(BasOrderPagedDto search);
        [OperateLog(LogName = "创建订单")]
        Task<AppSrvResult<long>> CreateAsync(BasOrderDto input);
        [OperateLog(LogName = "修改订单")]
        Task<AppSrvResult> UpdateAsync(BasOrderDto input);
        [OperateLog(LogName = "删除订单")]
        Task<AppSrvResult> DeleteAsync(long id);

        [OperateLog(LogName = "根据ID获取订单")]
        Task<ResultJson> GetByIdAsync(long id);

        [OperateLog(LogName = "根据ID获取订单明细")]
        Task<PartPlanPendingDto> GetDetailsByIdAsync(long id);

        [OperateLog(LogName = "修改订单")]
        Task<AppSrvResult> FinishedAsync(FinishedOrders input);

        [OperateLog(LogName = "导出订单")]
        MemoryStream ExportOrders(BasOrderPagedDto search);
        [OperateLog(LogName = "导入订单")]
        Task<ResultJson> ImportOrdersAsync(List<ImportOrderDto> orders);

        [OperateLog(LogName = "根据ID获取订单详情")]
        Task<ActionResult<OrderDetailDto>> GetOrderDetailAsync(long id);
    }
}
