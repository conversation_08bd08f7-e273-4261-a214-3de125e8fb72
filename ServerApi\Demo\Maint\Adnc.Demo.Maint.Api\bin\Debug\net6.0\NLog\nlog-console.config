﻿<?xml version="1.0" encoding="utf-8" ?>

<!--internalLogLevel 记录Nlog自身日志级别，正式环境改为Error
    autoReload="true" nlog.config配置文件修改，程序将会重新读取配置文件，也就是自动再配置
-->
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	  autoReload="true"
      internalLogLevel="Error"
      internalLogFile="${basedir}/internal-nlog.txt">

	<targets>
		<target xsi:type="AsyncWrapper" name="console">
			<target 
				  xsi:type="Console"
                  layout="${sequenceid}|${level}|${message}${onexception:|${exception:format=type,message,method:maxInnerExceptionLevel=5:innerFormat=shortType,message,method}}|source=${logger}|env=${aspnet-environment}|requestid=${aspnet-TraceIdentifier}">
				<label name="instanceid" layout="${local-ip}"/>
				<label name="app" layout="${configsetting:item=ServiceName}" />
				<label name="server" layout="${hostname:lowercase=true}" />
			</target>
		</target>
	</targets>

	<rules>
		<logger name="Adnc.*" minlevel="${configsetting:item=Logging.LogLevel.Adnc}" writeTo="console" />
		<logger name="Microsoft.*" minlevel="${configsetting:item=Logging.LogLevel.Microsoft}" writeTo="console" />
		<logger name="*" minlevel="${configsetting:item=Logging.LogLevel.Default}" writeTo="console" final="true"/>
	</rules>
</nlog>