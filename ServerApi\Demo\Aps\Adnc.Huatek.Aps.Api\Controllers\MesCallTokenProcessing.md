# MesCall Token Processing Implementation

## 概述

本文档描述了在 `OuterSystemController.MesCallFunction` 方法中实现的token处理功能，该功能实现了以下流程：

1. Java调用mescall接口，传递header中的token
2. 根据token生成Redis key
3. 从Redis获取用户信息
4. 将用户信息赋值给UserContext对象
5. 根据UserContext生成Authorization header
6. 添加到request.Headers中

## 实现详情

### 1. Header获取

在原有的header获取基础上，新增了token的获取：

```csharp
headers.TryGetValue("token", out var token);
```

### 2. Redis Key生成

根据传入的token生成Redis key，格式为：

```csharp
var redisKey = $"online-token-{tokenValue}";
```

示例：
- 输入token: `eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ7XCJncm91cElkXCI6XCIxOTQzNjE3MzEwOTk2Njk3MDg4XCIsXCJpZFwiOlwiZjUyYjFmOTMyOWNiNGNkNjhkMmZjNjYwZDljMmFhYzVcIixcInVzZXJOYW1lXCI6XCJhZG1pblwiLFwidXNlckNvZGVcIjpcImFkbWluXCJ9IiwianRpIjoiNzljYjcyNDktOWVlNy00YmNlLWI3MzktZjNmNzVlOGI5MTIxIn0.WT7oDfsHMGqhjkLX8xhM503Bc1_1zjngiQPTNuaHJ0dV7pntm22uXOrEyrufR_rMcGGKvsdVv9JC2ylvw13FQQ`
- 生成的Redis key: `online-token-eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ7XCJncm91cElkXCI6XCIxOTQzNjE3MzEwOTk2Njk3MDg4XCIsXCJpZFwiOlwiZjUyYjFmOTMyOWNiNGNkNjhkMmZjNjYwZDljMmFhYzVcIixcInVzZXJOYW1lXCI6XCJhZG1pblwiLFwidXNlckNvZGVcIjpcImFkbWluXCJ9IiwianRpIjoiNzljYjcyNDktOWVlNy00YmNlLWI3MzktZjNmNzVlOGI5MTIxIn0.WT7oDfsHMGqhjkLX8xhM503Bc1_1zjngiQPTNuaHJ0dV7pntm22uXOrEyrufR_rMcGGKvsdVv9JC2ylvw13FQQ`

### 3. Redis数据获取

从Redis获取用户信息，期望的数据格式：

```json
{
  "admin": false,
  "authorities": [],
  "comment": "131",
  "createTime": 1566334602000,
  "deleted": "0",
  "groupId": "1943617310996697088",
  "id": "f52b1f9329cb4cd68d2fc660d9c2aac5",
  "password": "9FC7E2FC757073DA914C2DC762A2D096",
  "passwordSalt": "b7016a1f88864747bbbb359527cc879b",
  "userCode": "admin",
  "userEmail": "<EMAIL>",
  "userMobile": "***********",
  "userName": "admin",
  "userPhone": "029-********"
}
```

### 4. UserContext对象创建

将Redis中的用户信息映射到UserContext对象：

```csharp
userContext = new UserContext
{
    Id = GetJsonPropertyAsLong(userInfo, "id"),
    Account = GetJsonPropertyAsString(userInfo, "userCode"),
    Name = GetJsonPropertyAsString(userInfo, "userName"),
    Email = GetJsonPropertyAsString(userInfo, "userEmail"),
    RoleIds = "", // 根据实际需要设置
    RemoteIpAddress = Request.HttpContext.Connection.RemoteIpAddress?.ToString() ?? ""
};
```

### 5. JWT Token生成

参考AccountController的实现方式生成JWT token：

```csharp
// 生成ValidationVersion作为jti（参考UserValidatedInfoDto的构造函数）
var validationVersion = Guid.NewGuid().ToString("N");

// 使用与AccountController相同的方式创建AccessToken
var accessToken = JwtTokenHelper.CreateAccessToken(
    jwtOptions,
    validationVersion,  // 使用validationVersion作为jti，而不是随机生成
    userContext.Account,
    userContext.Id.ToString(),
    userContext.Name,
    userContext.RoleIds,
    JwtBearerDefaults.Manager
);
authorizationHeader = $"Bearer {accessToken.Token}";
```

**改进说明**：
- 使用`Guid.NewGuid().ToString("N")`生成validationVersion作为jti，与AccountController保持一致
- 这样生成的token格式与系统标准登录流程完全一致

生成的Authorization header格式示例：
```
Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI0OGZiOGU3NzBlMjg0YWE5OThlMjU5NTUwNDFhOGE3YyIsInVuaXF1ZV9uYW1lIjoiYWxwaGEyMDA4IiwibmFtZWlkIjoiMTYwMDAwMDAwMDAwMCIsIm5hbWUiOiLkvZnlsI_njKsiLCJyb2xlaWRzIjoiMTYwMDAwMDAwMDAxMCIsImxvZ2luZXIiOiJtYW5hZ2VyIiwibmJmIjoxNzUzOTQzODQxLCJleHAiOjE3NTQzMDM4NDEsImlzcyI6ImFkbmMiLCJhdWQiOiJtYW5hZ2VyIn0.Qo7U3elz0M6V7zPgx8G97p8FwwBSyjq3kfCG-EWIWTc
```

### 6. Header转发

在请求转发时，优先使用生成的Authorization header：

```csharp
if (!string.IsNullOrEmpty(authorizationHeader))
{
    // 优先使用我们生成的Authorization header
    request.Headers.TryAddWithoutValidation("Authorization", authorizationHeader);
}
else if (Request.Headers.TryGetValue("Authorization", out var authHeader))
{
    // 如果没有生成新的，则使用原始的Authorization header
    var authValue = authHeader.FirstOrDefault();
    if (!string.IsNullOrEmpty(authValue))
    {
        request.Headers.TryAddWithoutValidation("Authorization", authValue);
    }
}
```

## 辅助方法

### GetJsonPropertyAsString
安全地从JsonElement中获取字符串属性值，如果属性不存在则返回空字符串。

### GetJsonPropertyAsLong
安全地从JsonElement中获取长整型属性值，支持字符串和数字类型的转换，如果属性不存在或转换失败则返回0。

## 错误处理

- 如果token为空或无效，不会中断流程，会继续使用原有的Authorization header
- 如果Redis中没有找到对应的用户信息，不会中断流程
- 如果JWT配置不可用，不会中断流程
- 所有异常都会被捕获，不会影响原有的业务流程

## 依赖注入

需要在构造函数中注入以下服务：
- `ICacheProvider _cacheProvider` - 用于Redis操作
- `IOptions<JWTOptions>` - 通过HttpContext.RequestServices获取JWT配置

## 使用示例

客户端调用时需要在header中包含token：

```http
POST /aps/outerSystem/mescall
Content-Type: application/json
appUserKey: your_app_user_key
timestamp: **********
sign: your_sign
nonce: your_nonce
token: eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ7XCJncm91cElkXCI6XCIxOTQzNjE3MzEwOTk2Njk3MDg4XCIsXCJpZFwiOlwiZjUyYjFmOTMyOWNiNGNkNjhkMmZjNjYwZDljMmFhYzVcIixcInVzZXJOYW1lXCI6XCJhZG1pblwiLFwidXNlckNvZGVcIjpcImFkbWluXCJ9IiwianRpIjoiNzljYjcyNDktOWVlNy00YmNlLWI3MzktZjNmNzVlOGI5MTIxIn0.WT7oDfsHMGqhjkLX8xhM503Bc1_1zjngiQPTNuaHJ0dV7pntm22uXOrEyrufR_rMcGGKvsdVv9JC2ylvw13FQQ

{
  "serviceUrl": "/api/your-endpoint",
  "param": "{\"key\":\"value\"}",
  "httpMethod": "POST"
}
```

系统会自动处理token，生成相应的Authorization header并转发给目标服务。
