﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasBomAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasBomListAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductAggregate;
using Adnc.Infra.Helper;
using Adnc.Shared.Rpc.Event;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using static StackExchange.Redis.Role;

namespace Adnc.Huatek.Aps.Domain.Services
{
    public class BasBomManagement : IDomainService
    {
        private readonly IEfBasicRepository<BasBom> _basBomRepo;
        private readonly IEfBasicRepository<BasProduct> _basProductRepo;

        public BasBomManagement(
            IEfBasicRepository<BasBom> basBomRepo,
            IEfBasicRepository<BasProduct> basProductRepo
            )
        {
            _basBomRepo = basBomRepo;
            _basProductRepo = basProductRepo;
        }


        public virtual async Task<BasBom> CreateAsync(string productCode, List<BasBomList> materialList)
        {

            var exists = await _basBomRepo.AnyAsync(x => x.ProductCode == productCode && !x.IsDeleted);
            if (exists)
            {
                var product = await _basProductRepo.Where(x=>x.Procode== productCode).FirstOrDefaultAsync();
                if (product != null)
                {
                    throw new BusinessException(HttpStatusCode.BadRequest, $"产品：{product.Proname} Bom已存在，不可重复添加！");
                }
                throw new BusinessException(HttpStatusCode.BadRequest, $"产品：{productCode} Bom已存在，不可重复添加！");
            }
            
            return new BasBom()
            {
                Id = IdGenerater.GetNextId(),
                ProductCode = productCode
            };
        }

        public virtual async Task<bool> UpdateAsync(string productCode, List<BasBomList> materialList, long id)
        {
            var exists = await _basBomRepo.AnyAsync(x => x.ProductCode == productCode && x.Id != id && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"该产品{productCode}Bom已存在，不可重复添加！");

            foreach (var item in materialList)
            {
                exists = await _basBomRepo.AnyAsync(x => x.ProductCode == productCode && x.MaterialCode == item.MaterialCode && !x.IsDeleted);
                if (exists)
                    throw new BusinessException($"该产品下物料编码{item.MaterialCode}已存在，不可重复添加！");
            }

            return true;
        }
    }
}
