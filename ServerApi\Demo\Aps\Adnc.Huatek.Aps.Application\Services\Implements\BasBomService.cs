﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStation;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStepAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasBomAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasBomListAggregate;
using Adnc.Huatek.Aps.Domain.Services;
using Adnc.Infra.IdGenerater.Yitter;
using Adnc.Shared.Application.Contracts.ResultModels;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Adnc.Huatek.Aps.Domain.Aggregates.BasMaterial;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.SysUserAggregate;
using System.Net;
using Adnc.Infra.Core.Exceptions;

namespace Adnc.Huatek.Aps.Application.Services.Implements
{
    public class BasBomService : AbstractAppService, IBasBomService
    {
        private readonly BasBomManagement _basBomMgr;
        private readonly IEfBasicRepository<BasBom> _basBomRepo;
        private readonly IEfBasicRepository<BasProduct> _basProductRepo;
        private readonly IEfBasicRepository<BasMaterial> _basMaterialRepo;
        private readonly IEfBasicRepository<BasBomList> _basBomListRepo;
        private readonly IEfBasicRepository<SysUser> _sysUserRepo;

        public BasBomService(
            IEfBasicRepository<BasBom> basBomRepo,
            IEfBasicRepository<BasProduct> basProductRepo,
            IEfBasicRepository<BasMaterial> basMaterialRepo,
             IEfBasicRepository<BasBomList> basBomListRepo,
              IEfBasicRepository<SysUser> sysUserRepo,
            BasBomManagement basBomMgr)
        {
            _basBomMgr = basBomMgr;
            _basBomRepo = basBomRepo;
            _basProductRepo = basProductRepo;
            _basMaterialRepo = basMaterialRepo;
            _basBomListRepo = basBomListRepo;
            _sysUserRepo = sysUserRepo;
        }
        /// <summary>
        /// 获取分页列表
        /// </summary>
        /// <param name="search"></param>
        /// <returns></returns>
        public async Task<PageModelDto<BasBomDto>> GetPagedAsync(BomPagedDto search)
        {
            search.TrimStringFields();
            var whereExpression = ExpressionCreator
                                                .New<BasBom>()
                                                .And(x => !x.IsDeleted)
                                                .AndIf(search.ProductCode.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.ProductCode!, $"%{search.ProductCode}%"));

            //var total = await _basBomRepo.CountAsync(whereExpression);
           // if (total == 0)
             //   return new PageModelDto<BasBomDto>(search);

            var entities = _basBomRepo.Where(whereExpression)
                                           .OrderByDescending(x => x.CreateTime);

            var whereP = ExpressionCreator
                                                .New<BasProduct>()
                                                .And(x => !x.IsDeleted)
                                                .AndIf(search.ProductName.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Proname!, $"%{search.ProductName}%"));
            var products = _basProductRepo.Where(whereP);
            var users = _sysUserRepo.Where(x => !x.IsDeleted);
            var bomDtos = (from i in entities
                           join j in products on i.ProductCode equals j.Procode
                           join u in users on i.CreateBy equals u.Id
                           select new BasBomDto
                           {
                               ProductCode = j.Procode ?? String.Empty,
                               ProductName = j.Proname ?? String.Empty,
                               //Id = i.Id,
                               CreatName = u.Name,
                               CreateTime = i.CreateTime,
                               Remark = i.Remark,
                           }).Distinct().ToList();
            //拼接物料名称
            var productCodes = bomDtos.Select(x => x.ProductCode)
                                           .Skip(((search.PageIndex - 1) * search.PageSize))
                                           .Take(search.PageSize).ToList();
            var materials = _basMaterialRepo.Where(x => true);
            var boms = _basBomRepo.Where(x => !x.IsDeleted);
            var bmaterials = (
                from b in boms
                join m in materials on b.MaterialCode equals m.Code
                select new
                {
                    ProductCode = b.ProductCode,
                    MaterialName = m.Name
                }
                ).Where(x => productCodes.Contains(x.ProductCode)).ToList();
            bomDtos.ForEach(x =>
            {
                x.MaterialName = String.Join(",", bmaterials.Where(p => p.ProductCode == x.ProductCode).Select(x => x.MaterialName).ToList());
            });
            return new PageModelDto<BasBomDto>(search, bomDtos, bomDtos.Count());
        }


        /// <summary>
        /// 根据ID获取BOM数据
        /// </summary>
        /// <returns></returns>

        public async Task<BasBomDto> GetByIdAsync(string productcode)
        {
            var bomDto = new BasBomList();

            var whereExpression = ExpressionCreator
                                           .New<BasBom>()
                                           .And(x => !x.IsDeleted)
                                           .AndIf(productcode.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.ProductCode, $"%{productcode}%"));

            var boms = _basBomRepo.Where(whereExpression);
            var result = (from b in boms
                          select new BasBomDto
                          {
                              Id = b.Id,
                              ProductCode = b.ProductCode,
                              Remark = b.Remark
                          }).FirstOrDefault();
            return result;
        }
        /// <summary>
        /// 根据productCode获取物料明细
        /// </summary>
        /// <param name="productCode"></param>
        /// <returns></returns>
        public async Task<List<BasBomListDto>> GetMaterialByProductCodeAsync(string productCode)
        {
            List<BasBomListDto> bomDto = new List<BasBomListDto>();

            var whereExpression = ExpressionCreator
                                           .New<BasBom>()
                                           .And(x => !x.IsDeleted)
                                           .AndIf(productCode.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.ProductCode, $"%{productCode}%"));

            var boms = _basBomRepo.Where(whereExpression);
            var materials = _basMaterialRepo.Where(x => true);

            var result = (from b in boms
                          join m in materials on b.MaterialCode equals m.Code
                          select new BasBomListDto
                          {
                              Id = b.Id,
                              IdBom = b.Id,
                              MaterialCode = b.MaterialCode,
                              MaterialName = m.Name ?? String.Empty,
                              Qty = b.Qty
                          }).ToList();
            return result;

        }



        /// <summary>
        /// 创建Bom
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult<string>> CreateAsync(BasBomDto input)
        {

            //input.TrimStringFields();
            //var mList = new List<BasBomList>();
            //foreach (var m in input.Items)
            //{
            //    var material = new BasBomList()
            //    {
            //        MaterialCode = m.MaterialCode,
            //        Qty = m.Qty
            //    };
            //    mList.Add(material);
            //}
            //try
            //{
            //    var boms = await _basBomMgr.CreateAsync(input.ProductCode, mList);
            //}
            //catch (BusinessException ex)
            //{
            //    return Problem(HttpStatusCode.BadRequest, detail:ex.Message);
            //}

            //var items = new List<BasBom>();
            //foreach (var item in input.Items)
            //{
            //    var bom = new BasBom()
            //    {
            //        Id = IdGenerater.GetNextId(),
            //        ProductCode = input.ProductCode,
            //        MaterialCode = item.MaterialCode,
            //        Remark = input.Remark,
            //        Qty = item.Qty
            //    };
            //    items.Add(bom);
            //}
            //await _basBomRepo.InsertRangeAsync(items);
            return input.ProductCode;
        }


        /// <summary>
        /// 更新Bom
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult> UpdateAsync(BasBomDto input)
        {
            //var mList = new List<BasBomList>();
            //foreach (var m in input.Items)
            //{
            //    var material = new BasBomList()
            //    {
            //        MaterialCode = m.MaterialCode,
            //        Qty = m.Qty
            //    };
            //    mList.Add(material);
            //}
            //// bool isUpdete = await _basBomMgr.UpdateAsync(input.ProductCode, mList, input.Id ?? 0);

            //var relations = await _basBomRepo.Where(x => !x.IsDeleted && x.ProductCode == input.ProductCode, false, false).ToListAsync();
            //await _basBomRepo.RemoveRangeAsync(relations);

            //var items = new List<BasBom>();
            //foreach (var item in input.Items)
            //{
            //    var bom = new BasBom()
            //    {
            //        Id = IdGenerater.GetNextId(),
            //        ProductCode = input.ProductCode,
            //        MaterialCode = item.MaterialCode,
            //        Remark = input.Remark,
            //        Qty = item.Qty
            //    };
            //    items.Add(bom);
            //}
            //await _basBomRepo.InsertRangeAsync(items);
            return AppSrvResult();
        }

        /// <summary>
        /// 删除Bom
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult> DeleteAsync(string productcode)
        {
            var boms = _basBomRepo.Where(x => x.ProductCode == productcode && !x.IsDeleted, noTracking: false).ToList();
            if (boms.Any())
            {
                foreach (var b in boms)
                {
                    b.IsDeleted = true;
                    await _basBomRepo.UpdateAsync(b);
                }
            }
            return AppSrvResult();


        }
    }
}
