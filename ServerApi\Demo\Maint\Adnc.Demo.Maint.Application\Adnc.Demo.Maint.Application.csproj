﻿<Project Sdk="Microsoft.NET.Sdk">
	<Import Project="..\..\..\common.props" />
	<Import Project="..\..\..\version_service.props" />
	<PropertyGroup>
		<GenerateDocumentationFile>True</GenerateDocumentationFile>
	</PropertyGroup>
	<ItemGroup>
		<ProjectReference Include="..\..\Shared\Adnc.Demo.Shared.Rpc.Grpc\Adnc.Demo.Shared.Rpc.Grpc.csproj" />
		<ProjectReference Include="..\..\Shared\Adnc.Demo.Shared.Rpc.Http\Adnc.Demo.Shared.Rpc.Http.csproj" />
		<ProjectReference Include="..\Adnc.Demo.Maint.Repository\Adnc.Demo.Maint.Repository.csproj" />
	</ItemGroup>

	<ItemGroup Condition="'$(SolutionName)'=='Adnc'">
		<ProjectReference Include="..\..\..\ServiceShared\Adnc.Shared.Application\Adnc.Shared.Application.csproj" />
	</ItemGroup>

	<ItemGroup Condition="'$(SolutionName)'=='Adnc.Demo' ">
		<PackageReference Include="Adnc.Shared.Application" Version="$(Shared_Version)" />
	</ItemGroup>
</Project>