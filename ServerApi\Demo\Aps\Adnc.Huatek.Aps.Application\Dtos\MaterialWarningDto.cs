﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Dtos
{
    public class MaterialWarningDto
    {
        public string? OrderCode { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string? PlanCode { get; set; }
        /// <summary>
        /// 物料编码
        /// </summary>
        public string? StepCode { get; set; }

        /// <summary>
        /// 物料数量
        /// </summary>
        public string? Mcode { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? Mname { get; set; }

        public decimal? Qty { get; set; }

        public decimal? StepQty { get; set; }

        public DateTime Schedule { get; set; }



    }

    public class MaterialWarningChartDto
    {
        public string? Mcode { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public decimal? Qty { get; set; }
        /// <summary>
        /// 剩余QTY
        /// </summary>
        public List<MaterialWarningLineDto>? linepoints { get; set; }
    }

    public class MaterialWarningLineDto
    {
        public string? Schedule { get; set; }
        /// <summary>
        /// 剩余QTY
        /// </summary>
        public decimal? LessQty { get; set; }
    }

    public class MaterialWarningReturnDto
    {

        /// <summary>
        /// 物料数量
        /// </summary>
        public string? Mcode { get; set; }

        public string? Mname { get; set; }

        public decimal? Qty { get; set; }

        public decimal? PlanQty { get; set; }

        public decimal? MissQty { get; set; }

        public string? Orders { get; set; }



    }

    public class MaterialWarningPagedDto : SearchPagedDto
    {
        /// <summary>
        /// 产品编码
        /// </summary>
        public string? Mcode { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string? Mname { get; set; }

        /// <summary>
        /// 产品编码
        /// </summary>
        public DateTime? dtBegin { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public DateTime? dtEnd { get; set; }
    }


}
