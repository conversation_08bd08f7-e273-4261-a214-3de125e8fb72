﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.BasNumberManagementAggregate;
using Adnc.Shared.Application.Contracts.ResultModels;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services
{
    public interface IBasNumberManagementService : IAppService
    {

        [OperateLog(LogName ="查询编码信息列表")]
        Task<ActionResult<PageModelDto<BasNumberManagementDto>>> GetPagedAsync(BasNumberManagementPagedDto search);

        [OperateLog(LogName = "创建编码信息")]
        Task<AppSrvResult<long>> CreateAsync(BasNumberManagementDto input);

        [OperateLog(LogName = "根据id删除编码")]
        Task<AppSrvResult> DeleteAsync(long id);

        [OperateLog(LogName = "根据id查询编码信息")]
        Task<ActionResult<BasNumberManagement>> GetByIdAsync(long id);

        [OperateLog(LogName = "更新编码信息")]
        Task<AppSrvResult> UpdateAsync(BasNumberManagementDto input);

        [OperateLog(LogName = "根据 名称简写 获取最新编号")]
        [UnitOfWork]
        Task<string> GetNumberBySimpleName(string simplename);
    }
}
