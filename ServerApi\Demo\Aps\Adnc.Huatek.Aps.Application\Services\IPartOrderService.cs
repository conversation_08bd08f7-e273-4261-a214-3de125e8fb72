﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.BasTechnologyAggregate;
using Adnc.Shared.Application.Contracts.ResultModels;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services
{
    public interface IPartOrderService : IAppService
    {
        [OperateLog(LogName = "获取生产订单列表信息")]
        Task<PageModelDto<PartOrderDto>> GetPagedAsync(PartOrderPagedDto search);
        [OperateLog(LogName = "创建生产订单")]
        [UnitOfWork]
        Task<AppSrvResult<long>> CreateAsync(PartOrderDto input);
        [OperateLog(LogName = "修改生产订单")]
        [UnitOfWork]
        Task<AppSrvResult> UpdateAsync(PartOrderDto input);
        [OperateLog(LogName = "删除生产订单")]
        [UnitOfWork]
        Task<AppSrvResult> DeleteAsync(long id);
        [OperateLog(LogName = "根据ID获取生产订单数据")]
        Task<PartOrderDto> GetByIdAsync(long id);
    }
}
