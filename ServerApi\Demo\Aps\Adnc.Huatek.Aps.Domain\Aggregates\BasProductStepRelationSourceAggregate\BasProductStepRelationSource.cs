﻿using Adnc.Infra.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Aggregates.BasProductStepRelationDeviceAggregate
{
    public class BasProductStepRelationSource : EfFullAuditEntity
    {
        /// <summary>
        /// ID
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// ID
        /// </summary>
        public long? MainId { get; set; }

        /// <summary>
        /// 资源大类
        /// </summary>
        public string? SourceType { get; set; }

        /// <summary>
        /// 资源小类
        /// </summary>
        public string? DeviceType { get; set; }

        /// <summary>
        /// 单位工作时长
        /// </summary>
        public decimal? Tat { get; set; }

        /// <summary>
        /// 单位产能
        /// </summary>
        public decimal? Capacity { get; set; }


        /// <summary>
        /// 是否考虑占用
        /// </summary>
        public bool? MainSource { get; set; }


        /// <summary>
        /// 产能约束
        /// </summary>
        public bool? IsCapacity { get; set; }
    }


}
