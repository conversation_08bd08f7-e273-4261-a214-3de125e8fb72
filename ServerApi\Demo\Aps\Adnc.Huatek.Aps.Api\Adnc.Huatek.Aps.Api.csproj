﻿<Project Sdk="Microsoft.NET.Sdk.Web">
	<Import Project="..\..\..\common.props" />
	<Import Project="..\..\..\version_service.props" />
  <PropertyGroup>
	  <GenerateDocumentationFile>true</GenerateDocumentationFile>
	  <Description>aps业务中心</Description>
	  <ServerGarbageCollection>false</ServerGarbageCollection>
	  <ConcurrentGarbageCollection>true</ConcurrentGarbageCollection>
	  <UserSecretsId>a6e495e1-1584-4388-be15-5be5294574e4</UserSecretsId>
  </PropertyGroup>
	<ItemGroup>
		<None Include="..\..\Shared\resources\**\*">
			<Link>%(RecursiveDir)/%(FileName)%(Extension)</Link>
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>
  <ItemGroup>
	  <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="$(Microsoft_EntityFrameworkCore_Design_Version)">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	  </PackageReference>
	  <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="$(Microsoft_EntityFrameworkCore_Tools_Version)">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	  </PackageReference>
	  <PackageReference Include="Npoi.Mapper" Version="6.2.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\ServiceShared\Adnc.Shared.WebApi\Adnc.Shared.WebApi.csproj" />
    <ProjectReference Include="..\Adnc.Huatek.Aps.Application\Adnc.Huatek.Aps.Application.csproj" />
    <ProjectReference Include="..\Adnc.Huatek.Aps.Domain\Adnc.Huatek.Aps.Domain.csproj" />
  </ItemGroup>

	<ItemGroup Condition="'$(SolutionName)'=='Adnc.Demo' ">
		<PackageReference Include="Adnc.Shared.WebApi" Version="$(Shared_Version)" />
	</ItemGroup>

	<ItemGroup>
	  <None Update="swagger_miniprofiler.html">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	  <None Update="Template\订单导入模板.xlsx">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </None>
	</ItemGroup>

	<ProjectExtensions><VisualStudio><UserProperties appsettings_1json__JsonSchema="" /></VisualStudio></ProjectExtensions>
</Project>
