﻿<?xml version="1.0" encoding="utf-8" ?>
<!--internalLogLevel 记录Nlog自身日志级别，正式环境改为Error
    autoReload="true" nlog.config配置文件修改，程序将会重新读取配置文件，也就是自动再配置
-->
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
      internalLogLevel="Error"
      internalLogFile="${basedir}/internal-nlog.txt">
	<!-- 启用.net core的核心布局渲染器 -->
	<extensions>
		<add assembly="NLog.Web.AspNetCore" />
		<add assembly="NLog.Mongo" />
	</extensions>
	<!-- 写入日志的目标配置 -->
	<targets>
		<![CDATA[
		<!-- 文件形式记录调试日志  -->
		<target xsi:type="File" name="debug" fileName="logs/debug-${shortdate}.log" layout="${longdate}|${event-properties:item=EventId_Id}|${uppercase:${level}}|${logger}|${message} ${exception:format=tostring}|url: ${aspnet-request-url}|action: ${aspnet-mvc-action}" />
		<!-- 文件形式记录警告日志  -->
		<target xsi:type="File" name="warn" fileName="logs/warn-${shortdate}.log" layout="${longdate}|${event-properties:item=EventId_Id}|${uppercase:${level}}|${logger}|${message} ${exception:format=tostring}|url: ${aspnet-request-url}|action: ${aspnet-mvc-action}" />
		<!-- 文件形式记录错误日志  -->
		<target xsi:type="File" name="error" fileName="logs/error-${shortdate}.log" layout="${longdate}|${event-properties:item=EventId_Id}|${uppercase:${level}}|${logger}|${message} ${exception:format=tostring}|url: ${aspnet-request-url}|action: ${aspnet-mvc-action}" />
		 ]]>
		<!--MongoDb形式记录日志 authSource 验证用户alpha所在的db,nlog默认是验证db是admin-->
		<target xsi:type="Mongo" name="mongoCustom"
			includeDefaults="true"
			connectionString="${configsetting:item=MongoDb.ConnectionString}"
			collectionName="LoggerLog"
			cappedCollectionSize="26214400">
			<field name="Date" layout="${date}" bsonType="DateTime" />
			<field name="Level" layout="${level}" />
			<field name="Message" layout="${message}" />
			<field name="Logger" layout="${logger}" />
			<field name="Exception" layout="${exception:format=tostring}" />
			<field name="ThreadID" layout="${threadid}" bsonType="Int32" />
			<field name="ThreadName" layout="${threadname}" />
			<field name="ProcessID" layout="${processid}" bsonType="Int32" />
			<field name="ProcessName" layout="${processname:fullName=true}" />
			<property name="RemoteIpAddress" layout="${aspnet-request-ip}" />
			<property name="BaseDir" layout="${aspnet-appbasepath}" />
			<property name="QueryUrl" layout="${aspnet-request-url}" />
			<property name="RequestMethod" layout="${aspnet-request-method}" />
			<property name="Controller" layout="${aspnet-mvc-controller}" />
			<property name="Method" layout="${aspnet-mvc-action}" />
			<property name="FormContent" layout="${aspnet-request-form}" />
			<property name="QueryContent" layout="${aspnet-request-querystring}" />
		</target>
	</targets>
	<!-- 映射规则 -->
	<rules>
		<![CDATA[
		<!-- 调试  -->
		<logger name="*" minlevel="Trace" maxlevel="Debug" writeTo="debug" />
		<!--跳过不重要的微软日志-->
		<logger name="Microsoft.*" maxlevel="Info" final="true" />
		<!-- 警告  -->
		<logger name="*" minlevel="Info" maxlevel="Warn" writeTo="warn" />
		<!-- 错误  -->
		<logger name="*" minlevel="Error" maxlevel="Fatal" writeTo="error" />
		 ]]>
		<!-- MongoDb记录日志  -->
		<![CDATA[
		<!--跳过不重要的微软日志 -->
		<logger name="Microsoft.*" maxlevel="Debug" final="true" />
		<logger name="*" minlevel="Debug" maxlevel="Fatal" writeTo="mongoCustom" />
		 ]]>
		<logger name="Adnc.*" minlevel="${configsetting:item=Logging.LogLevel.Adnc}" writeTo="mongoCustom" />
		<logger name="Microsoft.*" minlevel="${configsetting:item=Logging.LogLevel.Microsoft}" writeTo="mongoCustom" />
		<logger name="*" minlevel="${configsetting:item=Logging.LogLevel.Default}" writeTo="mongoCustom"  final="true"/>
	</rules>
</nlog>