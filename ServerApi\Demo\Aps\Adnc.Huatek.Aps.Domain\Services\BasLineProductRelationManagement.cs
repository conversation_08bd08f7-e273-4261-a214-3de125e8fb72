﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasLineProductRelationAggregate;
using Adnc.Infra.Helper;
using Adnc.Shared.Rpc.Event;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using static StackExchange.Redis.Role;

namespace Adnc.Huatek.Aps.Domain.Services
{
    public class BasLineProductRelationManagement : IDomainService
    {
        private readonly IEfBasicRepository<BasLineProductRelation> _basLineProRepo;

        public BasLineProductRelationManagement(IEfBasicRepository<BasLineProductRelation> basLineProRepo) 
        {
            _basLineProRepo = basLineProRepo;
        }

    }
}
