﻿using Adnc.Infra.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Aggregates.BasProductAggregate
{
    public class BasProduct : EfFullAuditEntity
    {
        /// <summary>
        /// ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 产品编码
        /// </summary>
        public string? Procode { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string? Proname { get; set; }

        /// <summary>
        /// 工艺编码
        /// </summary>
        public string? TCode { get; set; }

        /// <summary>
        /// 启用状态
        /// </summary>
        public int? Status { get; set; }





    }

   
}
