using Adnc.Demo.Shared.Const;
using Adnc.Huatek.Aps.Api.Util;
using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Shared;
using Microsoft.AspNetCore.Authorization;
using System.Text;
using System.Text.Json;
using Adnc.Infra.Redis.Caching;
using Adnc.Shared.WebApi.Authentication.JwtBearer;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Microsoft.Extensions.Options;
using Adnc.Shared.WebApi.Authentication;

[Route($"{RouteConsts.ApsRoot}/outerSystem")]
[ApiController]
public class OuterSystemController : AdncControllerBase
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IConfiguration _configuration;
    private readonly ICacheProvider _cacheProvider;

    public OuterSystemController(
        IConfiguration configuration,
        IHttpClientFactory httpClientFactory,
        ICacheProvider cacheProvider)
    {
        _httpClientFactory = httpClientFactory;
        _configuration = configuration;
        _cacheProvider = cacheProvider;
    }

    [AllowAnonymous]
    [HttpPost("mescall")]
    public async Task<ActionResult<object>> MesCallFunction(InputParamDto dto)
    {
        #region 获取 header
        var headers = Request.Headers;

        headers.TryGetValue("appUserKey", out var appUserKey);
        headers.TryGetValue("timestamp", out var timestamp);
        headers.TryGetValue("sign", out var sign);
        headers.TryGetValue("nonce", out var nonce);
        headers.TryGetValue("token", out var token);

        if (!appUserKey.Any() || !timestamp.Any() || !sign.Any() || !nonce.Any())
        {
            return BadRequest("非法请求。");
        }
        var hDto = new HeaderDto
        {
            appUserKey = appUserKey.FirstOrDefault(),
            timestamp = timestamp.FirstOrDefault(),
            sign = sign.FirstOrDefault(),
            nonce = nonce.FirstOrDefault()
        };
        #endregion

        #region 处理token和用户上下文
        UserContext userContext = null;
        string authorizationHeader = null;

        if (token.Any())
        {
            var tokenValue = token.FirstOrDefault();
            if (!string.IsNullOrEmpty(tokenValue))
            {
                try
                {
                    // 1. 根据token生成Redis key
                    var redisKey = $"online-token-{tokenValue}";

                    // 2. 从Redis获取用户信息
                    var userInfoCache = await _cacheProvider.GetAsync<string>(redisKey);
                    if (userInfoCache.HasValue && !string.IsNullOrEmpty(userInfoCache.Value))
                    {
                        // 3. 解析用户信息并创建UserContext
                        var userInfo = JsonSerializer.Deserialize<JsonElement>(userInfoCache.Value);

                        userContext = new UserContext
                        {
                            Id = GetJsonPropertyAsLong(userInfo, "id"),
                            Account = GetJsonPropertyAsString(userInfo, "userCode"),
                            Name = GetJsonPropertyAsString(userInfo, "userName"),
                            Email = GetJsonPropertyAsString(userInfo, "userEmail"),
                            RoleIds = "", // 根据实际需要设置
                            RemoteIpAddress = Request.HttpContext.Connection.RemoteIpAddress?.ToString() ?? ""
                        };

                        // 4. 根据UserContext生成Authorization header
                        var jti = JwtTokenHelper.GenerateJti();
                        var jwtOptions = HttpContext.RequestServices.GetService<IOptions<JWTOptions>>()?.Value;
                        if (jwtOptions != null)
                        {
                            var accessToken = JwtTokenHelper.CreateAccessToken(
                                jwtOptions,
                                jti,
                                userContext.Account,
                                userContext.Id.ToString(),
                                userContext.Name,
                                userContext.RoleIds,
                                JwtBearerDefaults.Manager
                            );
                            authorizationHeader = $"Bearer {accessToken.Token}";
                        }
                    }
                }
                catch (Exception ex)
                {
                    // 记录日志但不中断流程
                    // 可以根据需要决定是否返回错误
                }
            }
        }
        #endregion

        #region 校验身份
        string YYYYMMDDHH = DateTime.Now.ToString("yyyyMMddHH");
        //是管理员 并且时间戳是 年月日小时就跳过
        if (!(OuterSystem.OUTER_SYSTEM_ADMIN_SIGN.Equals(sign) && YYYYMMDDHH.Equals(timestamp)))
        {
            if (!await DateUtils.TimestampToDate(timestamp))
            {
                return BadRequest("非法请求。");
            }

            long requestInterval = DateTimeOffset.Now.ToUnixTimeMilliseconds() - Convert.ToInt64(timestamp);
            //如果请求时间间隔超过30秒，则认为请求过期
            if (requestInterval > 30 * 1000)
            {
                return BadRequest("非法请求。");
            }

            var _appUserKey = _configuration["OuterSystem:AppUserKey"] ?? "";
            if (!_appUserKey.Equals(appUserKey))
            {
                return BadRequest("非法请求。" );
            }

            string signString = appUserKey + nonce + timestamp;
            string signature = MD5Util.MD5Encode(signString);
            if (!signature.Equals(sign))
            {
                return BadRequest("非法请求。");
            }
        }
        #endregion

        #region 调用业务接口
        try
        {
            if (string.IsNullOrEmpty(dto.ServiceUrl))
            {
                return BadRequest("ServiceUrl不能为空");
            }

            // 构建目标URL
            var baseUrl = _configuration["ServiceAddress:BaseUrl"] ?? OuterSystem.BASEURL;
            var targetPath = dto.ServiceUrl.TrimStart('/');
            var fullUrl = $"{baseUrl.TrimEnd('/')}/{targetPath}";

            // 创建HTTP请求
            using var client = _httpClientFactory.CreateClient();

            // 根据方法名获取对应的 HttpMethod 对象
            var httpMethod = dto.HttpMethod?.ToUpper() switch
            {
                "GET" => HttpMethod.Get,
                "PUT" => HttpMethod.Put,
                "DELETE" => HttpMethod.Delete,
                "PATCH" => HttpMethod.Patch,
                _ => HttpMethod.Post // 默认使用 POST
            };

            var request = new HttpRequestMessage(httpMethod, fullUrl);

            // 转发原始请求的Headers
            foreach (var header in Request.Headers)
            {
                if (!header.Key.Equals("Host", StringComparison.OrdinalIgnoreCase) &&
                    !header.Key.Equals("Content-Length", StringComparison.OrdinalIgnoreCase) &&
                    !header.Key.Equals("Authorization", StringComparison.OrdinalIgnoreCase)) // 排除Authorization
                {
                    request.Headers.TryAddWithoutValidation(header.Key, header.Value.ToArray());
                }
            }

            // 6. 添加Authorization header到请求中
            if (!string.IsNullOrEmpty(authorizationHeader))
            {
                // 优先使用我们生成的Authorization header
                request.Headers.TryAddWithoutValidation("Authorization", authorizationHeader);
            }
            else if (Request.Headers.TryGetValue("Authorization", out var authHeader))
            {
                // 如果没有生成新的，则使用原始的Authorization header
                var authValue = authHeader.FirstOrDefault();
                if (!string.IsNullOrEmpty(authValue))
                {
                    request.Headers.TryAddWithoutValidation("Authorization", authValue);
                }
            }

            // 设置请求内容 (仅对POST, PUT, PATCH方法)
            if (!string.IsNullOrEmpty(dto.Param) &&
                (httpMethod == HttpMethod.Post || httpMethod == HttpMethod.Put || httpMethod == HttpMethod.Patch))
            {
                request.Content = new StringContent(
                    dto.Param,
                    Encoding.UTF8,
                    "application/json"
                );
            }
            else if (!string.IsNullOrEmpty(dto.Param) && httpMethod == HttpMethod.Get)
            {
                // 对于GET请求，将参数添加到URL查询字符串
                var uriBuilder = new UriBuilder(fullUrl);
                var query = System.Web.HttpUtility.ParseQueryString(uriBuilder.Query);
                try
                {
                    var paramDict = JsonSerializer.Deserialize<Dictionary<string, string>>(dto.Param);
                    if (paramDict != null)
                    {
                        foreach (var pair in paramDict)
                        {
                            query[pair.Key] = pair.Value;
                        }
                    }
                }
                catch
                {
                    // 如果解析JSON失败，直接将param作为查询字符串附加
                    query["param"] = dto.Param;
                }
                uriBuilder.Query = query.ToString();
                request.RequestUri = uriBuilder.Uri;
            }

            // 发送请求并获取响应
            var response = await client.SendAsync(request);

            // 读取响应内容
            var content = await response.Content.ReadAsStringAsync();

            // 如果响应成功，返回实际内容
            if (response.IsSuccessStatusCode)
            {
                // 尝试解析JSON响应
                try
                {
                    var jsonResult = JsonSerializer.Deserialize<object>(content);
                    return Ok(jsonResult);
                }
                catch
                {
                    // 如果不是JSON，返回原始内容
                    return Ok(content);
                }
            }

            // 处理错误响应
            return StatusCode((int)response.StatusCode, content);
        }
        catch (Exception ex)
        {
            return BadRequest($"处理请求时发生错误: {ex.Message}");
        }

        #endregion
    }

    /// <summary>
    /// 安全地从JsonElement中获取字符串属性值
    /// </summary>
    public static string GetJsonPropertyAsString(JsonElement element, string propertyName)
    {
        if (element.TryGetProperty(propertyName, out var property))
        {
            return property.GetString() ?? "";
        }
        return "";
    }

    /// <summary>
    /// 安全地从JsonElement中获取长整型属性值
    /// </summary>
    public static long GetJsonPropertyAsLong(JsonElement element, string propertyName)
    {
        if (element.TryGetProperty(propertyName, out var property))
        {
            if (property.ValueKind == JsonValueKind.String)
            {
                var stringValue = property.GetString();
                return stringValue?.ToLong() ?? 0;
            }
            else if (property.ValueKind == JsonValueKind.Number)
            {
                return property.GetInt64();
            }
        }
        return 0;
    }
}
