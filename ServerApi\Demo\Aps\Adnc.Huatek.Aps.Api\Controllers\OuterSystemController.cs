using Adnc.Demo.Shared.Const;
using Adnc.Huatek.Aps.Api.Util;
using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Shared;
using Microsoft.AspNetCore.Authorization;
using System.Text;
using System.Text.Json;

[Route($"{RouteConsts.ApsRoot}/outerSystem")]
[ApiController]
public class OuterSystemController : AdncControllerBase
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IConfiguration _configuration;

    public OuterSystemController(
        IConfiguration configuration,
        IHttpClientFactory httpClientFactory)
    {
        _httpClientFactory = httpClientFactory;
        _configuration = configuration;
    }

    [AllowAnonymous]
    [HttpPost("mescall")]
    public async Task<ActionResult<object>> MesCallFunction(InputParamDto dto)
    {
        #region 获取 header
        var headers = Request.Headers;

        headers.TryGetValue("appUserKey", out var appUserKey);
        headers.TryGetValue("timestamp", out var timestamp);
        headers.TryGetValue("sign", out var sign);
        headers.TryGetValue("nonce", out var nonce);

        if (!appUserKey.Any() || !timestamp.Any() || !sign.Any() || !nonce.Any())
        {
            return BadRequest("非法请求。");
        }
        var hDto = new HeaderDto
        {
            appUserKey = appUserKey.FirstOrDefault(),
            timestamp = timestamp.FirstOrDefault(),
            sign = sign.FirstOrDefault(),
            nonce = nonce.FirstOrDefault()
        };
        #endregion

        #region 校验身份
        string YYYYMMDDHH = DateTime.Now.ToString("yyyyMMddHH");
        //是管理员 并且时间戳是 年月日小时就跳过
        if (!(OuterSystem.OUTER_SYSTEM_ADMIN_SIGN.Equals(sign) && YYYYMMDDHH.Equals(timestamp)))
        {
            if (!await DateUtils.TimestampToDate(timestamp))
            {
                return BadRequest("非法请求。");
            }

            long requestInterval = DateTimeOffset.Now.ToUnixTimeMilliseconds() - Convert.ToInt64(timestamp);
            //如果请求时间间隔超过30秒，则认为请求过期
            if (requestInterval > 30 * 1000)
            {
                return BadRequest("非法请求。");
            }

            var _appUserKey = _configuration["OuterSystem:AppUserKey"] ?? "";
            if (!_appUserKey.Equals(appUserKey))
            {
                return BadRequest("非法请求。" );
            }

            string signString = appUserKey + nonce + timestamp;
            string signature = MD5Util.MD5Encode(signString);
            if (!signature.Equals(sign))
            {
                return BadRequest("非法请求。");
            }
        }
        #endregion

        #region 调用业务接口
        try
        {
            if (string.IsNullOrEmpty(dto.ServiceUrl))
            {
                return BadRequest("ServiceUrl不能为空");
            }

            // 构建目标URL
            var baseUrl = _configuration["ServiceAddress:BaseUrl"] ?? OuterSystem.BASEURL;
            var targetPath = dto.ServiceUrl.TrimStart('/');
            var fullUrl = $"{baseUrl.TrimEnd('/')}/{targetPath}";

            // 创建HTTP请求
            using var client = _httpClientFactory.CreateClient();

            // 根据方法名获取对应的 HttpMethod 对象
            var httpMethod = dto.HttpMethod?.ToUpper() switch
            {
                "GET" => HttpMethod.Get,
                "PUT" => HttpMethod.Put,
                "DELETE" => HttpMethod.Delete,
                "PATCH" => HttpMethod.Patch,
                _ => HttpMethod.Post // 默认使用 POST
            };

            var request = new HttpRequestMessage(httpMethod, fullUrl);

            // 转发原始请求的Headers
            foreach (var header in Request.Headers)
            {
                if (!header.Key.Equals("Host", StringComparison.OrdinalIgnoreCase) &&
                    !header.Key.Equals("Content-Length", StringComparison.OrdinalIgnoreCase) &&
                    !header.Key.Equals("Authorization", StringComparison.OrdinalIgnoreCase)) // 排除Authorization
                {
                    request.Headers.TryAddWithoutValidation(header.Key, header.Value.ToArray());
                }
            }

            // 单独处理 Authorization header，确保格式正确
            if (Request.Headers.TryGetValue("Authorization", out var authHeader))
            {
                var authValue = authHeader.FirstOrDefault();
                if (!string.IsNullOrEmpty(authValue))
                {
                    request.Headers.TryAddWithoutValidation("Authorization", authValue);
                }
            }

            // 设置请求内容 (仅对POST, PUT, PATCH方法)
            if (!string.IsNullOrEmpty(dto.Param) &&
                (httpMethod == HttpMethod.Post || httpMethod == HttpMethod.Put || httpMethod == HttpMethod.Patch))
            {
                request.Content = new StringContent(
                    dto.Param,
                    Encoding.UTF8,
                    "application/json"
                );
            }
            else if (!string.IsNullOrEmpty(dto.Param) && httpMethod == HttpMethod.Get)
            {
                // 对于GET请求，将参数添加到URL查询字符串
                var uriBuilder = new UriBuilder(fullUrl);
                var query = System.Web.HttpUtility.ParseQueryString(uriBuilder.Query);
                try
                {
                    var paramDict = JsonSerializer.Deserialize<Dictionary<string, string>>(dto.Param);
                    if (paramDict != null)
                    {
                        foreach (var pair in paramDict)
                        {
                            query[pair.Key] = pair.Value;
                        }
                    }
                }
                catch
                {
                    // 如果解析JSON失败，直接将param作为查询字符串附加
                    query["param"] = dto.Param;
                }
                uriBuilder.Query = query.ToString();
                request.RequestUri = uriBuilder.Uri;
            }

            // 发送请求并获取响应
            var response = await client.SendAsync(request);

            // 读取响应内容
            var content = await response.Content.ReadAsStringAsync();

            // 如果响应成功，返回实际内容
            if (response.IsSuccessStatusCode)
            {
                // 尝试解析JSON响应
                try
                {
                    var jsonResult = JsonSerializer.Deserialize<object>(content);
                    return Ok(jsonResult);
                }
                catch
                {
                    // 如果不是JSON，返回原始内容
                    return Ok(content);
                }
            }

            // 处理错误响应
            return StatusCode((int)response.StatusCode, content);
        }
        catch (Exception ex)
        {
            return BadRequest($"处理请求时发生错误: {ex.Message}");
        }

        #endregion
    }
}
