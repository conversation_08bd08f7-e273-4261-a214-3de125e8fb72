﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStepAggregate;
using Adnc.Shared.Application.Contracts.ResultModels;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services
{
    public interface IPartPlanPendingService : IAppService
    {
        [OperateLog(LogName = "获取待排产计划列表信息")]
        Task<PageModelDto<PartPlanPendingResultDto>> GetPagedAsync(PartPlanPendingPagedDto search);
        [OperateLog(LogName = "获取所有待排产计划信息")]
        Task<ResultJson> GetAllAsync();

        [OperateLog(LogName = "创建待排产计划")]
        [UnitOfWork]
        Task<AppSrvResult<long>> CreateAsync(PartPlanPendingDto input);

        [OperateLog(LogName = "修改待排产计划")]
        Task<AppSrvResult> UpdateAsync(PartPlanPendingDto input);
        [OperateLog(LogName = "删除待排产计划")]
        Task<AppSrvResult> DeleteAsync(long id);

        [OperateLog(LogName = "根据ID获取待排产计划")]
        Task<ResultJson> GetByIdAsync(long id);

        [OperateLog(LogName = "根据ID获取待排产计划New")]
        Task<ResultJson> GetPlanPendingByIdAsync(long id);
    }
}
