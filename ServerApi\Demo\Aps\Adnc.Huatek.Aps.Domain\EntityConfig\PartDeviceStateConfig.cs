﻿using Adnc.Huatek.Aps.Domain.Aggregates.PartDeviceStateAggregates;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class PartDeviceStateConfig : AbstractEntityTypeConfiguration<PartDeviceState>
    {

        public override void Configure(EntityTypeBuilder<PartDeviceState> builder)
        {
            base.Configure(builder);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.Status).HasColumnName("status");
            builder.Property(x => x.DevCode).HasColumnName("devcode");
            builder.Property(x => x.DevName).HasColumnName("devname");
            builder.Property(x => x.Remark).HasColumnName("Remark");
            builder.Property(x => x.CreateBy).HasColumnName("createdby");
            builder.Property(x => x.CreateTime).HasColumnName("createdtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
            builder.Property(x => x.BeginTime).HasColumnName("beginTime");
            builder.Property(x => x.EndTime).HasColumnName("endTime");
        }
    }
}
