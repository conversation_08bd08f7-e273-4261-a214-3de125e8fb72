﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStation;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStepAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasTechnologyAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasTechnologyStepRelationAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.SysUserAggregate;
using Adnc.Huatek.Aps.Domain.Services;
using Adnc.Infra.IdGenerater.Yitter;
using Adnc.Shared;
using Adnc.Shared.Application.Contracts.ResultModels;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services.Implements
{
    public class BasTechnologyService : AbstractAppService, IBasTechnologyService
    {
        private readonly BasTechnologyManagement _basTechnologyMgr;
        private readonly IEfBasicRepository<BasTechnology> _basTechnologyRepo;
        private readonly IEfBasicRepository<BasStep> _basStepRepo;
        private readonly IEfBasicRepository<BasProduct> _basProductRepo;
        private readonly IEfBasicRepository<SysUser> _sysUserRepo;
        private readonly IEfBasicRepository<BasTechnologyStepRelation> _basTechnologyStepRelationRepo;
        private readonly BasNumberManagementService _basNumberManagementService;

        public BasTechnologyService(
            IEfBasicRepository<BasTechnology> basTechnologyRepo,
            IEfBasicRepository<BasProduct> basProductRepo,
            IEfBasicRepository<BasTechnologyStepRelation> basTechnologyStepRelationRepo,
            IEfBasicRepository<SysUser> sysUserRepo,
            IEfBasicRepository<BasStep> basStepRepo,
            BasNumberManagementService basNumberManagementService,
            BasTechnologyManagement basTechnologyMgr)
        {
            _basTechnologyMgr = basTechnologyMgr;
            _basTechnologyRepo = basTechnologyRepo;
            _basProductRepo = basProductRepo;
            _basStepRepo = basStepRepo;
            _sysUserRepo = sysUserRepo;
            _basTechnologyStepRelationRepo = basTechnologyStepRelationRepo;
            _basNumberManagementService = basNumberManagementService;
        }
        /// <summary>
        /// 获取分页列表
        /// </summary>
        /// <param name="search"></param>
        /// <returns></returns>
        public async Task<PageModelDto<BasTechnologyDto>> GetPagedAsync(TechnologyPagedDto search)
        {
            search.TrimStringFields();
            var whereExpression = ExpressionCreator
                                                .New<BasTechnology>()
                                                .And(x => !x.IsDeleted)
                                                .AndIf(search.Tcode.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Tcode!, $"%{search.Tcode}%"))
                                                .AndIf(search.Tname.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Tname!, $"%{search.Tname}%"))
                                                .AndIf(search.Status != 999, x => EF.Functions.Like(x.Status!, $"%{search.Status}%"));

            var total = await _basTechnologyRepo.CountAsync(whereExpression);
            if (total == 0)
                return new PageModelDto<BasTechnologyDto>(search);

            var entities = await _basTechnologyRepo
                                            .Where(whereExpression)
                                            .OrderByDescending(x => x.CreateTime)
                                            .Skip(search.SkipRows())
                                            .Take(search.PageSize)
                                            .ToListAsync();

            var users = await _sysUserRepo.Where(x => true).ToListAsync();

            var stepDtos = Mapper.Map<List<BasTechnologyDto>>(entities);
            stepDtos.ForEach(x =>
            {
                var um = users.Find(u => u.Id == x.ModifyBy);
                x.ModifyName = um?.Name;
                var uc = users.Find(u => u.Id == x.CreateBy);
                x.CreatName = uc?.Name;
            });

            return new PageModelDto<BasTechnologyDto>(search, stepDtos, total);
        }
        /// <summary>
        /// 获取所有数据
        /// </summary>
        /// <returns></returns>

        public async Task<List<BasTechnologyDto>> GetAllAsync()
        {
            var BasTechnologys = await _basTechnologyRepo.Where(x => !x.IsDeleted && x.Status == 1).ToListAsync();

            var BasTechnologysDto = Mapper.Map<List<BasTechnologyDto>>(BasTechnologys);

            return BasTechnologysDto;
        }


        /// <summary>
        /// 根据ID获取工艺数据
        /// </summary>
        /// <returns></returns>

        public async Task<BasTechnologyDto> GetByIdAsync(long id)
        {
            var BasTechnology = await _basTechnologyRepo.GetAsync(id);

            var BasTechnologyDto = Mapper.Map<BasTechnologyDto>(BasTechnology);

            var relations = await _basTechnologyStepRelationRepo.Where(x => !x.IsDeleted && x.IdTechnology == id).ToListAsync();
            BasTechnologyDto.Items = Mapper.Map<List<BasTechnologyStepRelationDto>>(relations);

            var steps = await _basStepRepo.Where(x => !x.IsDeleted && relations.Select(p => p.IdStep).Contains(x.Id)).ToListAsync();

            BasTechnologyDto.Items.ForEach(x =>
            {
                x.StepName = steps.FirstOrDefault(p => p.Id == x.IdStep).Name ?? "";
                x.StepCode = steps.FirstOrDefault(p => p.Id == x.IdStep).Code ?? "";
                x.StepTime = steps.FirstOrDefault(p => p.Id == x.IdStep).StepTime ?? 0;
                x.PreStepName = steps?.FirstOrDefault(p => p.Code == x.PreStep)?.Name ?? "";
            });
            return BasTechnologyDto;
        }

        /// <summary>
        /// 根据ID获取工艺数据
        /// </summary>
        /// <returns></returns>

        public async Task<BasTechnologyDto> GetByTCodeAsync(string tCode)
        {
            var BasTechnology = await _basTechnologyRepo.Where(x => x.Tcode == tCode).FirstOrDefaultAsync();

            var BasTechnologyDto = Mapper.Map<BasTechnologyDto>(BasTechnology);

            var relations = await _basTechnologyStepRelationRepo.Where(x => !x.IsDeleted && x.TCode == tCode).ToListAsync();
            BasTechnologyDto.Items = Mapper.Map<List<BasTechnologyStepRelationDto>>(relations);

            var steps = await _basStepRepo.Where(x => !x.IsDeleted && relations.Select(p => p.IdStep).Contains(x.Id)).ToListAsync();

            BasTechnologyDto.Items.ForEach(x =>
            {
                x.StepName = steps.FirstOrDefault(p => p.Id == x.IdStep).Name ?? "";
                x.StepCode = steps.FirstOrDefault(p => p.Id == x.IdStep).Code ?? "";
                x.StepTime = steps.FirstOrDefault(p => p.Id == x.IdStep).StepTime ?? 0;
                x.PreStepName = steps?.FirstOrDefault(p => p.Code == x.PreStep)?.Name ?? "";
            });
            return BasTechnologyDto;
        }



        /// <summary>
        /// 创建工艺
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult<long>> CreateAsync(BasTechnologyDto input)
        {
            input.TrimStringFields();
            var autoCode = await _basNumberManagementService.GetNumberBySimpleName(CommonConst.TECHNUMBER);
            var technologys = await _basTechnologyMgr.CreateAsync(autoCode, input.Tname, input.Status ?? 0, input.Remark);

            var items = Mapper.Map<List<BasTechnologyStepRelation>>(input.Items);
            items.ForEach(o =>
            {
                o.Id = IdGenerater.GetNextId();
                o.IdTechnology = technologys.Id;
                o.TCode = technologys.Tcode;
            });
            await _basTechnologyRepo.InsertAsync(technologys);
            await _basTechnologyStepRelationRepo.InsertRangeAsync(items);
            return technologys.Id;
        }


        /// <summary>
        /// 更新工艺
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult> UpdateAsync(BasTechnologyDto input)
        {
            bool isUpdete = await _basTechnologyMgr.UpdateAsync(input.Tcode, input.Tname, input.Id ?? 0);
            if (isUpdete)
            {
                var technology = await _basTechnologyRepo.GetAsync(input.Id ?? 0);
                if (technology != null)
                {
                    technology.Status = input.Status;
                    technology.Tcode = input.Tcode;
                    technology.Tname = input.Tname;
                    technology.Remark = input.Remark;
                    await _basTechnologyRepo.UpdateAsync(technology);

                    var relations = await _basTechnologyStepRelationRepo.Where(x => !x.IsDeleted && x.IdTechnology == input.Id, false, false).ToListAsync();
                    await _basTechnologyStepRelationRepo.RemoveRangeAsync(relations);


                    var items = Mapper.Map<List<BasTechnologyStepRelation>>(input.Items);
                    items.ForEach(o =>
                    {
                        o.Id = IdGenerater.GetNextId();
                        o.IdTechnology = technology.Id;
                        o.TCode = input.Tcode;
                    });
                    await _basTechnologyStepRelationRepo.InsertRangeAsync(items);
                }
            }
            return AppSrvResult();
        }

        /// <summary>
        /// 删除工艺
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult> DeleteAsync(long id)
        {
            var step = await _basTechnologyRepo.GetAsync(id);
            if (step != null)
            {
                var items = await _basProductRepo.Where(x => !x.IsDeleted && x.TCode == step.Tcode, false, false).ToListAsync();

                if (items?.Count > 0)
                {
                    return Problem(HttpStatusCode.BadRequest,"该工艺有所属产品，不可以删除！");
                }

                var relations = await _basTechnologyStepRelationRepo.Where(x => !x.IsDeleted && x.IdTechnology == id, false, false).ToListAsync();
                relations.ForEach(o =>
                {
                    o.IsDeleted = true;
                });
                await _basTechnologyStepRelationRepo.UpdateRangeAsync(relations);

                step.IsDeleted = true;
                await _basTechnologyRepo.UpdateAsync(step);
            }
            return AppSrvResult();


        }

        /// <summary>
        /// 更新状态
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult> ChangeStatusAsync(ChangeStatusDto input)
        {
            if (input.CodeList.Count == 0)
                return Problem(HttpStatusCode.BadRequest, "编码列表不能为空！");

            var steps = await _basTechnologyRepo.Where(x => input.CodeList.Contains(x.Tcode ?? "-1"), false, false).ToListAsync();
            if (input.Status == 0)
            {
                steps.ForEach(x => { x.Status = 0; });
            }
            else
            {
                steps.ForEach(x => { x.Status = 1; });
            }

            await _basTechnologyRepo.UpdateRangeAsync(steps);

            return AppSrvResult();
        }
    }
}
