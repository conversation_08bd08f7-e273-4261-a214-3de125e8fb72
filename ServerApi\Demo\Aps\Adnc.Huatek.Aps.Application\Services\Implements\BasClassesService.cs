﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.BasClassesAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStation;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStepAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.SysUserAggregate;
using Adnc.Huatek.Aps.Domain.Services;
using Adnc.Infra.Helper;
using Adnc.Shared;
using Adnc.Shared.Application.Contracts.ResultModels;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services.Implements
{
    public class BasClassesService : AbstractAppService, IBasClassesService
    {
        private readonly BasClassesManagement _basClassesMgr;
        private readonly IEfBasicRepository<BasClasses> _basClassesRepo;
        private readonly IEfBasicRepository<BasStationClasses> _basStationClassesRepo;
        private readonly IEfBasicRepository<SysUser> _sysUserRepo;
        private readonly BasNumberManagementService _basNumberManagementService;

        public BasClassesService(
            IEfBasicRepository<BasClasses> basClassesRepo,
            IEfBasicRepository<BasStationClasses> basStationClassesRepo,
            BasClassesManagement basClassesMgr,
            IEfBasicRepository<SysUser> sysUserRepo,
            BasNumberManagementService basNumberManagementService)
        {
            _basClassesMgr = basClassesMgr;
            _basClassesRepo = basClassesRepo;
            _basStationClassesRepo = basStationClassesRepo;
            _sysUserRepo = sysUserRepo;
            _basNumberManagementService = basNumberManagementService;
        }
        /// <summary>
        /// 获取分页列表
        /// </summary>
        /// <param name="search"></param>
        /// <returns></returns>
        public async Task<PageModelDto<BasClassesDto>> GetPagedAsync(ClassesPagedDto search)
        {
            search.TrimStringFields();
            var whereExpression = ExpressionCreator
                                                .New<BasClasses>()
                                                .And(x => !x.IsDeleted)
                                                .AndIf(search.Ccode.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Ccode!, $"%{search.Ccode}%"))
                                                .AndIf(search.Cname.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Cname!, $"%{search.Cname}%"))
                                                .AndIf(search.Status > -1, x => x.Status == search.Status);

            var total = await _basClassesRepo.CountAsync(whereExpression);
            if (total == 0)
                return new PageModelDto<BasClassesDto>(search);

            var entities = _basClassesRepo
                                            .Where(whereExpression)
                                            .OrderByDescending(x => x.CreateTime)
                                            .Skip(search.SkipRows())
                                            .Take(search.PageSize);

            var createUsers = _sysUserRepo.Where(x => !x.IsDeleted);
            var modifyUsers = _sysUserRepo.Where(x => !x.IsDeleted);

            var results = await (from c in entities
                                 join cu in createUsers on c.CreateBy equals cu.Id
                                 join mu in modifyUsers on c.ModifyBy equals mu.Id
                                 select new BasClassesDto
                                 {
                                     Id = c.Id,
                                     BeginTime = c.BeginTime,
                                     Ccode = c.Ccode,
                                     Color = c.Color,
                                     Cname = c.Cname,
                                     CreatName = cu.Name,
                                     CreateTime = c.CreateTime,
                                     EndTime = c.EndTime,
                                     ModifyName = mu.Name,
                                     ModifyTime = c.ModifyTime,
                                     Remark = c.Remark,
                                     ShortName = c.ShortName,
                                     Status = c.Status
                                 }).ToListAsync();

            results.ForEach(c =>
            {
                c.Duration = InfraHelper.GetDuration(c.EndTime, c.BeginTime);
            });
            //var classesDtos = Mapper.Map<List<BasClassesDto>>(entities);
            return new PageModelDto<BasClassesDto>(search, results, total);
        }
        /// <summary>
        /// 获取所有数据
        /// </summary>
        /// <returns></returns>

        public async Task<List<BasClassesDto>> GetAllAsync()
        {
            var classes = await _basClassesRepo.Where(x => !x.IsDeleted && x.Status == 1).ToListAsync();

            var basClassesDto = Mapper.Map<List<BasClassesDto>>(classes);

            basClassesDto.ForEach(x =>
            {
                x.Duration = InfraHelper.GetDuration(x.EndTime, x.BeginTime);
            });
            return basClassesDto;
        }

        /// <summary>
        /// 创建班次
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult<long>> CreateAsync(BasClassesDto input)
        {
            input.TrimStringFields();
            var autoCode = _basNumberManagementService.GetNumberBySimpleName(CommonConst.CLASSNUMBER).Result;
            var classes = await _basClassesMgr.CreateAsync(autoCode, input.Cname, input.Status ?? 0, input.ShortName, input.BeginTime, input.EndTime, input.Color, input.Remark);
            classes.Ccode = autoCode;
            await _basClassesRepo.InsertAsync(classes);

            return classes.Id;
        }


        /// <summary>
        /// 更新班次
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult> UpdateAsync(BasClassesDto input)
        {
            bool isUpdete = await _basClassesMgr.UpdateAsync(input.Ccode, input.Cname, input.Status ?? 0, input.ShortName, input.BeginTime, input.EndTime, input.Color, input.Id ?? 0);
            if (isUpdete)
            {
                var classes = await _basClassesRepo.GetAsync(input.Id ?? 0);
                if (classes != null)
                {
                    classes.Ccode = input.Ccode;
                    classes.Cname = input.Cname;
                    classes.ShortName = input.ShortName;
                    classes.Status = input.Status;
                    classes.BeginTime = input.BeginTime;
                    classes.EndTime = input.EndTime;
                    classes.Color = input.Color;
                    classes.Remark = input.Remark;
                    await _basClassesRepo.UpdateAsync(classes);
                }
            }
            return AppSrvResult();
        }

        /// <summary>
        /// 删除班次
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult> DeleteAsync(long id)
        {
            var step = await _basClassesRepo.GetAsync(id);
            if (step != null)
            {
                var items = await _basStationClassesRepo.Where(x => x.ClassesId == step.Id, false, false).ToListAsync();

                if (items?.Count > 0)
                {
                    return Problem(HttpStatusCode.BadRequest, "该班次已被配置，不可以删除！");
                }

                step.IsDeleted = true;
                await _basClassesRepo.UpdateAsync(step);
            }
            return AppSrvResult();


        }


        /// <summary>
        /// 根据ID获取BOM数据
        /// </summary>
        /// <returns></returns>

        public async Task<BasClassesDto> GetByIdAsync(long id)
        {
            var bom = await _basClassesRepo.GetAsync(id);

            var bomDto = Mapper.Map<BasClassesDto>(bom);
            return bomDto;
        }

        /// <summary>
        /// 获取所有启用的班次
        /// </summary>
        /// <returns></returns>
        public async Task<List<ClassesDto>> GetAllClassesAsync()
        {
            var classes = _basClassesRepo.Where(x => !x.IsDeleted && x.Status == 1);

            var basClassesDto = await (from c in classes
                                       select new ClassesDto
                                       {
                                           Id = c.Id,
                                           Cname = c.Cname,
                                           BeginTime = c.BeginTime,
                                           EndTime = c.EndTime
                                       }).ToListAsync();

            basClassesDto.ForEach(x =>
            {
                x.Duration = InfraHelper.GetDuration(x.EndTime, x.BeginTime);
                x.Status = 1;
                x.Qty = 1;
            });
            return basClassesDto;
        }


        /// <summary>
        /// 批量禁用启用
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult> MakeEnableAsync(int status, List<long> ids)
        {
            var lines = await _basClassesRepo.Where(x => !x.IsDeleted && ids.Contains((long)x.Id), false, false).ToListAsync();

            if (lines.Any())
            {
                await lines.ForEachAsync(o =>
                {
                    o.Status = status;
                });
                await _basClassesRepo.UpdateRangeAsync(lines);
            }
            return AppSrvResult();
        }

    }
}
