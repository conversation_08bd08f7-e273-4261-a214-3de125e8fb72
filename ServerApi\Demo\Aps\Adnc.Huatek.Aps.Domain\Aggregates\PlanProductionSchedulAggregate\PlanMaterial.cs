﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulAggregate
{
    /// <summary>
    /// 计划物料信息
    /// </summary>
    [NotMapped]
    public class PlanMaterial
    {
        /// <summary>
        /// 排产日期
        /// </summary>
        public DateTime? SchedulDate { get; set; }

        /// <summary>
        /// 订单编号
        /// </summary>
        public string OrderNumber { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }
        /// <summary>
        /// 产品数量
        /// </summary>
        public decimal ProductQty { get; set; }
        /// <summary>
        /// 物料名称
        /// </summary>
        public string MaterialName { get; set; }
        /// <summary>
        /// 物料数量
        /// </summary>
        public decimal? MaterialQty { get; set; }
        /// <summary>
        /// 执行次数
        /// </summary>
        public int ExeNum { get; set; }
    }
}
