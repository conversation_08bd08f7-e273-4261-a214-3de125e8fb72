﻿using Adnc.Infra.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Aggregates.BasProductTechnologyRelationAggregate
{
    public class BasProductTechnologyRelation : EfFullAuditEntity
    {
        /// <summary>
        /// ID
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 产品id
        /// </summary>
        public long? IdProduct { get; set; }

        /// <summary>
        /// 工艺id
        /// </summary>
        public long? IdTechnology { get; set; }

        /// <summary>
        /// 启用状态
        /// </summary>
        public int? Status { get; set; }






    }


}
