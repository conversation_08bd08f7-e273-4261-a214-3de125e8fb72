﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class BasOrderProductConfig : AbstractEntityTypeConfiguration<BasOrderProduct>
    {
        public override void Configure(EntityTypeBuilder<BasOrderProduct> builder)
        {
            base.Configure(builder);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.OrderId).HasColumnName("orderId");
            builder.Property(x => x.ProductId).HasColumnName("productId");
            builder.Property(x => x.Status).HasColumnName("status");
            builder.Property(x => x.ProductCode).HasColumnName("productcode");
            builder.Property(x => x.ProductiveTime).HasColumnName("productivetime");
            builder.Property(x => x.Quantity).HasColumnName("quantity");
            builder.Property(x => x.PendingNum).HasColumnName("pendingnum");

            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.CreateBy).HasColumnName("createby");
            builder.Property(x => x.CreateTime).HasColumnName("createtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
        }
    }
}