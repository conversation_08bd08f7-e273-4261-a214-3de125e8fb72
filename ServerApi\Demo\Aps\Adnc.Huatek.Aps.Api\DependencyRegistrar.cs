﻿using Adnc.Shared.WebApi.Registrar;

namespace Adnc.Huatek.Aps.Api
{
    public sealed class ApsWebApiDependencyRegistrar : AbstractWebApiDependencyRegistrar
    {

        public ApsWebApiDependencyRegistrar(IServiceCollection services)
           : base(services)
        {
        }

        public ApsWebApiDependencyRegistrar(IApplicationBuilder app)
           : base(app)
        {
        }
        public override void AddAdnc()
        {
            AddWebApiDefault();

            AddHealthChecks(true, false, true, false);

        }

        public override void UseAdnc()
        {
            UseWebApiDefault();
        }
    }
}
