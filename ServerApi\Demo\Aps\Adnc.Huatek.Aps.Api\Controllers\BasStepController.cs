﻿using Adnc.Demo.Shared.Const;
using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Application.Services;
using Microsoft.AspNetCore.Authorization;
using Npoi.Mapper;

namespace Adnc.Huatek.Aps.Api.Controllers
{
    [Route($"{RouteConsts.ApsRoot}/Steps")]
    [ApiController]
    public class BasStepController : AdncControllerBase
    {
        private readonly IBasStepService _stepSrv;

        public BasStepController(IBasStepService stepSrv) => _stepSrv = stepSrv;


        /// <summary>
        /// 获取工序分页信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("page")]
        public async Task<ActionResult<PageModelDto<BasStepDto>>> GetPagedAsync([FromBody] StepPagedDto search)
            => await _stepSrv.GetPagedAsync(search);


        /// <summary>
        /// 获取工序分页信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getall")]
        public async Task<ActionResult<ResultJson>> GetAllAsync()
            => await _stepSrv.GetAllAsync();

        /// <summary>
        /// 新增工序
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("add")]
        [AllowAnonymous]
        public async Task<ActionResult<long>> CreateAsync([FromBody] BasStepDto input)
           => CreatedResult(await _stepSrv.CreateAsync(input));

        /// <summary>
        /// 更新工序
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("edit")]
        [AllowAnonymous]
        public async Task<ActionResult> UpdateAsync([FromBody] BasStepDto input)
           => Result(await _stepSrv.UpdateAsync(input));


        /// <summary>
        /// 删除工序
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
         [AllowAnonymous]
        [HttpDelete("delete/{id}")]
        public async Task<ActionResult> DeleteAsync([FromRoute] long id)
           => Result(await _stepSrv.DeleteAsync(id));

        /// <summary>
        /// 根据ID获取班次
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getbyid/{id}")]
        public async Task<ActionResult<ResultJson>> GetByIdAsync([FromRoute] long id)
            => await _stepSrv.GetByIdAsync(id);

        /// <summary>
        /// 更新启用/禁用状态
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("changestatus")]
        public async Task<ActionResult> ChangeStatus([FromBody] ChangeStatusDto input)
            => Result(await _stepSrv.ChangeStatusAsync(input));


        /// <summary>
        /// 新增物料
        /// </summary>
        /// <param name="input">物料信息</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("importasync")]
        public async Task<ResultJson> ImportAsync(IFormFile formFile)
        {
            //通过上传文件流初始化Mapper
            var mapper = new Mapper(formFile.OpenReadStream());
            //读取sheet1的数据
            var devices = mapper.Take<BasStepDto>("Sheet1").Select(i => i.Value).ToList();
            return await _stepSrv.ImportAsync(devices);
        }


    }
}
