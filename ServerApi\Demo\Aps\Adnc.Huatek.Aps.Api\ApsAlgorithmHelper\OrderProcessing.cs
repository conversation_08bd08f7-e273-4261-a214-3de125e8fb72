﻿namespace Adnc.Huatek.Aps.Api.ApsAlgorithmHelper
{
    public class Job
    {
        public int Id { get; set; }
        public int ProcessingTime { get; set; }
        public int Output { get; set; }
        public int Demand { get; set; }
        public List<int> PreviousJobIds { get; set; }
        public List<int> NextJobIds { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
    }

    public class Scheduler
    {
        public List<Job> Schedule(List<Job> jobs, int totalDemand, DateTime startDate)
        {
            List<Job> scheduledJobs = new List<Job>();

            // 按照工序顺序对作业进行排序
            jobs.Sort((j1, j2) => j1.Id.CompareTo(j2.Id));

            foreach (var job in jobs)
            {
                if (job.PreviousJobIds.Count == 0)
                {
                    // 没有前置工序的作业直接加入排程
                    scheduledJobs.Add(job);
                }
                else
                {
                    // 找到前置工序的作业
                    List<Job> previousJobs = new List<Job>();
                    foreach (var previousJobId in job.PreviousJobIds)
                    {
                        var previousJob = scheduledJobs.Find(j => j.Id == previousJobId);
                        previousJobs.Add(previousJob);
                    }

                    // 计算当前作业的开始时间
                    int startTime = 0;
                    foreach (var previousJob in previousJobs)
                    {
                        startTime = Math.Max(startTime, previousJob.ProcessingTime);
                    }

                    // 更新当前作业的开始时间和结束时间
                    job.ProcessingTime += startTime;

                    // 将当前作业加入排程
                    scheduledJobs.Add(job);
                }
            }

            // 检查总需求量是否满足
            int totalScheduledOutput = scheduledJobs[scheduledJobs.Count - 1].Output;
            if (totalScheduledOutput < totalDemand)
            {
                Console.WriteLine("无法满足总需求量！");
                return null;
            }

            // 计算每个作业的开始日期和结束日期
            DateTime currentDate = startDate;
            foreach (var job in scheduledJobs)
            {
                job.StartDate = currentDate;
                job.EndDate = currentDate.AddHours(job.ProcessingTime);
                currentDate = job.EndDate;
            }

            return scheduledJobs;
        }
    }
}
