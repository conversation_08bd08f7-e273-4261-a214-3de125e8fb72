﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.BasClassesAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasDevice;
using Adnc.Huatek.Aps.Domain.Aggregates.BasDeviceClassAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStepAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PartDeviceStateAggregates;
using Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanMaterialAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanRuleAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanSourceAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanStepAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.SysUserAggregate;
using Adnc.Huatek.Aps.Domain.Services;
using Adnc.Infra.Core.Exceptions;
using Adnc.Infra.IdGenerater.Yitter;
using Adnc.Shared;
using Adnc.Shared.Application.Contracts.ResultModels;
using MongoDB.Driver.Linq;
using Newtonsoft.Json;
using System.Globalization;
using System.Net;
using System.Text;

namespace Adnc.Huatek.Aps.Application.Services.Implements
{
    public class PartSchedPlanService : AbstractAppService, IPartSchedulePlanService
    {
        public IMaintRestClient _maintRestClient;
        private readonly IEfBasicRepository<BasDevice> _bDevRepo;
        private readonly IEfBasicRepository<BasProduct> _bProRepo;
        private readonly IEfBasicRepository<BasClasses> _classRepo;
        private readonly IEfBasicRepository<BasDeviceClass> _devClassRepo;
        private readonly IEfBasicRepository<PartDeviceState> _devStateRepo;
        private readonly PartPlanPendingManagement _planPendingMgr;
        private readonly IEfBasicRepository<PartPlanPending> _planPendingRepo;
        private readonly BasProductService _proServ;
        private readonly IEfBasicRepository<BasStep> _stepRepo;
        private readonly IEfBasicRepository<PartSchedulePlanRule> _ruleRepo;
        private readonly IEfBasicRepository<PartSchedulePlanInfo> _spinfoRepo;
        private readonly IEfBasicRepository<PartSchedulePlanMaterial> _spMaterialRepo;
        private readonly IEfBasicRepository<PartSchedulePlan> _spRepo;
        private readonly IEfBasicRepository<PartSchedulePlanSource> _spSourceRepo;
        private readonly IEfBasicRepository<PartSchedulePlanStep> _spStepRepo;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IEfBasicRepository<SysUser> _userRepo;

        public PartSchedPlanService(
            IEfBasicRepository<PartSchedulePlanMaterial> spMaterialRepo,
            IEfBasicRepository<PartSchedulePlanSource> spSourceRepo,
            IEfBasicRepository<PartSchedulePlanStep> spStepRepo,
            IEfBasicRepository<PartPlanPending> planPendingRepo,
            IEfBasicRepository<PartDeviceState> devStateRepo,
            IEfBasicRepository<PartSchedulePlan> spRepo,
            PartPlanPendingManagement planPendingMgr,
            IEfBasicRepository<SysUser> userRepo,
            IEfBasicRepository<BasDevice> bDevRepo,
            IEfBasicRepository<BasProduct> bProRepo,
            IEfBasicRepository<PartSchedulePlanInfo> spinfoRepo,
            IUnitOfWork unitOfWork,
            IEfBasicRepository<BasDeviceClass> devClassRepo,
            IEfBasicRepository<BasStep> stepRepo,
            IEfBasicRepository<BasClasses> classRepo,
            IEfBasicRepository<PartSchedulePlanRule> ruleRepo,
            IMaintRestClient maintRestClient,
            BasProductService proServ)
        {
            _devClassRepo = devClassRepo;
            _spMaterialRepo = spMaterialRepo;
            _spMaterialRepo = spMaterialRepo;
            _planPendingRepo = planPendingRepo;
            _planPendingMgr = planPendingMgr;
            _spSourceRepo = spSourceRepo;
            _devStateRepo = devStateRepo;
            _spStepRepo = spStepRepo;
            _unitOfWork = unitOfWork;
            _ruleRepo = ruleRepo;
            _bProRepo = bProRepo;
            _bDevRepo = bDevRepo;
            _proServ = proServ;
            _spinfoRepo = spinfoRepo;
            _spRepo = spRepo;
            _stepRepo = stepRepo;
            _userRepo = userRepo;
            _classRepo = classRepo;
            _maintRestClient = maintRestClient;
        }

        /// <summary>
        /// 列表
        /// </summary>
        /// <param name="search"></param>
        /// <returns></returns>
        public async Task<PageModelDto<PartSchedulePlanDto>> GetPagedAsync(PartSchedulPlanPageDto
            search)
        {
            search.TrimStringFields();
            var whereExpression = ExpressionCreator
                                    .New<PartSchedulePlan>()
                                    .And(x => !x.IsDeleted)
                                    .AndIf(search.PlanCode.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.PlanCode!, $"%{search.PlanCode}%"))
                                    .AndIf(search.ScheduleCode.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Code!, $"%{search.ScheduleCode}%"))
                                    .AndIf(search.State != 0, x => x.State == search.State);

            var planWhere = ExpressionCreator
                          .New<PartPlanPending>()
                          .And(x => !x.IsDeleted)
                          .AndIf(search.OrderNumber.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.OrderNumber!, $"%{search.OrderNumber}%"))
                          .AndIf(search.ProductName.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.ProductName!, $"%{search.ProductName}%"))
                          .AndIf(search.Priority != 0, x => x.Priority == search.Priority);

            var entities = await _spRepo.Where(whereExpression).ToListAsync();
            var dtos = Mapper.Map<List<PartSchedulePlanDto>>(entities);

            var PlanPendings = await _planPendingRepo.Where(planWhere).ToListAsync();

            var users = _userRepo.Where(x => !x.IsDeleted && x.Status == 1 && dtos.Select(o => o.CreateBy).Contains(x.Id) || dtos.Select(o => o.ModifyBy).Contains(x.Id));
            

            var results = (from p in dtos
                           join s in PlanPendings on p.PlanCode equals s.PlanCode
                           join u in users on p.CreateBy equals u.Id
                           join f in users on p.ModifyBy equals f.Id
                           select new PartSchedulePlanDto
                           {
                               Id = p.Id,
                               PlanCode = p.PlanCode,
                               Code = p.Code,
                               OrderNum = s.OrderNumber,
                               State = p.State,
                               ScheduleResult = (p.DtDelivery < p.PlanEndDate) ? "超期" : "正常",
                               Priority = s.Priority,
                               DtDelivery = s.DeliveryDate,
                               ProCode = s.ProductCode,
                               ProName = s.ProductName,
                               Qty = s.Qty,
                               PlanBeginDate = p.PlanBeginDate,
                               PlanEndDate = p.PlanEndDate,
                               CreateBy = p.CreateBy,
                               CreateTime = p.CreateTime,
                               CreatName = u.Name
                           }
                       ).ToList();

            var checkPlanList = await _spRepo.Where(x => x.State == SchedulStatusConst.Effect || x.State == SchedulStatusConst.Lock).Select(x => x.Code).ToListAsync();
            var sourceDtos = await _spSourceRepo.Where(x => !x.IsDeleted && checkPlanList.Contains(x.ScheduleCode)).ToListAsync();

            foreach(var item in results)
            {
                var stepSources = await _spSourceRepo.Where(x => x.ScheduleCode == item.Code).ToListAsync();
                if (item.State == SchedulStatusConst.Effect || item.State == SchedulStatusConst.Lock)
                {
                    //相同生产计划的不同排产计划之间不需要分析资源冲突
                    var scheduleCodes = results.Where(x => x.PlanCode == item.PlanCode).Select(x => x.Code).ToList();
                    var stepSourceDtos = sourceDtos.Where(x => !scheduleCodes.Contains(x.ScheduleCode)).ToList();

                    item.SourceConflict = await HasOverlap(stepSourceDtos, stepSources) ? "资源冲突" : "资源合理";
                }
                else
                {
                    item.SourceConflict = "N/A";
                }
            };

            if(!string.IsNullOrWhiteSpace(search.SourceConflict))
            {
                results = results.Where(x => x.SourceConflict == search.SourceConflict).ToList();
            }

            var total = results.Count();

            var resultDatas = results
                            .OrderByDescending(x => x.CreateTime)
                            .Skip(search.SkipRows())
                            .Take(search.PageSize)
                            .ToList();

            return new PageModelDto<PartSchedulePlanDto>(search, resultDatas, total);
        }

        /// <summary>
        /// 获取设备甘特图
        /// </summary>
        /// <param name="search"></param>
        /// <returns></returns>
        public async Task<List<DeviceGanttDto>> GetDeviceGanttPlan(PartSchedulPlanSourcePageDto search)
        {
            var result = new List<DeviceGanttDto>();
            search.TrimStringFields();
            DateTime BeginDate = search.BeginDate == null ? DateTime.Now.Date : search.BeginDate;
            DateTime EndDate = search.EndDate == null ? BeginDate.AddDays(30).Date : search.EndDate;
            var deviceWhereExpression = ExpressionCreator
                    .New<BasDevice>()
                    .And(x => !x.IsDeleted)
                    .AndIf(!string.IsNullOrWhiteSpace(search.DevName), x => EF.Functions.Like(x.Name, $"%{search.DevName}%"))
                    .AndIf(!string.IsNullOrWhiteSpace(search.DevCode), x => EF.Functions.Like(x.Code, $"%{search.DevCode}%"));

            var deviceEntities = await _bDevRepo.Where(deviceWhereExpression).ToListAsync();

            var SchedulePlan = await _spRepo.Where(x => !x.IsDeleted && x.State == SchedulStatusConst.Effect).ToListAsync();

            if (deviceEntities.Any())
            {
                var sourceDtos = await _spSourceRepo.Where(x => x.EndDate >= BeginDate
                                                            && x.EndDate <= EndDate
                                                            && SchedulePlan.Select(x => x.Code).Contains(x.ScheduleCode)
                                                            && deviceEntities.Select(x => x.Code).Contains(x.SourceCode)
                                                   ).ToListAsync();

                //保养 停用，维修
                var deviceStateDtos = await _devStateRepo.Where(x => !x.IsDeleted
                                                                && x.EndTime >= BeginDate
                                                                && x.EndTime <= EndDate
                                                                && deviceEntities.Select(x => x.Code).Contains(x.DevCode)
                                                                && (DeviceStateCodes.Unused == x.Status ||
                                                                DeviceStateCodes.Repair == x.Status ||
                                                                DeviceStateCodes.Mainance == x.Status)
                                                         ).ToListAsync();

                result = deviceEntities.Select(item => new DeviceGanttDto
                {
                    DevCode = item.Code,
                    DevName = item.Name
                }).ToList();

                var deviceStateEnum = new List<EnumDto>();
                var devStateRpcResult = await _maintRestClient.GetDictByNameAsync("设备状态");
                if (devStateRpcResult.IsSuccessStatusCode)
                {
                    var devStateEnums = devStateRpcResult.Content;
                    if (devStateEnums is not null)
                    {
                        deviceStateEnum = devStateEnums.Select(x => new EnumDto
                        {
                            Label = x.label,
                            Value = x.value
                        }).ToList();
                    }
                }

                result.ForEach(item =>
                {
                    item.Shifts = sourceDtos.Where(x => x.SourceCode == item.DevCode).Select(p => new DevicePlanDto
                    {
                        ScheduleCode = p.ScheduleCode,
                        StepCode = p.StepCode,
                        DevCode = p.SourceCode,
                        BeginDate = p.BeginDate,
                        EndDate = p.EndDate,
                        Tat = p.Tat,
                        Status = DeviceStateCodes.Working,
                        Notes = $"{p.StepCode}({p.ScheduleCode})"
                    }).ToList();

                    var devObjs = deviceStateDtos.Where(x => x.DevCode == item.DevCode);
                    if (devObjs.Any())
                    {
                        var objs = devObjs.Select(x => new DevicePlanDto
                        {
                            DevCode = x.DevCode,
                            BeginDate = x.BeginTime,
                            EndDate = x.EndTime,
                            Tat = (decimal)x.EndTime.Subtract(x.BeginTime).TotalHours,
                            Status = x.Status,
                            ScheduleCode = deviceStateEnum?.FirstOrDefault(o => o.Value == x.Status.ToString())?.Label ?? "",
                            Notes = deviceStateEnum?.FirstOrDefault(o => o.Value == x.Status.ToString())?.Label ?? ""
                        });
                        item.Shifts.AddRange(objs);
                    }
                });
            }

            return result;
        }

        /// <summary>
        /// 拖拉拽甘特图时调用接口核验资源
        /// </summary>
        /// <param name="dtos"></param>
        /// <returns></returns>
        public async Task<ResultJson> CheckSourceEffiveAsync(List<PartSchedulePlanStepDto> dtos)
        {
            StringBuilder sb = new StringBuilder();
            //获取当前排产计划变动工序所影响的资源
            var sourceEntities = await _spSourceRepo.Where(x => dtos.Select(x => x.ScheduleCode).Contains(x.ScheduleCode) && dtos.Select(x => x.StepCode).Contains(x.StepCode)).ToListAsync();
            foreach (var item in dtos)
            {
                var sourceDtos = sourceEntities.Where(x => x.ScheduleCode == item.ScheduleCode && x.StepCode == item.StepCode).ToList();
                foreach (var source in sourceDtos)
                {
                    var scheduleDtos = await _spRepo.Where(x => !x.IsDeleted && x.State == SchedulStatusConst.Effect).Select(x => x.Code).ToListAsync();

                    //该时间区间内已排计划的设备清单
                    var planDevDtos = await _spSourceRepo.Where(x => !x.IsDeleted
                                                                && x.ScheduleCode != item.ScheduleCode
                                                                && x.SourceCode == source.SourceCode
                                                                && scheduleDtos.Contains(x.ScheduleCode)
                                                                && x.BeginDate <= source.EndDate
                                                                && source.BeginDate <= x.EndDate).ToListAsync();

                    if (planDevDtos.Any())
                        sb.Append($"排产编号:{item.ScheduleCode}工序{item.StepCode}，设备{source.SourceCode}在时间段{item.BeginDate}！{item.EndDate}存在资源冲突，不可操作生效！");
                }
            }
            string msg = string.Empty;
            if (!string.IsNullOrWhiteSpace(sb.ToString()))
            {
                return new ResultJson(sb.ToString(), null, HttpStatusCode.BadRequest);
            }
            else
            {
                return new ResultJson("");
            }
        }



        public async Task<AppSrvResult<string>> CheckSourceConfilct(SchedulStatusNew input)
        {   
            //获取排产计划
            var spDto = await _spRepo.Where(x=>!x.IsDeleted && x.Code == input.SchedulCode).FirstOrDefaultAsync();

            //相同生产计划的排产计划
            var planSpDto = await _spRepo.Where(x => !x.IsDeleted && x.PlanCode == spDto.PlanCode).Select(x => x.Code).ToListAsync();

            //已生效或者已预定资源的排产计划
            var spStateDto = await _spRepo.Where(x => !x.IsDeleted && (x.State == SchedulStatusConst.Effect || x.State == SchedulStatusConst.Lock)).Select(x=>x.Code).ToListAsync();

            //已被占用的资源（排除了当前需要生效的排产计划对应的生产计划下锁定的资源）
            var sourceDtos = await _spSourceRepo.Where(x => spStateDto.Contains(x.ScheduleCode) 
                                                          && !planSpDto.Contains(x.ScheduleCode))
                                                .ToListAsync();

            //当前计划对应的资源
            var spSourceDtos = await _spSourceRepo.Where(x => x.ScheduleCode == spDto.Code)
                                                .ToListAsync();

            foreach(var item in spSourceDtos)
            {
                var conflictObjs = sourceDtos.Where(x => x.SourceCode == item.SourceCode && x.BeginDate < item.EndDate && item.BeginDate < x.EndDate);
                if (conflictObjs.Any())
                    return new AppSrvResult<string>(@$"当前计划与计划{string.Join(",",conflictObjs.Select(x=>x.ScheduleCode).Distinct())}均存在资源冲突，是否还要继续操作");
            };

            return new AppSrvResult<string>();
        }
        /// <summary>
        /// 一键排产
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<ResultJson> CreateSchedulePlan(PartSchedulePlanDto input)
        {
            //1.获取产品的产能模型
            var proModule = await LoadInitDataAsync(input.ProCode, input.Code);

            //2. 规则解析
            var rule = await ScheduleRuleNew(input.Rules);

            //3.一键排产
            var result = await SchedulePlan(proModule, rule, input);

            //非试算保存数据
            if (result.Msg.code == HttpStatusCode.OK)
            {
                var ruleDtos = Mapper.Map<List<PartSchedulePlanRule>>(input.Rules);
                result.Msg = await DataBaseDeal(proModule, result.reData, ruleDtos);
            }
            return result.Msg;
        }

        /// <summary>
        /// 试算
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<PlanGanttDto> TrialSchedulePlan(PartSchedulePlanDto input)
        {
            var resultData = new PlanGanttDto();
            //1.获取产品的产能模型
            var proModule = await LoadInitDataAsync(input.ProCode, input.Code);

            //2. 规则解析
            var rule = await ScheduleRuleNew(input.Rules);

            //3.一键排产
            var result = await SchedulePlan(proModule, rule, input);

            var stepDtos = await _stepRepo.Where(x => result.reData.StepLists.Select(x => x.StepCode).Contains(x.Code)).ToListAsync();

            if (!result.reData.StepLists.Any())
                throw new BusinessException($"该规则排产失败，不存在可排产的资源，请调整规则后再试");

            resultData.Tasks = (from a in result.reData.StepLists
                                join b in stepDtos on a.StepCode equals b.Code
                                //join c in tecStepDtos on b.Code equals c.StepCode
                                select new PlanProductionSchedulGanttChildItem
                                {
                                    Progress = a.ReportResult,
                                    ScheduleCode = a.ScheduleCode,
                                    Duration = Convert.ToInt32(a.Tat),
                                    EndTime = a.EndDate,
                                    StartTime = a.BeginDate,
                                    StepCode = a.StepCode,
                                    StepName = b.Name,
                                    Parent = a.PreCode,
                                    StepSort = 0,
                                    Id = a.StepCode,
                                }).ToList();

            resultData.Links = (from a in result.reData.StepLists
                                select new ScheduleGanttLink
                                {
                                    id = IdGenerater.GetNextId(),
                                    TargetCode = a.StepCode,
                                    SourceCode = a.PreCode,
                                    Type = 0
                                }).ToList();
            return resultData;
        }

        /// <summary>
        /// 修改计划
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<ResultJson> ModuleSchedulePlan(PartSchedulePlanDto input)
        {
            var steps = input.Steps;
            var result = new SchedulePlanResultDto();
            steps.ForEach(item => item.Id = IdGenerater.GetNextId());
            result.StepLists = Mapper.Map<List<PartSchedulePlanStep>>(steps);
            //1. 数据预加载优化
            var (oldResult, productModules) = await LoadInitialDataAsync(input.Code);

            //2.使用并行加载关联数据
            var (productModuleDto, ruleEntities, planPending) = await ProcessCoreDataAsync(productModules, input.Code, oldResult.SchedulePlan.PlanCode);

            //3.解析排产规则
            var rule = await ScheduleRuleNew(input.Rules);

            //4.获取工序工艺工序路径树
            var stepTrees = await BuildProcessTree(productModuleDto.StepItems, s => s.StepCode, s => s.PreStep);

            //5.核心逻辑处理
            var res = new QuerycheduleStepDto
            {
                Qty = planPending.Qty,
                ScheduleCode = input.Code,
                Rule = rule,
                ProModule = productModuleDto
            };
            var reData = new SchedulePlanResultDto();
            foreach (var item in stepTrees)
            {
                var step = steps.FirstOrDefault(x => x.StepCode == item.StepCode);
                res.BeginDate = step.BeginDate;
                res.EndDate = step.EndDate;
                res.Nodes = item;

                reData = rule.ForWardSchedule ?
                                               await CalculateByPreOrder(res, result, oldResult, true) :
                                               await CalculateByPostOrder(res, result, oldResult, true);
            }

            //5.保存数据处理逻辑
            oldResult.SchedulePlan.BeginDate = input.BeginDate;//开始排产时间
            oldResult.SchedulePlan.PlanBeginDate = reData.StepLists.Min(x => x.BeginDate);//计划开工时间
            oldResult.SchedulePlan.PlanEndDate = reData.StepLists.Max(x => x.EndDate);//计划完工时间
            var ruleDtos = Mapper.Map<List<PartSchedulePlanRule>>(ruleEntities);
            var resultMsg = await DataBaseRemoveDeal(oldResult);
            if (resultMsg.code == HttpStatusCode.OK)
            {
                resultMsg = await DataBaseDeal(productModuleDto, reData, ruleDtos, true);
            }
            return resultMsg;
        }

        /// <summary>
        /// 报工保存
        /// </summary>
        /// <param name="search"></param>
        /// <returns></returns>
        public async Task<AppSrvResult> SaveStepProgress(PartSchedulePlanStepReportDto input)
        {
            var stepPlan = await _spStepRepo.Where(x => !x.IsDeleted
                                                        && x.ScheduleCode == input.ScheduleCode
                                                        && x.StepCode == input.StepCode, false, false)
                                            .ToListAsync();
            var entity = Mapper.Map<PartSchedulePlanStep>(stepPlan.FirstOrDefault());
            entity.ReportResult = input.ReportResult;
            entity.ReportDate = DateTime.Now;
            await _spStepRepo.UpdateAsync(entity);
            return AppSrvResult();
        }

        /// <summary>
        /// 改变状态
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusinessException"></exception>
        public async Task<ResultJson> SetScheduleStatusAsync(SchedulStatusNew input)
        {
            if (null == input.SchedulCode)
            {
                return new ResultJson($"请选择数据！", null, HttpStatusCode.BadRequest);
            }
            var scheduleDto = await _spRepo.Where(x => input.SchedulCode == x.Code, false, false).FirstOrDefaultAsync();
            var planDto = await _planPendingRepo.Where(x => x.PlanCode == scheduleDto.PlanCode, false, false).FirstOrDefaultAsync();

            //待生效=》已生效

            switch (input.Status)
            {
                case SchedulStatusConst.Effect:

                    if (scheduleDto.State == SchedulStatusConst.Effect)
                        return new ResultJson($"排产编号:{scheduleDto.Code}已生效，不可重复操作！", null, HttpStatusCode.BadRequest);
                    //判断生产计划是否存在已经生效的生产计划
                    var effiveSchedule = await _spRepo.Where(x => !x.IsDeleted
                                                                && x.PlanCode == scheduleDto.PlanCode
                                                                && x.State == SchedulStatusConst.Effect)
                                                      .ToListAsync();
                    if (effiveSchedule.Any())
                        throw new BusinessException($"排产编号:{scheduleDto.Code} 对应的生产计划{scheduleDto.PlanCode}已存在已生效的排产计划，不可重复操作！");

                    scheduleDto.State = SchedulStatusConst.Effect;
                    planDto.Status = PendingPlanStatusConst.Planed;
                    
                    var schedulePlanDtos = await _spRepo.Where(x => x.PlanCode == scheduleDto.PlanCode && x.Code != scheduleDto.Code, false, false).ToListAsync();
                    schedulePlanDtos.ForEach(item =>
                    {
                        item.State = SchedulStatusConst.Cancel;
                    });
                    await _spRepo.UpdateRangeAsync(schedulePlanDtos);

                    break;

                case SchedulStatusConst.Cancel:

                    //作废
                    scheduleDto.State = SchedulStatusConst.Cancel;
                    planDto.Status = PendingPlanStatusConst.Waiting;
                    break;

                case SchedulStatusConst.Stop:

                    //暂停
                    scheduleDto.State = SchedulStatusConst.Stop;
                    planDto.Status = PendingPlanStatusConst.Waiting;
                    break;

                case SchedulStatusConst.Finish:

                    //完成
                    if (scheduleDto.State != SchedulStatusConst.Effect)
                        return new ResultJson($"只允许对已生效的排产计划操作完成，请确认当前排产计划编号:{scheduleDto.Code}状态是否为生效后操作！", null, HttpStatusCode.BadRequest);

                    scheduleDto.State = SchedulStatusConst.Finish;
                    planDto.Status = PendingPlanStatusConst.Finished;
                    break;
            }

            await _spRepo.UpdateAsync(scheduleDto);
            await _planPendingRepo.UpdateAsync(planDto);

            return new ResultJson("");
        }

        /// <summary>
        /// 分析与当前计划的资源冲突情况
        /// </summary>
        /// <param name="scheduleCode"></param>
        /// <param name="planCode"></param>
        /// <returns></returns>
        public async Task<ResultJson> CheckSourceConflict(QuerySourceConflict input)
        {
            // 1. 并行获取所有需要的数据
            var (schedulePlans, sourceDtos, stepDtos, currentSources) = await GetRequiredDataAsync(input.ScheduleCode, input.PlanCode);

            // 3. 获取可能冲突的源数据
            var sourceCodes = currentSources.Select(x => x.SourceCode).Distinct().ToList();
            var targetSources = await _spSourceRepo
                .Where(x => sourceCodes.Contains(x.SourceCode) &&
                            x.ScheduleCode != input.ScheduleCode &&
                            schedulePlans.Select(p => p.Code).Contains(x.ScheduleCode))
                .ToListAsync();

            // 4. 使用分组提高处理效率
            var conflictGroups = targetSources
                .GroupBy(x => x.SourceCode)
                .ToDictionary(g => g.Key, g => g.ToList());

            var warnings = new List<ScheduleSourceWarningDto>();

            //// 5. 处理冲突检测
            foreach (var current in currentSources)
            {
                if (!conflictGroups.TryGetValue(current.SourceCode, out var conflicts))
                    continue;

                foreach (var conflict in conflicts.Where(x =>
                         x.BeginDate < current.EndDate &&
                         current.BeginDate < x.EndDate))
                {
                    var warning = CreateWarningDto(
                        current, conflict,
                        sourceDtos, stepDtos, schedulePlans);

                    warnings.Add(warning);
                }
            }

            return new ResultJson("分析成功", warnings, HttpStatusCode.OK);
        }


        public async Task<AppSrvResult<long>> BatchDelAsync(List<string> codes)
        {
            
            if (codes?.Count == 0)
            {
                return Problem(HttpStatusCode.BadRequest, $"请选择数据！");
            }

            var schedulEntities = await _spRepo.Where(x => codes.Contains(x.Code) , false, false).ToListAsync();

            if (schedulEntities.Count > 0)
            {
                schedulEntities.ForEach(x => x.IsDeleted = true);

                await _spRepo.UpdateRangeAsync(schedulEntities);

                var stepDtos = await _spStepRepo.Where(x => codes.Contains(x.ScheduleCode), false, false).ToListAsync();
                stepDtos.ForEach(x => x.IsDeleted = true);
                await _spStepRepo.UpdateRangeAsync(stepDtos);

                var materials = await _spMaterialRepo.Where(x => codes.Contains(x.ScheduleCode), false, false).ToListAsync();
                materials.ForEach(x => x.IsDeleted = true);
                await _spMaterialRepo.UpdateRangeAsync(materials);

                var rules = await _ruleRepo.Where(x => codes.Contains(x.ScheduleCode), false, false).ToListAsync();
                rules.ForEach(x => x.IsDeleted = true);
                await _ruleRepo.UpdateRangeAsync(rules);

                var tasks = await _spinfoRepo.Where(x => codes.Contains(x.ScheduleCode), false, false).ToListAsync();
                tasks.ForEach(x => x.IsDeleted = true);
                await _spinfoRepo.UpdateRangeAsync(tasks);

                var sources = await _spSourceRepo.Where(x => codes.Contains(x.ScheduleCode), false, false).ToListAsync();
                sources.ForEach(x => x.IsDeleted = true);
                await _spSourceRepo.UpdateRangeAsync(sources);

            }

            return AppSrvResult();
        }


        #region 私有方法

        /// <summary>
        /// 检查指定设备是否存在时间段重叠
        /// </summary>
        /// <param name="devices">设备集合</param>
        /// <param name="targetDeviceName">要检查的设备名称</param>
        private async Task<bool> HasOverlap(List<PartSchedulePlanSource> devices, List<PartSchedulePlanSource> targetSourceCodes)
        {

            foreach (var item in targetSourceCodes)
            {
                var conflictObjs = devices.Where(x => x.SourceCode == item.SourceCode && x.BeginDate < item.EndDate && item.BeginDate < x.EndDate);
                if (conflictObjs.Any())
                    return true;
            };
            return false;
        }

        // 辅助方法：并行获取所需数据
        private async Task<(
            List<PartSchedulePlan> schedulePlans,
            List<BasDevice> sourceDtos,
            List<BasStep> stepDtos,
            List<PartSchedulePlanSource> currentSources
        )> GetRequiredDataAsync(string scheduleCode, string planCode)
        {
            var spQuery = await _spRepo.Where(x => x.PlanCode != planCode
                                        && x.Code != scheduleCode
                                        && !x.IsDeleted
                                        && (x.State == SchedulStatusConst.Effect || x.State == SchedulStatusConst.Lock))
                                        .ToListAsync();

            var sourceQuery = await _bDevRepo.Where(x => true).ToListAsync();
            var stepQuery = await _stepRepo.Where(x => true).ToListAsync();
            var currentSourceQuery = await _spSourceRepo.Where(x => x.ScheduleCode == scheduleCode).ToListAsync();

            return (
                spQuery,
                sourceQuery,
                stepQuery,
                currentSourceQuery
            );
        }

        // 辅助方法：创建警告DTO
        private ScheduleSourceWarningDto CreateWarningDto(
            PartSchedulePlanSource current,
            PartSchedulePlanSource conflict,
            List<BasDevice> sourceNameDict,
            List<BasStep> stepNameDict,
            List<PartSchedulePlan> schedulePlanDict)
        {
            var beginDate = conflict.BeginDate > current.BeginDate ? conflict.BeginDate : current.BeginDate;
            var endDate = conflict.EndDate < current.EndDate ? conflict.EndDate : current.EndDate;
            var duration = (endDate - beginDate).TotalHours;

            return new ScheduleSourceWarningDto
            {
                SourceCode = current.SourceCode,
                SourceName = sourceNameDict.FirstOrDefault(x => x.Code == current.SourceCode).Name,
                StepCode = current.StepCode,
                StepName = stepNameDict.FirstOrDefault(x => x.Code == current.StepCode).Name,
                WarningPlanCode = schedulePlanDict.FirstOrDefault(x => x.Code == conflict.ScheduleCode).PlanCode,
                WarningScheduleCode = conflict.ScheduleCode,
                WarningStepCode = conflict.StepCode,
                WarningStepName = stepNameDict.FirstOrDefault(x => x.Code == conflict.StepCode).Name,
                WarningBeginDate = beginDate,
                WarningEndDate = endDate,
                WarningDuration = duration
            };
        }

        /// <summary>
        /// 计算合并后的工作时间段
        /// </summary>
        private static List<TimeInterval> CalculateMergedIntervals(
            DateTime beginDate,
            DateTime endDate,
            List<BasDeviceClassDto> schedule)
        {
            var rawIntervals = GetDailyIntervals(beginDate, endDate, schedule);
            return MergeConsecutiveFullShifts(rawIntervals);
        }

        /// <summary>
        /// 构建树结构
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="steps"></param>
        /// <param name="getStepCode"></param>
        /// <param name="getPreCode"></param>
        /// <returns></returns>
        private async Task<List<StepTreeDto>> BuildProcessTree<T>(List<T> steps, Func<T, string> getStepCode, Func<T, string?> getPreCode)
        {
            var nodeDict = new Dictionary<string, StepTreeDto>();
            var rootNodes = new List<StepTreeDto>();

            // 第一阶段：创建所有节点
            foreach (var step in steps)
            {
                var code = getStepCode(step);
                var preCode = getPreCode(step);
                if (string.IsNullOrWhiteSpace(code)) continue;

                if (!nodeDict.ContainsKey(code))
                {
                    nodeDict[code] = new StepTreeDto { StepCode = code, PreCode = preCode };
                }
            }

            // 第二阶段：建立父子关系
            foreach (var step in steps)
            {
                var currentCode = getStepCode(step);
                var preCode = getPreCode(step);

                if (string.IsNullOrWhiteSpace(currentCode)) continue;

                // 查找父节点
                if (!string.IsNullOrWhiteSpace(preCode) &&
                    nodeDict.TryGetValue(preCode, out var parent))
                {
                    parent.ChildNodes.Add(nodeDict[currentCode]);
                }
            }

            // 第三阶段：识别根节点
            var childCodes = new HashSet<string>(
                steps.Select(s => getStepCode(s))
                    .Where(code => !string.IsNullOrWhiteSpace(code)));

            foreach (var kvp in nodeDict)
            {
                // 根节点条件：没有前置工序，或前置工序不存在于节点字典中
                var preCode = getPreCode(steps.First(s => getStepCode(s) == kvp.Key));
                if (string.IsNullOrWhiteSpace(preCode) || !nodeDict.ContainsKey(preCode))
                {
                    rootNodes.Add(kvp.Value);
                }
            }

            return rootNodes;
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        /// <param name="DevType"></param>
        /// <param name="planState"></param>
        /// <param name="BeginDate"></param>
        /// <param name="EndDate"></param>
        /// <returns></returns>
        private async Task<(List<BasDevice> devDtos, List<BasDeviceClassDto> devClassDtos, List<PartDeviceState> deviceStateEntities, List<PartSchedulePlanSource> planDevDtos)> LoadInitialDeviceDataAsync(string DevType, List<int> planState, DateTime BeginDate, DateTime EndDate)
        {
            var devDtos = await _bDevRepo.Where(x => !x.IsDeleted && x.Dtype == DevType).ToListAsync();
            //设备班次
            var devClasses = await _devClassRepo.Where(x => !x.IsDeleted && devDtos.Select(x => x.Code).Contains(x.DevCode)).ToListAsync();
            var devClassDtos = Mapper.Map<List<BasDeviceClassDto>>(devClasses);

            var classes = await _classRepo.Where(x => !x.IsDeleted && devClassDtos.Select(x => x.ClassCode).Contains(x.Ccode)).ToListAsync();

            devClassDtos.ForEach(item =>
            {
                item.BeginTime = classes?.FirstOrDefault(x => x.Ccode == item.ClassCode)?.BeginTime ?? "00:00";
                item.EndTime = classes?.FirstOrDefault(x => x.Ccode == item.ClassCode)?.EndTime ?? "23:59";
            });

            //获取设备的维修，停用，保养状态数据
            var deviceStateEntities = await _devStateRepo.Where(x => !x.IsDeleted
                                                                    && (DeviceStateCodes.Unused == x.Status ||
                                                                        DeviceStateCodes.Repair == x.Status ||
                                                                        DeviceStateCodes.Mainance == x.Status)
                                                                    && x.BeginTime < EndDate
                                                                    && BeginDate<x.EndTime  
                                                                    && devDtos.Select(x => x.Code).Contains(x.DevCode))
                                                         .ToListAsync();

            var scheduleDtos = await _spRepo.Where(x => !x.IsDeleted && planState.Contains(x.State)).Select(x => x.Code).ToListAsync();

            //该时间区间内已排计划的设备清单
            var planDevDtos = await _spSourceRepo.Where(x => !x.IsDeleted
                                                        && scheduleDtos.Contains(x.ScheduleCode)
                                                        && devDtos.Select(x => x.Code).Contains(x.SourceCode)
                                                        && x.BeginDate < EndDate
                                                        && BeginDate<x.EndDate  ).ToListAsync();

            return (devDtos, devClassDtos, deviceStateEntities, planDevDtos);
        }

        private static DateTime ClampEnd(DateTime globalEnd, DateTime localEnd)
                    => globalEnd < localEnd ? globalEnd : localEnd;

        private static DateTime ClampStart(DateTime globalStart, DateTime localStart)
                    => globalStart > localStart ? globalStart : localStart;

        /// <summary>
        /// 获取工序树
        /// </summary>
        /// <param name=input></param>
        /// <returns></returns>
        private static List<StepTreeDto> ComputeStepTree(List<BasProductStepRelationDto> input, List<string> nodeList, List<StepTreeDto> res, bool isRoot = true)
        {
            foreach (var item in input)
            {
                if (!nodeList.Contains(item.StepCode))
                {
                    nodeList.Add(item.StepCode);
                    var obj = new StepTreeDto();
                    obj.StepCode = item.StepCode;
                    var nextStep = input.Where(x => x.PreStep == obj.StepCode);
                    if (nextStep.Any())
                    {
                        obj.ChildNodes = ComputeStepTree(nextStep.ToList(), nodeList, res, false);
                    }
                    if (isRoot)
                    {
                        res.Add(obj);
                    }
                }
            }
            return res;
        }

        // 生成每日原始时间段
        private static List<TimeInterval> GetDailyIntervals(
            DateTime beginDate,
            DateTime endDate,
            List<BasDeviceClassDto> schedule)
        {
            var intervals = new List<TimeInterval>();

            DateTime currentDay = beginDate.Date;
            DateTime EndDate = endDate;
            if (endDate.Hour == 0)
            {
                EndDate = endDate.AddDays(1);
            }
            while (currentDay < EndDate)
            {
                var deviceClass = schedule.FirstOrDefault(x => x.WorkingDate == currentDay);
                if (deviceClass != null)
                {
                    var (shiftStart, shiftEnd) = GetShiftRange(currentDay, deviceClass);
                    var dayInterval = new TimeInterval(
                        ClampStart(beginDate, shiftStart),
                        ClampEnd(EndDate, shiftEnd)
                    );
                    intervals.Add(dayInterval);
                }
                currentDay = currentDay.AddDays(1);
            }
            return intervals;
        }

        // 以下工具方法与之前实现相同
        private static (DateTime start, DateTime end) GetShiftRange(DateTime day, BasDeviceClassDto shift)
        {
            TimeSpan begin, end;
            TimeSpan.TryParseExact(shift.BeginTime, @"hh\:mm", CultureInfo.InvariantCulture, out begin);
            TimeSpan.TryParseExact(shift.EndTime, @"hh\:mm", CultureInfo.InvariantCulture, out end);
            return (day.Add(begin), day.Add(end));
        }

        // 判断时间是否跨日连续（23:59 -> 00:00）
        private static bool IsMidnightConsecutive(DateTime prevEnd, DateTime nextStart)
        {
            return prevEnd.ToString("HH:mm") == "23:59" &&
                   nextStart.ToString("HH:mm") == "00:00" &&
                   prevEnd.Date.AddDays(1) == nextStart.Date;
        }

        // 合并连续全班时间段
        private static List<TimeInterval> MergeConsecutiveFullShifts(List<TimeInterval> intervals)
        {
            var merged = new List<TimeInterval>();
            TimeInterval current = null;

            foreach (var interval in intervals.OrderBy(i => i.Start))
            {
                if (current == null)
                {
                    current = interval;
                    continue;
                }

                // 检查是否连续全班
                if (IsMidnightConsecutive(current.End, interval.Start))
                {
                    current = new TimeInterval(current.Start, interval.End);
                }
                else
                {
                    merged.Add(current);
                    current = interval;
                }
            }

            if (current != null) merged.Add(current);
            return merged;
        }

        /// <summary>
        /// 获取不重叠的数据
        /// </summary>
        /// <param name="source"></param>
        /// <param name="subtractions"></param>
        /// <returns></returns>
        private static List<TimeInterval> SubtractIntervals(List<TimeInterval> source, List<TimeInterval> subtractions)
        {
            List<TimeInterval> result = source;

            foreach (var sub in subtractions.OrderBy(t => t.Start))
            {
                List<TimeInterval> newResult = new List<TimeInterval>();

                foreach (var current in result)
                {
                    // 无交集的情况
                    if (current.End <= sub.Start || current.Start >= sub.End)
                    {
                        newResult.Add(current);
                        continue;
                    }

                    // 切分左半部分
                    if (current.Start < sub.Start)
                    {
                        newResult.Add(new TimeInterval(current.Start, sub.Start));
                    }

                    // 切分右半部分
                    if (current.End > sub.End)
                    {
                        newResult.Add(new TimeInterval(sub.End, current.End));
                    }
                }

                result = newResult;
            }

            return result;
        }

        // 后序遍历：叶子节点结束时间 -> 父节点结束时间等于子节点开始时间
        private async Task<SchedulePlanResultDto> CalculateByPostOrder(QuerycheduleStepDto query, SchedulePlanResultDto result, SchedulePlanResultDto oldResult = default, bool edit = false)
        {
            if (!edit)
            {
                await PostOrderTraversal(query, result);
            }
            else
            {
                await PostOrderTraversalModify(query, result, oldResult, edit);
            }
            return result;
        }

        //树的前序遍历
        // 前序遍历：根节点开始时间 -> 子节点继承父节点结束时间
        private async Task<SchedulePlanResultDto> CalculateByPreOrder(QuerycheduleStepDto query, SchedulePlanResultDto result, SchedulePlanResultDto oldResult = default, bool edit = false)
        {
            if (!edit)
            {
                await PreOrderTraversal(query, result);
            }
            else
            {
                await PreOrderTraversalEdit(query, result, oldResult);
            }
            return result;
        }

        private async Task<(decimal UnitTat, decimal UnitCapacity, List<BasProductStepRelationSourceDto> DeviceItems)> ComputeStepCapacity(List<BasProductStepRelationDto> input, string stepCode)
        {
            var sourceList = new List<BasProductStepRelationSourceDto>();
            var SourceItems = input.FirstOrDefault(x => x.StepCode == stepCode)?.MainItems.ToList();
            sourceList.AddRange(SourceItems);
            sourceList.AddRange(input.FirstOrDefault(x => x.StepCode == stepCode)?.AssistItems);

            //单位产能，单位工作时长 取单位产能除以TAT的最小那一组
            var obj = sourceList.Where(x => x.IsCapacity).OrderBy(x => (x.Capacity ?? 0) / (x.Tat ?? 1)).FirstOrDefault();
            var unitTat = obj?.Tat ?? 0;
            var unitCapatity = obj?.Capacity ?? 0;
            return (unitTat, unitCapatity, SourceItems);
        }

        private async Task DealSchedulePlan(QuerycheduleStepDto query, SchedulePlanResultDto result, SchedulePlanResultDto oldResult = default, bool modify = false)
        {
            try
            {
                //该工序下的主资源清单
                var (unitTat, unitCapacity, SourceItems) = await ComputeStepCapacity(query.ProModule.StepItems, query.Nodes.StepCode);
                var isChoose = true;
                //设备
                foreach (var source in SourceItems.Where(x => x.SourceType == SourceTypeEnum.Device))
                {
                    //获取时间范围内的游侠资源
                    var queryDevice = new QueryEffrctiveDevice
                    {
                        SourceType = source.SourceType,
                        DevType = source.DeviceType,
                        UnitTat = unitTat,
                        UnitCapacity = unitCapacity,
                        Begin = query.BeginDate,
                        End = query.EndDate,
                        Modify = modify,
                        Qty = query.Qty
                    };

                    var availableDevices = await GetDevice(queryDevice, result.SourcesLists, query.Rule.SourceRange);
                    if (!availableDevices.Any())
                    {
                        if (modify)
                        {
                            isChoose = false;
                            result.SourcesLists.AddRange(oldResult.SourcesLists.Where(x => x.StepCode == query.Nodes.StepCode && x.DevType == source.DeviceType && x.SourceType == source.SourceType).ToList());
                            continue;
                        }
                        else
                        {
                            throw new InvalidOperationException("无符合该交付日期的设备可用");
                        }
                    }
                    else
                    {
                        //整批排产非非齐套排
                        var remainingQty = query.Qty;
                        while (remainingQty > 0)
                        {
                            var device = SelectOptimalDevice(availableDevices, query.Rule.ForWardSchedule, remainingQty);
                            availableDevices.Remove(device);
                            var effctiveDay = query.Rule.ForWardSchedule
                                ? device.Children.OrderBy(x => x.Start)
                                : device.Children.OrderByDescending(x => x.End);

                            foreach (var item in effctiveDay)
                            {
                                if (item.Tat < source.Tat) continue;
                                var kloop = Math.Ceiling(remainingQty / (device.UnitCapacity == 0 ? 1 : device.UnitCapacity));
                                var loopNum = Math.Floor(item.Tat / (source.Tat ?? 1));
                                var actualLoops = Math.Min(kloop, loopNum);

                                var deviceObj = new PartSchedulePlanSource
                                {
                                    Id = IdGenerater.GetNextId(),
                                    ScheduleCode = query.ScheduleCode,
                                    StepCode = query.Nodes.StepCode,
                                    SourceType = source.SourceType,
                                    DevType = source.DeviceType,
                                    SourceCode = device.DevCode,
                                    UnitTat = device.UnitTat,
                                    UnitCapacity = device.UnitCapacity,
                                    Tat = device.UnitTat * actualLoops,
                                    TotalCapacity = (device.UnitCapacity * actualLoops)
                                };
                                //正排
                                if (query.Rule.ForWardSchedule)
                                {
                                    deviceObj.BeginDate = item.Start;
                                    deviceObj.EndDate = item.Start.AddHours(Convert.ToDouble(device.UnitTat * actualLoops));
                                }
                                else
                                {   //倒排
                                    deviceObj.BeginDate = item.End.AddHours(0 - Convert.ToDouble(device.UnitTat * actualLoops));
                                    deviceObj.EndDate = item.End;
                                }
                                remainingQty -= deviceObj.TotalCapacity;
                                result.SourcesLists.Add(deviceObj);
                                if (remainingQty <= 0)
                                    break;
                            }
                        }
                    }
                }

                //如果时修改计划，调整后的计划需要重新适配资源时间做调整
                var stepObj = new PartSchedulePlanStep();
                if (isChoose)
                {
                    if (modify)
                    {
                        result.StepLists.RemoveAll(x => x.StepCode == query.Nodes.StepCode);
                    }
                    //工序 根据资源时间获取工序时间
                    var stepSourceDtos = result.SourcesLists.Where(x => x.StepCode == query.Nodes.StepCode);

                    stepObj.Id = IdGenerater.GetNextId();
                    stepObj.ScheduleCode = query.ScheduleCode;
                    stepObj.PreCode = query.Nodes.PreCode;
                    stepObj.StepCode = query.Nodes.StepCode;
                    stepObj.ScheduleType = SchedulTypeEnum.System;
                    stepObj.BeginDate = stepSourceDtos.Min(x => x.BeginDate);
                    stepObj.EndDate = stepSourceDtos.Max(x => x.EndDate);
                    stepObj.Tat = stepSourceDtos.Sum(x => x.Tat);
                    result.StepLists.Add(stepObj);
                }
                else
                {
                    stepObj = result.StepLists.FirstOrDefault(x => x.StepCode == query.Nodes.StepCode);
                }

                //物料
                var materialIns = query.ProModule.StepItems.FirstOrDefault(x => x.StepCode == query.Nodes.StepCode)?.MaterialsIn;
                foreach (var material in materialIns)
                {
                    var mObj = new PartSchedulePlanMaterial();
                    mObj.Id = IdGenerater.GetNextId();
                    mObj.ScheduleCode = query.ScheduleCode;
                    mObj.StepCode = query.Nodes.StepCode;
                    mObj.NeedDate = stepObj.BeginDate;
                    mObj.Mcode = material.MaterialCode;
                    mObj.Qty = material.Qty * query.Qty;
                    result.MaterialLists.Add(mObj);
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException(ex.Message.ToString());
            }
        }

        /// <summary>
        /// 根据设备类型获取该时间区间内可用的设备资源
        /// </summary>
        /// <param name=devType>设备类型</param>
        /// <param name=UnitTat>单位工作时长</param>
        /// <param name=UnitCapacity>单位产能</param>
        /// <param name=dtBegin>开始时间</param>
        /// <param name=dtEnd>结束时间</param>
        /// <returns></returns>
        private async Task<List<EffectiveDeviceDtos>> GetDevice(QueryEffrctiveDevice input, List<PartSchedulePlanSource> sourcesLists, bool SourceRage)
        {
            var res = new List<EffectiveDeviceDtos>();
            //资源范围控制：默认包含预定资源
            List<int> planState = new List<int>() { SchedulStatusConst.Effect };
            if (SourceRage)
            {
                planState.Add(SchedulStatusConst.Lock);
            }

            //1.基础数据初始化
            var (devDtos, devClassDtos, deviceStateEntities, planDevDtos) = await LoadInitialDeviceDataAsync(input.DevType, planState, input.Begin, input.End);

            //2.设备可用时间处理
            foreach (var item in devDtos)
            {
                //处理设备工作日历
                var devWorkDay = await queryDateRange(input.Begin, input.End, item.Code, devClassDtos);

                //设备维修保养停用计划
                var occupied = deviceStateEntities.Where(x => x.DevCode == item.Code)
                                                  .Select(x => new TimeInterval(x.BeginTime, x.EndTime))
                                                  .ToList();
                //如果是修改计划在考虑已占用资源时应排除该计划原占用的设备计划
                occupied.AddRange(planDevDtos.Where(x => x.SourceCode == item.Code 
                                                      && (!input.Modify || x.ScheduleCode != input.ScheduleCode))
                                          .Select(x => new TimeInterval(x.BeginDate, x.EndDate))
                                          .ToList());

                //本次排产所占资源
                occupied.AddRange(sourcesLists.Where(x => x.SourceCode == item.Code)
                                                 .Select(x => new TimeInterval(x.BeginDate, x.EndDate))
                                                 .ToList());
                // 计算剩余时间
                List<TimeInterval> available = SubtractIntervals(devWorkDay, occupied);
                // 计算总小时数
                decimal total = (decimal)available.Sum(t => t.Tat);
                decimal totalCapacity = available.Sum(x => (Math.Floor((decimal)x.Tat / (input.UnitTat == 0 ? 1 : input.UnitTat)) * (input.UnitCapacity * item.Newness))) ?? 0; ;
                if (input.UnitTat <= total && totalCapacity >= input.Qty)
                {
                    var obj = new EffectiveDeviceDtos();
                    obj.DtDate = available.Min(x => x.Start);
                    obj.DtMaxDate = available.Max(x => x.Start);
                    obj.DevCode = item.Code!;
                    obj.DevType = item.Dtype!;
                    obj.UnitTat = input.UnitTat;
                    obj.UnitCapacity = (input.UnitCapacity * item.Newness) ?? 0;
                    obj.TotalCapacity = totalCapacity;
                    obj.Children = available.OrderBy(x => x.Start).ToList();
                    res.Add(obj);
                }
            }
            return res.ToList();
        }

        private bool HasScheduleChange(PartSchedulePlanStep newStep, PartSchedulePlanStep oldStep)
        {
            return (newStep.BeginDate != oldStep.BeginDate || newStep.EndDate != oldStep.EndDate);
        }

        /// <summary>
        /// 加载初始化数据
        /// </summary>
        /// <param name="proCode"></param>
        /// <param name="scheduleCode"></param>
        /// <returns></returns>
        /// <exception cref="InvalidOperationException"></exception>
        /// <exception cref="InvalidDataException"></exception>
        private async Task<BasProductDto> LoadInitDataAsync(string proCode, string scheduleCode)
        {
            var proModule = new BasProductDto();
            var infoModule = await _spinfoRepo.Where(x => !x.IsDeleted && x.ScheduleCode == scheduleCode).FirstOrDefaultAsync();
            if (infoModule == null)
            {
                var product = await _bProRepo.Where(x => x.Procode == proCode).FirstOrDefaultAsync();
                if (product == null)
                    throw new InvalidOperationException("没找到该产品的基本信息");

                //获取产品的产能模型
                proModule = await _proServ.GetCapacityModuleByIdAsync(product?.Id ?? 0);
                if (proModule == null)
                    throw new InvalidOperationException("基础信息中不存在该产品，请维护后再试");
            }
            else
            {
                // 添加反序列化验证
                var json = infoModule?.ProductModule
                    ?? throw new InvalidOperationException("找不到产能模型数据");

                proModule = JsonConvert.DeserializeObject<BasProductDto>(json)
                    ?? throw new InvalidDataException("产能模型数据格式错误");
            }
            return proModule;
        }

        private async Task<(
           SchedulePlanResultDto oldResult,
            List<PartSchedulePlanInfo> modules)>
            LoadInitialDataAsync(string scheduleCode)
        {
            var Modules = await _spinfoRepo.Where(x => x.ScheduleCode == scheduleCode).ToListAsync();
            var oldResult = new SchedulePlanResultDto();
            oldResult.SchedulePlan = await _spRepo.Where(x => x.Code == scheduleCode, false, false).FirstOrDefaultAsync();
            oldResult.StepLists = await _spStepRepo.Where(x => x.ScheduleCode == scheduleCode, false, false).ToListAsync();
            oldResult.MaterialLists = await _spMaterialRepo.Where(x => x.ScheduleCode == scheduleCode, false, false).ToListAsync();
            oldResult.SourcesLists = await _spSourceRepo.Where(x => x.ScheduleCode == scheduleCode, false, false).ToListAsync();

            return (oldResult, Modules);
        }

        private async Task<DateTime> PostOrderTraversal(QuerycheduleStepDto query, SchedulePlanResultDto result)
        {
            var nodes = query.Nodes;

            if (query.Nodes.IsLeaf)
            {
                await DealSchedulePlan(query, result);
                var step = result.StepLists.FirstOrDefault(x => x.StepCode == query.Nodes.StepCode);
                return step.BeginDate;
            }
            // 非叶子节点：父节点结束时间 = 子节点开始时间的最小值
            DateTime minChildStart = DateTime.MaxValue;
            foreach (var child in query.Nodes.ChildNodes)
            {
                query.Nodes = child;
                DateTime childStart = await PostOrderTraversal(query, result);
                minChildStart = childStart < minChildStart ? childStart : minChildStart;
            }
            query.Nodes = nodes;
            query.EndDate = minChildStart;
            await DealSchedulePlan(query, result);
            var nodeStep = result.StepLists.FirstOrDefault(x => x.StepCode == nodes.StepCode);
            return nodeStep.BeginDate;
        }

        /// <summary>
        /// 拖拉拽接口保存
        /// </summary>
        /// <param name="query"></param>
        /// <param name="result"></param>
        /// <param name="oldResult"></param>
        /// <param name="edit"></param>
        /// <returns></returns>
        private async Task<DateTime> PostOrderTraversalModify(QuerycheduleStepDto query, SchedulePlanResultDto result, SchedulePlanResultDto oldResult, bool edit = false)
        {
            var nodes = query.Nodes;

            if (query.Nodes.IsLeaf) 
            {
                await DealSchedulePlan(query, result, oldResult, edit);
                var step = result.StepLists.FirstOrDefault(x => x.StepCode == query.Nodes.StepCode);
                return step.BeginDate;
            }
            // 非叶子节点：父节点结束时间 = 子节点开始时间的最小值
            DateTime minChildStart = DateTime.MaxValue;
            foreach (var child in query.Nodes.ChildNodes)
            {
                query.Nodes = child;
                DateTime childStart = await PostOrderTraversal(query, result);
                minChildStart = childStart < minChildStart ? childStart : minChildStart;
            }
            query.Nodes = nodes;
            query.EndDate = minChildStart;
            await DealSchedulePlan(query, result, oldResult, edit);
            var nodeStep = result.StepLists.FirstOrDefault(x => x.StepCode == nodes.StepCode);
            return nodeStep.BeginDate;
        }

        private async Task PreOrderTraversal(
                    QuerycheduleStepDto query, SchedulePlanResultDto result)
        {
            if (query.Nodes == null)
                throw new InvalidOperationException("工序数据不能为空");

            await DealSchedulePlan(query, result);
            foreach (var child in query.Nodes.ChildNodes)
            {
                query.BeginDate = result.StepLists.FirstOrDefault(x => x.StepCode == child.PreCode).EndDate;
                query.Nodes = child;
                await PreOrderTraversal(query, result); // 子节点开始时间 = 父节点结束时间
            }
        }

        /// <summary>
        /// 数据写入
        /// </summary>
        /// <param name="proModule"></param>
        /// <param name="steps"></param>
        /// <param name="sources"></param>
        /// <param name="materials"></param>
        /// <param name="plan"></param>
        /// <returns></returns>
        private async Task<ResultJson> DataBaseDeal(
            BasProductDto proModule,
            SchedulePlanResultDto reData,
            List<PartSchedulePlanRule> rules, bool edit = false)
        {
            try
            {
                await _spSourceRepo.InsertRangeAsync(reData.SourcesLists);
                await _spStepRepo.InsertRangeAsync(reData.StepLists);
                await _spMaterialRepo.InsertRangeAsync(reData.MaterialLists);

                //排产-产能模型
                var spInfoDto = new PartSchedulePlanInfo();
                spInfoDto.Id = IdGenerater.GetNextId();
                spInfoDto.ScheduleCode = reData.SchedulePlan.Code;
                spInfoDto.PlanCode = reData.SchedulePlan.PlanCode;
                spInfoDto.ProductModule = JsonConvert.SerializeObject(proModule);
                await _spinfoRepo.InsertAsync(spInfoDto);

                //排产规则
                var scheduleRuleDtos = Mapper.Map<List<PartSchedulePlanRule>>(rules);
                scheduleRuleDtos.ForEach(o =>
                {
                    o.Id = IdGenerater.GetNextId();
                    o.ScheduleCode = reData.SchedulePlan.Code;
                });
                await _ruleRepo.InsertRangeAsync(scheduleRuleDtos);

                if (!edit)
                {
                    reData.SchedulePlan.Id = IdGenerater.GetNextId();
                    reData.SchedulePlan.PlanBeginDate = reData.StepLists.Min(x => x.BeginDate);
                    reData.SchedulePlan.PlanEndDate = reData.StepLists.Max(x => x.EndDate);
                    await _spRepo.InsertAsync(reData.SchedulePlan);
                }
                return new ResultJson("排产成功");
            }
            catch (Exception ex)
            {
                return new ResultJson(ex.Message.ToString(), null, HttpStatusCode.BadRequest);
            }
        }

        /// <summary>
        /// 清楚该排产计划的工序 资源 物料，规则等数据
        /// </summary>
        /// <param name="plan"></param>
        /// <returns></returns>
        private async Task<ResultJson> DataBaseRemoveDeal(
            SchedulePlanResultDto plan)
        {
            try
            {
                await _spSourceRepo.RemoveRangeAsync(plan.SourcesLists);

                await _spMaterialRepo.RemoveRangeAsync(plan.MaterialLists);

                await _spStepRepo.RemoveRangeAsync(plan.StepLists);

                await _spRepo.UpdateAsync(plan.SchedulePlan);

                var ruleList = await _ruleRepo.Where(x => x.ScheduleCode == plan.SchedulePlan.Code, false, false).ToListAsync();
                await _ruleRepo.RemoveRangeAsync(ruleList);

                return new ResultJson("处理成功");
            }
            catch (Exception ex)
            {
                return new ResultJson(ex.Message.ToString(), null, HttpStatusCode.BadRequest);
            }
        }

        /// <summary>
        /// 正排修改
        /// </summary>
        /// <param name="query"></param>
        /// <param name="result"></param>
        /// <param name="oldResult"></param>
        /// <returns></returns>
        /// <exception cref="InvalidOperationException"></exception>
        private async Task PreOrderTraversalEdit(
                    QuerycheduleStepDto query, SchedulePlanResultDto result, SchedulePlanResultDto oldResult)
        {
            if (query.Nodes == null)
                throw new InvalidOperationException("工序数据不能为空");
            string stepCode = query.Nodes.StepCode;
            var nowStepDto = result.StepLists.FirstOrDefault(x => x.StepCode == stepCode);
            var oldStepDto = oldResult.StepLists.FirstOrDefault(x => x.StepCode == stepCode);//调整前的计划
            if (HasScheduleChange(nowStepDto, oldStepDto))//判断开始时间和结束时间是否发生变化，为发生变化的不重排
            {
                await DealSchedulePlan(query, result, oldResult, true);
            }
            else
            {
                result.SourcesLists.AddRange(oldResult.SourcesLists.Where(x => x.StepCode == stepCode).ToList());
                result.MaterialLists.AddRange(oldResult.MaterialLists.Where(x => x.StepCode == stepCode).ToList());
            }
            foreach (var child in query.Nodes.ChildNodes)
            {
                query.BeginDate = result.StepLists.FirstOrDefault(x => x.StepCode == child.PreCode).EndDate;
                query.Nodes = child;
                await PreOrderTraversalEdit(query, result, oldResult); // 子节点开始时间 = 父节点结束时间
            }
        }

        /// <summary>
        /// 获取产能模型
        /// </summary>
        /// <param name="modules"></param>
        /// <param name="scheduleCode"></param>
        /// <param name="PlanCode"></param>
        /// <returns></returns>
        /// <exception cref="InvalidOperationException"></exception>
        /// <exception cref="InvalidDataException"></exception>
        private async Task<(BasProductDto productDto, List<PartSchedulePlanRuleDto> rules, PartPlanPending planPending)>
            ProcessCoreDataAsync(List<PartSchedulePlanInfo> modules, string scheduleCode, string PlanCode)
        {
            // 添加反序列化验证
            var json = modules.FirstOrDefault()?.ProductModule
                ?? throw new InvalidOperationException("找不到产能模型数据");

            var productDto = JsonConvert.DeserializeObject<BasProductDto>(json)
                ?? throw new InvalidDataException("产能模型数据格式错误");

            var rules = await _ruleRepo.Where(x => x.ScheduleCode == scheduleCode).ToListAsync();
            var ruleDto = Mapper.Map<List<PartSchedulePlanRuleDto>>(rules);

            var planPendings = await _planPendingRepo.Where(x => x.PlanCode == PlanCode).ToListAsync();
            var planPending = planPendings.FirstOrDefault();
            return (productDto, ruleDto, planPending);
        }

        /// <summary>
        /// 获取时间范围
        /// </summary>
        /// <param name="BeginDate"></param>
        /// <param name="EndDate"></param>
        /// <param name="DevCode"></param>
        /// <param name="devClassDtos"></param>
        /// <returns></returns>
        private async Task<List<TimeInterval>> queryDateRange(DateTime BeginDate, DateTime EndDate, string DevCode, List<BasDeviceClassDto> devClassDtos)
        {
            var workDays = devClassDtos.Where(x => x.WorkingDate >= BeginDate.Date && x.WorkingDate <= EndDate.Date && x.DevCode == DevCode).ToList();

            var devWorkDay = CalculateMergedIntervals(BeginDate, EndDate, workDays);

            return devWorkDay;
        }

        /// <summary>
        /// 根据排产计划编号 删除设备资源计划表
        /// </summary>
        /// <param name="ScheduleCode"></param>
        /// <param name="stepCode"></param>
        private void RemoveScheduleStepSource(string ScheduleCode, string stepCode)
        {
            var objs = _spSourceRepo.Where(x => x.ScheduleCode == ScheduleCode && x.StepCode == stepCode, false, false).ToList();
            _spSourceRepo.RemoveRangeAsync(objs);
        }

        /// <summary>
        /// 处理排产侏罗纪
        /// </summary>
        /// <param name="proModule"></param>
        /// <param name="rule"></param>
        /// <param name="input"></param>
        /// <returns></returns>
        private async Task<(SchedulePlanResultDto reData, ResultJson Msg)>
           SchedulePlan(BasProductDto proModule, ScheduleRule rule, PartSchedulePlanDto input)
        {
            var Msg = new ResultJson("");
            var reData = new SchedulePlanResultDto();

            // reData.SchedulePlan = Mapper.Map<PartSchedulePlan>(input);
            try
            {
                //1.获取工序工艺工序路径
                var stepTrees = await BuildProcessTree(proModule.StepItems, s => s.StepCode, s => s.PreStep);

                //2.核心逻辑处理
                DateTime dtBegin = input.BeginDate < DateTime.Now.Date ? DateTime.Now.Date : input.BeginDate;
                DateTime dtEnd = input.DtDelivery.AddDays(30);
                var res = new QuerycheduleStepDto
                {
                    BeginDate = dtBegin,
                    EndDate = dtEnd,
                    Qty = input.Qty,
                    ScheduleCode = input.Code,
                    Rule = rule,
                    ProModule = proModule
                };

                foreach (var item in stepTrees)
                {
                    res.Nodes = item;
                    if (rule.ForWardSchedule)
                    {
                        reData = await CalculateByPreOrder(res, reData);
                    }
                    else
                    {
                        res.EndDate = dtBegin;
                        res.BeginDate = DateTime.Now.Date;
                        reData = await CalculateByPostOrder(res, reData);
                    }
                }
            }
            catch (Exception ex)
            {
                Msg = new ResultJson(ex.Message.ToString(), null, HttpStatusCode.BadRequest);
            }
            reData.SchedulePlan = Mapper.Map<PartSchedulePlan>(input);
            return (reData, Msg);
        }

        /// <summary>
        /// 排产规则处理
        /// </summary>
        /// <param name="Rules"></param>
        /// <returns></returns>
        private async Task<ScheduleRule> ScheduleRuleNew(List<PartSchedulePlanRuleDto> Rules)
        {
            var rule = new ScheduleRule();
            var scheduleRules = Rules.ToDictionary(r => r.RuleType, r => r.RuleValue);

            rule.ForWardSchedule = scheduleRules.GetValueOrDefault(ScheduleRuleEnum.ScheduleFun) == ScheduleRuleScheduleFun.ForWard;

            rule.BatchOperation = scheduleRules.GetValueOrDefault(ScheduleRuleEnum.BatchSchedule) == ScheduleRuleBatchSchedule.Batch;

            rule.KittingSchedule = scheduleRules.GetValueOrDefault(ScheduleRuleEnum.KittingShcdule) == ScheduleRuleKittingShcdule.Kitting;

            rule.SourceRange = scheduleRules.GetValueOrDefault(ScheduleRuleEnum.SourceRange) == ScheduleRuleSourceRange.No;

            return rule;
        }

        /// <summary>
        /// 获取单台设备
        /// </summary>
        /// <param name="devices"></param>
        /// <param name="isForward"></param>
        /// <param name="productNum"></param>
        /// <returns></returns>
        private EffectiveDeviceDtos SelectOptimalDevice(List<EffectiveDeviceDtos> devices, bool isForward, decimal productNum)
        {
            var obj = new EffectiveDeviceDtos();
            obj = isForward
                ? devices.OrderBy(x => Math.Abs(x.TotalCapacity - productNum))
                        .ThenBy(x => x.DtDate)
                        .ThenBy(x => x.TotalCapacity)
                        .FirstOrDefault()
                : devices.OrderBy(x => Math.Abs(x.TotalCapacity - productNum))
                        .ThenByDescending(x => x.DtMaxDate)
                        .ThenBy(x => x.TotalCapacity)
                        .FirstOrDefault();
            return obj;
        }

        #endregion 私有方法
    }
}