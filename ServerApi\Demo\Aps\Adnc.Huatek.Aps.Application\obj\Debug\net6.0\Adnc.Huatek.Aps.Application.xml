<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Adnc.Huatek.Aps.Application</name>
    </assembly>
    <members>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasBomDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasBomDto.ProductCode">
            <summary>
            产品编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasBomDto.ProductName">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasBomDto.MaterialName">
            <summary>
            物料名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasBomDto.MaterialCode">
            <summary>
            物料编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasBomDto.Qty">
            <summary>
            物料数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasBomDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BomPagedDto.ProductCode">
            <summary>
            产品编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BomPagedDto.ProductName">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BomPagedDto.MaterialCode">
            <summary>
            物料编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasBomListDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasBomListDto.IdBom">
            <summary>
            BomID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasBomListDto.MaterialName">
            <summary>
            物料名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasBomListDto.MaterialCode">
            <summary>
            物料编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasBomListDto.Qty">
            <summary>
            数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasClassesDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasClassesDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasClassesDto.ShortName">
            <summary>
            简称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasClassesDto.Cname">
            <summary>
            班次名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasClassesDto.Ccode">
            <summary>
            班次编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasClassesDto.BeginTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasClassesDto.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasClassesDto.Status">
            <summary>
            启用状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasClassesDto.Duration">
            <summary>
            工作时长(h)
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasClassesDto.Qty">
            <summary>
            人员数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ClassesPagedDto.Cname">
            <summary>
            班次名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ClassesPagedDto.Ccode">
            <summary>
            班次编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ClassesPagedDto.ShortName">
            <summary>
            班次简称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ClassesPagedDto.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ClassesDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ClassesDto.Cname">
            <summary>
            班次名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ClassesDto.BeginTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ClassesDto.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ClassesDto.Duration">
            <summary>
            班次时长(h)
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ClassesDto.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ClassesDto.Qty">
            <summary>
            人员数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceCalendarDto.BeginTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceCalendarDto.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceCalendarDto.DeviceCode">
            <summary>
            设备编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceCalendarDto.DeviceName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceCalendarDto.ProductName">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceCalendarDto.ProductCode">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceCalendarDto.StationName">
            <summary>
            所属工位名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceCalendarDto.StationCode">
            <summary>
            所属工位编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceCalendarDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceCalendarResultDto.BeginTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceCalendarResultDto.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceCalendarResultDto.DeviceCode">
            <summary>
            设备编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceCalendarResultDto.DeviceName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceCalendarResultDto.DeviceWorkStatus">
            <summary>
            设备工作状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceCalendarResultDto.OrderList">
            <summary>
            订单产品信息列表
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceCalendarResultDto.StationName">
            <summary>
            所属工位名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceCalendarResultDto.StationCode">
            <summary>
            所属工位编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceCalendarResultDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceCalendarResultDto.StandardWorkTime">
            <summary>
            标准工作时长
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceCalendarResultDto.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceCalendarResultDto.Creater">
            <summary>
            创建人
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.OrderInfoItem.OrderNumber">
            <summary>
            订单号
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.OrderInfoItem.ProductName">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceClassDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceClassDto.Isdeleted">
            <summary>
            是否删除
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceClassDto.CreateBy">
            <summary>
            创建人
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceClassDto.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceClassDto.ModifyBy">
            <summary>
            更新人
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceClassDto.ModifyTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceClassDto.CreatedName">
            <summary>
            创建人名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceClassDto.ModifyName">
            <summary>
            修改人名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceClassDto.DevCode">
            <summary>
            设备编号
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceClassDto.DevName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceClassDto.ClassCode">
            <summary>
            班次编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceClassDto.ClassName">
            <summary>
            班次名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceClassDto.ClassColor">
            <summary>
            班次颜色
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceClassDto.BeginTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceClassDto.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceClassDto.WorkingDate">
            <summary>
            工作时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceClassPagedDto.DevCode">
            <summary>
            设备编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceClassPagedDto.Dtype">
            <summary>
            设备类型
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceClassPagedDto.ClassCode">
            <summary>
            班次
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceClassPagedDto.CurrentMonth">
            <summary>
            当前月
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceDto.Isdeleted">
            <summary>
            是否删除
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceDto.CreateBy">
            <summary>
            创建人
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceDto.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceDto.ModifyBy">
            <summary>
            更新人
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceDto.ModifyTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceDto.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceDto.Code">
            <summary>
            设备编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceDto.Name">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceDto.CreatedName">
            <summary>
            创建人名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceDto.ModifyName">
            <summary>
            修改人名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceDto.Capacity">
            <summary>
            产能
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceDto.DtypeDis">
            <summary>
            设备类型
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDeviceDto.Newness">
            <summary>
            设备新度
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDevicePagedDto.Name">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDevicePagedDto.Code">
            <summary>
            设备编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDevicePagedDto.Dtype">
            <summary>
            设备编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasDevicePagedDto.Status">
            <summary>
            设备编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.QueryEffrctiveDevice.Qty">
            <summary>
            资源大类
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.QueryEffrctiveDevice.ScheduleCode">
            <summary>
            资源大类
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.QueryEffrctiveDevice.SourceType">
            <summary>
            资源大类
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.QueryEffrctiveDevice.DevType">
            <summary>
            资源类型
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.QueryEffrctiveDevice.UnitTat">
            <summary>
            资源单位工作时长
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.QueryEffrctiveDevice.UnitCapacity">
            <summary>
            资源单位产能
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.QueryEffrctiveDevice.Begin">
            <summary>
            查询范围的开始时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.QueryEffrctiveDevice.End">
            <summary>
            查询范围的结束时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasHolidayDto.CurrentDate">
            <summary>
            日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasHolidayDto.IsHoliday">
            <summary>
            是否工作日
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasHolidayDto.IsPlaned">
            <summary>
            是否排产
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasHolidayDto.WeekName">
            <summary>
            星期名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.HolidayPagedDto.Start">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.HolidayPagedDto.End">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.HolidayPagedDto.IsHoliday">
            <summary>
            是否工作日
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.HolidayPagedDto.IsPlaned">
            <summary>
            是否排产
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasLineDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasLineDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasLineDto.Lname">
            <summary>
            产线名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasLineDto.Lcode">
            <summary>
            产线编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.LinePagedDto.Lname">
            <summary>
            产线名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.LinePagedDto.Lcode">
            <summary>
            产线编码
            </summary>
        </member>
        <member name="T:Adnc.Huatek.Aps.Application.Dtos.ProductLineDto">
            <summary>
            查找产品对应的产线
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ProductLineDto.Lname">
            <summary>
            产线名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ProductLineDto.Lcode">
            <summary>
            产线编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasLineProductRelationDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasLineProductRelationDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasLineProductRelationDto.IdLine">
            <summary>
            产线ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasLineProductRelationDto.IdProduct">
            <summary>
            产品id
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasMaterialDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasMaterialDto.Isdeleted">
            <summary>
            是否删除
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasMaterialDto.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasMaterialDto.StatusDis">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasMaterialDto.Code">
            <summary>
            物料编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasMaterialDto.Name">
            <summary>
            物料名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasMaterialDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasMaterialDto.Mtype">
            <summary>
            物料类型
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasMaterialDto.Unit">
            <summary>
            单位
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasMaterialPagedDto.Name">
            <summary>
            工位名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasMaterialPagedDto.Code">
            <summary>
            工位编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasMaterialPagedDto.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasMaterialPagedDto.Mtype">
            <summary>
            物料类型
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasMaterialPagedDto.Unit">
            <summary>
            单位
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasNumberManagementDto.CreateBy">
            <summary>
            创建人
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasNumberManagementDto.CreateTime">
            <summary>
            创建时间/注册时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasNumberManagementDto.ModifyBy">
            <summary>
            最后更新人
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasNumberManagementDto.ModifyTime">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasNumberManagementDto.IsDeleted">
            <summary>
            是否删除标识
            </summary>
        </member>
        <member name="T:Adnc.Huatek.Aps.Application.Dtos.BasOrderDto">
            <summary>
            客户订单
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasOrderDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasOrderDto.IdDetail">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasOrderDto.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasOrderDto.Status">
            <summary>
            生产状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasOrderDto.OrderNumber">
            <summary>
            订单编号
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasOrderDto.UserName">
            <summary>
            客户
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasOrderDto.DeliveryDate">
            <summary>
            交付日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasOrderDto.ProductName">
            <summary>
             产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasOrderDto.ProductCode">
            <summary>
             产品编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasOrderDto.Quantity">
            <summary>
            产品数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasOrderDto.SaleNumber">
            <summary>
            销售单号
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasOrderDto.PendingNum">
            <summary>
            待排产产品数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasOrderPagedDto.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasOrderPagedDto.Status">
            <summary>
            生产状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasOrderPagedDto.SaleNumber">
            <summary>
            销售单号
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasOrderPagedDto.OrderNumber">
            <summary>
            订单编号
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasOrderPagedDto.UserName">
            <summary>
            客户
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasOrderPagedDto.ProductCode">
            <summary>
             产品编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasOrderPagedDto.ProductName">
            <summary>
             产品名称
            </summary>
        </member>
        <member name="T:Adnc.Huatek.Aps.Application.Dtos.FinishedOrders">
            <summary>
            批量更新订单状态为完成
            </summary>
        </member>
        <member name="T:Adnc.Huatek.Aps.Application.Dtos.ImportOrderDto">
            <summary>
            导入订单
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductDto.Remark">
            <summary>
            备注
            </summary>
            
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductDto.Proname">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductDto.Procode">
            <summary>
            产品编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductDto.TCode">
            <summary>
            工艺编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductDto.TName">
            <summary>
            工艺编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductDto.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductDto.CreatName">
            <summary>
            创建名
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductDto.ModifyName">
            <summary>
            更新人
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductDto.CreateTime">
            <summary>
            创建时间/注册时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductDto.ModifyBy">
            <summary>
            最后更新人
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductDto.ModifyTime">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductBomDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductBomDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductBomDto.Proname">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductBomDto.Procode">
            <summary>
            产品编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductBomDto.TCode">
            <summary>
            工艺编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductBomDto.TName">
            <summary>
            工艺编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductBomDto.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductBomDto.CreatName">
            <summary>
            创建名
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductBomDto.ModifyName">
            <summary>
            更新人
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductBomDto.CreateTime">
            <summary>
            创建时间/注册时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductBomDto.ModifyBy">
            <summary>
            最后更新人
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductBomDto.ModifyTime">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ProductPagedDto.Proname">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ProductPagedDto.Procode">
            <summary>
            产品编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ProductPagedDto.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ReloadBasProcuct.PlanCode">
            <summary>
            计划编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ReloadBasProcuct.SchedulCode">
            <summary>
            排产编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStandardCapacityDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStandardCapacityDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStandardCapacityDto.Status">
            <summary>
            启用状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStandardCapacityDto.SolutionName">
            <summary>
            产能方案
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStandardCapacityDto.ProCode">
            <summary>
            产品编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStandardCapacityDto.StepCode">
            <summary>
            工序编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStandardCapacityDto.StepName">
            <summary>
            工序名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStandardCapacityDto.StationCode">
            <summary>
            工位编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStandardCapacityDto.StationName">
            <summary>
            工位名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStandardCapacityDto.LineCode">
            <summary>
            产线编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStandardCapacityDto.LineName">
            <summary>
            产线名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStandardCapacityDto.DeviceCode">
            <summary>
            设备编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStandardCapacityDto.DeviceName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStandardCapacityDto.Capacity">
            <summary>
            产能
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStandardCapacityDto.WorkUnitTime">
            <summary>
            单位工作时长
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStandardCapacityDto.StandardWorkTime">
            <summary>
            标准工作时长
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStandardCapacityDto.MinWorkingDuration">
            <summary>
            工序最短工作时长
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStandardCapacityDto.CreateBy">
            <summary>
            创建人ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStandardCapacityDto.CreateTime">
            <summary>
            创建时间/注册时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStandardCapacityDto.ModifyBy">
            <summary>
            最后更新人
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStandardCapacityDto.ModifyTime">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStandardCapacityDto.Sort">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.SchedulStatus.Status">
            <summary>
            排产状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.SchedulStatus.SchedulCode">
            <summary>
            排产编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.SchedulStatusNew.Status">
            <summary>
            排产状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.SchedulStatusNew.SchedulCode">
            <summary>
            排产编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationDto.ProCode">
            <summary>
            产品编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationDto.TecCode">
            <summary>
            工艺编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationDto.StepCode">
            <summary>
            工序编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationDto.StepName">
            <summary>
            工序名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationDto.Sort">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationDto.PreStep">
            <summary>
            前置工序
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationDto.PreStepName">
            <summary>
            前置工序名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationDto.IntervalTime">
            <summary>
            前置工序间隔时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationDto.StepTime">
            <summary>
            单位工作时长
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationDto.Tat">
            <summary>
            单位工作时长
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationDto.IsKey">
            <summary>
            是否关键工序
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationDto.CreateBy">
            <summary>
            创建人ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationDto.CreateTime">
            <summary>
            创建时间/注册时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationDto.ModifyBy">
            <summary>
            最后更新人
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationDto.ModifyTime">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="F:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationDto._materialsIn">
            <summary>
            输入物料
            </summary>
        </member>
        <member name="F:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationDto._materialsOut">
            <summary>
            输出物料
            </summary>
        </member>
        <member name="F:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationDto._mainItems">
            <summary>
            设备
            </summary>
        </member>
        <member name="F:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationDto._assistItems">
            <summary>
            设备
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationMaterialDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationMaterialDto.IsEdit">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationMaterialDto.MainId">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationMaterialDto.MaterialType">
            <summary>
            物料类型 in out
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationMaterialDto.MaterialCode">
            <summary>
            物料编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationMaterialDto.MaterialName">
            <summary>
            物料名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationMaterialDto.Qty">
            <summary>
            数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationMaterialDto.BatchQty">
            <summary>
            结批量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationMaterialDto.CreateBy">
            <summary>
            创建人ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationMaterialDto.CreateTime">
            <summary>
            创建时间/注册时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationMaterialDto.ModifyBy">
            <summary>
            最后更新人
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationMaterialDto.ModifyTime">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationSourceDto.IsEdit">
            <summary>
            是否编辑
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationSourceDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationSourceDto.MainId">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationSourceDto.SourceType">
            <summary>
            资源大类
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationSourceDto.SourceTypeName">
            <summary>
            资源大类名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationSourceDto.DeviceType">
            <summary>
            资源小类
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationSourceDto.DeviceTypeName">
            <summary>
            物料类型设备类型
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationSourceDto.MainSource">
            <summary>
            是否主资源
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationSourceDto.IsCapacity">
            <summary>
            是否考虑产能
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationSourceDto.Tat">
            <summary>
            单位工作时长
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationSourceDto.Capacity">
            <summary>
            单位产能
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationSourceDto.CreateBy">
            <summary>
            创建人ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationSourceDto.CreateTime">
            <summary>
            创建时间/注册时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationSourceDto.ModifyBy">
            <summary>
            最后更新人
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationSourceDto.ModifyTime">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductTechnologyRelationDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductTechnologyRelationDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductTechnologyRelationDto.IdTechnology">
            <summary>
            工艺路线ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductTechnologyRelationDto.IdProduct">
            <summary>
            产品id
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductTechnologyRelationDto.TecCode">
            <summary>
            工艺编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductTechnologyRelationDto.TecName">
            <summary>
            工艺名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductTechnologyRelationDto.CreateBy">
            <summary>
            创建人ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductTechnologyRelationDto.CreateTime">
            <summary>
            创建时间/注册时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductTechnologyRelationDto.ModifyBy">
            <summary>
            最后更新人
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductTechnologyRelationDto.ModifyTime">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductTechnologyRelationDto.Status">
            <summary>
            启用状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasRelationDeviceDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasRelationDeviceDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasRelationDeviceDto.IdRelation">
            <summary>
            工序id
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasRelationDeviceDto.IdDevice">
            <summary>
            工序编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasRelationDeviceDto.Capacity">
            <summary>
            创建人姓名
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasRelationDeviceDto.ModifyName">
            <summary>
            更新人
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasRelationDeviceDto.CreateTime">
            <summary>
            创建时间/注册时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasRelationDeviceDto.ModifyBy">
            <summary>
            最后更新人
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasRelationDeviceDto.ModifyTime">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasRelationDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasRelationDto.IsDeleted">
            <summary>
            是否删除
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasRelationDto.ModifyName">
            <summary>
            更新人
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasRelationDto.CreateTime">
            <summary>
            创建时间/注册时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasRelationDto.ModifyBy">
            <summary>
            最后更新人
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasRelationDto.ModifyTime">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasRelationDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasRelationDto.IdStep">
            <summary>
            工序id
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasRelationDto.StepCode">
            <summary>
            工序编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.RelationPagedDto.IdStep">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStationDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStationDto.Isdeleted">
            <summary>
            是否删除
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStationDto.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStationDto.Code">
            <summary>
            工位编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStationDto.Name">
            <summary>
            工位名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStationDto.Worksjopid">
            <summary>
            所属车间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStationDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStationDto.WorksjopName">
            <summary>
            车间名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStationDto.DeviceCode">
            <summary>
            设备编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStationDto.Linecode">
            <summary>
            产线code
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStationDto.DeviceName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStationDto.LineName">
            <summary>
            产线名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStationDto.Duration">
            <summary>
            标准工作时长(h)
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStationDto.Classes">
            <summary>
            工位中设置的班次列表
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStationPagedDto.Name">
            <summary>
            工位名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStationPagedDto.Code">
            <summary>
            工位编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStationPagedDto.DevName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStationPagedDto.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStationPagedDto.LineCode">
            <summary>
            产线编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStationSearchDto.LineCode">
            <summary>
            产线编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStepDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStepDto.Remark">
            <summary>
            备注
            </summary>
            
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStepDto.Name">
            <summary>
            工序名称
            </summary>
            
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStepDto.Code">
             <summary>
             工序编码S
             </summary>
            
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStepDto.StepTime">
            <summary>
            工序最短时长(小时)
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStepDto.Tat">
            <summary>
            工序标准时长(小时)
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStepDto.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.StepPagedDto.Name">
            <summary>
            工序名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.StepPagedDto.Code">
            <summary>
            工序编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.StepPagedDto.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ChangeStatusDto.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ChangeStatusDto.CodeList">
            <summary>
            编码列表
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStepMaterialRelationDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStepMaterialRelationDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStepMaterialRelationDto.ProCode">
            <summary>
            产品编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStepMaterialRelationDto.TCode">
            <summary>
            工艺编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStepMaterialRelationDto.StepCode">
            <summary>
            工序编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStepMaterialRelationDto.MaterialCode">
            <summary>
            物料编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStepMaterialRelationDto.MaterialName">
            <summary>
            物料名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStepMaterialRelationDto.Qty">
            <summary>
            数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStepMaterialRelationDto.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStepMaterialRelationDto.CreateBy">
            <summary>
            创建人ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStepMaterialRelationDto.CreateTime">
            <summary>
            创建时间/注册时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStepMaterialRelationDto.ModifyBy">
            <summary>
            最后更新人
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasStepMaterialRelationDto.ModifyTime">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasTechnologyDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasTechnologyDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasTechnologyDto.Tname">
            <summary>
            工序名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasTechnologyDto.Tcode">
            <summary>
            工序编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasTechnologyDto.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasTechnologyDto.CreatName">
            <summary>
            创建名
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasTechnologyDto.ModifyName">
            <summary>
            更新人
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasTechnologyDto.CreateTime">
            <summary>
            创建时间/注册时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasTechnologyDto.ModifyBy">
            <summary>
            最后更新人
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasTechnologyDto.ModifyTime">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.TechnologyPagedDto.Tname">
            <summary>
            工序名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.TechnologyPagedDto.Tcode">
            <summary>
            工序编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.TechnologyPagedDto.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasTechnologyStepRelationDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasTechnologyStepRelationDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasTechnologyStepRelationDto.IdTechnology">
            <summary>
            工艺路线ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasTechnologyStepRelationDto.IdStep">
            <summary>
            工序路线id
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasTechnologyStepRelationDto.PreStep">
            <summary>
            前置工序编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasTechnologyStepRelationDto.PreStepName">
            <summary>
            前置工序名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasTechnologyStepRelationDto.IntervalTime">
            <summary>
            前置工序间隔时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasTechnologyStepRelationDto.IsKey">
            <summary>
            是否关键工序
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasTechnologyStepRelationDto.Sort">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasTechnologyStepRelationDto.RelationType">
            <summary>
            与下一道工序的关系
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasTechnologyStepRelationDto.CreateBy">
            <summary>
            创建人ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasTechnologyStepRelationDto.CreateTime">
            <summary>
            创建时间/注册时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasTechnologyStepRelationDto.ModifyBy">
            <summary>
            最后更新人
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasTechnologyStepRelationDto.ModifyTime">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasWorksjopDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasWorksjopDto.Isdeleted">
            <summary>
            是否删除
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasWorksjopDto.Createdby">
            <summary>
            创建人
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasWorksjopDto.Createdtime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasWorksjopDto.Createdname">
            <summary>
            创建人姓名
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasWorksjopDto.Modifyby">
            <summary>
            更新人
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasWorksjopDto.Modifytime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasWorksjopDto.Modifyname">
            <summary>
            更新人姓名
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasWorksjopDto.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasWorksjopDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasWorksjopDto.Code">
            <summary>
            车间编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasWorksjopDto.Name">
            <summary>
            车间名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.HomeOrderNumDto.status">
            <summary>
            订单状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.HomeOrderNumDto.num">
            <summary>
            数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.HomeOrderNumByDateDto.Orderdate">
            <summary>
            订单创建时间(月份)
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.HomeOrderNumByDateDto.Num">
            <summary>
            数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanNumByDateDto.PlanDate">
            <summary>
            计划创建时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanNumByDateDto.PlanNum">
            <summary>
            生产计划数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanNumByDateDto.SchedulingNum">
            <summary>
            已排产数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.HomeDeviceNumDto.Status">
             <summary>
            设备状态
             </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.HomeDeviceNumDto.Num">
            <summary>
            数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.InputParamDto.ServiceUrl">
            <summary>
            接口路径
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.InputParamDto.Param">
            <summary>
            参数
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.InputParamDto.HttpMethod">
            <summary>
            HTTP请求方法(GET/POST/PUT/DELETE等)，默认为POST
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.MaterialWarningDto.PlanCode">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.MaterialWarningDto.StepCode">
            <summary>
            物料编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.MaterialWarningDto.Mcode">
            <summary>
            物料数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.MaterialWarningDto.Mname">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.MaterialWarningChartDto.Qty">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.MaterialWarningChartDto.linepoints">
            <summary>
            剩余QTY
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.MaterialWarningLineDto.LessQty">
            <summary>
            剩余QTY
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.MaterialWarningReturnDto.Mcode">
            <summary>
            物料数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.MaterialWarningPagedDto.Mcode">
            <summary>
            产品编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.MaterialWarningPagedDto.Mname">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.MaterialWarningPagedDto.dtBegin">
            <summary>
            产品编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.MaterialWarningPagedDto.dtEnd">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartDeviceStateDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartDeviceStateDto.Isdeleted">
            <summary>
            是否删除
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartDeviceStateDto.CreateBy">
            <summary>
            创建人
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartDeviceStateDto.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartDeviceStateDto.ModifyBy">
            <summary>
            更新人
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartDeviceStateDto.ModifyTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartDeviceStateDto.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartDeviceStateDto.StatusDis">
            <summary>
            状态
            </summary>
            
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartDeviceStateDto.Dtype">
            <summary>
            设备类型
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartDeviceStateDto.DtypeDis">
            <summary>
            设备类型编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartDeviceStateDto.DevCode">
            <summary>
            设备编码
            </summary>
            
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartDeviceStateDto.DevName">
            <summary>
            设备名称
            </summary>
            
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartDeviceStateDto.BeginTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartDeviceStateDto.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartDeviceStateDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartDeviceStateDto.CreatedName">
            <summary>
            创建人名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartDeviceStateDto.ModifyName">
            <summary>
            修改人名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartOrderDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartOrderDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartOrderDto.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartOrderDto.CreatName">
            <summary>
            创建名
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartOrderDto.ModifyName">
            <summary>
            更新人
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartOrderDto.CreateTime">
            <summary>
            创建时间/注册时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartOrderDto.ModifyBy">
            <summary>
            最后更新人
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartOrderDto.ModifyTime">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartOrderMaterialDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartOrderMaterialDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartOrderMaterialDto.IdMaterial">
            <summary>
            BomID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartOrderMaterialDto.CreateBy">
            <summary>
            创建人ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartOrderMaterialDto.CreateTime">
            <summary>
            创建时间/注册时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartOrderMaterialDto.ModifyBy">
            <summary>
            最后更新人
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartOrderMaterialDto.ModifyTime">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartOrderStepDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartOrderStepDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartOrderStepDto.IdStep">
            <summary>
            BomID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartOrderStepDto.CreateBy">
            <summary>
            创建人ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartOrderStepDto.CreateTime">
            <summary>
            创建时间/注册时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartOrderStepDto.ModifyBy">
            <summary>
            最后更新人
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartOrderStepDto.ModifyTime">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="T:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingDto">
            <summary>
            待排产计划
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingDto.IdDetail">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingDto.PlanCode">
            <summary>
            计划编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingDto.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingDto.PriorityDis">
            <summary>
            优先级
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingDto.Status">
            <summary>
            生产状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingDto.OrderId">
            <summary>
            订单id
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingDto.OrderNumber">
            <summary>
            订单编号
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingDto.UserName">
            <summary>
            客户
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingDto.DeliveryDate">
            <summary>
            交付日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingDto.Qty">
            <summary>
            计划排产数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingDto.ProductQty">
            <summary>
            产品实际数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingDto.ProductName">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingDto.ProductCode">
            <summary>
            产品编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingDto.TName">
            <summary>
            工艺名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingDto.ProductList">
            <summary>
            产品列表
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingResultDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingResultDto.PlanCode">
            <summary>
            计划编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingResultDto.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingResultDto.Status">
            <summary>
            生产状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingResultDto.StatusDis">
            <summary>
            生产状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingResultDto.OrderId">
            <summary>
            订单id
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingResultDto.OrderNumber">
            <summary>
            订单编号
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingResultDto.UserName">
            <summary>
            客户
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingResultDto.DeliveryDate">
            <summary>
            交付日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingResultDto.Qty">
            <summary>
            计划排产数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingResultDto.ProductQty">
            <summary>
            产品实际数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingResultDto.ProductName">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingResultDto.ProductCode">
            <summary>
            产品编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingResultDto.TName">
            <summary>
            工艺名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingResultDto.ProductList">
            <summary>
            产品列表
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasOrderProductDto.ProductName">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasOrderProductDto.ProductCode">
            <summary>
            产品编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasOrderProductDto.Qty">
            <summary>
            订单产品待排产数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingPagedDto.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingPagedDto.Status">
            <summary>
            生产状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingPagedDto.OrderNumber">
            <summary>
            订单编号
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingPagedDto.UserName">
            <summary>
            客户
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingPagedDto.ProductCode">
            <summary>
             产品编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingPagedDto.ProductName">
            <summary>
             产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.OrderDetailDto.Status">
            <summary>
            生产状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.OrderDetailDto.OrderNumber">
            <summary>
            订单编号
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.OrderDetailDto.DeliveryDate">
            <summary>
            交付日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.OrderDetailDto.UserName">
            <summary>
            客户
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.OrderDetailDto.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.OrderDetailDto.PlanStartDate">
            <summary>
            计划开始日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.OrderDetailDto.PlanEndDate">
            <summary>
            计划结束日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.OrderDetailDto.MaterialList">
            <summary>
            物料列表
            </summary>
        </member>
        <member name="T:Adnc.Huatek.Aps.Application.Dtos.BasProductMaterialDto">
            <summary>
            订单物料BOM
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductMaterialDto.ProductName">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasProductMaterialDto.Materials">
            <summary>
            物料BOM
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.MaterialDto.MaterialName">
            <summary>
            物料编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.MaterialDto.Qty">
            <summary>
            数量
            </summary>
        </member>
        <member name="T:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanDto">
            <summary>
            生产计划
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanDto.Id">
            <summary>
            排产计划编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanDto.Code">
            <summary>
            排产计划编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanDto.PlanCode">
            <summary>
            生产计划编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanDto.ProductId">
            <summary>
            产品
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanDto.Tcode">
            <summary>
            产品
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanDto.Tname">
            <summary>
            产品
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanDto.ProCode">
            <summary>
            产品
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanDto.ProName">
            <summary>
            产品
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanDto.UserName">
            <summary>
            产品
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanDto.OrderNum">
            <summary>
            产品
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanDto.SourceConflict">
            <summary>
            资源冲突结果
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanDto.Qty">
            <summary>
            生产数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanDto.BeginDate">
            <summary>
            开始排产时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanDto.DtDelivery">
            <summary>
            delivery time
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanDto.PlanBeginDate">
            <summary>
            排产计划编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanDto.PlanEndDate">
            <summary>
            生产计划编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanDto.State">
            <summary>
            计划状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanDto.StateDis">
            <summary>
            计划状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanDto.ScheduleType">
            <summary>
            来源
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanDto.ScheduleTypeDis">
            <summary>
            来源
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanDto.Rules">
            <summary>
            排产规则
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulPlanPageDto.OrderNumber">
            <summary>
            订单编号
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulPlanPageDto.PlanCode">
            <summary>
            计划编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulPlanPageDto.ScheduleCode">
            <summary>
            排产编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulPlanPageDto.ProductName">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.QuerySourceConflict.ScheduleCode">
            <summary>
            排产编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.QuerySourceConflict.PlanCode">
            <summary>
            计划编码
            </summary>
        </member>
        <member name="T:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanMaterialDto">
            <summary>
            生产计划
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanMaterialDto.Id">
            <summary>
            排产计划编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanMaterialDto.ScheduleCode">
            <summary>
            排产计划编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanMaterialDto.StepCode">
            <summary>
            生产计划编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanMaterialDto.Mcode">
            <summary>
            产品
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanMaterialDto.Mname">
            <summary>
            生产数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanMaterialDto.NeedDate">
            <summary>
            开始排产时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanMaterialDto.Qty">
            <summary>
            delivery time
            </summary>
        </member>
        <member name="T:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanRuleDto">
            <summary>
            生产计划
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanRuleDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanRuleDto.RuleType">
            <summary>
            规则类型
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanRuleDto.RuleTypeName">
            <summary>
            规则类型
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanRuleDto.RuleValue">
            <summary>
            规则值
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanRuleDto.RuleValueName">
            <summary>
            规则值
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanRuleDto.ScheduleCode">
            <summary>
            排产计划编码
            </summary>
        </member>
        <member name="T:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanSourceDto">
            <summary>
            生产计划
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanSourceDto.Id">
            <summary>
            
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanSourceDto.ScheduleCode">
            <summary>
            排产计划编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanSourceDto.StepCode">
            <summary>
            工序编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanSourceDto.SourceType">
            <summary>
            资源大类
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanSourceDto.SourceTypeName">
            <summary>
            资源大类名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanSourceDto.DevType">
            <summary>
            资源类型
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanSourceDto.DevTypeName">
            <summary>
            资源类型
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanSourceDto.SourceCode">
            <summary>
            资源编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanSourceDto.SourceName">
            <summary>
            资源编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanSourceDto.BeginDate">
            <summary>
            开始日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanSourceDto.EndDate">
            <summary>
            结束日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanSourceDto.Tat">
            <summary>
            总工作时长工时
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanSourceDto.UnitTat">
            <summary>
            单位工作时长
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanSourceDto.UnitCapacity">
            <summary>
            单位产能
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanSourceDto.TotalCapacity">
            <summary>
            总产能
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanSourceDto.IsEdit">
            <summary>
            是否人为调整
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulPlanSourcePageDto.DevCode">
            <summary>
            设备编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulPlanSourcePageDto.DevName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulPlanSourcePageDto.Dtype">
            <summary>
            设备名称
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulPlanSourcePageDto.BeginDate" -->
        <!-- Badly formed XML comment ignored for member "P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulPlanSourcePageDto.EndDate" -->
        <member name="T:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanStepDto">
            <summary>
            生产计划
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanStepDto.Id">
            <summary>
            
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanStepDto.ScheduleCode">
            <summary>
            排产计划Code
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanStepDto.StepCode">
            <summary>
            工序编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanStepDto.PreCode">
            <summary>
            前置工序
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanStepDto.ReportDate">
            <summary>
            报工时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanStepDto.BeginDate">
            <summary>
            开始日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanStepDto.EndDate">
            <summary>
            结束日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanStepDto.Tat">
            <summary>
            消耗工时;用产能模型中考虑产能的资源的单元工作时长计算消耗时长
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanStepDto.ScheduleType">
            <summary>
            来源
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanStepDto.ScheduleTypeDis">
            <summary>
            来源
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanStepReportDto.ScheduleCode">
            <summary>
            排产计划Code
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanStepReportDto.StepCode">
            <summary>
            工序编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDetailsDto.Procode">
            <summary>
            产品编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasOrderInfo.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasOrderInfo.Status">
            <summary>
            生产状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasOrderInfo.OrderNumber">
            <summary>
            订单编号
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasOrderInfo.UserName">
            <summary>
            客户
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasOrderInfo.DeliveryDate">
            <summary>
            交付日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasOrderInfo.ProductName">
            <summary>
             产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasOrderInfo.ProductCode">
            <summary>
             产品编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasOrderInfo.Quantity">
            <summary>
            产品数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.BasOrderInfo.SaleNumber">
            <summary>
            销售单号
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDetailsResultDto.BasOrderInfo">
            <summary>
            定单产品基本信息
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulGanttDto.DeliveryDate">
            <summary>
            交付日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulGanttDto.PlanStart">
            <summary>
            计划开始日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulGanttDto.PlanEnd">
            <summary>
            计划结束日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ProductionSchedulGanttDto.OrderNumber">
            <summary>
            订单编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ProductionSchedulGanttDto.ProductCode">
            <summary>
            产品编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ProductionSchedulGanttDto.ScheduleCode">
            <summary>
            排产编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ProductionSchedulGanttDto.ScheduleStatus">
            <summary>
            排产状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ProductionSchedulGanttDto.BeginTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ProductionSchedulGanttDto.EndTime">
            <summary>
            截止时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.OrderProdcutGanttDto.OrderNumber">
            <summary>
            订单编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.OrderProdcutGanttDto.ProductCode">
            <summary>
            产品编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulGanttResultDto.OrderNumber">
            <summary>
            订单编号
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulGanttResultDto.ScheduleItems">
            <summary>
            
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ProductScheduleProductItem.ProductName">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ProductScheduleProductItem.ScheduleCode">
            <summary>
            排产编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ProductScheduleProductItem.ScheduleStatus">
            <summary>
            排产状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulData.OrderNumber">
            <summary>
            订单编号
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulData.ProductName">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulData.ProductCode">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulData.StepName">
            <summary>
            工序名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulData.StepCode">
            <summary>
            工序编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulData.PlanCode">
            <summary>
            工序编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulData.SchedulDate">
            <summary>
            开始日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulData.PlanStart">
            <summary>
            开始日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulData.PlanEnd">
            <summary>
            开始日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulData.BeginTime">
            <summary>
            开始日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulData.EndTime">
            <summary>
            结束日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulData.DeliveryDate">
            <summary>
            交付日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulData.ScheduleStatus">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulData.StepSort">
            <summary>
            工序排序码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulData.TecStepCode">
            <summary>
            工序编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDataResultDto.OrderNumber">
            <summary>
            订单编号
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDataResultDto.ProductName">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDataResultDto.ProductCode">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDataResultDto.StepName">
            <summary>
            工序名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDataResultDto.StepCode">
            <summary>
            工序编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDataResultDto.PlanCode">
            <summary>
            工序编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDataResultDto.SchedulDate">
            <summary>
            开始日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDataResultDto.PlanStart">
            <summary>
            开始日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDataResultDto.PlanEnd">
            <summary>
            开始日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDataResultDto.BeginTime">
            <summary>
            开始日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDataResultDto.EndTime">
            <summary>
            结束日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDataResultDto.DeliveryDate">
            <summary>
            交付日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDataResultDto.ScheduleStatus">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ProductScheduleGanttItem.StepName">
            <summary>
            工序名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ProductScheduleGanttItem.StepCode">
            <summary>
            工序编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ProductScheduleGanttItem.BeginTime">
            <summary>
            开始日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ProductScheduleGanttItem.EndTime">
            <summary>
            结束日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ProductScheduleGanttItem.DateList">
            <summary>
            日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulGanttNewResultDto.OrderNumber">
            <summary>
            订单编号
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulGanttNewResultDto.ProductName">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulGanttNewResultDto.ProductCount">
            <summary>
            产品数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulGanttNewResultDto.ScheduleCount">
            <summary>
            已排产数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulGanttNewResultDto.ScheduleCode">
            <summary>
            排产编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulGanttNewResultDto.PlanCode">
            <summary>
            计划编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulGanttNewResultDto.ScheduleStatus">
            <summary>
            排产状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulGanttNewResultDto.DeliveryDate">
            <summary>
            交付日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulGanttNewResultDto.BeginTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulGanttNewResultDto.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulGanttForOrderDto.OrderNumber">
            <summary>
            订单编号
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulGanttForOrderDto.ProductName">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulGanttForOrderDto.DeliveryDate">
            <summary>
            交付日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulGanttForOrderDto.BeginTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulGanttForOrderDto.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.GanttDate.StepCode">
            <summary>
            工序编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.GanttDate.BeginTime">
            <summary>
            开始日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.GanttDate.EndTime">
            <summary>
            结束日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ProductionSchedulDeviceGanttResultDto.OrderNumber">
            <summary>
            订单编号
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ProductionSchedulDeviceGanttResultDto.ScheduleCode">
            <summary>
            排产编号
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ProductionSchedulDeviceGanttResultDto.ProductName">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ProductionSchedulDeviceGanttResultDto.BeginTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ProductionSchedulDeviceGanttResultDto.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ProductionSchedulDeviceGanttResultDto.DeviceItems">
            <summary>
            设备序列
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ProductScheduleDeviceGanttItem.DeviceName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ProductScheduleDeviceGanttItem.DeviceCode">
            <summary>
            设备编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ProductScheduleDeviceGanttItem.DateList">
            <summary>
            日期序列
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.DeviceGanttDate.StepName">
            <summary>
            工序名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ScheduleDeviceGanttItem.DeviceName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ScheduleDeviceGanttItem.StepName">
            <summary>
            工序名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ScheduleDeviceGanttItem.BeginTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.ScheduleDeviceGanttItem.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulGanttParentItem.StepSort">
            <summary>
            工序排序码
            </summary>
        </member>
        <member name="T:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDto">
            <summary>
            生产计划
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDto.PlanCode">
            <summary>
            计划编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDto.SchedulCode">
            <summary>
            排产编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDto.PlanStart">
            <summary>
            计划开始日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDto.PlanEnd">
            <summary>
            计划结束日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDto.Sorts">
            <summary>
            排序方式
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDto.PreRule">
            <summary>
            前置规则
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDto.LCode">
            <summary>
            产线编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDto.LName">
            <summary>
            产线名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDto.Status">
            <summary>
            计划状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDto.PlanType">
            <summary>
            计划类型
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDto.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDto.OrderNumber">
            <summary>
            订单编号
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDto.UserName">
            <summary>
            客户
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDto.DeliveryDate">
            <summary>
            交付日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDto.ProductCode">
            <summary>
            产品编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDto.ProductName">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDto.Qty">
            <summary>
            排产数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulResultDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulResultDto.PlanCode">
            <summary>
            计划编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulResultDto.SchedulCode">
            <summary>
            排产编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulResultDto.PlanStart">
            <summary>
            计划开始日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulResultDto.PlanEnd">
            <summary>
            计划结束日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulResultDto.Sorts">
            <summary>
            排序方式
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulResultDto.PreRule">
            <summary>
            前置规则
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulResultDto.LCode">
            <summary>
            产线编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulResultDto.LName">
            <summary>
            产线名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulResultDto.Status">
            <summary>
            计划状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulResultDto.PlanType">
            <summary>
            计划类型
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulResultDto.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulResultDto.OrderNumber">
            <summary>
            订单编号
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulResultDto.UserName">
            <summary>
            客户
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulResultDto.DeliveryDate">
            <summary>
            交付日期
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulResultDto.ProductCode">
            <summary>
            产品编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulResultDto.ProductName">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulResultDto.Qty">
            <summary>
            排产数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulPageDto.Status">
            <summary>
            计划状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulPageDto.OrderNumber">
            <summary>
            订单编号
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulPageDto.PlanCode">
            <summary>
            计划编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulPageDto.ScheduleCode">
            <summary>
            排产编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulPageDto.ProductName">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanMaterialDto.OrderNumber">
            <summary>
            订单编号
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanMaterialDto.MaterialName">
            <summary>
            物料名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanMaterialDto.MaterialCode">
            <summary>
            物料编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanMaterialDto.GetDays">
            <summary>
            默认获取7天的物料数据
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanMaterialDetailsDto.ProductName">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanMaterialDetailsDto.ProductQty">
            <summary>
            产品数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanMaterialDetailsDto.MaterialQty">
            <summary>
            物料数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanMaterialChartDto.NeedQty">
            <summary>
            所需物料数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanMaterialChartDto.TotalQty">
            <summary>
            累计物料数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanMaterialListDto.Items">
            <summary>
            物料清单
            </summary>
        </member>
        <member name="T:Adnc.Huatek.Aps.Application.Dtos.PersonnelDto">
            <summary>
            人员计划查询条件
            </summary>
        </member>
        <member name="T:Adnc.Huatek.Aps.Application.Dtos.PersonnelResultDto">
            <summary>
            人员计划返回结果
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PersonnelResultDto.PlanDates">
            <summary>
            计划时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.Personnel.ClassesName">
            <summary>
            班次名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.Personnel.Color">
            <summary>
            班次颜色
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.Personnel.Qty">
            <summary>
            人员数量
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanSolution.Name">
            <summary>
            方案名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanSolution.ExecuteTime">
            <summary>
            执行时间
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanSolution.StartTime">
             <summary>
            开始时间
             </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.PlanSolution.StationCode">
            <summary>
            工位编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.StockMaterialDto.Mcode">
            <summary>
            物料编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.StockMaterialDto.unit">
            <summary>
            单位枚举
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.StockMaterialDto.unit_dis">
            <summary>
            单位显示值
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.StockMaterialDto.Mname">
            <summary>
            物料名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.StockMaterialDto.Qty">
            <summary>
            物料库存
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.StockMaterialDto.PreLockqty">
            <summary>
            预锁库存
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.StockMaterialPagedDto.Mcode">
            <summary>
             物料编码
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.StockMaterialPagedDto.Mname">
            <summary>
             物料名称
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.UpdateStatusDto.Ids">
            <summary>
            IDS
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.UpdateStatusDto.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Adnc.Huatek.Aps.Application.Dtos.UpdateStatusDto.Type">
            <summary>
            类型
            </summary>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasDeviceClassService.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.BasDeviceClassPagedDto)">
            <summary>
            获取所有工位信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasDeviceService.GetAllAsync">
            <summary>
            获取所有工位信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasDeviceService.GetAppointAsync(System.Int64)">
            <summary>
            获取单个工位信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasDeviceService.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.BasDeviceDto)">
            <summary>
            新增工位
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasDeviceService.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.BasDeviceDto)">
            <summary>
            修改工位
            </summary>
            <param name="id"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasDeviceService.DeleteAsync(System.Int64)">
            <summary>
            删除工位
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasDeviceService.ChangeStatusAsync(System.Int64,System.Int32)">
            <summary>
            修改工位状态
            </summary>
            <param name="id"></param>
            <param name="status"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasDeviceService.ChangeStatusAsync(Adnc.Huatek.Aps.Application.Dtos.UpdateStatusDto)">
            <summary>
            批量修改工位状态
            </summary>
            <param name="ids"></param>
            <param name="status"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasDeviceService.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.BasDevicePagedDto)">
            <summary>
            获取用户列表
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasDeviceService.GetCalendarAsync(Adnc.Huatek.Aps.Application.Dtos.BasDeviceCalendarDto)">
            <summary>
            获取设备日历信息
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasDeviceService.UpdateCalendarAsync(System.Int32,Adnc.Huatek.Aps.Application.Dtos.BasDeviceCalendarDto)">
            <summary>
            更新设备日历信息
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasDeviceService.GetDevicePreserveAsync(System.Int64)">
            <summary>
            获取设备保养信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasDeviceService.ImportAsync(System.Collections.Generic.List{Adnc.Huatek.Aps.Application.Dtos.BasDeviceDto})">
            <summary>
            导入
            </summary>
            <param name="dtos"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasDeviceService.ImportDeviceStateAsync(System.Collections.Generic.List{Adnc.Huatek.Aps.Application.Dtos.PartDeviceStateDto})">
            <summary>
            导入
            </summary>
            <param name="dtos"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasDeviceService.QueryDeviceAllType(Adnc.Huatek.Aps.Application.Dtos.BasDevicePagedDto)">
            <summary>
            导入
            </summary>
            <param name="dtos"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasMaterialService.GetAllAsync">
            <summary>
            获取所有物料信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasMaterialService.GetAppointAsync(System.Int64)">
            <summary>
            获取单个物料信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasMaterialService.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.BasMaterialDto)">
            <summary>
            新增物料
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasMaterialService.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.BasMaterialDto)">
            <summary>
            修改物料
            </summary>
            <param name="id"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasMaterialService.DeleteAsync(System.Int64)">
            <summary>
            删除物料
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasMaterialService.ChangeStatusAsync(System.Int64,System.Int32)">
            <summary>
            修改物料状态
            </summary>
            <param name="id"></param>
            <param name="status"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasMaterialService.ChangeStatusAsync(Adnc.Huatek.Aps.Application.Dtos.UpdateStatusDto)">
            <summary>
            批量修改物料状态
            </summary>
            <param name="ids"></param>
            <param name="status"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasMaterialService.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.BasMaterialPagedDto)">
            <summary>
            获取用户列表
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasRelationService.GetByIdAsync(System.Int64)">
            <summary>
            获取单个资源关系搭建信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasRelationService.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.BasRelationDto)">
            <summary>
            新增资源关系搭建
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasRelationService.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.BasRelationDto)">
            <summary>
            修改资源关系搭建
            </summary>
            <param name="id"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasRelationService.DeleteAsync(System.Int64)">
            <summary>
            删除资源关系搭建
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasRelationService.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.RelationPagedDto)">
            <summary>
            获取用户列表
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasStationService.GetAllAsync">
            <summary>
            获取所有工位信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasStationService.GetAppointAsync(System.Int64)">
            <summary>
            获取单个工位信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasStationService.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.BasStationDto)">
            <summary>
            新增工位
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasStationService.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.BasStationDto)">
            <summary>
            修改工位
            </summary>
            <param name="id"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasStationService.DeleteAsync(System.Int64)">
            <summary>
            删除工位
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasStationService.ChangeStatusAsync(System.Int64,System.Int32)">
            <summary>
            修改工位状态
            </summary>
            <param name="id"></param>
            <param name="status"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasStationService.ChangeStatusAsync(Adnc.Huatek.Aps.Application.Dtos.UpdateStatusDto)">
            <summary>
            批量修改工位状态
            </summary>
            <param name="ids"></param>
            <param name="status"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasStationService.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.BasStationPagedDto)">
            <summary>
            获取用户列表
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasStationService.GetClassesInfoAsync(System.Int64)">
            <summary>
            获取所有工位信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasWorksjopService.GetAllAsync">
            <summary>
            获取所有车间信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasWorksjopService.GetAllWorkshopAsync">
            <summary>
            获取所有车间信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasWorksjopService.GetAppointAsync(System.Int64)">
            <summary>
            获取单个车间信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasWorksjopService.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.BasWorksjopDto)">
            <summary>
            新增车间
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasWorksjopService.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.BasWorksjopDto)">
            <summary>
            修改车间
            </summary>
            <param name="id"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasWorksjopService.DeleteAsync(System.Int64)">
            <summary>
            删除车间
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasWorksjopService.ChangeStatusAsync(System.Int64,System.Int32)">
            <summary>
            修改车间状态
            </summary>
            <param name="id"></param>
            <param name="status"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasWorksjopService.ChangeStatusAsync(Adnc.Huatek.Aps.Application.Dtos.UpdateStatusDto)">
            <summary>
            批量修改车间状态
            </summary>
            <param name="ids"></param>
            <param name="status"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IBasWorksjopService.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.BasStationPagedDto)">
            <summary>
            获取车间列表
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasBomService.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.BomPagedDto)">
            <summary>
            获取分页列表
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasBomService.GetByIdAsync(System.String)">
            <summary>
            根据ID获取BOM数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasBomService.GetMaterialByProductCodeAsync(System.String)">
            <summary>
            根据productCode获取物料明细
            </summary>
            <param name="productCode"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasBomService.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.BasBomDto)">
            <summary>
            创建Bom
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasBomService.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.BasBomDto)">
            <summary>
            更新Bom
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasBomService.DeleteAsync(System.String)">
            <summary>
            删除Bom
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasClassesService.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.ClassesPagedDto)">
            <summary>
            获取分页列表
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasClassesService.GetAllAsync">
            <summary>
            获取所有数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasClassesService.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.BasClassesDto)">
            <summary>
            创建班次
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasClassesService.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.BasClassesDto)">
            <summary>
            更新班次
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasClassesService.DeleteAsync(System.Int64)">
            <summary>
            删除班次
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasClassesService.GetByIdAsync(System.Int64)">
            <summary>
            根据ID获取BOM数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasClassesService.GetAllClassesAsync">
            <summary>
            获取所有启用的班次
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasClassesService.MakeEnableAsync(System.Int32,System.Collections.Generic.List{System.Int64})">
            <summary>
            批量禁用启用
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasDeviceClassService.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.BasDeviceClassDto)">
            <summary>
            新增工位
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasDeviceClassService.DeleteAsync(System.Int64)">
            <summary>
            删除工位
            </summary>
            <param name="id"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasDeviceClassService.GetAllAsync">
            <summary>
            获取所有工位
            </summary>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasDeviceClassService.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.BasDeviceClassDto)">
            <summary>
            修改工位
            </summary>
            <param name="id"></param>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasDeviceClassService.GetByIdAsync(System.Int64)">
            <summary>
            获取指定工位
            </summary>
            <param name="id"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasDeviceService.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.BasDeviceDto)">
            <summary>
            新增工位
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasDeviceService.DeleteAsync(System.Int64)">
            <summary>
            删除工位
            </summary>
            <param name="id"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasDeviceService.GetAllAsync">
            <summary>
            获取所有工位
            </summary>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasDeviceService.GetAppointAsync(System.Int64)">
            <summary>
            获取指定工位
            </summary>
            <param name="id"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasDeviceService.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.BasDeviceDto)">
            <summary>
            修改工位
            </summary>
            <param name="id"></param>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasDeviceService.ChangeStatusAsync(Adnc.Huatek.Aps.Application.Dtos.ChangeStatusDto)">
            <summary>
            更新状态
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasDeviceService.GetDevicePreserveAsync(System.Int64)">
            <summary>
            获取设备保养信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasDeviceService.ImportAsync(System.Collections.Generic.List{Adnc.Huatek.Aps.Application.Dtos.BasDeviceDto})">
            <summary>
            import
            </summary>
            <param name="dtos"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasDeviceService.QueryDeviceAllType(Adnc.Huatek.Aps.Application.Dtos.BasDevicePagedDto)">
            <summary>
            import
            </summary>
            <param name="dtos"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasHolidayService.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.HolidayPagedDto)">
            <summary>
            获取分页列表
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasHolidayService.GetByIdAsync(System.Int64)">
            <summary>
            根据ID获取数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasHolidayService.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.BasHolidayDto)">
            <summary>
            创建
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasHolidayService.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.BasHolidayDto)">
            <summary>
            更新
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasHolidayService.DeleteAsync(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasHolidayService.GetAllAsync">
            <summary>
            获取所有数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasHolidayService.DeleteAllAsync">
            <summary>
            删除全部
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasLineService.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.LinePagedDto)">
            <summary>
            获取分页列表
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasLineService.GetByIdAsync(System.Int64)">
            <summary>
            根据ID获取产线数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasLineService.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.BasLineDto)">
            <summary>
            创建产线
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasLineService.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.BasLineDto)">
            <summary>
            更新产线
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasLineService.DeleteAsync(System.Int64)">
            <summary>
            删除产线
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasLineService.MakeEnableAsync(System.Int32,System.Collections.Generic.List{System.Int64})">
            <summary>
            批量禁用启用
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasLineService.GetAllAsync">
            <summary>
            获取所有产线数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasMaterialService.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.BasMaterialDto)">
            <summary>
            新增物料
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasMaterialService.DeleteAsync(System.Int64)">
            <summary>
            删除物料
            </summary>
            <param name="id"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasMaterialService.GetAllAsync">
            <summary>
            获取所有物料
            </summary>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasMaterialService.GetAppointAsync(System.Int64)">
            <summary>
            获取指定物料
            </summary>
            <param name="id"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasMaterialService.MakeEnableAsync(System.Int32,System.Collections.Generic.List{System.Int64})">
            <summary>
            批量禁用启用
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasMaterialService.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.BasMaterialDto)">
            <summary>
            修改物料
            </summary>
            <param name="id"></param>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasMaterialService.ImportAsync(System.Collections.Generic.List{Adnc.Huatek.Aps.Application.Dtos.BasMaterialDto})">
            <summary>
            import
            </summary>
            <param name="dtos"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasOrderService.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.BasOrderPagedDto)">
            <summary>
            获取分页列表
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasOrderService.GetAllAsync(Adnc.Huatek.Aps.Application.Dtos.BasOrderPagedDto)">
            <summary>
            获取所有数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasOrderService.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.BasOrderDto)">
            <summary>
            创建订单
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasOrderService.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.BasOrderDto)">
            <summary>
            更新订单
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasOrderService.DeleteAsync(System.Int64)">
            <summary>
            删除订单
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasOrderService.GetByIdAsync(System.Int64)">
            <summary>
            根据ID获取订单数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasOrderService.GetDetailsByIdAsync(System.Int64)">
            <summary>
            初始化新增生产计划页面
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasOrderService.FinishedAsync(Adnc.Huatek.Aps.Application.Dtos.FinishedOrders)">
            <summary>
            批量更新订单为已完成
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasOrderService.ImportOrdersAsync(System.Collections.Generic.List{Adnc.Huatek.Aps.Application.Dtos.ImportOrderDto})">
            <summary>
            导入订单
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasOrderService.ExportOrders(Adnc.Huatek.Aps.Application.Dtos.BasOrderPagedDto)">
            <summary>
            导出列表到excel文件
            </summary>
            <typeparam name="T"></typeparam>
            <param name="data">需要导出的列表数据</param>
            <param name="headers">需要自定义的字段和表头值</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasOrderService.GetOrderDetailAsync(System.Int64)">
            <summary>
            初始化订单详情页面
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasProductService.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.ProductPagedDto)">
            <summary>
            获取分页列表
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasProductService.GetAllAsync">
            <summary>
            获取所有数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasProductService.GetByIdAsync(System.Int64)">
            <summary>
            根据ID获取产品数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasProductService.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.BasProductBomDto)">
            <summary>
            创建产品
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasProductService.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.BasProductBomDto)">
            <summary>
            更新产品
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasProductService.UpdateStepMaterialAsync(Adnc.Huatek.Aps.Application.Dtos.BasProductDto)">
            <summary>
            更新产能模型
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasProductService.UpdateProductCapacityAsync(Adnc.Huatek.Aps.Application.Dtos.BasProductDto)">
            <summary>
            更新产品产能
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasProductService.DeleteAsync(System.Int64)">
            <summary>
            删除产品
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasProductService.ChangeStatusAsync(Adnc.Huatek.Aps.Application.Dtos.ChangeStatusDto)">
            <summary>
            更新状态
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasRelationService.GetByIdAsync(System.Int64)">
            <summary>
            获取指定资源关系搭建
            </summary>
            <param name="id"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasRelationService.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.BasRelationDto)">
            <summary>
            新增资源关系搭建
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasRelationService.DeleteAsync(System.Int64)">
            <summary>
            删除资源关系搭建
            </summary>
            <param name="id"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasRelationService.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.BasRelationDto)">
            <summary>
            修改资源关系搭建
            </summary>
            <param name="id"></param>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasRelationService.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.RelationPagedDto)">
            <summary>
            获取分页
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasStationService.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.BasStationDto)">
            <summary>
            新增工位
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasStationService.DeleteAsync(System.Int64)">
            <summary>
            删除工位
            </summary>
            <param name="id"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasStationService.GetAllAsync">
            <summary>
            获取所有工位
            </summary>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasStationService.GetAppointAsync(System.Int64)">
            <summary>
            获取指定工位
            </summary>
            <param name="id"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasStationService.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.BasStationDto)">
            <summary>
            修改工位
            </summary>
            <param name="id"></param>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasStationService.GetClassesInfoAsync(System.Int64)">
            <summary>
            获取所有班次信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasStationService.MakeEnableAsync(System.Int32,System.Collections.Generic.List{System.Int64})">
            <summary>
            批量禁用启用
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasStepService.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.StepPagedDto)">
            <summary>
            获取分页列表
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasStepService.GetAllAsync">
            <summary>
            获取所有数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasStepService.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.BasStepDto)">
            <summary>
            创建工序
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasStepService.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.BasStepDto)">
            <summary>
            更新工序
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasStepService.DeleteAsync(System.Int64)">
            <summary>
            删除工序
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasStepService.GetByIdAsync(System.Int64)">
            <summary>
            根据ID获取BOM数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasStepService.ChangeStatusAsync(Adnc.Huatek.Aps.Application.Dtos.ChangeStatusDto)">
            <summary>
            更新状态
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasTechnologyService.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.TechnologyPagedDto)">
            <summary>
            获取分页列表
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasTechnologyService.GetAllAsync">
            <summary>
            获取所有数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasTechnologyService.GetByIdAsync(System.Int64)">
            <summary>
            根据ID获取工艺数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasTechnologyService.GetByTCodeAsync(System.String)">
            <summary>
            根据ID获取工艺数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasTechnologyService.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.BasTechnologyDto)">
            <summary>
            创建工艺
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasTechnologyService.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.BasTechnologyDto)">
            <summary>
            更新工艺
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasTechnologyService.DeleteAsync(System.Int64)">
            <summary>
            删除工艺
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasTechnologyService.ChangeStatusAsync(Adnc.Huatek.Aps.Application.Dtos.ChangeStatusDto)">
            <summary>
            更新状态
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasWorksjopService.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.BasWorksjopDto)">
            <summary>
            新增车间
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasWorksjopService.DeleteAsync(System.Int64)">
            <summary>
            删除车间
            </summary>
            <param name="id"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasWorksjopService.GetAllAsync">
            <summary>
            获取所有车间
            </summary>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Adnc.Huatek.Aps.Application.Services.Implements.BasWorksjopService.GetAllWorkshopAsync" -->
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasWorksjopService.GetAppointAsync(System.Int64)">
            <summary>
            获取指定车间
            </summary>
            <param name="id"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.BasWorksjopService.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.BasWorksjopDto)">
            <summary>
            修改车间
            </summary>
            <param name="id"></param>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.HomePageService.GetIdleDevicesCount">
            <summary>
            获取首页空闲设备数量
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.HomePageService.GetDevicesStatusCount">
            <summary>
            获取首页设备数量饼型图统计
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.HomePageService.GetOrderNumByDateAsync">
            <summary>
            首页订单数量统计
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.HomePageService.GetPlanByStatusAsync">
            <summary>
            统计首页计划曲线图
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartOrderService.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.PartOrderPagedDto)">
            <summary>
            获取分页列表
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartOrderService.GetByIdAsync(System.Int64)">
            <summary>
            根据ID获取工艺数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartOrderService.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.PartOrderDto)">
            <summary>
            创建工艺
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartOrderService.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.PartOrderDto)">
            <summary>
            更新工艺
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartOrderService.DeleteAsync(System.Int64)">
            <summary>
            删除工艺
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartPlanPendingService.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingPagedDto)">
            <summary>
            获取分页列表
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartPlanPendingService.GetAllAsync">
            <summary>
            获取所有数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartPlanPendingService.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingDto)">
            <summary>
            创建生产计划
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartPlanPendingService.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingDto)">
            <summary>
            更新生产计划
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartPlanPendingService.DeleteAsync(System.Int64)">
            <summary>
            删除生产计划
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartPlanPendingService.GetByIdAsync(System.Int64)">
            <summary>
            根据ID获取生产计划数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartSchedPlanService.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.PartSchedulPlanPageDto)">
            <summary>
            列表
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartSchedPlanService.GetDeviceGanttPlan(Adnc.Huatek.Aps.Application.Dtos.PartSchedulPlanSourcePageDto)">
            <summary>
            获取设备甘特图
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartSchedPlanService.CheckSourceEffiveAsync(System.Collections.Generic.List{Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanStepDto})">
            <summary>
            拖拉拽甘特图时调用接口核验资源
            </summary>
            <param name="dtos"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartSchedPlanService.CreateSchedulePlan(Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanDto)">
            <summary>
            一键排产
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartSchedPlanService.TrialSchedulePlan(Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanDto)">
            <summary>
            试算
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartSchedPlanService.ModuleSchedulePlan(Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanDto)">
            <summary>
            修改计划
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartSchedPlanService.SaveStepProgress(Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanStepReportDto)">
            <summary>
            报工保存
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartSchedPlanService.SetScheduleStatusAsync(Adnc.Huatek.Aps.Application.Dtos.SchedulStatusNew)">
            <summary>
            改变状态
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:Adnc.Infra.Core.Exceptions.BusinessException"></exception>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartSchedPlanService.CheckSourceConflict(Adnc.Huatek.Aps.Application.Dtos.QuerySourceConflict)">
            <summary>
            分析与当前计划的资源冲突情况
            </summary>
            <param name="scheduleCode"></param>
            <param name="planCode"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartSchedPlanService.HasOverlap(System.Collections.Generic.List{Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanSourceAggregate.PartSchedulePlanSource},System.Collections.Generic.List{Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanSourceAggregate.PartSchedulePlanSource})">
            <summary>
            检查指定设备是否存在时间段重叠
            </summary>
            <param name="devices">设备集合</param>
            <param name="targetDeviceName">要检查的设备名称</param>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartSchedPlanService.CalculateMergedIntervals(System.DateTime,System.DateTime,System.Collections.Generic.List{Adnc.Huatek.Aps.Application.Dtos.BasDeviceClassDto})">
            <summary>
            计算合并后的工作时间段
            </summary>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartSchedPlanService.BuildProcessTree``1(System.Collections.Generic.List{``0},System.Func{``0,System.String},System.Func{``0,System.String})">
            <summary>
            构建树结构
            </summary>
            <typeparam name="T"></typeparam>
            <param name="steps"></param>
            <param name="getStepCode"></param>
            <param name="getPreCode"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartSchedPlanService.LoadInitialDeviceDataAsync(System.String,System.Collections.Generic.List{System.Int32},System.DateTime,System.DateTime)">
            <summary>
            初始化数据
            </summary>
            <param name="DevType"></param>
            <param name="planState"></param>
            <param name="BeginDate"></param>
            <param name="EndDate"></param>
            <returns></returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Adnc.Huatek.Aps.Application.Services.Implements.PartSchedPlanService.ComputeStepTree(System.Collections.Generic.List{Adnc.Huatek.Aps.Application.Dtos.BasProductStepRelationDto},System.Collections.Generic.List{System.String},System.Collections.Generic.List{Adnc.Huatek.Aps.Application.Dtos.StepTreeDto},System.Boolean)" -->
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartSchedPlanService.SubtractIntervals(System.Collections.Generic.List{Adnc.Huatek.Aps.Application.Dtos.TimeInterval},System.Collections.Generic.List{Adnc.Huatek.Aps.Application.Dtos.TimeInterval})">
            <summary>
            获取不重叠的数据
            </summary>
            <param name="source"></param>
            <param name="subtractions"></param>
            <returns></returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Adnc.Huatek.Aps.Application.Services.Implements.PartSchedPlanService.GetDevice(Adnc.Huatek.Aps.Application.Dtos.QueryEffrctiveDevice,System.Collections.Generic.List{Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanSourceAggregate.PartSchedulePlanSource},System.Boolean)" -->
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartSchedPlanService.LoadInitDataAsync(System.String,System.String)">
            <summary>
            加载初始化数据
            </summary>
            <param name="proCode"></param>
            <param name="scheduleCode"></param>
            <returns></returns>
            <exception cref="T:System.InvalidOperationException"></exception>
            <exception cref="T:System.IO.InvalidDataException"></exception>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartSchedPlanService.PostOrderTraversalModify(Adnc.Huatek.Aps.Application.Dtos.QuerycheduleStepDto,Adnc.Huatek.Aps.Application.Dtos.SchedulePlanResultDto,Adnc.Huatek.Aps.Application.Dtos.SchedulePlanResultDto,System.Boolean)">
            <summary>
            拖拉拽接口保存
            </summary>
            <param name="query"></param>
            <param name="result"></param>
            <param name="oldResult"></param>
            <param name="edit"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartSchedPlanService.DataBaseDeal(Adnc.Huatek.Aps.Application.Dtos.BasProductDto,Adnc.Huatek.Aps.Application.Dtos.SchedulePlanResultDto,System.Collections.Generic.List{Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanRuleAggregate.PartSchedulePlanRule},System.Boolean)">
            <summary>
            数据写入
            </summary>
            <param name="proModule"></param>
            <param name="steps"></param>
            <param name="sources"></param>
            <param name="materials"></param>
            <param name="plan"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartSchedPlanService.DataBaseRemoveDeal(Adnc.Huatek.Aps.Application.Dtos.SchedulePlanResultDto)">
            <summary>
            清楚该排产计划的工序 资源 物料，规则等数据
            </summary>
            <param name="plan"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartSchedPlanService.PreOrderTraversalEdit(Adnc.Huatek.Aps.Application.Dtos.QuerycheduleStepDto,Adnc.Huatek.Aps.Application.Dtos.SchedulePlanResultDto,Adnc.Huatek.Aps.Application.Dtos.SchedulePlanResultDto)">
            <summary>
            正排修改
            </summary>
            <param name="query"></param>
            <param name="result"></param>
            <param name="oldResult"></param>
            <returns></returns>
            <exception cref="T:System.InvalidOperationException"></exception>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartSchedPlanService.ProcessCoreDataAsync(System.Collections.Generic.List{Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanAggregate.PartSchedulePlanInfo},System.String,System.String)">
            <summary>
            获取产能模型
            </summary>
            <param name="modules"></param>
            <param name="scheduleCode"></param>
            <param name="PlanCode"></param>
            <returns></returns>
            <exception cref="T:System.InvalidOperationException"></exception>
            <exception cref="T:System.IO.InvalidDataException"></exception>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartSchedPlanService.queryDateRange(System.DateTime,System.DateTime,System.String,System.Collections.Generic.List{Adnc.Huatek.Aps.Application.Dtos.BasDeviceClassDto})">
            <summary>
            获取时间范围
            </summary>
            <param name="BeginDate"></param>
            <param name="EndDate"></param>
            <param name="DevCode"></param>
            <param name="devClassDtos"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartSchedPlanService.RemoveScheduleStepSource(System.String,System.String)">
            <summary>
            根据排产计划编号 删除设备资源计划表
            </summary>
            <param name="ScheduleCode"></param>
            <param name="stepCode"></param>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartSchedPlanService.SchedulePlan(Adnc.Huatek.Aps.Application.Dtos.BasProductDto,Adnc.Huatek.Aps.Application.Dtos.ScheduleRule,Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanDto)">
            <summary>
            处理排产侏罗纪
            </summary>
            <param name="proModule"></param>
            <param name="rule"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartSchedPlanService.ScheduleRuleNew(System.Collections.Generic.List{Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanRuleDto})">
            <summary>
            排产规则处理
            </summary>
            <param name="Rules"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PartSchedPlanService.SelectOptimalDevice(System.Collections.Generic.List{Adnc.Huatek.Aps.Application.Dtos.EffectiveDeviceDtos},System.Boolean,System.Decimal)">
            <summary>
            获取单台设备
            </summary>
            <param name="devices"></param>
            <param name="isForward"></param>
            <param name="productNum"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PlanProductionSchedulService.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulPageDto)">
            <summary>
            获取分页列表
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PlanProductionSchedulService.GetMaterials(Adnc.Huatek.Aps.Application.Dtos.PlanMaterialDto)">
            <summary>
            获取物料计划
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PlanProductionSchedulService.GetMaterialsNew(Adnc.Huatek.Aps.Application.Dtos.PlanMaterialDto)">
            <summary>
            获取物料计划
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PlanProductionSchedulService.GetMaterialsWarning(Adnc.Huatek.Aps.Application.Dtos.PlanMaterialDto)">
            <summary>
            计算物料预警
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PlanProductionSchedulService.GetPersonnels(Adnc.Huatek.Aps.Application.Dtos.PersonnelDto)">
            <summary>
            获取人员计划
            </summary>
            <param name="schCode"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PlanProductionSchedulService.GetLeaveDate(Adnc.Huatek.Aps.Application.Adnc.Huatek.Aps.Application.Services.Implements.ProductionProcess,Adnc.Huatek.Aps.Application.Adnc.Huatek.Aps.Application.Services.Implements.ProductionProcess,System.Int32)">
            <summary>
            根据工序剩余产能排产日期，及生产量
            </summary>
            <param name="process"></param>
            <param name="processes"></param>
            <param name="sort"></param>
            <param name="currentProcess"></param>
            <param name="productQuantity"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PlanProductionSchedulService.CalculateRemainingTime(System.Int32,System.Double,System.Double,System.Double)">
            <summary>
             计算剩余时间的方法
            </summary>
            <param name="productQ"></param>
            <param name="quantity"></param>
            <param name="duration"></param>
            <param name="limitHours"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PlanProductionSchedulService.GetStationClasses(System.String)">
            <summary>
            获取工序班次信息的方法
            </summary>
            <param name="stationCode"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PlanProductionSchedulService.GetMaxTime(System.Collections.Generic.List{Adnc.Huatek.Aps.Application.Dtos.StationClass})">
             <summary>
            根据传入工位获取最大时间点
             </summary>
             <param name="presStations"></param>
             <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PlanProductionSchedulService.GetMinTime(System.Collections.Generic.List{Adnc.Huatek.Aps.Application.Dtos.StationClass})">
            <summary>
            根据传入工位获取最小时间点
            </summary>
            <param name="presStations"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PlanProductionSchedulService.RemoveDuplicateData(System.Collections.Generic.List{Adnc.Huatek.Aps.Domain.Aggregates.BasProductStandardCapacityAggregate.BasProductStandardCapacity},Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDto)">
            <summary>
            获取可执行的最优方案方法
            </summary>
            <param name="dataItems"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PlanProductionSchedulService.RealoadSchedulAsync(Adnc.Huatek.Aps.Application.Dtos.ReloadBasProcuct)">
            <summary>
            重排功能
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PlanProductionSchedulService.SaveSchedulingProduction(Adnc.Huatek.Aps.Application.Dtos.ReloadBasProcuct)">
            <summary>
            重排并保存
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PlanProductionSchedulService.SavePartialProductionScheduling(System.Collections.Generic.List{Adnc.Huatek.Aps.Application.Adnc.Huatek.Aps.Application.Services.Implements.ProcessSchedulDto},Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulAggregate.PlanProductionSchedule,Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate.PartPlanPending)">
            <summary>
            部分排产
            </summary>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PlanProductionSchedulService.SaveCompleteProductionScheduling(System.Collections.Generic.List{Adnc.Huatek.Aps.Application.Adnc.Huatek.Aps.Application.Services.Implements.ProcessSchedulDto},Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulAggregate.PlanProductionSchedule,Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate.PartPlanPending)">
            <summary>
            整排
            </summary>
            <param name="capacitys"></param>
            <param name="planProductionSchedul"></param>
            <param name="basplanPending"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PlanProductionSchedulService.isVacation(System.DateTime)">
            <summary>
            正排判断是否是工作日
            </summary>
            <param name="dt"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PlanProductionSchedulService.isVacationDesc(System.DateTime)">
            <summary>
            倒排判断是否工作日
            </summary>
            <param name="dt"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PlanProductionSchedulService.GetGanttByProductAsync(Adnc.Huatek.Aps.Application.Dtos.ProductionSchedulGanttDto)">
            <summary>
            获取产品甘特图
            </summary>
            <param name="productCode"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PlanProductionSchedulService.RealoadSchedulYwgAsync(Adnc.Huatek.Aps.Application.Dtos.ReloadBasProcuct)">
            <summary>
            试算
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PlanProductionSchedulService.ComputeMaterialWarning(Adnc.Huatek.Aps.Application.Dtos.MaterialWarningPagedDto)">
            <summary>
            物料预警
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.PlanProductionSchedulService.GetMaterialWarningChart(System.String)">
            <summary>
            物料预警图
            </summary>
            <param name="Mcode"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.RulesService.Test2">
            <summary>
            算法思路
            不考虑物料
            核心限制因素 工序：结批量、消耗时间周期
            </summary>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.StockMaterialService.ImportAsync(System.Collections.Generic.List{Adnc.Huatek.Aps.Application.Dtos.StockMaterialDto})">
            <summary>
            新增物料
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.StockMaterialService.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.StockMaterialPagedDto)">
            <summary>
            获取列表分页
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.StockMaterialService.GetAllAsync">
            <summary>
            获取所有数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.StockMaterialService.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.StockMaterialDto)">
            <summary>
            创建物料库存
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.StockMaterialService.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.StockMaterialDto)">
            <summary>
            更新产线
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.Implements.StockMaterialService.GetByIdAsync(System.Int64)">
            <summary>
            根据ID获取库存数量
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IPlanProductionSchedulService.GetMaterials(Adnc.Huatek.Aps.Application.Dtos.PlanMaterialDto)">
            <summary>
            获取物料计划
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IPlanProductionSchedulService.GetPersonnels(Adnc.Huatek.Aps.Application.Dtos.PersonnelDto)">
            <summary>
            获取人员计划
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IStockMaterialService.GetAllAsync">
            <summary>
            获取所有物料信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IStockMaterialService.ImportAsync(System.Collections.Generic.List{Adnc.Huatek.Aps.Application.Dtos.StockMaterialDto})">
            <summary>
            导入物料库存
            </summary>
            <param name="Dtos"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IStockMaterialService.GetPagedAsync(Adnc.Huatek.Aps.Application.Dtos.StockMaterialPagedDto)">
            <summary>
            
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IStockMaterialService.UpdateAsync(Adnc.Huatek.Aps.Application.Dtos.StockMaterialDto)">
            <summary>
            
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Services.IStockMaterialService.CreateAsync(Adnc.Huatek.Aps.Application.Dtos.StockMaterialDto)">
            <summary>
            
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Huatek.Aps.Application.Subscribers.CapEventSubscriber.ProcessWarehouseQtyBlockedEvent(Adnc.Shared.Rpc.Event.WarehouseQtyBlockedEvent)">
            <summary>
            订阅库存锁定事件
            </summary>
            <param name="eventDto"></param>
            <returns></returns>
        </member>
    </members>
</doc>
