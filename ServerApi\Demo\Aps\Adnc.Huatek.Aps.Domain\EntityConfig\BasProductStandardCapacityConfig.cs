﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasProductStandardCapacityAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductTechnologyRelationAggregate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class BasProductStandardCapacityConfig : AbstractEntityTypeConfiguration<BasProductStandardCapacity>
    {
        public override void Configure(EntityTypeBuilder<BasProductStandardCapacity> builder)
        {
            base.Configure(builder);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.ProCode).HasColumnName("procode");
            builder.Property(x => x.StepCode).HasColumnName("stepcode");
            builder.Property(x => x.SolutionName).HasColumnName("solutionname");
            builder.Property(x => x.Capacity).HasColumnName("capacity");
            builder.Property(x => x.StationCode).HasColumnName("stationcode");
            builder.Property(x => x.LineCode).HasColumnName("linecode");
            builder.Property(x => x.DeviceCode).HasColumnName("devicecode");
            builder.Property(x => x.WorkUnitTime).HasColumnName("workunittime");
            builder.Property(x => x.StandardWorkTime).HasColumnName("standardworktime");

            builder.Property(x => x.Status).HasColumnName("status");
            builder.Property(x => x.Remark).HasColumnName("remark");
            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.CreateBy).HasColumnName("createby");
            builder.Property(x => x.CreateTime).HasColumnName("createtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
        }
    }
}
