﻿using Adnc.Infra.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulAggregate
{
    /// <summary>
    /// 排产计划表
    /// </summary>
    public class PlanProductionSchedule : EfFullAuditEntity
    {
        /// <summary>
        /// 排产编码
        /// </summary>
        public string? SchedulCode { get; set; }
        /// <summary>
        /// 计划编码
        /// </summary>
        public string? PlanCode { get; set; }
        /// <summary>
        /// 计划开始日期
        /// </summary>
        public DateTime PlanStart { get; set; }
        /// <summary>
        /// 计划结束日期
        /// </summary>
        public DateTime? PlanEnd { get; set; }
        /// <summary>
        /// 排序方式
        /// </summary>
        public int Sorts { get; set; }
        /// <summary>
        /// 前置规则
        /// </summary>
        public int PreRule { get; set; }
        /// <summary>
        /// 产线编码
        /// </summary>
        public string? LCode { get; set; }

        /// <summary>
        /// 计划状态 0=待生效;1=生效;2=作废
        /// </summary>
        public int Status { get; set; }

        public string? Remark { get; set; }
        /// <summary>
        /// 计划类型
        /// </summary>
        public int? PlanType { get; set; }


        public DateTime? SchedulTime { get; set; }

    }
}
