﻿global using Adnc.Infra.Core.Exceptions;
global using Adnc.Infra.Core.Guard;
global using Adnc.Infra.IdGenerater.Yitter;
global using Adnc.Infra.IRepositories;
global using Adnc.Shared.Domain;
global using Adnc.Shared.Domain.Entities;
global using Adnc.Shared.Repository.EfEntities.Config;
global using Microsoft.EntityFrameworkCore;
global using Microsoft.EntityFrameworkCore.Metadata.Builders;
global using System.Reflection;

