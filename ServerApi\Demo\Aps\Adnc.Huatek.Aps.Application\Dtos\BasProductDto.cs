﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Dtos
{
    public class BasProductDto : IDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public long Id { get; set; }

        public bool isEdit { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        /// 
        [Display(Name = "描述")]
        public string? Remark { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        [Display(Name = "产品名称")]
        public string? Proname { get; set; }

        /// <summary>
        /// 产品编码
        /// </summary>
        [Display(Name = "产品编码")]
        public string? Procode { get; set; }
        /// <summary>
        /// 工艺编码
        /// </summary>
        public string? TCode { get; set; }
        /// <summary>
        /// 工艺编码
        /// </summary>
        [Display(Name = "工艺路线")]
        public string? TName { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public int? Status { get; set; }

        [Display(Name = "启用/禁用")]
        public String? StatusDis { get; set; }
        /// <summary>
        /// 创建名
        /// </summary>
        public string? CreatName { get; set; }
        /// <summary>
        /// 更新人
        /// </summary>
        public string? ModifyName { get; set; }

        public long CreateBy { get; set; }

        /// <summary>
        /// 创建时间/注册时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 最后更新人
        /// </summary>
        public long? ModifyBy { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        public List<BasProductStepRelationDto> StepItems { get; set; }

        public List<BasBomListDto>? BomItems { get; set; }




    }


    public class BasProductBomDto : IDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public long? Id { get; set; }

        public bool isEdit { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string? Proname { get; set; }

        /// <summary>
        /// 产品编码
        /// </summary>
        public string? Procode { get; set; }
        /// <summary>
        /// 工艺编码
        /// </summary>
        public string? TCode { get; set; }
        /// <summary>
        /// 工艺编码
        /// </summary>
        public string? TName { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public int? Status { get; set; }
        /// <summary>
        /// 创建名
        /// </summary>
        public string? CreatName { get; set; }
        /// <summary>
        /// 更新人
        /// </summary>
        public string? ModifyName { get; set; }

        public long CreateBy { get; set; }

        /// <summary>
        /// 创建时间/注册时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 最后更新人
        /// </summary>
        public long? ModifyBy { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        public List<BasBomListDto> BomItems { get; set; }


    }

    public class ProductPagedDto : SearchPagedDto
    {
        /// <summary>
        /// 产品名称
        /// </summary>
        public string? Proname { get; set; }

        /// <summary>
        /// 产品编码
        /// </summary>
        public string? Procode { get; set; }


        /// <summary>
        /// 状态
        /// </summary>
        public int Status { get; set; }
    }
}
