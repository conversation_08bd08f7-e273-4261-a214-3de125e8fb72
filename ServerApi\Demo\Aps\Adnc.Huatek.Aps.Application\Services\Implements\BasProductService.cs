﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.BasBomAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasDevice;
using Adnc.Huatek.Aps.Domain.Aggregates.BasLineAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasMaterial;
using Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductStandardCapacityAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductStepRelationAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductStepRelationDeviceAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductTechnologyRelationAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStation;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStepAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStepMaterialRelationAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasTechnologyAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasTechnologyStepRelationAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.SysUserAggregate;
using Adnc.Huatek.Aps.Domain.Services;
using Adnc.Infra.IdGenerater.Yitter;
using Adnc.Shared;
using Adnc.Shared.Application.Contracts.ResultModels;
using System.Net;

namespace Adnc.Huatek.Aps.Application.Services.Implements
{
    public class BasProductService : AbstractAppService, IBasProductService
    {
        private readonly BasProductManagement _basProductMgr;
        private readonly IEfBasicRepository<BasProduct> _basProductRepo;
        private readonly IEfBasicRepository<BasTechnology> _basTecRepo;
        private readonly IEfBasicRepository<BasOrder> _basOrderRepo;
        private readonly IEfBasicRepository<BasOrderProduct> _basOrderProductRepo;
        private readonly IEfBasicRepository<BasProductTechnologyRelation> _basProTecRelRepo;
        private readonly IEfBasicRepository<BasStep> _basStepRepo;
        private readonly IEfBasicRepository<BasProductStepRelation> _basProStepRelRepo;
        private readonly IEfBasicRepository<BasProductStepRelationMaterial> _basProStepRelMatRepo;

        private readonly IEfBasicRepository<BasStepMaterialRelation> _basStepMaterialRelRepo;
        private readonly IEfBasicRepository<BasTechnologyStepRelation> _basTechnologyStepRelationRepo;
        private readonly IEfBasicRepository<BasProductStandardCapacity> _basProductStandardCapacityRepo;
        private readonly BasNumberManagementService _basNumberManagementService;
        private readonly IEfBasicRepository<PlanProductionSchedule> _planProductionSchedulRepo;
        private readonly IEfBasicRepository<BasDevice> _basDeviceRepo;
        private readonly IEfBasicRepository<PlanStepCapacity> _planStepCapacityRepo;
        private readonly IEfBasicRepository<BasLine> _basLineRepo;
        private readonly IEfBasicRepository<BasStation> _basStationRepo;
        private readonly IEfBasicRepository<SysUser> _sysUserRepo;
        private readonly IEfBasicRepository<BasBom> _bomRepo;
        private readonly IEfBasicRepository<BasMaterial> _materialRepo;
        private IMaintRestClient _maintRestClient;
        private readonly IEfBasicRepository<BasProductStepRelationSource> _basProStepRelSouRepo;


        public BasProductService(
            IEfBasicRepository<BasProduct> basProductRepo,
            IEfBasicRepository<PlanProductionSchedule> planProductionSchedulRepo,
            IEfBasicRepository<BasProductStepRelationSource> basProStepRelSouRepo,
        IEfBasicRepository<BasOrder> basOrderRepo,
            IEfBasicRepository<BasOrderProduct> basOrderProductRepo,
            IEfBasicRepository<BasProductTechnologyRelation> basProTecRelRepo,
            IEfBasicRepository<BasTechnology> basTecRepo,
            IEfBasicRepository<BasProductStepRelation> basProStepRelRepo,
            IEfBasicRepository<BasProductStepRelationMaterial> basProStepRelMatRepo,
            IEfBasicRepository<BasStepMaterialRelation> basStepMaterialRelRepo,
            IEfBasicRepository<BasProductStandardCapacity> basProductStandardCapacityRepo,
            IEfBasicRepository<BasStep> basStepRepo,
            BasProductManagement basProductMgr, BasNumberManagementService basNumberManagementService,
            IEfBasicRepository<BasTechnologyStepRelation> basTechnologyRepo,
            IEfBasicRepository<PlanStepCapacity> planStepCapacityRepo,
            IEfBasicRepository<BasDevice> basDeviceRepo,
            IEfBasicRepository<BasLine> basLineRepo, IMaintRestClient maintRestClient,
            IEfBasicRepository<BasStation> basStationRepo, 
            IEfBasicRepository<SysUser> sysUserRepo, IEfBasicRepository<BasBom> bomRepo, IEfBasicRepository<BasMaterial> materialRepo)
        {
            _basProductMgr = basProductMgr;
            _basOrderRepo = basOrderRepo;
            _basOrderProductRepo = basOrderProductRepo;
            _basProductRepo = basProductRepo;
            _basTecRepo = basTecRepo;
            _basProTecRelRepo = basProTecRelRepo;
            _basProStepRelRepo = basProStepRelRepo;
            _basTechnologyStepRelationRepo = basTechnologyRepo;
            _basStepMaterialRelRepo = basStepMaterialRelRepo;
            _basStepRepo = basStepRepo;
            _basProductStandardCapacityRepo = basProductStandardCapacityRepo;
            _basNumberManagementService = basNumberManagementService;
            _basDeviceRepo = basDeviceRepo;
            _basLineRepo = basLineRepo;
            _basStationRepo = basStationRepo;
            _sysUserRepo = sysUserRepo;
            _planStepCapacityRepo = planStepCapacityRepo;
            _planProductionSchedulRepo = planProductionSchedulRepo;
            _bomRepo = bomRepo;
            _materialRepo = materialRepo;
            _basProStepRelMatRepo = basProStepRelMatRepo;
            _maintRestClient = maintRestClient;
            _basProStepRelSouRepo = basProStepRelSouRepo;
        }
        /// <summary>
        /// 获取分页列表
        /// </summary>
        /// <param name="search"></param>
        /// <returns></returns>
        public async Task<PageModelDto<BasProductDto>> GetPagedAsync(ProductPagedDto search)
        {
            search.TrimStringFields();
            var whereExpression = ExpressionCreator
                                                .New<BasProduct>()
                                                .And(x => !x.IsDeleted)
                                                .AndIf(search.Procode.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Procode!, $"%{search.Procode}%"))
                                                .AndIf(search.Proname.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Proname!, $"%{search.Proname}%"))
                                                .AndIf(search.Status != 999, x => EF.Functions.Like(x.Status!, $"%{search.Status}%"));

            var total = await _basProductRepo.CountAsync(whereExpression);
            if (total == 0)
                return new PageModelDto<BasProductDto>(search);
            var users = _sysUserRepo.Where(x => true);
            var entities = _basProductRepo.Where(whereExpression);
            var results = await (from s in entities
                                 join u in users on s.CreateBy equals u.Id
                                 join mu in users on s.ModifyBy equals mu.Id
                                 select new BasProductDto
                                 {
                                     Id = s.Id,
                                     Procode = s.Procode,
                                     Proname = s.Proname,
                                     Status = s.Status,
                                     CreateBy = s.CreateBy,
                                     CreatName = u.Name,
                                     CreateTime = s.CreateTime,
                                     ModifyBy = s.ModifyBy,
                                     ModifyName = mu.Name,
                                     ModifyTime = s.ModifyTime,
                                     Remark = s.Remark,
                                     TCode = s.TCode
                                 }).OrderByDescending(x => x.CreateTime)
                                            .Skip(search.SkipRows())
                                            .Take(search.PageSize)
                                            .ToListAsync();

            return new PageModelDto<BasProductDto>(search, results, total);
        }
        /// <summary>
        /// 获取所有数据
        /// </summary>
        /// <returns></returns>

        public async Task<List<BasProductDto>> GetAllAsync()
        {
            var BasProducts = await _basProductRepo.Where(x => !x.IsDeleted && x.Status == 1).ToListAsync();

            var BasProductsDto = Mapper.Map<List<BasProductDto>>(BasProducts);

            return BasProductsDto;
        }


        /// <summary>
        /// 根据ID获取产品数据
        /// </summary>
        /// <returns></returns>

        public async Task<BasProductDto> GetByIdAsync(long id)
        {
            var BasProduct = await _basProductRepo.GetAsync(id);

            var BasProductsDto = Mapper.Map<BasProductDto>(BasProduct);
            //获取产品的BOM清单
            BasProductsDto.BomItems = _bomRepo.Where(x => x.ProductCode == (BasProductsDto.Procode ?? "")).Select(x => new BasBomListDto
            {
                MaterialCode = x.MaterialCode,
                MaterialName = x.MaterialName,
                Qty = x.Qty
            }).ToList();

            return BasProductsDto;
        }


        public async Task<BasProductDto> GetCapacityModuleByIdAsync(long id)
        {
            var BasProduct = await _basProductRepo.GetAsync(id);

            var productsDto = Mapper.Map<BasProductDto>(BasProduct);
            //获取产品的BOM清单
            productsDto.BomItems = _bomRepo.Where(x => x.ProductCode == (productsDto.Procode ?? "")).Select(x => new BasBomListDto
            {
                MaterialCode = x.MaterialCode,
                MaterialName = x.MaterialName,
                Qty = x.Qty
            }).ToList();

            //工艺
            var tech = await _basTecRepo.Where(x => x.Tcode == BasProduct.TCode).FirstOrDefaultAsync();
            productsDto.TName = tech?.Tname;

            var stepItems = await _basTechnologyStepRelationRepo.Where(x => x.TCode == BasProduct.TCode).ToListAsync();
            var steps = await _basStepRepo.Where(x => stepItems.Select(x => x.StepCode).Contains(x.Code)).ToListAsync();

            var capecityMObj = await _basProStepRelRepo.Where(x => x.ProCode == productsDto.Procode && x.TecCode == productsDto.TCode).ToListAsync();


            if (capecityMObj.Any())
            {
                var mainIds = capecityMObj.Select(x => x.Id);
                var stepMaterials = await _basProStepRelMatRepo.Where(x => mainIds.Contains(x.MainId)).ToListAsync();
                var stepMaterialsDto = Mapper.Map<List<BasProductStepRelationMaterialDto>>(stepMaterials);
                stepMaterialsDto.ForEach(p =>
                {
                    p.MaterialName = productsDto.BomItems?.Where(x => x.MaterialCode == p.MaterialCode)?.FirstOrDefault()?.MaterialName ??"";
                });

                var devices = await _basProStepRelSouRepo.Where(x => mainIds.Contains(x.MainId)).ToListAsync();
                var deviceDtos = Mapper.Map<List<BasProductStepRelationSourceDto>>(devices);
                var restRpcResult = await _maintRestClient.GetDictByNameAsync("设备类型");
                if (restRpcResult.IsSuccessStatusCode)
                {
                    var unitEnums = restRpcResult.Content;
                    if (unitEnums is not null)
                    {
                        deviceDtos.ForEach(p =>
                        {
                            p.DeviceTypeName = unitEnums?.Where(x => x.value == p.DeviceType)?.FirstOrDefault()?.label ?? "";
                        });
                    }
                }

                restRpcResult = await _maintRestClient.GetDictByNameAsync("资源大类");
                if (restRpcResult.IsSuccessStatusCode)
                {
                    var sourceTypeEnums = restRpcResult.Content;
                    if (sourceTypeEnums is not null)
                    {
                        deviceDtos.ForEach(p =>
                        {
                            p.SourceTypeName = sourceTypeEnums?.Where(x => x.value == p.SourceType)?.FirstOrDefault()?.label ?? "";
                        });
                    }
                }
                productsDto.StepItems = (from s in capecityMObj
                                         join d in stepItems on s.StepCode equals d.StepCode
                                         join b in steps on s.StepCode equals b.Code
                                         select new BasProductStepRelationDto
                                         {
                                             Id = s.Id,
                                             ProCode = productsDto.Procode,
                                             StepName = b.Name,
                                             StepCode = s.StepCode,
                                             Sort = d.Sort,
                                             StepTime = b.StepTime,
                                             Tat = b.Tat,
                                             PreStep = d.PreStep,
                                             PreStepName = steps.Where(x => x.Code == d.PreStep)?.FirstOrDefault()?.Name ?? "",
                                             IntervalTime = d.IntervalTime,
                                             IsKey = d.IsKey,
                                             MaterialsIn = stepMaterials.Any() ? stepMaterialsDto.Where(x => x.MainId == s.Id && x.MaterialType == "in").ToList() : new List<BasProductStepRelationMaterialDto>(),
                                             MaterialsOut = stepMaterials.Any() ? stepMaterialsDto.Where(x => x.MainId == s.Id && x.MaterialType == "out").ToList() : new List<BasProductStepRelationMaterialDto>(),
                                             MainItems = deviceDtos.Any() ? deviceDtos.Where(x => x.MainId == s.Id && x.MainSource).ToList() : new List<BasProductStepRelationSourceDto>(),
                                          AssistItems = deviceDtos.Any() ? deviceDtos.Where(x => x.MainId == s.Id && !x.MainSource).ToList() : new List<BasProductStepRelationSourceDto>()
                                         }).ToList();

            }
            else
            {
                productsDto.StepItems = (from s in stepItems
                                         join b in steps on s.StepCode equals b.Code
                                         select new BasProductStepRelationDto
                                         {
                                             Id = IdGenerater.GetNextId(),
                                             ProCode = productsDto.Procode,
                                             StepName = b.Name,
                                             StepCode = s.StepCode,
                                             StepTime = b.StepTime,
                                             Tat = b.Tat,
                                             Sort = s.Sort,
                                             PreStep = s.PreStep,
                                             PreStepName = steps.Where(x => x.Code == s.PreStep)?.FirstOrDefault()?.Name ??"",
                                             IntervalTime = s.IntervalTime,
                                             IsKey = s.IsKey
                                         }
                                 ).ToList();
            }

            return productsDto;
        }


        /// <summary>
        /// 创建产品
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult<long>> CreateAsync(BasProductBomDto input)
        {
            input.TrimStringFields();
            var autoCode = _basNumberManagementService.GetNumberBySimpleName(CommonConst.PRODUCTNUMBER).Result;
            var products = await _basProductMgr.CreateAsync(autoCode, input.Proname, input.Status ?? 0, input.Remark, input.TCode);
            var bomItems = Mapper.Map<List<BasBom>>(input.BomItems);
            bomItems.ForEach(o =>
            {
                o.Id = IdGenerater.GetNextId();
                o.ProductCode = products.Procode;
            });
            await _basProductRepo.InsertAsync(products);
            //await _basProStepRelRepo.InsertRangeAsync(items);
            await _bomRepo.InsertRangeAsync(bomItems);
            return products.Id;
        }


        /// <summary>
        /// 更新产品
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult> UpdateAsync(BasProductBomDto input)
        {
            bool isUpdete = await _basProductMgr.UpdateAsync(input.Procode, input.Proname, input.Id ?? 0);
            if (isUpdete)
            {
                var product = await _basProductRepo.GetAsync(input.Id ?? 0);
                if (product != null)
                {

                    var preserve = await _bomRepo.Where(x => x.ProductCode == product.Procode && !x.IsDeleted).ToListAsync();
                    if (preserve != null)
                        await _bomRepo?.RemoveRangeAsync(preserve);

                    product.Status = input.Status;
                    product.Procode = input.Procode;
                    product.Proname = input.Proname;
                    product.Remark = input.Remark;
                    product.TCode = input.TCode;
                    await _basProductRepo.UpdateAsync(product);

                    var bomItems = Mapper.Map<List<BasBom>>(input.BomItems);
                    bomItems.ForEach(o =>
                    {
                        o.Id = IdGenerater.GetNextId();
                        o.ProductCode = input.Procode;
                    });
                    await _bomRepo.InsertRangeAsync(bomItems);

                }
            }
            return AppSrvResult();
        }

        /// <summary>
        /// 更新产能模型
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult> UpdateStepMaterialAsync(BasProductDto input)
        {
            var stepMaterials = new List<BasProductStepRelationMaterial>();
            var stepDevices = new List<BasProductStepRelationSource>();
            var stepDtos = await _basProStepRelRepo.Where(x => x.ProCode == input.Procode && x.TecCode == input.TCode && !x.IsDeleted).ToListAsync();
            if (stepDtos.Any())
            {
                //清空工序物料信息
                var preserve = await _basProStepRelMatRepo.Where(x => stepDtos.Select(x => x.Id).Contains(x.MainId) && !x.IsDeleted).ToListAsync();
                if (preserve.Any())
                    await _basProStepRelMatRepo?.RemoveRangeAsync(preserve);

                //清空工序设备信息
                var devices = await _basProStepRelSouRepo.Where(x => stepDtos.Select(x => x.Id).Contains(x.MainId) && !x.IsDeleted).ToListAsync();
                if (devices.Any())
                    await _basProStepRelSouRepo?.RemoveRangeAsync(devices);

                //判断工序数据是否相同
                if (stepDtos.Count != input.StepItems.Count)
                {
                    //不相同，删除重新添加
                    await _basProStepRelRepo?.RemoveRangeAsync(stepDtos);

                    var objs = await dealCapacityStep(input);
                    if (objs.materials.Any())
                        stepMaterials.AddRange(objs.materials.ToList());
                    if (objs.devices.Any())
                        stepDevices.AddRange(objs.devices.ToList());

                }
                else
                {
                    stepDtos.ForEach(o =>
                    {
                        o.Id = o.Id;
                        o.TecCode = input.TCode;
                        var materialDtos = input.StepItems.Where(x => x.StepCode == o.StepCode).FirstOrDefault();

                        var objs = DealStepMaterials(materialDtos, o.Id);
                        if(objs.materials.Any())
                            stepMaterials.AddRange(objs.materials);
                        if (objs.devices.Any())
                            stepDevices.AddRange(objs.devices);
                    });
                }
            }
            else
            {
                var objs = await dealCapacityStep(input);
                if (objs.materials.Any())
                    stepMaterials.AddRange(objs.materials.ToList());
                if (objs.devices.Any())
                    stepDevices.AddRange(objs.devices.ToList());
            }
            await _basProStepRelMatRepo.InsertRangeAsync(stepMaterials);
            await _basProStepRelSouRepo.InsertRangeAsync(stepDevices);

            return AppSrvResult();
        }

        public async Task<(List<BasProductStepRelationMaterial> materials, List<BasProductStepRelationSource> devices)> dealCapacityStep(BasProductDto input)
        {
            var reData = new List<BasProductStepRelationMaterial>();
            var reDevice = new List<BasProductStepRelationSource>();
            input.StepItems.ForEach(o =>
            {
                o.Id =  o.Id==0 ? IdGenerater.GetNextId():o.Id;
                o.TecCode = input.TCode;

                var objs = DealStepMaterials(o, o.Id);
                if (objs.materials.Any())
                    reData.AddRange(objs.materials);
                if (objs.devices.Any())
                    reDevice.AddRange(objs.devices);
            });
            var stepRelItems = Mapper.Map<List<BasProductStepRelation>>(input.StepItems);
            await _basProStepRelRepo.InsertRangeAsync(stepRelItems);

            return (reData, reDevice);
        }

        public (List<BasProductStepRelationMaterial> materials, List<BasProductStepRelationSource> devices) DealStepMaterials(BasProductStepRelationDto input,long? id)
        {
            var reData = new List<BasProductStepRelationMaterial>();
            var reDevice = new List<BasProductStepRelationSource>();
            var stepMaterialItems = Mapper.Map<List<BasProductStepRelationMaterial>>(input.MaterialsIn);
            stepMaterialItems.ForEach(k =>
            {
                k.Id = IdGenerater.GetNextId();
                k.MainId = id;
                k.MaterialType = "in";
            });
            reData.AddRange(stepMaterialItems);

            stepMaterialItems = Mapper.Map<List<BasProductStepRelationMaterial>>(input.MaterialsOut);
            stepMaterialItems.ForEach(k =>
            {
                k.Id = IdGenerater.GetNextId();
                k.MainId = id;
                k.MaterialType = "out";
            });
            reData.AddRange(stepMaterialItems);

            var stepDeviceItems = Mapper.Map<List<BasProductStepRelationSource>>(input.MainItems);
            stepDeviceItems.ForEach(k =>
            {
                k.Id = IdGenerater.GetNextId();
                k.MainId = id;
            });
            reDevice.AddRange(stepDeviceItems);

            stepDeviceItems = Mapper.Map<List<BasProductStepRelationSource>>(input.AssistItems);
            stepDeviceItems.ForEach(k =>
            {
                k.Id = IdGenerater.GetNextId();
                k.MainId = id;
            });
            reDevice.AddRange(stepDeviceItems);

            return (reData, reDevice);
        }




        /// <summary>
        /// 更新产品产能
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult> UpdateProductCapacityAsync(BasProductDto input)
        {
            //var stdCaps = input.StandardCapacityItems?
            //    .Select(x => new BasProductStandardCapacity
            //    {
            //        Id = IdGenerater.GetNextId(),
            //        StepCode = x.StepCode,
            //        LineCode = x.LineCode,
            //        ProCode = input.Procode,
            //        DeviceCode = x.DeviceCode,
            //        SolutionName = x.SolutionName,
            //        Capacity = x.Capacity,
            //        WorkUnitTime = x.WorkUnitTime,
            //        StandardWorkTime = x.StandardWorkTime,
            //        StationCode = x.StationCode,
            //        Remark = x.Remark,
            //    }).ToList();
            //if (stdCaps != null)
            //{
            //    var s = stdCaps.Find(x =>
            //    {
            //        if (stdCaps.Exists(y => y.Id != x.Id && y.StepCode == x.StepCode && y.SolutionName == x.SolutionName))
            //            return true;
            //        else
            //            return false;
            //    });
            //    if (s != null)
            //    {
            //        var step = input.StepItems.Find(x => x.StepCode == s.StepCode);
            //        return Problem(HttpStatusCode.BadRequest, $"{step?.StepName}下的方案{s.SolutionName}已存在，请修改方案名称！");
            //    }
            //}
            //stdCaps = Mapper.Map<List<BasProductStandardCapacity>>(stdCaps);

            //if (stdCaps.Count > 0)
            //{
            //    var relations = await _basProductStandardCapacityRepo.Where(x => x.ProCode == input.Procode, false, false).ToListAsync();
            //    await _basProductStandardCapacityRepo.RemoveRangeAsync(relations);

            //    await _basProductStandardCapacityRepo.InsertRangeAsync(stdCaps);
            //}

            return AppSrvResult();
        }

        /// <summary>
        /// 删除产品
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult> DeleteAsync(long id)
        {
            var step = await _basProductRepo.GetAsync(id);
            if (step != null)
            {
                var o = await _basOrderProductRepo.Where(x => !x.IsDeleted && x.ProductCode == step.Procode).ToListAsync();
                if(o.Any())
                {
                    return Problem(HttpStatusCode.BadRequest, "该产品所属订单目前为有效状态，不可以删除！");
                }

                var relations = await _basProStepRelRepo.Where(x => !x.IsDeleted && x.ProCode == step.Procode, false, false).ToListAsync();
                relations.ForEach(o =>
                {
                    o.IsDeleted = true;
                });
                await _basProStepRelRepo.UpdateRangeAsync(relations);

                step.IsDeleted = true;
                await _basProductRepo.UpdateAsync(step);
            }
            return AppSrvResult();


        }

        /// <summary>
        /// 更新状态
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult> ChangeStatusAsync(ChangeStatusDto input)
        {
            if (input.CodeList.Count == 0)
                return Problem(HttpStatusCode.BadRequest, "编码列表不能为空！");

            var steps = await _basProductRepo.Where(x => input.CodeList.Contains(x.Procode ?? "-1"), false, false).ToListAsync();
            if (input.Status == 0)
            {
                steps.ForEach(x => { x.Status = 0; });
            }
            else
            {
                steps.ForEach(x => { x.Status = 1; });
            }

            await _basProductRepo.UpdateRangeAsync(steps);

            return AppSrvResult();
        }



        public async Task<ResultJson> ImportAsync(List<BasProductDto> dtos)
        {
            var emptyName = dtos.Where(x => string.IsNullOrWhiteSpace(x.Proname)).Any();
            if (emptyName)
                return new ResultJson("请确保您所导入产品名称都必填，维护后再试！");
            var emptyTname = dtos.Where(x => string.IsNullOrWhiteSpace(x.TName)).Any();
            if (emptyTname)
                return new ResultJson("请确保您所导入产品的工艺路线都必填，维护后再试！");

            dtos.ForEach(async x =>
            {
                if (string.IsNullOrWhiteSpace(x.Procode))
                    x.Procode = _basNumberManagementService.GetNumberBySimpleName(CommonConst.PRODUCTNUMBER).Result;
                x.Status = x.StatusDis == "启用" ? 1 : 0;
                var tecDto = _basTecRepo.Where(x => !x.IsDeleted && x.Tname == x.Tname).ToList();
                x.TCode = tecDto?.FirstOrDefault()?.Tcode ??"";

            });

            var entities = Mapper.Map<List<BasProduct>>(dtos);

            var resultObj = await _basProductMgr.ImportAsync(entities);

            if (resultObj.insertEntities.Any())
                await _basProductRepo.InsertRangeAsync(resultObj.insertEntities);

            if (resultObj.updateEntities.Any())
                await _basProductRepo.UpdateRangeAsync(resultObj.updateEntities);

            return new ResultJson(resultObj.Msg);
        }
    }
}
