﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasProductStepRelationAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductTechnologyRelationAggregate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class BasProductStepRelationMaterialConfig : AbstractEntityTypeConfiguration<BasProductStepRelationMaterial>
    {
        public override void Configure(EntityTypeBuilder<BasProductStepRelationMaterial> builder)
        {
            base.Configure(builder);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.MainId).HasColumnName("mainid");
            builder.Property(x => x.MaterialCode).HasColumnName("materialcode");
            builder.Property(x => x.Qty).HasColumnName("qty");
            builder.Property(x => x.MaterialType).HasColumnName("materialtype");
            builder.Property(x => x.BatchQty).HasColumnName("batchqty");
            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.CreateBy).HasColumnName("createby");
            builder.Property(x => x.CreateTime).HasColumnName("createtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
        }
    }
}
