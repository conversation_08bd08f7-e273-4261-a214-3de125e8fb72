﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Dtos
{
    public class PartOrderDto : IDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
        public int? Level { get; set; }
        public string? OrderCode { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public int? Status { get; set; }
        public string? TechnologyCode { get; set; }
        public string? TechnologyName { get; set; }

        public long? IdTec { get; set; }

        public decimal? Qty { get; set; }

        public string? CustormCode { get; set; }
        public string? CustormName { get; set; }

        public long? IdCustorm { get; set; }

        public DateTime? DeliveryTime { get; set; }

        public string? ProductCode { get; set; }
        public string? ProductName { get; set; }
        public long? IdProduct { get; set; }
        /// <summary>
        /// 创建名
        /// </summary>
        public string? CreatName { get; set; }
        /// <summary>
        /// 更新人
        /// </summary>
        public string? ModifyName { get; set; }

        public long CreateBy { get; set; }

        /// <summary>
        /// 创建时间/注册时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 最后更新人
        /// </summary>
        public long? ModifyBy { get; set; }
   
        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        public List<PartOrderMaterialDto> Materials { get; set; }

        public List<PartOrderStepDto> Steps { get; set; }


    }

    public class PartOrderPagedDto : SearchPagedDto
    {
        public string? OrderCode { get; set; }
        public long? IdProduct { get; set; }

        public long? IdCustorm { get; set; }
        public long? IdTec { get; set; }
        public long? Status { get; set; }

    }
}
