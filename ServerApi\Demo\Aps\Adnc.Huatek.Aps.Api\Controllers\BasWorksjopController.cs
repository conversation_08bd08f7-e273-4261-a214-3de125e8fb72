﻿using Adnc.Demo.Shared.Const;
using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Application.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Adnc.Huatek.Aps.Api.Controllers
{
    [Route($"{RouteConsts.ApsRoot}/worksjop")]
    [ApiController]
    public class BasWorksjopController : AdncControllerBase
    {
        private readonly IBasWorksjopService _stationSrv;

        public BasWorksjopController(IBasWorksjopService stationSrv) => _stationSrv = stationSrv;

        /// <summary>
        /// 获取所有信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getallasync")]
        public async Task<ActionResult<List<BasWorksjopDto>>> GetAllAsync() => await _stationSrv.GetAllAsync();


        /// <summary>
        /// 获取所有信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getworkshop")]
        public async Task<ActionResult<List<dynamic>>> GetWorkShopAsync() => await _stationSrv.GetAllWorkshopAsync();

        /// <summary>
        /// 新增车间
        /// </summary>
        /// <param name="input">车间信息</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("createasync")]
        public async Task<ActionResult<long>> CreateAsync([FromBody] BasWorksjopDto input)
            => CreatedResult(await _stationSrv.CreateAsync(input));

        /// <summary>
        /// 删除车间
        /// </summary>
        /// <param name="id">车间ID</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpDelete("deleteasync/{id}")]
        public async Task<ActionResult> DeleteAsync([FromRoute] long id)
            => Result(await _stationSrv.DeleteAsync(id));


        /// <summary>
        /// 修改车间
        /// </summary>
        /// <param name="id">id</param>
        /// <param name="input">车间信息</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPut("updateasync")]
        public async Task<ActionResult> UpdateAsync([FromBody] BasWorksjopDto input)
            => Result(await _stationSrv.UpdateAsync(input));



        /// <summary>
        /// 变更车间状态
        /// </summary>
        /// <param name="id">车间ID</param>
        /// <param name="status">状态</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPut("batch/changestatus/{id}")]
        public async Task<ActionResult> ChangeStatus([FromRoute] long id, [FromBody] SimpleDto<int> status)
            => Result(await _stationSrv.ChangeStatusAsync(id, status.Value));

        /// <summary>
        /// 批量变更车间状态
        /// </summary>
        /// <param name="input">车间Ids与状态</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPut("changestatus")]
        public async Task<ActionResult> ChangeStatus([FromBody] UpdateStatusDto input)
            => Result(await _stationSrv.ChangeStatusAsync(input));

        /// <summary>
        /// 获取车间列表分页
        /// </summary>
        /// <param name="search">查询条件</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("page")]
        public async Task<ActionResult<PageModelDto<BasWorksjopDto>>> GetPagedAsync([FromBody] BasStationPagedDto search)
            => await _stationSrv.GetPagedAsync(search);

        /// <summary>
        /// 获取单个车间详情信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("current/{id}")]
        public async Task<ActionResult<ResultJson>> GetCurrentUserInfoAsync(long id) => await _stationSrv.GetAppointAsync(id);

    }
}
