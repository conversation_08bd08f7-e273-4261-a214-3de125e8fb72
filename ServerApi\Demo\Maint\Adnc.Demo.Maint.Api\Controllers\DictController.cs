﻿namespace Adnc.Demo.Maint.Api.Controllers;

/// <summary>
/// 字典管理
/// </summary>
[Route($"{RouteConsts.MaintRoot}/dicts")]
[ApiController]
public class DictController : AdncControllerBase
{
    private readonly IDictAppService _dictAppService;

    public DictController(IDictAppService dictAppService) =>
        _dictAppService = dictAppService;

    /// <summary>
    /// 新增字典
    /// </summary>
    /// <param name="input"><see cref="DictCreationDto"/></param>
    /// <returns></returns>
    [AdncAuthorize(PermissionConsts.Dict.Create)]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [HttpPost("add")]
    public async Task<ActionResult<long>> CreateAsync([FromBody] DictCreationDto input) =>
        CreatedResult(await _dictAppService.CreateAsync(input));

    /// <summary>
    /// 修改字典
    /// </summary>
    /// <param name="id">id</param>
    /// <param name="input"><see cref="DictUpdationDto"/></param>
    /// <returns></returns>
    [AdncAuthorize(PermissionConsts.Dict.Update)]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [HttpPost("edit")]
    public async Task<ActionResult<long>> UpdateAsync([FromRoute] long id, [FromBody] DictUpdationDto input) =>
        Result(await _dictAppService.UpdateAsync(id, input));

    /// <summary>
    /// 删除字典
    /// </summary>
    /// <param name="id">字典ID</param>
    /// <returns></returns>
    [HttpDelete("delete/{id}")]
    [AdncAuthorize(PermissionConsts.Dict.Delete)]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    public async Task<ActionResult> DeleteAsync([FromRoute] long id) =>
        Result(await _dictAppService.DeleteAsync(id));

    /// <summary>
    /// 获取字典列表
    /// </summary>
    /// <returns></returns>
    [HttpPost("page")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [AllowAnonymous]
    public async Task<ActionResult<PageModelDto<DictDto>>> GetListAsync([FromBody] DictSearchDto search) =>
        await _dictAppService.GetListAsync(search);

    /// <summary>
    /// 获取单个字典数据
    /// </summary>
    /// <returns></returns>
    [HttpGet("/getbyid/{id}")]
    [AdncAuthorize(PermissionConsts.Dict.GetList, AdncAuthorizeAttribute.JwtWithBasicSchemes)]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<DictDto>> GetAsync([FromRoute] long id)
    {
        var dict = await _dictAppService.GetAsync(id);
        if (dict is not null)
            return dict;

        return NotFound();
    }

    [AllowAnonymous]
    [HttpGet("getbyname/{name}")]
    public async Task<ActionResult<List<EnumDto>>> GetByCodeAsync([FromRoute] string name)
    {

        var dict = await _dictAppService.GetByCodeAsync(name);
        if (dict is not null)
            return  dict;
        return NotFound();
    }


    [AllowAnonymous]
    [HttpGet("gettreebyname/{name}")]
    public async Task<ActionResult<EnumTreeDto>> GetChildByCodeAsync([FromRoute] string name)
    {
        var dict = await _dictAppService.GetChildByCodeAsync(name);
        if (dict is not null)
            return dict;
        return NotFound();
    }
     



    /// <summary>
    /// 查询单个字典详情
    /// </summary>
    /// <returns></returns>
    [HttpPost("/getDetail")]
    //[AdncAuthorize(PermissionConsts.Dict.GetList, AdncAuthorizeAttribute.JwtWithBasicSchemes)]
    [AllowAnonymous]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<DictDto>> GetDetail([FromBody] DictSearchDetailDto input)
    {
        var dict = await _dictAppService.GetDetail(input);
        if (dict is not null)
            return dict;

        return NotFound();
    }
}