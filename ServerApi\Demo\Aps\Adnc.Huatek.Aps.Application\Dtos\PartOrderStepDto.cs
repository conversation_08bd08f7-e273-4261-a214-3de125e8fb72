﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Dtos
{
    public class PartOrderStepDto : IDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// BomID
        /// </summary>
        public long? IdStep { get; set; }

        public string? StepCode { get; set; }

        public string? StepName { get; set; }
        public decimal? StepTime { get; set; }
        public decimal? WaitTime { get; set; }

        public int? IsKey { get; set; }

        public int? RelationType { get; set; }

        public long? IdOrder { get; set; }
        /// <summary>
        /// 创建人ID
        /// </summary>
        public long? CreateBy { get; set; }

        /// <summary>
        /// 创建时间/注册时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 最后更新人
        /// </summary>
        public long? ModifyBy { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        public int? Sort { get; set; }
    }
}
