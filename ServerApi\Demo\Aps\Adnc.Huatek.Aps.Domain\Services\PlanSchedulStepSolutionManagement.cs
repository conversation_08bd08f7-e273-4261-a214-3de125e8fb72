﻿using Adnc.Huatek.Aps.Domain.Aggregates.PlanSchedulStepSolutionAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.SysTestAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.SysUserAggregate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Services
{
    public class PlanSchedulStepSolutionManagement : IDomainService
    {
        private readonly IEfBasicRepository<PlanSchedulStepSolution> _planSchedulStepSolutionManagement;

        public PlanSchedulStepSolutionManagement(IEfBasicRepository<PlanSchedulStepSolution> planSchedulStepSolutionManagement)
        {
            _planSchedulStepSolutionManagement = planSchedulStepSolutionManagement;
        }
    }
}
