﻿using Adnc.Infra.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulStepDetailsAggregate
{
    public class PlanProductionSchedulStepDetails : EfFullAuditEntity
    {
        public string? PlanCode { get; set; }
        
        public string? TaskCode { get; set;}

        public string? StepCode { get; set; }

        public string? SchedulCode { get; set; }

        public DateTime? SchedulDate { get; set; }

        public string? Remark { get; set; }

        public int Exenum { get; set; }

        public double? RemainingTime { get; set; }                              


    }
}
