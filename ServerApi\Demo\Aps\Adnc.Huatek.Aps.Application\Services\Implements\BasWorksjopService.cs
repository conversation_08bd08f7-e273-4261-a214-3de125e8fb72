﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.BasWorksjop;
using Adnc.Huatek.Aps.Domain.Services;
using Adnc.Infra.IdGenerater.Yitter;
using Adnc.Shared.Application.Contracts.ResultModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services.Implements
{
   
    public class BasWorksjopService : AbstractAppService, IBasWorksjopService
    {
        private readonly BasWorksjopManager _stationMgr;
        private readonly IEfBasicRepository<BasWorksjop> _stationRepo;

        public BasWorksjopService(
            IEfBasicRepository<BasWorksjop> stationRepo,
            BasWorksjopManager stationMgr)
        {
            _stationRepo = stationRepo;
            _stationMgr = stationMgr;
        }

        /// <summary>
        /// 新增车间
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<AppSrvResult<long>> CreateAsync(BasWorksjopDto input)
        {
            var menu = Mapper.Map<BasWorksjop>(input);

            var bom = await _stationMgr.CreateAsync(menu.Code, menu.Name, menu.Remark);

            menu.Id = IdGenerater.GetNextId();
            await _stationRepo.InsertAsync(bom);
            return menu.Id;
        }

        /// <summary>
        /// 删除车间
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<AppSrvResult> DeleteAsync(long id)
        {

            var userProfile = await _stationRepo.GetAsync(id);
            if (userProfile != null)
            {
                var menu = Mapper.Map<BasWorksjop>(userProfile);
                menu.IsDeleted = true;
                await _stationRepo.UpdateAsync(menu);
            }
            return AppSrvResult();
        }

        /// <summary>
        /// 获取所有车间
        /// </summary>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<List<BasWorksjopDto>> GetAllAsync()
        {
            var sysTests = await _stationRepo.Where(x=>!x.IsDeleted && x.Status == 1).ToListAsync();

            var sysTestsDto = Mapper.Map<List<BasWorksjopDto>>(sysTests);
            return sysTestsDto;
        }
        // <summary>
        /// 获取所有车间
        /// </summary>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<List<dynamic>> GetAllWorkshopAsync()
        {
            var sysTests = await _stationRepo.Where(x => !x.IsDeleted && x.Status == 1).ToListAsync();
            var items = sysTests.Select(x => new { Wcode = x.Code, Wname = x.Name, Id = x.Id }).ToList<dynamic>();
            return items;
        }



        /// <summary>
        /// 获取指定车间
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<ResultJson> GetAppointAsync(long id)
        {
            var userProfile = await _stationRepo.Where(x => x.IsDeleted == false && x.Id == id).FirstAsync();

            if (userProfile == null)
                return new ResultJson("查询成功！无数据", null);
            var menu = Mapper.Map<BasWorksjopDto>(userProfile);
            return new ResultJson("查询成功", menu, 0);
        }

        /// <summary>
        /// 修改车间
        /// </summary>
        /// <param name="id"></param>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<AppSrvResult> UpdateAsync(BasWorksjopDto input)
        {
            bool isUpdete = await _stationMgr.UpdateAsync(input.Code, input.Name, input.Id ?? 0);
            if (isUpdete)
            {
                input.TrimStringFields();
                var station = Mapper.Map<BasWorksjop>(input);
                var userProfile = await _stationRepo.GetAsync(input.Id??0);
                if (userProfile != null)
                {
                    userProfile.Status = station.Status;
                    userProfile.Code = station.Code;
                    userProfile.Name = station.Name;
                    userProfile.Remark = station.Remark;
                    userProfile.ModifyTime = station.ModifyTime;
                    userProfile.ModifyBy = station.ModifyBy;
                    await _stationRepo.UpdateAsync(userProfile);
                }
            }

            return AppSrvResult();
        }

        public async Task<AppSrvResult> ChangeStatusAsync(long id, int status)
        {
            var userProfile = await _stationRepo.GetAsync(id);
            if (userProfile != null)
            {
                //如果数据已作废是否可以启用
                {

                }
                userProfile.Status = status;
                await _stationRepo.UpdateAsync(userProfile);
            }
            return AppSrvResult();
        }

        public async Task<AppSrvResult> ChangeStatusAsync(UpdateStatusDto input)
        {
            string[] Ids = new string[input.Ids.Length];
            for (int i = 0; i < input.Ids.Length; i++)
            {
                Ids[i] = input.Ids[i].ToString();
            }
            var userProfile = await _stationRepo.Where(x => x.Status != 2 & Ids.Contains(x.Id.ToString())).ToListAsync();
            if (userProfile != null)
            {
                //type==0为删除，type ==1为启用停用
                if (input.Type == 0)
                {
                    foreach (var item in userProfile)
                    {
                        item.IsDeleted = true;
                    }
                }
                else
                {
                    foreach (var item in userProfile)
                    {
                        item.Status = input.Status;
                    }
                }
                await _stationRepo.UpdateRangeAsync(userProfile);
            }
            return AppSrvResult();
        }


        public async Task<PageModelDto<BasWorksjopDto>> GetPagedAsync(BasStationPagedDto search)
        {
            search.TrimStringFields();
            var whereExpression = ExpressionCreator
                                                .New<BasWorksjop>()
                                                .AndIf(search.Code.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Code!, $"%{search.Code}%"))
                                                .AndIf(search.Name.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Name!, $"%{search.Name}%"))
                                                .AndIf(search.Status != -1, x => x.Status == search.Status)
                                                .And(x => x.IsDeleted == false);

            var total = await _stationRepo.CountAsync(whereExpression);
            if (total == 0)
                return new PageModelDto<BasWorksjopDto>(search);

            var entities = await _stationRepo
                                            .Where(whereExpression)
                                            .OrderByDescending(x => x.Id)
                                            .Skip(search.SkipRows())
                                            .Take(search.PageSize)
                                            .ToListAsync();

            var userDtos = Mapper.Map<List<BasWorksjopDto>>(entities);
            return new PageModelDto<BasWorksjopDto>(search, userDtos, total);
        }

    }
}
