﻿using Adnc.Huatek.Aps.Domain.Aggregates.SysTestAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.SysUserAggregate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class SysUserConfig : AbstractEntityTypeConfiguration<SysUser>
    {

        public override void Configure(EntityTypeBuilder<SysUser> builder)
        {
            base.Configure(builder);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.Account).HasColumnName("account");
            builder.Property(x => x.Avatar).HasColumnName("avatar");
            builder.Property(x => x.Birthday).HasColumnName("birthday");
            builder.Property(x => x.Deptid).HasColumnName("deptid");
            builder.Property(x => x.Email).HasColumnName("email");
            builder.Property(x => x.Name).HasColumnName("name");
            builder.Property(x => x.Password).HasColumnName("password");
            builder.Property(x => x.Phone).HasColumnName("phone");
            builder.Property(x => x.Roleids).HasColumnName("roleids");
            builder.Property(x => x.Salt).HasColumnName("salt");
            builder.Property(x => x.Sex).HasColumnName("sex");
            builder.Property(x => x.Status).HasColumnName("status");

            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.CreateBy).HasColumnName("createby");
            builder.Property(x => x.CreateTime).HasColumnName("createtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
        }
    }
}
