﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.BasDevicePreserveAggregate;
using Adnc.Infra.Redis.Caching.Core.Interceptor;
using Adnc.Shared.Application.Contracts.ResultModels;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services
{
    
    public interface IBasDeviceService : IAppService
    {
        /// <summary>
        /// 获取所有工位信息
        /// </summary>
        /// <returns></returns>
        [OperateLog(LogName = "获取所有工位信息")]
        Task<List<BasDeviceDto>> GetAllAsync();

        /// <summary>
        /// 获取单个工位信息
        /// </summary>
        /// <returns></returns>
        [OperateLog(LogName = "获取指定工位信息")]
        Task<ResultJson> GetAppointAsync(long id);

        /// <summary>
        /// 新增工位
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [OperateLog(LogName = "新增工位")]
        Task<AppSrvResult<long>> CreateAsync(BasDeviceDto input);

        /// <summary>
        /// 修改工位
        /// </summary>
        /// <param name="id"></param>
        /// <param name="input"></param>
        /// <returns></returns>
        [OperateLog(LogName = "修改工位")]
        Task<AppSrvResult> UpdateAsync(BasDeviceDto input);


        /// <summary>
        /// 删除工位
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [OperateLog(LogName = "删除工位")]
        Task<AppSrvResult> DeleteAsync(long id);



        /// <summary>
        /// 修改工位状态
        /// </summary>
        /// <param name="id"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        [OperateLog(LogName = "修改工位状态")]
        Task<AppSrvResult> ChangeStatusAsync([CachingParam] long id, int status);

        /// <summary>
        /// 批量修改工位状态
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        [OperateLog(LogName = "批量修改工位状态")]
        Task<AppSrvResult> ChangeStatusAsync(UpdateStatusDto input);


        /// <summary>
        /// 获取用户列表
        /// </summary>
        /// <param name="search"></param>
        /// <returns></returns>
        Task<PageModelDto<BasDeviceDto>> GetPagedAsync(BasDevicePagedDto search);

        /// <summary>
        /// 获取设备日历信息
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        Task<List<BasDeviceCalendarResultDto>> GetCalendarAsync(BasDeviceCalendarDto dto);

        /// <summary>
        /// 更新设备日历信息
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        Task<AppSrvResult> UpdateCalendarAsync(int t, BasDeviceCalendarDto dto);

        [OperateLog(LogName = "更新状态")]
        Task<AppSrvResult> ChangeStatusAsync(ChangeStatusDto input);


        /// <summary>
        /// 获取设备保养信息
        /// </summary>
        /// <returns></returns>
        [OperateLog(LogName = "获取设备保养信息")]
        Task<ActionResult<ResultJson>> GetDevicePreserveAsync(long id);

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="dtos"></param>
        /// <returns></returns>
        [OperateLog(LogName = "导入设备")]
        Task<ResultJson> ImportAsync(List<BasDeviceDto> dtos);
        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="dtos"></param>
        /// <returns></returns>
        [OperateLog(LogName = "导入设备状态")]
        Task<ResultJson> ImportDeviceStateAsync(List<PartDeviceStateDto> dtos);

        

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="dtos"></param>
        /// <returns></returns>
        [OperateLog(LogName = "获取全类型设备")]
        Task<List<DeviceTypeTreeDto>> QueryDeviceAllType(BasDevicePagedDto search);

        [OperateLog(LogName = "获取设备状态")]
        Task<List<DeviceStateDto>> QueryDeviceState(QueryDevStateDto queryData);

        [OperateLog(LogName = "根据id获取设备状态")]
        Task<ResultJson> GetDeviceStateAsync(long id);

        [OperateLog(LogName = "获取所有设备状态")]
        Task<PageModelDto<PartDeviceStateDto>> GetDeviceStateAsync(BasDevicePagedDto search);

        [OperateLog(LogName = "更新设备状态根据id")]
        Task<AppSrvResult> UpdateDeviceStateAsync(PartDeviceStateDto input);

        [OperateLog(LogName = "删除设备状态根据id")]
        Task<AppSrvResult> DeleteDeviceStateAsync(long id);

    }
}
