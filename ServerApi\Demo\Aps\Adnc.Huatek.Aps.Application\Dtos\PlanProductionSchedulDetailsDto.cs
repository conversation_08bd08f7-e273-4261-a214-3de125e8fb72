﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate;
using Adnc.Shared;
using System;
using System.Collections.Generic;
using System.Drawing.Printing;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Dtos
{

    public class PlanProductionSchedulDetailsDto : IDto
    {
        /// <summary>
        /// 产品编码
        /// </summary>
        public string? Procode { get; set; }
    }

    public class BasOrderInfo : IDto
    {

        public List<PartSchedulePlanRuleDto> Rules { get; set; }

        /// <summary>
        /// 优先级
        /// </summary>
        public int Priority { get; set; }
        /// <summary>
        /// 生产状态
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 订单编号
        /// </summary>
        public string OrderNumber { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 交付日期
        /// </summary>
        public DateTime? DeliveryDate { get; set; }

        /// <summary>
        ///  产品名称
        /// </summary>
        public string? ProductName { get; set; }

        /// <summary>
        ///  产品编码
        /// </summary>
        public string? ProductCode { get; set; }

        /// <summary>
        /// 产品数量
        /// </summary>
        public decimal Quantity { get; set; }
        /// <summary>
        /// 销售单号
        /// </summary>
        public string? SaleNumber { get; set; }
        public long? ProductId { get; set; }
        public string? TCode { get; set; }
        public string? TName { get; set; }
        public string? Remark { get; set; }
        public decimal? Qty { get; set; }

        public string? PlanCode { get; set; }

        public string? SchedulCode { get; set; }
        //工序编码
        public string? StepCode { get; set; }

        //产品编码
        public string? ProCode { get; set; }

        public List<string>? Lines { get; set; }

        [JsonConverter(typeof(DateTimeJsonConverter))]
        public DateTime? ScheduleTime {  get; set; }


        public DateTime? PlanStartTime { get; set; }


        public DateTime? PlanEndTime { get; set; }

        public string? Creator { get; set; }

    }

    public class PlanProductionSchedulDetailsResultDto : IDto
    {
        /// <summary>
        /// 定单产品基本信息
        /// </summary>
        public BasOrderInfo? BasOrderInfo { get; set; }

        /// <summary>
        /// 加工工序列表
        /// </summary>
        //public List<BasProductStepRelationDto> StepItems { get; set; }

        /// <summary>
        /// 生产计划列表
        /// </summary>
        //public List<PartPlanPendingDto> SolutionItems { get; set; }

    }

    public class PlanProductionSchedulGanttDto : IDto
    {
        public string? OrderNumber { get; set; }
        /// <summary>
        /// 交付日期
        /// </summary>
        public DateTime? DeliveryDate { get; set; }
        /// <summary>
        /// 计划开始日期
        /// </summary>
        public DateTime? PlanStart { get; set; }
        /// <summary>
        /// 计划结束日期
        /// </summary>
        public DateTime? PlanEnd { get; set; }
    }

    public class ProductionSchedulGanttDto : IDto
    {
        /// <summary>
        /// 订单编码
        /// </summary>
        public string? OrderNumber { get; set; }
        /// <summary>
        /// 产品编码
        /// </summary>
        public string? ProductCode { get; set; }
        /// <summary>
        /// 排产编码
        /// </summary>
        public string? ScheduleCode { get; set; }
        /// <summary>
        /// 排产状态
        /// </summary>
        public string? ScheduleStatus { get; set; }
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? BeginTime { get; set; }
        /// <summary>
        /// 截止时间
        /// </summary>
        public DateTime? EndTime { get; set; }
    }

    public class OrderProdcutGanttDto : IDto
    {
        /// <summary>
        /// 订单编码
        /// </summary>
        public string? OrderNumber { get; set; }
        /// <summary>
        /// 产品编码
        /// </summary>
        public string? ProductCode { get; set; }
    }

    public class PlanProductionSchedulGanttResultDto : IDto
    {
        /// <summary>
        /// 订单编号
        /// </summary>
        public string? OrderNumber { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public List<ProductScheduleProductItem>? ScheduleItems { get; set; } = new();
    }

    public class ProductScheduleProductItem
    {
        /// <summary>
        /// 产品名称
        /// </summary>
        public string? ProductName { get; set; }
        /// <summary>
        /// 排产编码
        /// </summary>
        public string? ScheduleCode { get; set; }
        /// <summary>
        /// 排产状态
        /// </summary>
        public string? ScheduleStatus { get; set; }

        public List<ProductScheduleGanttItem>? GanttItems { get; set; } = new();
    }

    public class PlanProductionSchedulData
    {
        /// <summary>
        /// 订单编号
        /// </summary>
        public string? OrderNumber { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string? ProductName { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string? ProductCode { get; set; }
        /// <summary>
        /// 工序名称
        /// </summary>
        public string? StepName { get; set; }
        /// <summary>
        /// 工序编码
        /// </summary>
        public string? StepCode { get; set; }
        /// <summary>
        /// 工序编码
        /// </summary>
        public string? PlanCode { get; set; }
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime? SchedulDate { get; set; }
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime? PlanStart { get; set; }
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime? PlanEnd { get; set; }
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime? BeginTime { get; set; }
        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime? EndTime { get; set; }
        /// <summary>
        /// 交付日期
        /// </summary>
        public DateTime? DeliveryDate { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public int? ScheduleStatus { get; set; }
        /// <summary>
        /// 工序排序码
        /// </summary>
        public int StepSort { get; set; }
        /// <summary>
        /// 工序编码
        /// </summary>
        public string? TecStepCode { get; set; }
    }

    public class PlanProductionSchedulDataResultDto : IDto
    {
        /// <summary>
        /// 订单编号
        /// </summary>
        public string? OrderNumber { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string? ProductName { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string? ProductCode { get; set; }
        /// <summary>
        /// 工序名称
        /// </summary>
        public string? StepName { get; set; }
        /// <summary>
        /// 工序编码
        /// </summary>
        public string? StepCode { get; set; }
        /// <summary>
        /// 工序编码
        /// </summary>
        public string? PlanCode { get; set; }
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime? SchedulDate { get; set; }
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime? PlanStart { get; set; }
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime? PlanEnd { get; set; }
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime? BeginTime { get; set; }
        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime? EndTime { get; set; }
        /// <summary>
        /// 交付日期
        /// </summary>
        public DateTime? DeliveryDate { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public int? ScheduleStatus { get; set; }
    }

    public class ProductScheduleGanttItem
    {
        /// <summary>
        /// 工序名称
        /// </summary>
        public string? StepName { internal get; set; }
        /// <summary>
        /// 工序编码
        /// </summary>
        public string? StepCode { internal get; set; }
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime? BeginTime { get; set; }
        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime? EndTime { get; set; }
        /// <summary>
        /// 日期
        /// </summary>
        public List<GanttDate>? DateList { internal get; set; }
    }

    public class PlanProductionSchedulGanttNewResultDto : ProductScheduleGanttItem, IDto
    {
        /// <summary>
        /// 订单编号
        /// </summary>
        public string? OrderNumber { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string? ProductName { private get; set; }
        /// <summary>
        /// 产品数量
        /// </summary>
        public string? ProductCount { get; set; }
        /// <summary>
        /// 已排产数量
        /// </summary>
        public string? ScheduleCount { get; set; }
        /// <summary>
        /// 排产编码
        /// </summary>
        public string? ScheduleCode { get; set; }
        /// <summary>
        /// 计划编码
        /// </summary>
        public string? PlanCode { get; set; }
        /// <summary>
        /// 排产状态
        /// </summary>
        public string? ScheduleStatus { get; set; }
        /// <summary>
        /// 交付日期
        /// </summary>
        [JsonConverter(typeof(DateTimeJsonConverter))]
        public DateTime? DeliveryDate { get; set; }
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? BeginTime { get; set; }
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        public int? Duration => (EndTime - BeginTime)?.Days + 1;

        public PlanGanttDto? GanttData { get; set; }
    }

    public class PlanProductionSchedulGanttForOrderDto : ProductScheduleGanttItem, IDto
    {

        public string? Parent { get; set; }
        /// <summary>
        /// 订单编号
        /// </summary>
        public string? OrderNumber { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string? ProductName { get; set; }
        /// <summary>
        /// 交付日期
        /// </summary>
        //[JsonConverter(typeof(DateTimeJsonConverter))]
        public DateTime? DeliveryDate { get; set; }
        /// <summary>
        /// 开始时间
        /// </summary>
        //[JsonConverter(typeof(DateTimeJsonConverter))]
        public DateTime? BeginTime { get; set; }
        /// <summary>
        /// 结束时间
        /// </summary>
        //[JsonConverter(typeof(DateTimeJsonConverter))]
        public DateTime? EndTime { get; set; }

        public int? Duration => ((EndTime - BeginTime)?.Days + 1) * 24;

    }

    public class PlanProductionSchedulGanttNewResultForPlanDto : PlanProductionSchedulGanttNewResultDto
    {
        public string? ScheduleType { get; set; }
        public DateTime? ScheduleDate { get; set; }
        public DateTime? CreateTime { get; set; }
    }

    public class GanttDate
    {
        /// <summary>
        /// 工序编码
        /// </summary>
        public string? StepCode { get; set; }
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime? BeginTime { get; set; }
        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime? EndTime { get; set; }
    }

    public class ProductionSchedulDeviceGanttResultDto : IDto
    {
        /// <summary>
        /// 订单编号
        /// </summary>
        public string? OrderNumber { get; set; }
        /// <summary>
        /// 排产编号
        /// </summary>
        public string? ScheduleCode { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string? ProductName { get; set; }
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? BeginTime { get; set; }
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }
        /// <summary>
        /// 设备序列
        /// </summary>
        public List<ProductScheduleDeviceGanttItem>? DeviceItems { get; set; } = new();
    }

    public class ProductScheduleDeviceGanttItem
    {
        /// <summary>
        /// 设备名称
        /// </summary>
        public string? DeviceName { get; set; }
        /// <summary>
        /// 设备编码
        /// </summary>
        public string? DeviceCode { get; set; }
        /// <summary>
        /// 日期序列
        /// </summary>
        public List<DeviceGanttDate>? DateList { get; set; }
    }

    public class DeviceGanttDate : GanttDate
    {
        /// <summary>
        /// 工序名称
        /// </summary>
        public string? StepName { get; set; }
    }

    public class ScheduleDeviceGanttItem
    {
        /// <summary>
        /// 设备名称
        /// </summary>
        public string? DeviceName { get; set; }
        /// <summary>
        /// 工序名称
        /// </summary>
        public string? StepName { get; set; }
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? BeginTime { get; set; }
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }
    }

    public class PlanProductionSchedulGanttParentItem
    {
        public string? Id { get; set; }
        public string? ProductName { get; set; }
        public string? StepCode { get; set; }
        public string? StepName { get; set; }
        public DateTime? StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public int? Duration { get; set; }
        /// <summary>
        /// 工序排序码
        /// </summary>
        public int StepSort { get; set; }
    }

    public class PlanProductionSchedulGanttChildItem : PlanProductionSchedulGanttParentItem
    {
        public string? Parent { get; set; }

        public string? ScheduleCode { get; set; }

        public decimal? Progress { get; set; }
    }



    public class PlanGanttDto
    {
        public List<PlanProductionSchedulGanttChildItem> Tasks { get;set; }
        public List<ScheduleGanttLink> Links { get; set; }
    }


    public class ScheduleGanttLink
    {
        public long id { get; set; }
        public  string TargetCode { get; set; }
        public string SourceCode { get; set; }
        public int Type { get; set; }
    }


        public class SchduleStepSolutionItem
    {
        public string? StepCode { get; set; }
        public string? StepName { get; set; }
        public string? SolutionName { get; set; }
        public string? StationCode { get; set;}
        public string? StationName { get; set;}
        public string? LineCode { get; set;}
        public string? LineName { get; set;}
        public string? DeviceCode { get; set;}
        public string? DeviceName { get; set;}
        public decimal? WorkUnitTime { get; set;}
        public decimal? StandardWorkTime { get; set;}
        public decimal? Capacity { get; set;}
    }
}