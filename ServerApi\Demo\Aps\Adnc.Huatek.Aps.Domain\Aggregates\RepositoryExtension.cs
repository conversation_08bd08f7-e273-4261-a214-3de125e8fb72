﻿

using Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulStepDetailsAggregate;

namespace Adnc.Huatek.Aps.Domain
{
    public static class RepositoryExtension
    {
        /// <summary>
        ///  通过sql查询
        /// </summary>
        /// <typeparam name="TResult"></typeparam>
        /// <returns></returns>
        /// <exception cref="NullReferenceException"></exception>
        public static async Task<List<TResult>> GetMaterialsBySqlAsync<TResult>(this IEfRepository<PlanProductionSchedulStepDetails> repository, string sql)
            where TResult : notnull
        {
            var adoQuerier = repository.AdoQuerier ?? throw new NullReferenceException("AdoQuerier is null");
           
            var result = await adoQuerier.QueryAsync<TResult>(sql);
            return result.ToList();
        }
    }
}
