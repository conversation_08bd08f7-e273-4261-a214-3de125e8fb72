﻿using Adnc.Demo.Shared.Rpc.Grpc.Messages;
using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.BasBomAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasBomListAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasClassesAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasDevice;
using Adnc.Huatek.Aps.Domain.Aggregates.StockMaterial;
using Adnc.Huatek.Aps.Domain.Aggregates.BasDevicePreserveAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasHolidayAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasLineAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasLineProductRelationAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasMaterial;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductStandardCapacityAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductStepRelationAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductTechnologyRelationAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasRelation;
using Adnc.Huatek.Aps.Domain.Aggregates.BasRelationDeviceAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStation;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStepAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStepMaterialRelationAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasTechnologyAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasTechnologyStepRelationAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasWorksjop;
using Adnc.Huatek.Aps.Domain.Aggregates.PartOrderAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PartOrderStepAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.SysTestAggregate;
using Adnc.Huatek.Aps.Domain.AggregatesPartOrderMaterialAggregate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductStepRelationDeviceAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PartDeviceStateAggregates;
using Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanRuleAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasDeviceClassAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanStepAggregate;

namespace Adnc.Huatek.Aps.Application
{
    public class ApsProfile : Profile
    {
        public ApsProfile()
        {
            CreateMap<SysTest, SysTestDto>();
            CreateMap<BasStation, BasStationDto>();
            CreateMap<BasStationDto, BasStation>();
            CreateMap<BasDevice, BasDeviceDto>();
            CreateMap<BasDeviceDto, BasDevice>();
            CreateMap<BasStep, BasStepDto>();
            CreateMap<BasStepDto, BasStep>();
            CreateMap<BasMaterial, BasMaterialDto>();
            CreateMap<BasMaterialDto, BasMaterial>();
            CreateMap<BasWorksjop, BasWorksjopDto>();
            CreateMap<BasWorksjopDto, BasWorksjop>();
            CreateMap<BasRelation, BasRelationDto>();
            CreateMap<BasRelationDto, BasRelation>();
            CreateMap<BasRelationDevice, BasRelationDeviceDto>();
            CreateMap<BasRelationDeviceDto, BasRelationDevice>();
            CreateMap<BasTechnologyStepRelationDto, BasTechnologyStepRelation>();
            CreateMap<BasTechnologyStepRelation, BasTechnologyStepRelationDto>();
            CreateMap<BasStepMaterialRelationDto, BasStepMaterialRelation>();
            CreateMap<BasStepMaterialRelation, BasStepMaterialRelationDto>();
            CreateMap<BasTechnologyDto, BasTechnology>();
            CreateMap<BasTechnology, BasTechnologyDto>();
            CreateMap<BasProduct, BasProductDto>();
            CreateMap<BasProductDto, BasProduct>();
            CreateMap<BasProductStandardCapacityDto, BasProductStandardCapacity>();
            CreateMap<BasProductStandardCapacity, BasProductStandardCapacityDto>();
            CreateMap<BasProductStepRelationDto, BasProductStepRelation>();
            CreateMap<BasProductStepRelation, BasProductStepRelationDto>();
            CreateMap<BasProductStepRelationMaterial, BasProductStepRelationMaterialDto>();
            CreateMap<BasProductStepRelationMaterialDto, BasProductStepRelationMaterial>();
            CreateMap<BasProductTechnologyRelation, BasProductTechnologyRelationDto>();
            CreateMap<BasProductTechnologyRelationDto, BasProductTechnologyRelation>();
            CreateMap<BasBom, BasBomDto>();
            CreateMap<BasBomDto, BasBom>();
            CreateMap<BasBomListDto, BasBom>();
            CreateMap<BasBom, BasBomListDto>();
            CreateMap<BasLine, BasLineDto>();
            CreateMap<BasLineDto, BasLine>();
            CreateMap<BasLineProductRelation, BasLineProductRelationDto>();
            CreateMap<BasLineProductRelationDto, BasLineProductRelation>();
            CreateMap<BasClassesDto, BasClasses>();
            CreateMap<BasClasses, BasClassesDto>();
            CreateMap<PartOrderDto, PartOrder>();
            CreateMap<PartOrder, PartOrderDto>();
            CreateMap<PartOrderMaterialDto, PartOrderMaterial>();
            CreateMap<PartOrderMaterial, PartOrderMaterialDto>();
            CreateMap<PartOrderStepDto, PartOrderStep>();
            CreateMap<PartOrderStep, PartOrderStepDto>();
            CreateMap<BasDevicePreserve, BasDeviceCalendarDto>();
            CreateMap<BasDeviceCalendarDto, BasDevicePreserve>();
            CreateMap<BasHolidayDto, BasHoliday>();
            CreateMap<BasHoliday, BasHolidayDto>();
            CreateMap<PlanProductionSchedulDto, PlanProductionSchedule>();
            CreateMap<PlanProductionSchedule, PlanProductionSchedulDto>();
            CreateMap<StockMaterial, StockMaterialDto>();
            CreateMap<StockMaterialDto,StockMaterial>();

            CreateMap<BasProductStepRelationSourceDto, BasProductStepRelationSource>();
            CreateMap<BasProductStepRelationSource, BasProductStepRelationSourceDto>();

            CreateMap<PartDeviceStateDto, PartDeviceState>();
            CreateMap<PartDeviceState, PartDeviceStateDto>();

            CreateMap<BasDevice, DeviceTreeDtos>();
            CreateMap<DeviceTreeDtos, BasDevice>();


            CreateMap<PartSchedulePlanDto, PartSchedulePlan>();
            CreateMap<PartSchedulePlan, PartSchedulePlanDto>();

            CreateMap<PartPlanPendingDto, PartPlanPending>();
            CreateMap<PartPlanPending, PartPlanPendingDto>();

            CreateMap<PartSchedulePlanRuleDto, PartSchedulePlanRule>();
            CreateMap<PartSchedulePlanRule, PartSchedulePlanRuleDto>();

            CreateMap<PartSchedulePlanStepDto, PartSchedulePlanStep>();
            CreateMap<PartSchedulePlanStep, PartSchedulePlanStepDto>();

            CreateMap<BasDeviceClassDto, BasDeviceClass>();
            CreateMap<BasDeviceClass, BasDeviceClassDto>();

        }
    }
}
