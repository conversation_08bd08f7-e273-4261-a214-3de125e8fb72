﻿using Adnc.Huatek.Aps.Domain.Aggregates.SysTestAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.SysUserAggregate;
using Adnc.Huatek.Aps.Domain.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services.Implements
{
    public class SysUserService : AbstractAppService, ISysUserService
    {
        private readonly SysUserManagement _sysUserMgr;
        private readonly IEfBasicRepository<SysUser> _sysUserRepo;

        public SysUserService(
            IEfBasicRepository<SysUser> sysUserRepo,
            SysUserManagement sysUserMgr)
        {
            _sysUserRepo = sysUserRepo;
            _sysUserMgr = sysUserMgr;
        }

        public async Task<List<SysUser>> GetAllUser() 
        {
          var users=  await _sysUserRepo.Where(o=>true).ToListAsync();

            return users;

        }
    }
}
