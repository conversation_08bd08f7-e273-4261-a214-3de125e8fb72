﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Dtos
{
    public class BasLineProductRelationDto : IDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 产线ID
        /// </summary>
        public long? IdLine { get; set; }
        /// <summary>
        /// 产品id
        /// </summary>
        public long?  IdProduct { get; set; }
        public string? ProductCode { get; set; }
        public string? ProductName { get; set; }
        public decimal? Capacity  { get; set; }
        public string? Unit { get; set; }

    }
}
