﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Dtos
{
    public class BasLineDto : Dto
    {
        /// <summary>
        /// ID
        /// </summary>
        public long? Id { get; set; }


        public int? Status  { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 产线名称
        /// </summary>
        public string? Lname { get; set; }

        /// <summary>
        /// 产线编码
        /// </summary>
        public string? Lcode { get; set; }

        public string? WorkshopCode { get; set; }
        public string? WorkshopName { get; set; }
        public long? IdWorkshop { get; set; }
      
        public List<BasLineProductRelationDto> Items { get; set; }


    }

    public class LinePagedDto : SearchPagedDto
    {
        /// <summary>
        /// 产线名称
        /// </summary>
        public string? Lname { get; set; }

        /// <summary>
        /// 产线编码
        /// </summary>
        public string? Lcode { get; set; }


        //public long? IdWorkshop { get; set; }

    }
    /// <summary>
    /// 查找产品对应的产线
    /// </summary>
    public class ProductLineDto
    {
        /// <summary>
        /// 产线名称
        /// </summary>
        public string? Lname { get; set; }

        /// <summary>
        /// 产线编码
        /// </summary>
        public string? Lcode { get; set; }

    }
}
