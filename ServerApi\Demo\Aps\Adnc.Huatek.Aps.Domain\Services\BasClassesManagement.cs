﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasClassesAggregate;
using Adnc.Infra.Helper;
using Adnc.Shared.Rpc.Event;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Net;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;
using static StackExchange.Redis.Role;

namespace Adnc.Huatek.Aps.Domain.Services
{
    public class BasClassesManagement : IDomainService
    {
        private readonly IEfBasicRepository<BasClasses> _basClassesRepo;

        public BasClassesManagement(IEfBasicRepository<BasClasses> basClassesRepo) 
        {
            _basClassesRepo = basClassesRepo;
        }


        public virtual async Task<BasClasses> CreateAsync(string? Code, string? Name, int? Status, string? ShortName,string? BeginTime
            , string? EndTime,string? Color,string? Remark)
        {
            if (string.IsNullOrWhiteSpace(Code))
            {
                throw new BusinessException($"班次编码规则未设置！请前往【系统管理】->【编码管理】进行设置");
            }
            var exists = await _basClassesRepo.AnyAsync(x => x.Ccode == Code && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"班次编码:{Code}已存在，不可重复添加！");

            exists = await _basClassesRepo.AnyAsync(x => x.Cname == Name && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"班次名称:{Name}已存在，不可重复添加！");

            exists = await _basClassesRepo.AnyAsync(x => x.ShortName == ShortName && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"班次简称:{ShortName}已存在，不可重复添加！");

            exists = await _basClassesRepo.AnyAsync(x => x.BeginTime == BeginTime && x.EndTime == EndTime && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"班次时间段:{BeginTime}-{EndTime}已存在，不可重复添加！");

            exists = await _basClassesRepo.AnyAsync(x => x.Color == Color && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"班次名称:{Color}已被其他班次使用，请重新选择！");



            return new BasClasses()
            {
                Id= IdGenerater.GetNextId(),
                Cname = Name,
                Ccode = Code,
                BeginTime= BeginTime,
                ShortName = ShortName,
                Status = Status,
                EndTime= EndTime,
                Color = Color,
                Remark = Remark
            };
        }

        public virtual async Task<bool> UpdateAsync(string? Code, string? Name, int? Status, string? ShortName, string? BeginTime
            , string? EndTime, string? Color, long? id)
        {
            var exists = await _basClassesRepo.AnyAsync(x => x.Ccode == Code && x.Id != id && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"班次编码:{Code}已存在，不可重复添加！");

            exists = await _basClassesRepo.AnyAsync(x => x.Cname == Name && x.Id != id && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"班次名称:{Name}已存在，不可重复添加！");
            exists = await _basClassesRepo.AnyAsync(x => x.ShortName == ShortName && x.Id != id && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"班次简称:{ShortName}已存在，不可重复添加！");

            exists = await _basClassesRepo.AnyAsync(x => x.BeginTime == BeginTime && x.EndTime == EndTime && x.Id != id && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"班次时间段:{BeginTime}-{EndTime}已存在，不可重复添加！");

            exists = await _basClassesRepo.AnyAsync(x => x.Color == Color && x.Id != id && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"班次名称:{Color}已被其他班次使用，请重新选择！");
            return true;
        }
    }
}
