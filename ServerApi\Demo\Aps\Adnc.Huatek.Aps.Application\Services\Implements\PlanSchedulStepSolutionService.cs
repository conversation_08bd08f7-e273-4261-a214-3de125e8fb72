﻿using Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulStepDetailsAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PlanSchedulStepSolutionAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.SysUserAggregate;
using Adnc.Huatek.Aps.Domain.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services.Implements
{
    public class PlanSchedulStepSolutionService : AbstractAppService, IPlanSchedulStepSolutionService
    {
        private readonly PlanSchedulStepSolutionManagement _planSchedulStepSolutionMgr;
        private readonly IEfBasicRepository<PlanSchedulStepSolution> _planSchedulStepSolutionRepo;
        private readonly IEfBasicRepository<SysUser> _sysUserRepo;

        public PlanSchedulStepSolutionService(PlanSchedulStepSolutionManagement planSchedulStepSolutionMgr,
           IEfBasicRepository<PlanSchedulStepSolution> planSchedulStepSolutionRepo,
           IEfBasicRepository<SysUser> sysUserRepo) 
        {
            _planSchedulStepSolutionMgr = planSchedulStepSolutionMgr;
            _planSchedulStepSolutionRepo = planSchedulStepSolutionRepo;
            _sysUserRepo = sysUserRepo;
        }

    }
}
