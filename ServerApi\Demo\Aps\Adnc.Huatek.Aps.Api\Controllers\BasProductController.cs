﻿using Adnc.Demo.Shared.Const;
using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Application.Services;
using Microsoft.AspNetCore.Authorization;
using Npoi.Mapper;

namespace Adnc.Huatek.Aps.Api.Controllers
{
    [Route($"{RouteConsts.ApsRoot}/product")]
    [ApiController]
    public class BasProductController : AdncControllerBase
    {
        private readonly IBasProductService _proSrv;

        public BasProductController(IBasProductService proSrv) => _proSrv = proSrv;


        /// <summary>
        /// 获取产品分页信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("page")]
        public async Task<ActionResult<PageModelDto<BasProductDto>>> GetPagedAsync([FromBody] ProductPagedDto search) => await _proSrv.GetPagedAsync(search);




        /// <summary>
        /// 获取所有信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getallasync")]
        public async Task<ActionResult<List<BasProductDto>>> GetAllAsync() => await _proSrv.GetAllAsync();


        /// <summary>
        /// 新增产品
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        //[AllowAnonymous]
        [HttpPost("add")]
        public async Task<ActionResult<long>> CreateAsync([FromBody] BasProductBomDto input)
           => CreatedResult(await _proSrv.CreateAsync(input));

        /// <summary>
        /// 更新产品
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("edit")]
        public async Task<ActionResult> UpdateAsync([FromBody] BasProductBomDto input)
           => Result(await _proSrv.UpdateAsync(input));

        /// <summary>
        /// 更新产品工序物料
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("edit/stepmaterial")]
        public async Task<ActionResult> UpdateStepMaterialAsync([FromBody] BasProductDto input)
           => Result(await _proSrv.UpdateStepMaterialAsync(input));


        /// <summary>
        /// 删除产品
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpDelete("delete/{id}")]
        public async Task<ActionResult> DeleteAsync([FromRoute] long id)
          => Result(await _proSrv.DeleteAsync(id));

        /// <summary>
        /// 获取产品详细信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getbyid/{id}")]
        public async Task<ActionResult<BasProductDto>> GetByIdAsync([FromRoute] long id)
            => await _proSrv.GetByIdAsync(id);

        /// <summary>
        /// 更新产品产能
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("edit/standardcapacity")]
        public async Task<ActionResult> UpdateProductStdCapAsync([FromBody] BasProductDto input)
           => Result(await _proSrv.UpdateProductCapacityAsync(input));

        /// <summary>
        /// 更新启用/禁用状态
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("changestatus")]
        public async Task<ActionResult> ChangeStatus([FromBody] ChangeStatusDto input)
            => Result(await _proSrv.ChangeStatusAsync(input));

        /// <summary>
        /// 获取产品详细信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getmodulebyid/{id}")]
        public async Task<ActionResult<BasProductDto>> GetModuleByIdAsync([FromRoute] long id)
            => await _proSrv.GetCapacityModuleByIdAsync(id);



        /// <summary>
        /// 新增物料
        /// </summary>
        /// <param name=input>产品信息</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("importasync")]
        public async Task<ResultJson> ImportAsync(IFormFile formFile)
        {
            //通过上传文件流初始化Mapper
            var mapper = new Mapper(formFile.OpenReadStream());
            //读取sheet1的数据
            var devices = mapper.Take<BasProductDto>("Sheet1").Select(i => i.Value).ToList();
            return await _proSrv.ImportAsync(devices);
        }

    }
}
