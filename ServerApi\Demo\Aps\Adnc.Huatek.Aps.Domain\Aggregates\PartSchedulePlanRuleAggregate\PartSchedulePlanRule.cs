﻿using Adnc.Infra.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanRuleAggregate
{
    public class PartSchedulePlanRule : EfFullAuditEntity
    {
        /// <summary>
        /// ID
        /// </summary>
        public long Id { get; set; }
        /// <summary>
        /// 规则类型
        /// </summary>
        public string RuleType { get; set; }

        /// <summary>
        /// 规则值
        /// </summary>
        public string RuleValue { get; set; }

        /// <summary>
        /// 排产计划编码
        /// </summary>
        public string? ScheduleCode { get; set; }
    }

   
}
