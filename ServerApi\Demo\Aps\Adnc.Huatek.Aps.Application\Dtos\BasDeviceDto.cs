﻿using System.ComponentModel.DataAnnotations;

namespace Adnc.Huatek.Aps.Application.Dtos
{
    public class BasDeviceDto : IDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool Isdeleted { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public long? CreateBy { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 更新人
        /// </summary>
        public long? ModifyBy { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [Display(Name = "启用/禁用")]
        public int? Status { get; set; }

        /// <summary>
        /// 设备编码
        /// </summary>
        [Display(Name = "设备编码")]
        public string? Code { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        [Display(Name = "设备名称")]
        public string? Name { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 创建人名称
        /// </summary>
        public string? CreatedName { get; set; }

        /// <summary>
        /// 修改人名称
        /// </summary>
        public string? ModifyName { get; set; }

        /// <summary>
        /// 产能
        /// </summary>
        public int? Capacity { get; set; }


        public string? Dtype { get; set; }
        /// <summary>
        /// 设备类型
        /// </summary>
        [Display(Name = "设备类型")]
        public string? DtypeDis { get; set; }

        /// <summary>
        /// 设备新度
        /// </summary>
        [Display(Name = "设备新度")]
        public decimal? Newness { get; set; }
    }


    public class DeviceTreeDtos : BasDeviceDto
    {
        public string? Label { get; set; }
    }

    public class DeviceTypeTreeDto
    {
        public string Dtype { get; set; }
        public string Label { get; set; }

        public List<DeviceTreeDtos> Children { get; set; }
    }


    public class BasDevicePagedDto : SearchPagedDto
    {

        /// <summary>
        /// 设备名称
        /// </summary>
        public string? Name { get; set; }


        /// <summary>
        /// 设备编码
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 设备编码
        /// </summary>
        public string? Dtype { get; set; }

        /// <summary>
        /// 设备编码
        /// </summary>
        public int? Status { get; set; }

    }


    public class EffectiveDeviceDtos
    {
        public DateTime DtDate { get; set; }

        public DateTime DtMaxDate { get; set; }
        public string DevCode { get; set; }
        public string DevType { get; set; }
        public decimal UnitTat { get; set; }
        public decimal UnitCapacity { get; set; }
        public decimal LoopNum { get; set; }
        public decimal TotalCapacity { get; set; }
        public List<TimeInterval> Children { get; set; }
    }


    public class QueryEffrctiveDevice
    {
        /// <summary>
        /// 资源大类
        /// </summary>
        public decimal Qty { get; set; }
        /// <summary>
        /// 资源大类
        /// </summary>
        public string ScheduleCode { get; set; }
        /// <summary>
        /// 资源大类
        /// </summary>
        public string SourceType { get; set; }
        /// <summary>
        /// 资源类型
        /// </summary>
        public string DevType { get; set; }
        /// <summary>
        /// 资源单位工作时长
        /// </summary>
        public decimal UnitTat { get; set; }
        /// <summary>
        /// 资源单位产能
        /// </summary>
        public decimal UnitCapacity { get; set; }
        /// <summary>
        /// 查询范围的开始时间
        /// </summary>
        public DateTime Begin { get; set; }

        /// <summary>
        /// 查询范围的结束时间
        /// </summary>
        public DateTime End { get; set; }

        private bool _modify;

        public bool Modify
        {
            get
            {
                if (_modify == null)
                    _modify = false;
                return _modify;
            }
            set
            {
                _modify = value;
            }
        }
    }
}
