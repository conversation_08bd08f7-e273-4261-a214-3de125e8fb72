﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasStepAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PartOrderStepAggregate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class PartOrderStepConfig : AbstractEntityTypeConfiguration<PartOrderStep>
    {
        public override void Configure(EntityTypeBuilder<PartOrderStep> builder)
        {
            base.Configure(builder);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.IdOrder).HasColumnName("idorder");
            builder.Property(x => x.IdStep).HasColumnName("idstep");
            builder.Property(x => x.IsKey).HasColumnName("iskey");
            builder.Property(x => x.Sort).HasColumnName("sort");
            builder.Property(x => x.RelationType ).HasColumnName("relationtype");
            builder.Property(x => x.Remark).HasColumnName("remark");
            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.CreateBy).HasColumnName("createby");
            builder.Property(x => x.CreateTime).HasColumnName("createtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
        }
    }
}
