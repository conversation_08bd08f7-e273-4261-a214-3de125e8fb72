﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Dtos
{
    public class BasWorksjopDto : IDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool Isdeleted { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public long Createdby { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime Createdtime { get; set; }

        /// <summary>
        /// 创建人姓名
        /// </summary>
        public string? Createdname { get; set; }

        /// <summary>
        /// 更新人
        /// </summary>
        public long Modifyby { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime Modifytime { get; set; }

        /// <summary>
        /// 更新人姓名
        /// </summary>
        public string? Modifyname { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 车间编码
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 车间名称
        /// </summary>
        public string? Name { get; set; }
    }
}
