﻿using Adnc.Infra.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Aggregates.SourceSchedulDeviceStationAggregate
{
    public class SourceSchedulDeviceStation : EfFullAuditEntity
    {
        public string? OrderNumber { get; set; }

        public string? StationCode { get; set; }

        public string? DiviceCode { get; set; }


        public DateTime? UseDate { get; set; }

        public int Status { get; set; }

        public string? Remark { get; set; }

        public string? SchedulCode { get; set;}

        public string? StepCode { get; set; }

        public DateTime? UseendDate { get; set; }

        public string? StepName { get; set; }
    }
}
