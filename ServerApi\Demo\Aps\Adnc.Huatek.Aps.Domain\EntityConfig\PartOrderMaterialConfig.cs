﻿using Adnc.Huatek.Aps.Domain.AggregatesPartOrderMaterialAggregate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class PartOrderMaterialConfig : AbstractEntityTypeConfiguration<PartOrderMaterial>
    {
        public override void Configure(EntityTypeBuilder<PartOrderMaterial> builder)
        {
            base.Configure(builder);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.IdMaterial).HasColumnName("idmaterial");
            builder.Property(x => x.Qty).HasColumnName("qty");
            builder.Property(x => x.SumQty).HasColumnName("sumqty");
            builder.Property(x => x.IdOrder).HasColumnName("idorder");
            builder.Property(x => x.Remark).HasColumnName("remark");
            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.CreateBy).HasColumnName("createby");
            builder.Property(x => x.CreateTime).HasColumnName("createtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
        }
    }
}
