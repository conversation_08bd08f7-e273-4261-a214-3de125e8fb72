﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.BasClassesAggregate;
using Adnc.Shared.Application.Contracts.ResultModels;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services
{
    public interface IBasClassesService : IAppService
    {
        [OperateLog(LogName = "获取班次列表信息")]
        Task<PageModelDto<BasClassesDto>> GetPagedAsync(ClassesPagedDto search);
        [OperateLog(LogName = "获取所有班次信息")]
        Task<List<BasClassesDto>> GetAllAsync();
        [OperateLog(LogName = "创建班次")]
        Task<AppSrvResult<long>> CreateAsync(BasClassesDto input);
        [OperateLog(LogName = "修改班次")]
        Task<AppSrvResult> UpdateAsync(BasClassesDto input);
        [OperateLog(LogName = "删除班次")]
        Task<AppSrvResult> DeleteAsync(long id);
        [OperateLog(LogName = "根据ID获取班次")]
        Task<BasClassesDto> GetByIdAsync(long id);

        [OperateLog(LogName = "获取所有班次信息")]
        Task<List<ClassesDto>> GetAllClassesAsync();

        [OperateLog(LogName = "批量启用禁用")]
        Task<AppSrvResult> MakeEnableAsync(int status, List<long> ids);
    }
}
