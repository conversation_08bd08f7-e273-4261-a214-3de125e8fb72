<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Adnc.Infra.Repository</name>
    </assembly>
    <members>
        <member name="P:Adnc.Infra.Entities.EfBasicAuditEntity.CreateBy">
            <summary>
            创建人
            </summary>
        </member>
        <member name="P:Adnc.Infra.Entities.EfBasicAuditEntity.CreateTime">
            <summary>
            创建时间/注册时间
            </summary>
        </member>
        <member name="P:Adnc.Infra.Entities.EfFullAuditEntity.CreateBy">
            <summary>
            创建人
            </summary>
        </member>
        <member name="P:Adnc.Infra.Entities.EfFullAuditEntity.CreateTime">
            <summary>
            创建时间/注册时间
            </summary>
        </member>
        <member name="P:Adnc.Infra.Entities.EfFullAuditEntity.ModifyBy">
            <summary>
            最后更新人
            </summary>
        </member>
        <member name="P:Adnc.Infra.Entities.EfFullAuditEntity.ModifyTime">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="P:Adnc.Infra.Entities.EfFullAuditEntity.IsDeleted">
            <summary>
            是否删除标识
            </summary>
        </member>
        <member name="P:Adnc.Infra.Entities.IBasicAuditInfo.CreateBy">
            <summary>
            创建人
            </summary>
        </member>
        <member name="P:Adnc.Infra.Entities.IBasicAuditInfo.CreateTime">
            <summary>
            创建时间/注册时间
            </summary>
        </member>
        <member name="P:Adnc.Infra.Entities.IConcurrency.RowVersion">
            <summary>
            并发控制列
            </summary>
        </member>
        <member name="P:Adnc.Infra.Entities.IFullAuditInfo.ModifyBy">
            <summary>
            最后更新人
            </summary>
        </member>
        <member name="P:Adnc.Infra.Entities.IFullAuditInfo.ModifyTime">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="T:Adnc.Infra.Entities.MongoEntity">
            <summary>
            An entity in a MongoDB repository.
            </summary>
        </member>
        <member name="P:Adnc.Infra.Entities.MongoEntity.Id">
            <summary>
            Gets or sets the identifier.
            </summary>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IAdoExecuterRepository.ExecuteReaderAsync(System.String,System.Object,System.Data.IDbTransaction,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType})">
            <summary>
            Execute parameterized SQL and return an <see cref="T:System.Data.IDataReader"/>.
            </summary>
            <param name="sql">The SQL to execute.</param>
            <param name="param">The parameters to use for this command.</param>
            <param name="transaction">The transaction to use for this command.</param>
            <param name="commandTimeout">Number of seconds before command execution timeout.</param>
            <param name="commandType">Is it a stored proc or a batch?</param>
            <returns>An <see cref="T:System.Data.IDataReader"/> that can be used to iterate over the results of the SQL query.</returns>
            <remarks>
            This is typically used when the results of a query are not processed by Dapper, for example, used to fill a <see cref="T:System.Data.DataTable"/>
            or <see cref="T:DataSet"/>.
            </remarks>
            <example>
            <code>
            <![CDATA[
            DataTable table = new DataTable("MyTable");
            using (var reader = ExecuteReader(cnn, sql, param))
            {
                table.Load(reader);
            }
            ]]>
            </code>
            </example>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IAdoExecuterRepository.ExecuteScalarAsync(System.String,System.Object,System.Data.IDbTransaction,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType})">
            <summary>
            Execute parameterized SQL that selects a single value.
            </summary>
            <param name="sql">The SQL to execute.</param>
            <param name="param">The parameters to use for this command.</param>
            <param name="transaction">The transaction to use for this command.</param>
            <param name="commandTimeout">Number of seconds before command execution timeout.</param>
            <param name="commandType">Is it a stored proc or a batch?</param>
            <returns>The first cell returned, as <see cref="T:System.Object"/>.</returns>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IAdoExecuterRepository.ExecuteScalarAsync``1(System.String,System.Object,System.Data.IDbTransaction,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType})">
            <summary>
            Execute parameterized SQL that selects a single value.
            </summary>
            <typeparam name="T">The type to return.</typeparam>
            <param name="sql">The SQL to execute.</param>
            <param name="param">The parameters to use for this command.</param>
            <param name="transaction">The transaction to use for this command.</param>
            <param name="commandTimeout">Number of seconds before command execution timeout.</param>
            <param name="commandType">Is it a stored proc or a batch?</param>
            <returns>The first cell returned, as <typeparamref name="T"/>.</returns>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IAdoExecuterRepository.ExecuteAsync(System.String,System.Object,System.Data.IDbTransaction,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType})">
            <summary>
            Execute a command asynchronously using Task.
            </summary>
            <param name="sql">The SQL to execute for this query.</param>
            <param name="param">The parameters to use for this query.</param>
            <param name="transaction">The transaction to use for this query.</param>
            <param name="commandTimeout">Number of seconds before command execution timeout.</param>
            <param name="commandType">Is it a stored proc or a batch?</param>
            <returns>The number of rows affected.</returns>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IAdoQuerierRepository.QueryAsync(System.String,System.Object,System.Data.IDbTransaction,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType},System.Boolean)">
            <summary>
            Execute a query asynchronously using Task.
            </summary>
            <param name="sql">The SQL to execute for the query.</param>
            <param name="param">The parameters to pass, if any.</param>
            <param name="transaction">The transaction to use, if any.</param>
            <param name="commandTimeout">The command timeout (in seconds).</param>
            <param name="commandType">The type of command to execute.</param>
            <param name="writeDb">if true writedb else readdb</param>
            <remarks>Note: each row can be accessed via "dynamic", or by casting to an IDictionary&lt;string,object&gt;</remarks>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IAdoQuerierRepository.QueryAsync``1(System.String,System.Object,System.Data.IDbTransaction,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType},System.Boolean)">
            <summary>
            Execute a query asynchronously using Task.
            </summary>
            <typeparam name="T">The type of results to return.</typeparam>
            <param name="sql">The SQL to execute for the query.</param>
            <param name="param">The parameters to pass, if any.</param>
            <param name="transaction">The transaction to use, if any.</param>
            <param name="commandTimeout">The command timeout (in seconds).</param>
            <param name="commandType">The type of command to execute.</param>
            <param name="writeDb">if true writedb else readdb</param>
            <returns>
            A sequence of data of <typeparamref name="T"/>; if a basic type (int, string, etc) is queried then the data from the first column in assumed, otherwise an instance is
            created per row, and a direct column-name===member-name mapping is assumed (case insensitive).
            </returns>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IAdoQuerierRepository.QueryFirstAsync``1(System.String,System.Object,System.Data.IDbTransaction,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType},System.Boolean)">
            <summary>
            Execute a single-row query asynchronously using Task.
            </summary>
            <typeparam name="T">The type of result to return.</typeparam>
            <param name="sql">The SQL to execute for the query.</param>
            <param name="param">The parameters to pass, if any.</param>
            <param name="transaction">The transaction to use, if any.</param>
            <param name="commandTimeout">The command timeout (in seconds).</param>
            <param name="commandType">The type of command to execute.</param>
            <param name="writeDb">if true writedb else readdb</param>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IAdoQuerierRepository.QueryFirstOrDefaultAsync``1(System.String,System.Object,System.Data.IDbTransaction,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType},System.Boolean)">
            <summary>
            Execute a single-row query asynchronously using Task.
            </summary>
            <typeparam name="T">The type of result to return.</typeparam>
            <param name="sql">The SQL to execute for the query.</param>
            <param name="param">The parameters to pass, if any.</param>
            <param name="transaction">The transaction to use, if any.</param>
            <param name="commandTimeout">The command timeout (in seconds).</param>
            <param name="commandType">The type of command to execute.</param>
            <param name="writeDb">if true writedb else readdb</param>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IAdoQuerierRepository.QuerySingleAsync``1(System.String,System.Object,System.Data.IDbTransaction,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType},System.Boolean)">
            <summary>
            Execute a single-row query asynchronously using Task.
            </summary>
            <typeparam name="T">The type of result to return.</typeparam>
            <param name="sql">The SQL to execute for the query.</param>
            <param name="param">The parameters to pass, if any.</param>
            <param name="transaction">The transaction to use, if any.</param>
            <param name="commandTimeout">The command timeout (in seconds).</param>
            <param name="commandType">The type of command to execute.</param>
            <param name="writeDb">if true writedb else readdb</param>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IAdoQuerierRepository.QuerySingleOrDefaultAsync``1(System.String,System.Object,System.Data.IDbTransaction,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType},System.Boolean)">
            <summary>
            Execute a single-row query asynchronously using Task.
            </summary>
            <typeparam name="T">The type to return.</typeparam>
            <param name="sql">The SQL to execute for the query.</param>
            <param name="param">The parameters to pass, if any.</param>
            <param name="transaction">The transaction to use, if any.</param>
            <param name="commandTimeout">The command timeout (in seconds).</param>
            <param name="commandType">The type of command to execute.</param>
            <param name="writeDb">if true writedb else readdb</param>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IAdoQuerierRepository.QueryFirstAsync(System.String,System.Object,System.Data.IDbTransaction,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType},System.Boolean)">
            <summary>
            Execute a single-row query asynchronously using Task.
            </summary>
            <param name="sql">The SQL to execute for the query.</param>
            <param name="param">The parameters to pass, if any.</param>
            <param name="transaction">The transaction to use, if any.</param>
            <param name="commandTimeout">The command timeout (in seconds).</param>
            <param name="commandType">The type of command to execute.</param>
            <param name="writeDb">if true writedb else readdb</param>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IAdoQuerierRepository.QueryFirstOrDefaultAsync(System.String,System.Object,System.Data.IDbTransaction,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType},System.Boolean)">
            <summary>
            Execute a single-row query asynchronously using Task.
            </summary>
            <param name="sql">The SQL to execute for the query.</param>
            <param name="param">The parameters to pass, if any.</param>
            <param name="transaction">The transaction to use, if any.</param>
            <param name="commandTimeout">The command timeout (in seconds).</param>
            <param name="commandType">The type of command to execute.</param>
            <param name="writeDb">if true writedb else readdb</param>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IAdoQuerierRepository.QuerySingleAsync(System.String,System.Object,System.Data.IDbTransaction,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType},System.Boolean)">
            <summary>
            Execute a single-row query asynchronously using Task.
            </summary>
            <param name="sql">The SQL to execute for the query.</param>
            <param name="param">The parameters to pass, if any.</param>
            <param name="transaction">The transaction to use, if any.</param>
            <param name="commandTimeout">The command timeout (in seconds).</param>
            <param name="commandType">The type of command to execute.</param>
            <param name="writeDb">if true writedb else readdb</param>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IAdoQuerierRepository.QuerySingleOrDefaultAsync(System.String,System.Object,System.Data.IDbTransaction,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType},System.Boolean)">
            <summary>
            Execute a single-row query asynchronously using Task.
            </summary>
            <param name="sql">The SQL to execute for the query.</param>
            <param name="param">The parameters to pass, if any.</param>
            <param name="transaction">The transaction to use, if any.</param>
            <param name="commandTimeout">The command timeout (in seconds).</param>
            <param name="commandType">The type of command to execute.</param>
            <param name="writeDb">if true writedb else readdb</param>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IAdoQuerierRepository.QueryAsync(System.Type,System.String,System.Object,System.Data.IDbTransaction,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType},System.Boolean)">
            <summary>
            Execute a query asynchronously using Task.
            </summary>
            <param name="type">The type to return.</param>
            <param name="sql">The SQL to execute for the query.</param>
            <param name="param">The parameters to pass, if any.</param>
            <param name="transaction">The transaction to use, if any.</param>
            <param name="commandTimeout">The command timeout (in seconds).</param>
            <param name="commandType">The type of command to execute.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="type"/> is <c>null</c>.</exception>
            <param name="writeDb">if true writedb else readdb</param>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IAdoQuerierRepository.QueryFirstAsync(System.Type,System.String,System.Object,System.Data.IDbTransaction,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType},System.Boolean)">
            <summary>
            Execute a single-row query asynchronously using Task.
            </summary>
            <param name="type">The type to return.</param>
            <param name="sql">The SQL to execute for the query.</param>
            <param name="param">The parameters to pass, if any.</param>
            <param name="transaction">The transaction to use, if any.</param>
            <param name="commandTimeout">The command timeout (in seconds).</param>
            <param name="commandType">The type of command to execute.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="type"/> is <c>null</c>.</exception>
            <param name="writeDb">if true writedb else readdb</param>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IAdoQuerierRepository.QueryFirstOrDefaultAsync(System.Type,System.String,System.Object,System.Data.IDbTransaction,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType},System.Boolean)">
            <summary>
            Execute a single-row query asynchronously using Task.
            </summary>
            <param name="type">The type to return.</param>
            <param name="sql">The SQL to execute for the query.</param>
            <param name="param">The parameters to pass, if any.</param>
            <param name="transaction">The transaction to use, if any.</param>
            <param name="commandTimeout">The command timeout (in seconds).</param>
            <param name="commandType">The type of command to execute.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="type"/> is <c>null</c>.</exception>
            <param name="writeDb">if true writedb else readdb</param>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IAdoQuerierRepository.QuerySingleAsync(System.Type,System.String,System.Object,System.Data.IDbTransaction,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType},System.Boolean)">
            <summary>
            Execute a single-row query asynchronously using Task.
            </summary>
            <param name="type">The type to return.</param>
            <param name="sql">The SQL to execute for the query.</param>
            <param name="param">The parameters to pass, if any.</param>
            <param name="transaction">The transaction to use, if any.</param>
            <param name="commandTimeout">The command timeout (in seconds).</param>
            <param name="commandType">The type of command to execute.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="type"/> is <c>null</c>.</exception>
            <param name="writeDb">if true writedb else readdb</param>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IAdoQuerierRepository.QuerySingleOrDefaultAsync(System.Type,System.String,System.Object,System.Data.IDbTransaction,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType},System.Boolean)">
            <summary>
            Execute a single-row query asynchronously using Task.
            </summary>
            <param name="type">The type to return.</param>
            <param name="sql">The SQL to execute for the query.</param>
            <param name="param">The parameters to pass, if any.</param>
            <param name="transaction">The transaction to use, if any.</param>
            <param name="commandTimeout">The command timeout (in seconds).</param>
            <param name="commandType">The type of command to execute.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="type"/> is <c>null</c>.</exception>
            <param name="writeDb">if true writedb else readdb</param>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IAdoQuerierRepository.QueryAsync``3(System.String,System.Func{``0,``1,``2},System.Object,System.Data.IDbTransaction,System.Boolean,System.String,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType},System.Boolean)">
            <summary>
            Perform an asynchronous multi-mapping query with 2 input types.
            This returns a single type, combined from the raw types via <paramref name="map"/>.
            </summary>
            <typeparam name="TFirst">The first type in the recordset.</typeparam>
            <typeparam name="TSecond">The second type in the recordset.</typeparam>
            <typeparam name="TReturn">The combined type to return.</typeparam>
            <param name="sql">The SQL to execute for this query.</param>
            <param name="map">The function to map row types to the return type.</param>
            <param name="param">The parameters to use for this query.</param>
            <param name="transaction">The transaction to use for this query.</param>
            <param name="buffered">Whether to buffer the results in memory.</param>
            <param name="splitOn">The field we should split and read the second object from (default: "Id").</param>
            <param name="commandTimeout">Number of seconds before command execution timeout.</param>
            <param name="writeDb">if true writedb else readdb</param>
            <param name="commandType">Is it a stored proc or a batch?</param>
            <returns>An enumerable of <typeparamref name="TReturn"/>.</returns>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IAdoQuerierRepository.QueryAsync``4(System.String,System.Func{``0,``1,``2,``3},System.Object,System.Data.IDbTransaction,System.Boolean,System.String,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType},System.Boolean)">
            <summary>
            Perform an asynchronous multi-mapping query with 3 input types.
            This returns a single type, combined from the raw types via <paramref name="map"/>.
            </summary>
            <typeparam name="TFirst">The first type in the recordset.</typeparam>
            <typeparam name="TSecond">The second type in the recordset.</typeparam>
            <typeparam name="TThird">The third type in the recordset.</typeparam>
            <typeparam name="TReturn">The combined type to return.</typeparam>
            <param name="sql">The SQL to execute for this query.</param>
            <param name="map">The function to map row types to the return type.</param>
            <param name="param">The parameters to use for this query.</param>
            <param name="transaction">The transaction to use for this query.</param>
            <param name="buffered">Whether to buffer the results in memory.</param>
            <param name="splitOn">The field we should split and read the second object from (default: "Id").</param>
            <param name="commandTimeout">Number of seconds before command execution timeout.</param>
            <param name="writeDb">if true writedb else readdb</param>
            <param name="commandType">Is it a stored proc or a batch?</param>
            <returns>An enumerable of <typeparamref name="TReturn"/>.</returns>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IAdoQuerierRepository.QueryAsync``5(System.String,System.Func{``0,``1,``2,``3,``4},System.Object,System.Data.IDbTransaction,System.Boolean,System.String,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType},System.Boolean)">
            <summary>
            Perform an asynchronous multi-mapping query with 4 input types.
            This returns a single type, combined from the raw types via <paramref name="map"/>.
            </summary>
            <typeparam name="TFirst">The first type in the recordset.</typeparam>
            <typeparam name="TSecond">The second type in the recordset.</typeparam>
            <typeparam name="TThird">The third type in the recordset.</typeparam>
            <typeparam name="TFourth">The fourth type in the recordset.</typeparam>
            <typeparam name="TReturn">The combined type to return.</typeparam>
            <param name="sql">The SQL to execute for this query.</param>
            <param name="map">The function to map row types to the return type.</param>
            <param name="param">The parameters to use for this query.</param>
            <param name="transaction">The transaction to use for this query.</param>
            <param name="buffered">Whether to buffer the results in memory.</param>
            <param name="splitOn">The field we should split and read the second object from (default: "Id").</param>
            <param name="commandTimeout">Number of seconds before command execution timeout.</param>
            <param name="commandType">Is it a stored proc or a batch?</param>
            <param name="writeDb">if true writedb else readdb</param>
            <returns>An enumerable of <typeparamref name="TReturn"/>.</returns>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IAdoQuerierRepository.QueryAsync``6(System.String,System.Func{``0,``1,``2,``3,``4,``5},System.Object,System.Data.IDbTransaction,System.Boolean,System.String,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType},System.Boolean)">
            <summary>
            Perform an asynchronous multi-mapping query with 5 input types.
            This returns a single type, combined from the raw types via <paramref name="map"/>.
            </summary>
            <typeparam name="TFirst">The first type in the recordset.</typeparam>
            <typeparam name="TSecond">The second type in the recordset.</typeparam>
            <typeparam name="TThird">The third type in the recordset.</typeparam>
            <typeparam name="TFourth">The fourth type in the recordset.</typeparam>
            <typeparam name="TFifth">The fifth type in the recordset.</typeparam>
            <typeparam name="TReturn">The combined type to return.</typeparam>
            <param name="sql">The SQL to execute for this query.</param>
            <param name="map">The function to map row types to the return type.</param>
            <param name="param">The parameters to use for this query.</param>
            <param name="transaction">The transaction to use for this query.</param>
            <param name="buffered">Whether to buffer the results in memory.</param>
            <param name="splitOn">The field we should split and read the second object from (default: "Id").</param>
            <param name="commandTimeout">Number of seconds before command execution timeout.</param>
            <param name="commandType">Is it a stored proc or a batch?</param>
            <param name="writeDb">if true writedb else readdb</param>
            <returns>An enumerable of <typeparamref name="TReturn"/>.</returns>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IAdoQuerierRepository.QueryAsync``7(System.String,System.Func{``0,``1,``2,``3,``4,``5,``6},System.Object,System.Data.IDbTransaction,System.Boolean,System.String,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType},System.Boolean)">
            <summary>
            Perform an asynchronous multi-mapping query with 6 input types.
            This returns a single type, combined from the raw types via <paramref name="map"/>.
            </summary>
            <typeparam name="TFirst">The first type in the recordset.</typeparam>
            <typeparam name="TSecond">The second type in the recordset.</typeparam>
            <typeparam name="TThird">The third type in the recordset.</typeparam>
            <typeparam name="TFourth">The fourth type in the recordset.</typeparam>
            <typeparam name="TFifth">The fifth type in the recordset.</typeparam>
            <typeparam name="TSixth">The sixth type in the recordset.</typeparam>
            <typeparam name="TReturn">The combined type to return.</typeparam>
            <param name="sql">The SQL to execute for this query.</param>
            <param name="map">The function to map row types to the return type.</param>
            <param name="param">The parameters to use for this query.</param>
            <param name="transaction">The transaction to use for this query.</param>
            <param name="buffered">Whether to buffer the results in memory.</param>
            <param name="splitOn">The field we should split and read the second object from (default: "Id").</param>
            <param name="commandTimeout">Number of seconds before command execution timeout.</param>
            <param name="commandType">Is it a stored proc or a batch?</param>
            <param name="writeDb">if true writedb else readdb</param>
            <returns>An enumerable of <typeparamref name="TReturn"/>.</returns>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IAdoQuerierRepository.QueryAsync``8(System.String,System.Func{``0,``1,``2,``3,``4,``5,``6,``7},System.Object,System.Data.IDbTransaction,System.Boolean,System.String,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType},System.Boolean)">
            <summary>
            Perform an asynchronous multi-mapping query with 7 input types.
            This returns a single type, combined from the raw types via <paramref name="map"/>.
            </summary>
            <typeparam name="TFirst">The first type in the recordset.</typeparam>
            <typeparam name="TSecond">The second type in the recordset.</typeparam>
            <typeparam name="TThird">The third type in the recordset.</typeparam>
            <typeparam name="TFourth">The fourth type in the recordset.</typeparam>
            <typeparam name="TFifth">The fifth type in the recordset.</typeparam>
            <typeparam name="TSixth">The sixth type in the recordset.</typeparam>
            <typeparam name="TSeventh">The seventh type in the recordset.</typeparam>
            <typeparam name="TReturn">The combined type to return.</typeparam>
            <param name="sql">The SQL to execute for this query.</param>
            <param name="map">The function to map row types to the return type.</param>
            <param name="param">The parameters to use for this query.</param>
            <param name="transaction">The transaction to use for this query.</param>
            <param name="buffered">Whether to buffer the results in memory.</param>
            <param name="splitOn">The field we should split and read the second object from (default: "Id").</param>
            <param name="commandTimeout">Number of seconds before command execution timeout.</param>
            <param name="commandType">Is it a stored proc or a batch?</param>
            <param name="writeDb">if true writedb else readdb</param>
            <returns>An enumerable of <typeparamref name="TReturn"/>.</returns>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IAdoQuerierRepository.QueryAsync``1(System.String,System.Type[],System.Func{System.Object[],``0},System.Object,System.Data.IDbTransaction,System.Boolean,System.String,System.Nullable{System.Int32},System.Nullable{System.Data.CommandType},System.Boolean)">
            <summary>
            Perform an asynchronous multi-mapping query with an arbitrary number of input types.
            This returns a single type, combined from the raw types via <paramref name="map"/>.
            </summary>
            <typeparam name="TReturn">The combined type to return.</typeparam>
            <param name="sql">The SQL to execute for this query.</param>
            <param name="types">Array of types in the recordset.</param>
            <param name="map">The function to map row types to the return type.</param>
            <param name="param">The parameters to use for this query.</param>
            <param name="transaction">The transaction to use for this query.</param>
            <param name="buffered">Whether to buffer the results in memory.</param>
            <param name="splitOn">The field we should split and read the second object from (default: "Id").</param>
            <param name="commandTimeout">Number of seconds before command execution timeout.</param>
            <param name="commandType">Is it a stored proc or a batch?</param>
            <param name="writeDb">if true writedb else readdb</param>
            <returns>An enumerable of <typeparamref name="TReturn"/>.</returns>
        </member>
        <member name="T:Adnc.Infra.IRepositories.IEfBaseRepository`1">
            <summary>
            Ef仓储的基类接口
            </summary>
            <typeparam name="TEntity"></typeparam>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IEfBaseRepository`1.InsertAsync(`0,System.Threading.CancellationToken)">
            <summary>
            插入单个实体
            </summary>
            <param name="entity"><see cref="T:TEntity"/></param>
            <param name="cancellationToken"><see cref="T:System.Threading.CancellationToken"/></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IEfBaseRepository`1.InsertRangeAsync(System.Collections.Generic.IEnumerable{`0},System.Threading.CancellationToken)">
            <summary>
            批量插入实体
            </summary>
            <param name="entities"><see cref="T:TEntity"/></param>
            <param name="cancellationToken"><see cref="T:System.Threading.CancellationToken"/></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IEfBaseRepository`1.UpdateAsync(`0,System.Threading.CancellationToken)">
            <summary>
            更新单个实体
            </summary>
            <param name="entity"><see cref="T:TEntity"/></param>
            <param name="cancellationToken"><see cref="T:System.Threading.CancellationToken"/></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IEfBaseRepository`1.UpdateRangeAsync(System.Collections.Generic.IEnumerable{`0},System.Threading.CancellationToken)">
            <summary>
            批量更新实体
            </summary>
            <param name="entities"></param>
            <param name="cancellationToken"><see cref="T:System.Threading.CancellationToken"/></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IEfBaseRepository`1.AnyAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Boolean,System.Threading.CancellationToken)">
            <summary>
            根据条件查询实体是否存在
            </summary>
            <param name="whereExpression">查询条件</param>
            <param name="writeDb">是否读写库，默认false,可选参数</param>
            <param name="cancellationToken"><see cref="T:System.Threading.CancellationToken"/></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IEfBaseRepository`1.CountAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Boolean,System.Threading.CancellationToken)">
            <summary>
            统计符合条件的实体数量
            </summary>
            <param name="whereExpression">查询条件</param>
            <param name="writeDb">是否读写库，默认false,可选参数</param>
            <param name="cancellationToken"><see cref="T:System.Threading.CancellationToken"/></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IEfBaseRepository`1.Where(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Boolean,System.Boolean)">
            <summary>
            根据条件查询，返回IQueryable{TEntity}
            </summary>
            <param name="expression">查询条件</param>
            <param name="writeDb">是否读写库，默认false,可选参数</param>
            <param name="noTracking">是否开启跟踪，默认false,可选参数</param>
            <returns></returns>
        </member>
        <member name="T:Adnc.Infra.IRepositories.IEfBasicRepository`1">
            <summary>
            Ef简单的、基础的，初级的仓储接口
            适合DDD开发模式,实体必须继承AggregateRoot
            </summary>
            <typeparam name="TEntity"></typeparam>
        </member>
        <member name="T:Adnc.Infra.IRepositories.IEfRepository`1">
            <summary>
            Ef默认的、全功能的仓储接口
            适合传统三层模式开发，实体必须继承 EfEntity
            </summary>
            <typeparam name="TEntity"></typeparam>
        </member>
        <member name="P:Adnc.Infra.IRepositories.IEfRepository`1.AdoQuerier">
            <summary>
            执行原生Sql查询
            </summary>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IEfRepository`1.ExecuteSqlInterpolatedAsync(System.FormattableString,System.Threading.CancellationToken)">
            <summary>
            执行原生Sql写操作
            </summary>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IEfRepository`1.ExecuteSqlRawAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            执行原生Sql写操作
            </summary>
        </member>
        <member name="P:Adnc.Infra.IRepositories.IEfRepository`1.CurrentDbTransaction">
            <summary>
            当前事务
            </summary>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IEfRepository`1.GetAll(System.Boolean,System.Boolean)">
            <summary>
            返回IQueryable{TEntity}
            </summary>
            <param name="writeDb">是否读写库，默认false,可选参数</param>
            <param name="noTracking">是否开启跟踪，默认false,可选参数</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IEfRepository`1.FindAsync(System.Int64,System.Linq.Expressions.Expression{System.Func{`0,System.Object}},System.Boolean,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            根据Id查询,返回单个实体
            </summary>
            <param name="keyValue">Id</param>
            <param name="navigationPropertyPath">导航属性,可选参数</param>
            <param name="writeDb">是否读写库,默认false，可选参数</param>
            <param name="noTracking">是否开启跟踪，默认不开启，可选参数</param>
            <param name="cancellationToken"><see cref="T:System.Threading.CancellationToken"/></param>
            <returns><see cref="T:TEntity"/></returns>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IEfRepository`1.FindAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{`0,System.Object}},System.Linq.Expressions.Expression{System.Func{`0,System.Object}},System.Boolean,System.Boolean,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            根据条件查询,返回单个实体
            </summary>
            <param name="whereExpression">查询条件</param>
            <param name="navigationPropertyPath">导航属性,可选参数</param>
            <param name="orderByExpression">排序字段，默认主键，可选参数</param>
            <param name="ascending">排序方式，默认逆序，可选参数</param>
            <param name="writeDb">是否读写库,默认false，可选参数</param>
            <param name="noTracking">是否开启跟踪，默认不开启，可选参数</param>
            <param name="cancellationToken"><see cref="T:System.Threading.CancellationToken"/></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IEfRepository`1.FetchAsync``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{`0,System.Object}},System.Boolean,System.Boolean,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            根据条件查询,返回单个实体或对象
            </summary>
            <typeparam name="TResult">匿名对象</typeparam>
            <param name="selector">选择器</param>
            <param name="whereExpression">查询条件</param>
            <param name="orderByExpression">排序字段，默认主键，可选参数</param>
            <param name="ascending">排序方式，默认逆序，可选参数</param>
            <param name="writeDb">是否读写库,默认false，可选参数</param>
            <param name="noTracking">是否开启跟踪，默认不开启，可选参数</param>
            <param name="cancellationToken"><see cref="T:System.Threading.CancellationToken"/></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IEfRepository`1.UpdateAsync(`0,System.Linq.Expressions.Expression{System.Func{`0,System.Object}}[],System.Threading.CancellationToken)">
            <summary>
            更新单个实体
            </summary>
            <param name="entity"><see cref="T:TEntity"/></param>
            <param name="updatingExpressions">需要更新列的表达式树数组</param>
            <param name="cancellationToken"><see cref="T:System.Threading.CancellationToken"/></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IEfRepository`1.UpdateRangeAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{`0,`0}},System.Threading.CancellationToken)">
            <summary>
            批量更新
            </summary>
            <param name="whereExpression">查询条件</param>
            <param name="updatingExpression">需要更新的字段</param>
            <param name="cancellationToken"><see cref="T:System.Threading.CancellationToken"/></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IEfRepository`1.UpdateRangeAsync(System.Collections.Generic.Dictionary{System.Int64,System.Collections.Generic.List{System.ValueTuple{System.String,System.Object}}},System.Threading.CancellationToken)">
            <summary>
            批量更新
            </summary>
            <param name="propertyNameAndValues">需要更新的字段与值</param>
            <param name="cancellationToken"><see cref="T:System.Threading.CancellationToken"/></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IEfRepository`1.DeleteAsync(System.Int64,System.Threading.CancellationToken)">
            <summary>
            删除实体
            </summary>
            <param name="keyValue">Id</param>
            <param name="cancellationToken"><see cref="T:System.Threading.CancellationToken"/></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IEfRepository`1.DeleteRangeAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Threading.CancellationToken)">
            <summary>
            批量删除实体
            </summary>
            <param name="whereExpression">查询条件</param>
            <param name="cancellationToken"><see cref="T:System.Threading.CancellationToken"/></param>
            <returns></returns>
        </member>
        <member name="T:Adnc.Infra.IRepositories.IMongoRepository`1">
            <summary>
            A MongoDB based repository of <see cref="T:TEntity" />.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IMongoRepository`1.GetAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets the entity with the specified identifier.
            </summary>
            <param name="id">The identifier.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IMongoRepository`1.GetAsync(MongoDB.Driver.FilterDefinition{`0},System.Threading.CancellationToken)">
            <summary>
            Gets the entity with the specified identifier.
            </summary>
            <param name="filter"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IMongoRepository`1.GetAllAsync(System.Threading.CancellationToken)">
            <summary>
            Gets all entities in this repository.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IMongoRepository`1.AddAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Adds the specified entity.
            </summary>
            <param name="entity">The entity.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IMongoRepository`1.AddManyAsync(System.Collections.Generic.IEnumerable{`0},System.Threading.CancellationToken)">
            <summary>
            Adds the specified entities.
            </summary>
            <param name="entities">The entities.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IMongoRepository`1.DeleteAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Deletes the entity with the specified identifier.
            </summary>
            <param name="id">The identifier.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IMongoRepository`1.DeleteManyAsync(MongoDB.Driver.FilterDefinition{`0},System.Threading.CancellationToken)">
            <summary>
            Batch delete
            </summary>
            <param name="filter">删除条件</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IMongoRepository`1.ReplaceAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Replaces the specified entity with the same identifier.
            </summary>
            <param name="entity">The entity.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.IRepositories.IMongoRepository`1.PagedAsync(System.Int32,System.Int32,MongoDB.Driver.FilterDefinition{`0},System.Linq.Expressions.Expression{System.Func{`0,System.Object}},System.Boolean,System.Threading.CancellationToken)">
            <summary>
            分页查询
            </summary>
            <param name="pageNumber"></param>
            <param name="pageSize"></param>
            <param name="filter"></param>
            <param name="orderByExpression"></param>
            <param name="ascending"></param>
            <param name="cancellationToken"></param>
        </member>
        <member name="T:Adnc.Infra.IRepositories.IRepository`1">
            <summary>
            仓储基类接口
            </summary>
            <typeparam name="TEntity"></typeparam>
        </member>
    </members>
</doc>
