﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Services;
using Adnc.Infra.IdGenerater.Yitter;
using Adnc.Shared.Application.Contracts.ResultModels;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Adnc.Huatek.Aps.Domain.Aggregates.BasMaterial;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasLineAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasWorksjop;
using Adnc.Huatek.Aps.Domain.Aggregates.BasLineProductRelationAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasBomAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasBomListAggregate;
using Adnc.Shared;
using Adnc.Huatek.Aps.Domain.Aggregates.SysUserAggregate;
using System.Net;

namespace Adnc.Huatek.Aps.Application.Services.Implements
{
    public class BasLineService : AbstractAppService, IBasLineService
    {
        private readonly BasLineManagement _basLineMgr;
        private readonly IEfBasicRepository<BasLine> _basLineRepo;
        private readonly IEfBasicRepository<BasWorksjop> _basWorkshopRepo;
        private readonly IEfBasicRepository<BasProduct> _basProductRepo;
        private readonly IEfBasicRepository<BasLineProductRelation> _basLineProRepo;
        private readonly BasNumberManagementService _basNumberManagementService;
        private readonly IEfBasicRepository<SysUser> _sysUserRepo;

        public BasLineService(
            BasLineManagement basLineMgr,
            IEfBasicRepository<BasLine> basLineRepo,
            IEfBasicRepository<BasWorksjop> basWorkshopRepo,
            IEfBasicRepository<BasProduct> basProductRepo,
            IEfBasicRepository<BasLineProductRelation> basLineProRepo,
            BasNumberManagementService basNumberManagementService,
            IEfBasicRepository<SysUser> sysUserRepo
            )
        {
            _basLineMgr = basLineMgr;
            _basLineRepo = basLineRepo;
            _basWorkshopRepo = basWorkshopRepo;
            _basProductRepo = basProductRepo;
            _basLineProRepo = basLineProRepo;
            _basNumberManagementService = basNumberManagementService;
            _sysUserRepo = sysUserRepo;
        }
        /// <summary>
        /// 获取分页列表
        /// </summary>
        /// <param name="search"></param>
        /// <returns></returns>
        public async Task<PageModelDto<BasLineDto>> GetPagedAsync(LinePagedDto search)
        {
            search.TrimStringFields();
            var whereExpression = ExpressionCreator
                                                 .New<BasLine>()
                                                 .And(x => !x.IsDeleted)
                                                 .AndIf(search.Lcode.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Lcode!, $"%{search.Lcode}%"))
                                                 .AndIf(search.Lname.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Lname!, $"%{search.Lname}%"));

            var total = await _basLineRepo.CountAsync(whereExpression);
            if (total == 0)
                return new PageModelDto<BasLineDto>(search);
            var users = _sysUserRepo.Where(x => true);
            var lines = _basLineRepo.Where(whereExpression);
            var entitis = await (from l in lines
                                 join u in users on l.CreateBy equals u.Id
                                 join mu in users on l.ModifyBy equals mu.Id
                                 select new BasLineDto
                                 {
                                     Id = l.Id,
                                     Lcode = l.Lcode,
                                     Lname = l.Lname,
                                     Status = l.Status,
                                     Remark = l.Remark,
                                     CreateBy = l.CreateBy,
                                     CreatName = u.Name,
                                     CreateTime = l.CreateTime,
                                     ModifyBy = l.ModifyBy,
                                     ModifyName = mu.Name,
                                     ModifyTime = l.ModifyTime
                                 }).OrderByDescending(x => x.CreateTime)
                                            .Skip(search.SkipRows())
                                            .Take(search.PageSize)
                                            .ToListAsync();
            return new PageModelDto<BasLineDto>(search, entitis, total);
        }


        /// <summary>
        /// 根据ID获取产线数据
        /// </summary>
        /// <returns></returns>

        public async Task<BasLineDto> GetByIdAsync(long id)
        {
            var line = await _basLineRepo.GetAsync(id);

            var lineDto = Mapper.Map<BasLineDto>(line);

            var items = await _basLineProRepo.Where(x => !x.IsDeleted && x.LineCode == line.Lcode).ToListAsync();
            lineDto.Items = Mapper.Map<List<BasLineProductRelationDto>>(items);

            var products = await _basProductRepo.Where(x => !x.IsDeleted && items.Select(p => p.ProductCode).Contains(x.Procode)).ToListAsync();

            lineDto.Items.ForEach(x =>
            {
                // x.ProductCode = products.FirstOrDefault(p=>p.p==x.IdProduct).Procode??""; 
                x.ProductName = products.FirstOrDefault(p => p.Procode == x.ProductCode)?.Proname ?? "";
            });
            return lineDto;
        }



        /// <summary>
        /// 创建产线
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult<long>> CreateAsync(BasLineDto input)
        {
            input.TrimStringFields();
            var line = await _basLineMgr.CreateAsync(input.Lcode ?? "", input.Lname ?? "", input.IdWorkshop ?? 0, input.Remark ?? "", input.Status ?? 0);

            var autoCode = _basNumberManagementService.GetNumberBySimpleName(CommonConst.LINENUMBER).Result;
            line.Lcode = autoCode;
            var items = Mapper.Map<List<BasLineProductRelation>>(input.Items);
            items.ForEach(o =>
            {
                o.Id = IdGenerater.GetNextId();
                o.LineCode = autoCode;
            });
            await _basLineRepo.InsertAsync(line);
            await _basLineProRepo.InsertRangeAsync(items);
            return line.Id;
        }



        /// <summary>
        /// 更新产线
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult> UpdateAsync(BasLineDto input)
        {
            bool isUpdete = await _basLineMgr.UpdateAsync(input.Lcode ?? "", input.Lname ?? "", input.Id ?? 0);
            if (isUpdete)
            {
                var line = await _basLineRepo.GetAsync(input.Id ?? 0);
                string lCode = line.Lcode;
                if (line != null)
                {
                    line.IdWorkshop = input.IdWorkshop;
                    line.Lcode = input.Lcode;
                    line.Lname = input.Lname;
                    line.Status = input.Status;
                    line.Remark = input.Remark;
                    await _basLineRepo.UpdateAsync(line);

                    var items = await _basLineProRepo.Where(x => x.LineCode == lCode, false, false).ToListAsync();
                    await _basLineProRepo.RemoveRangeAsync(items);


                    var insertItems = Mapper.Map<List<BasLineProductRelation>>(input.Items);
                    insertItems.ForEach(o =>
                    {
                        o.Id = IdGenerater.GetNextId();
                        o.LineCode = line.Lcode;
                    });
                    await _basLineProRepo.InsertRangeAsync(insertItems);


                }
            }
            return AppSrvResult();
        }

        /// <summary>
        /// 删除产线
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult> DeleteAsync(long id)
        {
            var line = await _basLineRepo.GetAsync(id);
            if (line != null)
            {
                var items = await _basLineProRepo.Where(x => !x.IsDeleted && x.LineCode == line.Lcode, false, false).ToListAsync();

                if(items?.Count > 0)
                {
                    return Problem(HttpStatusCode.BadRequest, "该产线有所属产品，不可以删除！");
                }

                await items.ForEachAsync(o =>
                {
                    o.IsDeleted = true;
                });
                await _basLineProRepo.UpdateRangeAsync(items);

                line.IsDeleted = true;
                await _basLineRepo.UpdateAsync(line);
            }
            return AppSrvResult();


        }
        /// <summary>
        /// 批量禁用启用
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult> MakeEnableAsync(int status,List<long> ids)
        {
            var lines = await _basLineRepo.Where(x => !x.IsDeleted && ids.Contains((long)x.Id), false, false).ToListAsync();

            if (lines.Any())
            {
                await lines.ForEachAsync(o =>
                {
                    o.Status = status;
                });
                await _basLineRepo.UpdateRangeAsync(lines);
            }
            return AppSrvResult();
        }

        /// <summary>
        /// 获取所有产线数据
        /// </summary>
        /// <returns></returns>

        public async Task<List<BasLineDto>> GetAllAsync()
        {
            var lines = await _basLineRepo.Where(x => !x.IsDeleted && x.Status == 1).ToListAsync();

            var linesDto = Mapper.Map<List<BasLineDto>>(lines);

            return linesDto;
        }

        public async Task<List<ProductLineDto>> GetLinebyProductcode(string productCode)
        {

            var lines = _basLineRepo.Where(x => !x.IsDeleted && x.Status == 1);
            var linePros = _basLineProRepo.Where(x => !x.IsDeleted && x.ProductCode == productCode);
            var results =await (from l in lines
                           join p in linePros on l.Lcode equals p.LineCode
                           select new ProductLineDto
                           {
                               Lcode = l.Lcode,
                               Lname = l.Lname
                           }).Distinct().ToListAsync();
            return results;
        }
    }
}
