﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasProductTechnologyRelationAggregate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class BasProductTechnologyRelationConfig : AbstractEntityTypeConfiguration<BasProductTechnologyRelation>
    {
        public override void Configure(EntityTypeBuilder<BasProductTechnologyRelation> builder)
        {
            base.Configure(builder);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.IdTechnology).HasColumnName("idtechnology");
            builder.Property(x => x.IdProduct).HasColumnName("idproduct");
            builder.Property(x => x.Status).HasColumnName("status");
            builder.Property(x => x.Remark).HasColumnName("remark");
            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.CreateBy).HasColumnName("createby");
            builder.Property(x => x.CreateTime).HasColumnName("createtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
        }
    }
}
