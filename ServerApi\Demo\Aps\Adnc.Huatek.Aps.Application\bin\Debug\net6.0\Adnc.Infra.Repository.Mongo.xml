<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Adnc.Infra.Repository.Mongo</name>
    </assembly>
    <members>
        <member name="T:Adnc.Infra.Repository.Mongo.Configuration.MongoConfigurationBuilder">
            <summary>
            A configuration builder for this package.
            </summary>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.Configuration.MongoConfigurationBuilder.#ctor(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Initializes a new instance of the <see cref="T:Adnc.Infra.Repository.Mongo.Configuration.MongoConfigurationBuilder" /> class.
            </summary>
            <param name="services">The services.</param>
        </member>
        <member name="P:Adnc.Infra.Repository.Mongo.Configuration.MongoConfigurationBuilder.Services">
            <summary>
            Gets the service collection.
            </summary>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.Configuration.MongoConfigurationBuilder.FromAssembly(System.Reflection.Assembly)">
            <summary>
            Searches the specified assembly for type and registers them.
            * Registers all public implementations of <see cref="T:Adnc.Infra.IRepositories.IMongoRepository`1"/> as each of their non-generic interfaces.
            * Adds entity configuration provided by implementations of <see cref="T:Adnc.Infra.Repository.Mongo.Interfaces.IMongoEntityConfiguration`1"/>.
            </summary>
            <param name="assembly">The assembly.</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.Configuration.MongoConfigurationBuilder.FromAssemblyContaining``1">
            <summary>
            Registers all public implementations of <see cref="T:Adnc.Infra.IRepositories.IMongoRepository`1"/> as each of their non-generic interfaces.
            Adds indexes defined in public implementations of <see cref="T:Adnc.Infra.Repository.Mongo.Interfaces.IMongoEntityConfiguration`1"/>.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Adnc.Infra.Repository.Mongo.Configuration.MongoEntityBuilder`1">
            <summary>
            Mongo entity builder.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
        </member>
        <member name="P:Adnc.Infra.Repository.Mongo.Configuration.MongoEntityBuilder`1.Indexes">
            <summary>
            Configures indexes.
            </summary>
        </member>
        <member name="P:Adnc.Infra.Repository.Mongo.Configuration.MongoEntityBuilder`1.Seed">
            <summary>
            Configures seed data.
            </summary>
        </member>
        <member name="T:Adnc.Infra.Repository.Mongo.Configuration.MongoIndexContext`1">
            <summary>
            A collection of mongo indexes.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.Configuration.MongoIndexContext`1.GetEnumerator">
            <inheritdoc />
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.Configuration.MongoIndexContext`1.System#Collections#IEnumerable#GetEnumerator">
            <inheritdoc />
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.Configuration.MongoIndexContext`1.Add(MongoDB.Driver.CreateIndexModel{`0})">
            <inheritdoc />
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.Configuration.MongoIndexContext`1.Clear">
            <inheritdoc />
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.Configuration.MongoIndexContext`1.Contains(MongoDB.Driver.CreateIndexModel{`0})">
            <inheritdoc />
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.Configuration.MongoIndexContext`1.CopyTo(MongoDB.Driver.CreateIndexModel{`0}[],System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.Configuration.MongoIndexContext`1.Remove(MongoDB.Driver.CreateIndexModel{`0})">
            <inheritdoc />
        </member>
        <member name="P:Adnc.Infra.Repository.Mongo.Configuration.MongoIndexContext`1.Count">
            <inheritdoc />
        </member>
        <member name="P:Adnc.Infra.Repository.Mongo.Configuration.MongoIndexContext`1.IsReadOnly">
            <inheritdoc />
        </member>
        <member name="P:Adnc.Infra.Repository.Mongo.Configuration.MongoRepositoryOptions.ConnectionString">
            <summary>
            Gets or sets the MongoDB connection string.
            </summary>
        </member>
        <member name="P:Adnc.Infra.Repository.Mongo.Configuration.MongoRepositoryOptions.CollectionNamingConvention">
            <summary>
            Gets or sets the collection naming convention.
            Defaults to <see cref="F:Adnc.Infra.Repository.Mongo.Configuration.NamingConvention.Snake"/>.
            </summary>
        </member>
        <member name="P:Adnc.Infra.Repository.Mongo.Configuration.MongoRepositoryOptions.PluralizeCollectionNames">
            <summary>
            Gets or sets a value indicating whether to pluralize collection names.
            Defaults to <c>true</c>.
            </summary>
        </member>
        <member name="F:Adnc.Infra.Repository.Mongo.Configuration.NamingConvention.LowerCase">
            <summary>
            Convert names to "lowercase" without word separators.
            </summary>
        </member>
        <member name="F:Adnc.Infra.Repository.Mongo.Configuration.NamingConvention.UpperCase">
            <summary>
            Convert names to "UPPERCASE" without word separators.
            </summary>
        </member>
        <member name="F:Adnc.Infra.Repository.Mongo.Configuration.NamingConvention.Pascal">
            <summary>
            Convert names to "UpperCamelCase".
            </summary>
        </member>
        <member name="F:Adnc.Infra.Repository.Mongo.Configuration.NamingConvention.Camel">
            <summary>
            Convert names to "camelCase".
            </summary>
        </member>
        <member name="F:Adnc.Infra.Repository.Mongo.Configuration.NamingConvention.Snake">
            <summary>
            Convert names to "snake_case".
            </summary>
        </member>
        <member name="T:Adnc.Infra.Repository.Mongo.Entities.ExpiringMongoEntity">
            <summary>
            A mongo entity that will be automatically deleted after a configured time has elapsed.
            </summary>
            <seealso cref="T:Adnc.Infra.Entities.MongoEntity" />
        </member>
        <member name="P:Adnc.Infra.Repository.Mongo.Entities.ExpiringMongoEntity.DateCreated">
            <summary>
            Gets or sets the date at which this entity was created.
            </summary>
        </member>
        <member name="T:Adnc.Infra.Repository.Mongo.Entities.SoftDeletableMongoEntity">
            <summary>
            A mongo entity with soft delete support.
            </summary>
        </member>
        <member name="P:Adnc.Infra.Repository.Mongo.Entities.SoftDeletableMongoEntity.DateDeleted">
            <summary>
            Gets or sets the date that this entity was soft deleted.
            Or null if it was not soft deleted.
            </summary>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.Extensions.MongoConfigurationExtensions.GetCollectionName``1(Adnc.Infra.Repository.Mongo.Configuration.MongoRepositoryOptions)">
            <summary>
            Gets the name of the mongo collection configured for the specified type.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="options">The configuration.</param>
            <returns></returns>
        </member>
        <member name="T:Adnc.Infra.Repository.Mongo.Extensions.MongoIndexContextExtensions">
            <summary>
            Extensions for <see cref="T:Adnc.Infra.Repository.Mongo.Configuration.MongoIndexContext`1"/>.
            </summary>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.Extensions.MongoIndexContextExtensions.Add``1(Adnc.Infra.Repository.Mongo.Configuration.MongoIndexContext{``0},System.String,MongoDB.Driver.IndexKeysDefinition{``0},System.Action{MongoDB.Driver.CreateIndexOptions})">
            <summary>
            Adds the specified named index.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="context">The context.</param>
            <param name="name">The name.</param>
            <param name="keys">The keys.</param>
            <param name="optionsConfigurator">The options configurator.</param>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.Extensions.MongoIndexContextExtensions.AddSoftDeletableIndex``1(Adnc.Infra.Repository.Mongo.Configuration.MongoIndexContext{``0})">
            <summary>
            Adds the date_deleted index that's required for soft deletable support.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="context">The context.</param>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.Extensions.MongoIndexContextExtensions.AddExpiringIndex``1(Adnc.Infra.Repository.Mongo.Configuration.MongoIndexContext{``0},System.TimeSpan)">
            <summary>
            Adds the date_created index that's required for expiring entity support.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="context">The context.</param>
            <param name="expireAfter">The expire after.</param>
        </member>
        <member name="T:Adnc.Infra.Repository.Mongo.Extensions.MongoOptionsExtensions">
            <summary>
            Extensions for options classes in MongoDB. E.g. <see cref="T:MongoDB.Driver.FindOptions`1"/>, <see cref="T:MongoDB.Driver.CreateIndexOptions`1"/>.
            </summary>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.Extensions.MongoOptionsExtensions.WithCaseInsensitiveCollation``1(MongoDB.Driver.FindOptions{``0})">
            <summary>
            Adds a case-insensitive collation option to the specified find options.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="options">The options.</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.Extensions.MongoOptionsExtensions.WithCaseInsensitiveCollation(MongoDB.Driver.CreateIndexOptions)">
            <summary>
            Adds a case-insensitive collation option to the specified create index options.
            </summary>
            <param name="options">The options.</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.Extensions.MongoOptionsExtensions.Unique(MongoDB.Driver.CreateIndexOptions)">
            <summary>
            Turns on the unique flag in the specified options.
            </summary>
            <param name="options">The options.</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.Extensions.MongoOptionsExtensions.ExpireAfter(MongoDB.Driver.CreateIndexOptions,System.TimeSpan)">
            <summary>
            Configures the expires after configuration in the specified options.
            </summary>
            <param name="options">The options.</param>
            <param name="expireAfter">The expire after.</param>
            <returns></returns>
        </member>
        <member name="T:Adnc.Infra.Repository.Mongo.Extensions.ServiceCollectionExtensions">
            <summary>
            Extensions for <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> to add easy MongoDB wiring.
            </summary>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.Extensions.ServiceCollectionExtensions.AddAdncInfraMongo``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{Adnc.Infra.Repository.Mongo.Configuration.MongoRepositoryOptions})">
            <summary>
            Registers the MongoDB context with the specified service collection.
            </summary>
            <param name="services">The services.</param>
            <param name="configurator">The configurator.</param>
            <returns></returns>
            <remarks>
            This currently requires wiring up memory caching and logging.
            </remarks>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.Extensions.ServiceCollectionExtensions.AddAdncInfraMongo``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.String)">
            <summary>
            Registers the MongoDB context with the specified service collection.
            </summary>
            <param name="services">The services.</param>
            <param name="connectionString">The connection string.</param>
            <returns></returns>
            <remarks>
            This currently requires wiring up memory caching and logging.
            </remarks>
        </member>
        <member name="T:Adnc.Infra.Repository.Mongo.Interfaces.IMongoContext">
            <summary>
            Context used to maintain a single MongoDB connection.
            </summary>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.Interfaces.IMongoContext.GetCollectionAsync``1(System.Threading.CancellationToken)">
            <summary>
            Gets the MongoDB collection for the specified type.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="cancellationToken">The cancellation token.</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.Interfaces.IMongoContext.DropCollectionAsync``1(System.Threading.CancellationToken)">
            <summary>
            Drops the collection for the specified entity type.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="cancellationToken">The cancellation token.</param>
            <returns></returns>
        </member>
        <member name="T:Adnc.Infra.Repository.Mongo.Interfaces.IMongoEntityConfiguration`1">
            <summary>
            Mongo entity configuration.
            </summary>
        </member>
        <member name="T:Adnc.Infra.Repository.Mongo.Interfaces.ISoftDeletableMongoRepository`1">
            <summary>
            A MongoDB based repository of <see cref="T:TEntity" /> that supports soft deletion.
            Entities that implement soft deletion should probably have an index defined on the <see cref="P:Adnc.Infra.Repository.Mongo.Entities.SoftDeletableMongoEntity.DateDeleted"/> field.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <seealso cref="T:Adnc.Infra.IRepositories.IMongoRepository`1" />
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.Interfaces.ISoftDeletableMongoRepository`1.UnDeleteAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Un-deletes the entity with the specified identifier.
            </summary>
            <param name="id">The identifier.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns></returns>
        </member>
        <member name="T:Adnc.Infra.Repository.Mongo.MongoContext">
            <summary>
            Context used to maintain a single MongoDB connection.
            </summary>
            <seealso cref="T:Adnc.Infra.Repository.Mongo.Interfaces.IMongoContext" />
            <seealso cref="T:System.IDisposable" />
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.MongoContext.#ctor(Microsoft.Extensions.Options.IOptions{Adnc.Infra.Repository.Mongo.Configuration.MongoRepositoryOptions},System.IServiceProvider)">
            <summary>
            Initializes a new instance of the <see cref="T:Adnc.Infra.Repository.Mongo.MongoContext" /> class.
            </summary>
            <param name="options">The options.</param>
            <param name="serviceProvider">The service provider.</param>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.MongoContext.GetCollectionAsync``1(System.Threading.CancellationToken)">
            <summary>
            Gets the MongoDB collection for the specified type.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="cancellationToken">The cancellation token.</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.MongoContext.DropCollectionAsync``1(System.Threading.CancellationToken)">
            <summary>
            Drops the collection for the specified entity type.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <param name="cancellationToken">The cancellation token.</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.MongoContext.Dispose">
            <summary>
            Releases unmanaged and - optionally - managed resources.
            </summary>
        </member>
        <member name="T:Adnc.Infra.Repository.Mongo.MongoRepository`1">
            <summary>
            A MongoDB based repository of <see cref="T:TEntity"/>.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.MongoRepository`1.#ctor(Adnc.Infra.Repository.Mongo.Interfaces.IMongoContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Adnc.Infra.Repository.Mongo.MongoRepository`1"/> class.
            </summary>
            <param name="context">The context.</param>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.MongoRepository`1.GetAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets the entity with the specified identifier.
            </summary>
            <param name="id">The identifier.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.MongoRepository`1.GetAllAsync(System.Threading.CancellationToken)">
            <summary>
            Gets all entities in this repository.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.MongoRepository`1.AddAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Adds the specified entity.
            </summary>
            <param name="entity">The entity.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.MongoRepository`1.AddManyAsync(System.Collections.Generic.IEnumerable{`0},System.Threading.CancellationToken)">
            <summary>
            Adds the specified entities.
            </summary>
            <param name="entities">The entities.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.MongoRepository`1.DeleteAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Deletes the entity with the specified identifier.
            </summary>
            <param name="id">The identifier.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.MongoRepository`1.DeleteManyAsync(MongoDB.Driver.FilterDefinition{`0},System.Threading.CancellationToken)">
            <summary>
            批量删除
            </summary>
            <param name="filter">删除条件</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.MongoRepository`1.ReplaceAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Replaces the specified entity with the same identifier.
            </summary>
            <param name="entity">The entity.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns>The replaced document.</returns>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.MongoRepository`1.UpdateAsync(System.String,MongoDB.Driver.UpdateDefinition{`0},MongoDB.Driver.FindOneAndUpdateOptions{`0},System.Threading.CancellationToken)">
            <summary>
            Updates the entity with the specified key according to the specified update definition.
            </summary>
            <param name="id">The identifier.</param>
            <param name="updateDefinition">The update definition.</param>
            <param name="options">The options.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.MongoRepository`1.FindOneAsync(MongoDB.Driver.FilterDefinition{`0},MongoDB.Driver.FindOptions{`0},System.Threading.CancellationToken)">
            <summary>
            Finds the entity according to the specified filter definition.
            </summary>
            <param name="filter">The filter.</param>
            <param name="options">The options.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.MongoRepository`1.FindAsync(MongoDB.Driver.FilterDefinition{`0},MongoDB.Driver.FindOptions{`0},System.Threading.CancellationToken)">
            <summary>
            Finds all entities according to the specified filter definition.
            </summary>
            <param name="filter">The filter.</param>
            <param name="options">The options.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.MongoRepository`1.GetCollectionAsync(System.Threading.CancellationToken)">
            <summary>
            Gets the Mongo collection that backs this repository.
            </summary>
            <param name="cancellationToken">The cancellation token.</param>
            <returns></returns>
        </member>
        <member name="T:Adnc.Infra.Repository.Mongo.SoftDeletableMongoRepository`1">
            <summary>
            A MongoDB based repository of <see cref="T:TEntity"/> that supports soft deletion.
            Entities that implement soft deletion should probably have an index defined on the <see cref="P:Adnc.Infra.Repository.Mongo.Entities.SoftDeletableMongoEntity.DateDeleted"/> field.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
            <seealso cref="T:Adnc.Infra.Repository.Mongo.MongoRepository`1" />
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.SoftDeletableMongoRepository`1.#ctor(Adnc.Infra.Repository.Mongo.Interfaces.IMongoContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Adnc.Infra.Repository.Mongo.SoftDeletableMongoRepository`1"/> class.
            </summary>
            <param name="context">The context.</param>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.SoftDeletableMongoRepository`1.DeleteAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Soft deletes the entity with the specified identifier.
            </summary>
            <param name="id">The identifier.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.SoftDeletableMongoRepository`1.UnDeleteAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Un-deletes the entity with the specified identifier.
            </summary>
            <param name="id">The identifier.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.SoftDeletableMongoRepository`1.GetAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets the non-deleted entity with the specified identifier.
            </summary>
            <param name="id">The identifier.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns></returns>
        </member>
        <member name="M:Adnc.Infra.Repository.Mongo.SoftDeletableMongoRepository`1.GetAllAsync(System.Threading.CancellationToken)">
            <summary>
            Gets all non-deleted entities in this repository.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Adnc.Infra.Core.Configuration.MongoOptions">
            <summary>
            Mongodb配置
            </summary>
        </member>
        <member name="P:Adnc.Infra.Core.Configuration.MongoOptions.ConnectionString">
            <summary>
            Gets or sets the MongoDB connection string.
            </summary>
        </member>
        <member name="P:Adnc.Infra.Core.Configuration.MongoOptions.CollectionNamingConvention">
            <summary>
            Gets or sets the collection naming convention.
            Defaults to NamingConvention.Snake/>.
            </summary>
        </member>
        <member name="P:Adnc.Infra.Core.Configuration.MongoOptions.PluralizeCollectionNames">
            <summary>
            Gets or sets a value indicating whether to pluralize collection names.
            Defaults to <c>true</c>.
            </summary>
        </member>
        <member name="F:Adnc.Infra.Core.Configuration.MongoNamingConvention.LowerCase">
            <summary>
            Convert names to "lowercase" without word separators.
            </summary>
        </member>
        <member name="F:Adnc.Infra.Core.Configuration.MongoNamingConvention.UpperCase">
            <summary>
            Convert names to "UPPERCASE" without word separators.
            </summary>
        </member>
        <member name="F:Adnc.Infra.Core.Configuration.MongoNamingConvention.Pascal">
            <summary>
            Convert names to "UpperCamelCase".
            </summary>
        </member>
        <member name="F:Adnc.Infra.Core.Configuration.MongoNamingConvention.Camel">
            <summary>
            Convert names to "camelCase".
            </summary>
        </member>
        <member name="F:Adnc.Infra.Core.Configuration.MongoNamingConvention.Snake">
            <summary>
            Convert names to "snake_case".
            </summary>
        </member>
    </members>
</doc>
