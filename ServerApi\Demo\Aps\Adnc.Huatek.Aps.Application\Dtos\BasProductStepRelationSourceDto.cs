﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Dtos
{
    public class BasProductStepRelationSourceDto : IDto
    {
        /// <summary>
        /// 是否编辑
        /// </summary>
        public bool IsEdit { get; set; }

        /// <summary>
        /// ID
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// ID
        /// </summary>
        public long? MainId { get; set; }

        /// <summary>
        /// 资源大类
        /// </summary>
        public string? SourceType { get; set; }


        /// <summary>
        /// 资源大类名称
        /// </summary>
        public string? SourceTypeName { get; set; }

        /// <summary>
        /// 资源小类
        /// </summary>
        public string? DeviceType { get; set; }

        /// <summary>
        /// 物料类型设备类型
        /// </summary>
        public string? DeviceTypeName { get; set; }

        /// <summary>
        /// 是否主资源
        /// </summary>
        public bool MainSource { get; set; }


        /// <summary>
        /// 是否考虑产能
        /// </summary>
        public bool IsCapacity { get; set; }

        /// <summary>
        /// 单位工作时长
        /// </summary>
        public decimal? Tat { get; set; }

        /// <summary>
        /// 单位产能
        /// </summary>
        public decimal? Capacity { get; set; }

        

        /// <summary>
        /// 创建人ID
        /// </summary>
        public long? CreateBy { get; set; }

        /// <summary>
        /// 创建时间/注册时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 最后更新人
        /// </summary>
        public long? ModifyBy { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }


    }
}
