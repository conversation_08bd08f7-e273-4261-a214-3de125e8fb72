﻿using Adnc.Huatek.Aps.Application.Dtos;

namespace Adnc.Huatek.Aps.Application.Dtos
{
    /// <summary>
    /// 生产计划
    /// </summary>
    public class PartSchedulePlanMaterialDto : Dto
    {
        /// <summary>
        /// 排产计划编码
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 排产计划编码
        /// </summary>
        public string? ScheduleCode { get; set; }

        /// <summary>
        /// 生产计划编码
        /// </summary>
        public string? StepCode { get; set; }

        /// <summary>
        /// 产品
        /// </summary>
        public string? Mcode { get; set; }

        /// <summary>
        /// 生产数量
        /// </summary>
        public string? Mname { get; set; }

        /// <summary>
        /// 开始排产时间
        /// </summary>
        public DateTime NeedDate  { get; set; }

        /// <summary>
        /// delivery time
        /// </summary>
        public decimal? Qty { get; set; }
    }

}

