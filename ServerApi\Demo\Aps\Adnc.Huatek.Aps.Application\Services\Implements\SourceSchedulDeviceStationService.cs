﻿using Adnc.Huatek.Aps.Domain.Aggregates.PlanSchedulStepSolutionAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.SysUserAggregate;
using Adnc.Huatek.Aps.Domain.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services.Implements
{
    public class SourceSchedulDeviceStationService : AbstractAppService, ISourceSchedulDeviceStationService
    {
        private readonly SourceSchedulDeviceStationManagement _sourceSchedulDeviceStationMgr;
        private readonly IEfBasicRepository<PlanSchedulStepSolution> _planSchedulStepSolutionRepo;
        private readonly IEfBasicRepository<SysUser> _sysUserRepo;

        public SourceSchedulDeviceStationService(SourceSchedulDeviceStationManagement sourceSchedulDeviceStationMgr,
           IEfBasicRepository<PlanSchedulStepSolution> planSchedulStepSolutionRepo,
          IEfBasicRepository<SysUser> sysUserRepo) 
        {
            _sourceSchedulDeviceStationMgr = sourceSchedulDeviceStationMgr;
            _planSchedulStepSolutionRepo = planSchedulStepSolutionRepo;
            _sysUserRepo = sysUserRepo;
        }
    }
}
