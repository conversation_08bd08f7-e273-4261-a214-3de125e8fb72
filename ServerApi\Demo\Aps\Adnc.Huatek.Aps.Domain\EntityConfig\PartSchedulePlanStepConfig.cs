﻿using Adnc.Huatek.Aps.Domain.Aggregates.PartSchedulePlanStepAggregate;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class PartSchedulePlanStepConfig : AbstractEntityTypeConfiguration<PartSchedulePlanStep>
    {
        public override void Configure(EntityTypeBuilder<PartSchedulePlanStep> builder)
        {
            base.Configure(builder);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.ScheduleCode).HasColumnName("schedulecode");
            builder.Property(x => x.BeginDate ).HasColumnName("begindate");
            builder.Property(x => x.EndDate).HasColumnName("enddate");
            builder.Property(x => x.Tat).HasColumnName("tat");
            builder.Property(x => x.PreCode).HasColumnName("precode");
            builder.Property(x => x.ReportResult).HasColumnName("reportresult");
            builder.Property(x => x.ReportDate).HasColumnName("reportdate");
            builder.Property(x => x.ScheduleType).HasColumnName("scheduletype");
            builder.Property(x => x.StepCode).HasColumnName("stepcode");
            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.CreateBy).HasColumnName("createby");
            builder.Property(x => x.CreateTime).HasColumnName("createtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
           
        }
    }
}
