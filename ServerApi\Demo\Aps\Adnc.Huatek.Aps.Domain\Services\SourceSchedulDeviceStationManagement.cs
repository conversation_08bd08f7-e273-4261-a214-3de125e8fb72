﻿using Adnc.Huatek.Aps.Domain.Aggregates.SourceSchedulDeviceStationAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.SysTestAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.SysUserAggregate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Services
{
    public class SourceSchedulDeviceStationManagement : IDomainService
    {
        private readonly IEfBasicRepository<SourceSchedulDeviceStation> _sourceSchedulDeviceStationManagementRepo;

        public SourceSchedulDeviceStationManagement(IEfBasicRepository<SourceSchedulDeviceStation> sourceSchedulDeviceStationManagementRepo)
        {
            _sourceSchedulDeviceStationManagementRepo = sourceSchedulDeviceStationManagementRepo;
        }
    }
}
