﻿using Adnc.Demo.Shared.Const;
using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Application.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Adnc.Huatek.Aps.Api.Controllers
{

    [Route($"{RouteConsts.ApsRoot}/relationship")]
    [ApiController]
    public class BasRelationController : AdncControllerBase
    {
        private readonly IBasRelationService _relationSrv;

        public BasRelationController(IBasRelationService relationSrv) => _relationSrv = relationSrv;

        /// <summary>
        /// 新增资源关系搭建
        /// </summary>
        /// <param name="input">资源关系搭建信息</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("createasync")]
        public async Task<ActionResult<long>> CreateAsync([FromBody] BasRelationDto input)
            => CreatedResult(await _relationSrv.CreateAsync(input));

        /// <summary>
        /// 删除资源关系搭建
        /// </summary>
        /// <param name="id">资源关系搭建ID</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpDelete("deleteasync/{id}")]
        public async Task<ActionResult> DeleteAsync([FromRoute] long id)
            => Result(await _relationSrv.DeleteAsync(id));


        /// <summary>
        /// 修改资源关系搭建
        /// </summary>
        /// <param name="id">id</param>
        /// <param name="input">资源关系搭建信息</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPut("updateasync")]
        public async Task<ActionResult> UpdateAsync([FromBody] BasRelationDto input)
            => Result(await _relationSrv.UpdateAsync(input));

        /// <summary>
        /// 获取资源关系搭建列表分页
        /// </summary>
        /// <param name="search">查询条件</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("page")]
        public async Task<ActionResult<PageModelDto<BasRelationDto>>> GetPagedAsync([FromBody] RelationPagedDto search)
            => await _relationSrv.GetPagedAsync(search);

        /// <summary>
        /// 获取单个资源关系搭建详情信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("current/{id}")]
        public async Task<ActionResult<BasRelationDto>> GetCurrentUserInfoAsync(long id) => await _relationSrv.GetByIdAsync(id);

    }
}
