﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Infra.Redis.Caching.Core.Interceptor;
using Adnc.Shared.Application.Contracts.ResultModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services
{
    

    public interface IBasWorksjopService : IAppService
    {
        /// <summary>
        /// 获取所有车间信息
        /// </summary>
        /// <returns></returns>
        [OperateLog(LogName = "获取所有车间信息")]
        Task<List<BasWorksjopDto>> GetAllAsync();

        /// <summary>
        /// 获取所有车间信息
        /// </summary>
        /// <returns></returns>
        [OperateLog(LogName = "获取所有车间信息")]

        Task<List<dynamic>> GetAllWorkshopAsync();

        /// <summary>
        /// 获取单个车间信息
        /// </summary>
        /// <returns></returns>
        [OperateLog(LogName = "获取指定车间信息")]
        Task<ResultJson> GetAppointAsync(long id);

        /// <summary>
        /// 新增车间
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [OperateLog(LogName = "新增车间")]
        Task<AppSrvResult<long>> CreateAsync(BasWorksjopDto input);

        /// <summary>
        /// 修改车间
        /// </summary>
        /// <param name="id"></param>
        /// <param name="input"></param>
        /// <returns></returns>
        [OperateLog(LogName = "修改车间")]
        Task<AppSrvResult> UpdateAsync(BasWorksjopDto input);


        /// <summary>
        /// 删除车间
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [OperateLog(LogName = "删除车间")]
        Task<AppSrvResult> DeleteAsync(long id);



        /// <summary>
        /// 修改车间状态
        /// </summary>
        /// <param name="id"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        [OperateLog(LogName = "修改车间状态")]
        Task<AppSrvResult> ChangeStatusAsync([CachingParam] long id, int status);

        /// <summary>
        /// 批量修改车间状态
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        [OperateLog(LogName = "批量修改车间状态")]
        Task<AppSrvResult> ChangeStatusAsync(UpdateStatusDto input);


        /// <summary>
        /// 获取车间列表
        /// </summary>
        /// <param name="search"></param>
        /// <returns></returns>
        Task<PageModelDto<BasWorksjopDto>> GetPagedAsync(BasStationPagedDto search);

    }
}
