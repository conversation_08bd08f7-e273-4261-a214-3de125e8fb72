﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Shared.Application.Contracts.ResultModels;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services
{
    public interface IPlanProductionSchedulService : IAppService
    {

        [OperateLog(LogName = "获取生产计划列表信息")]
        Task<PageModelDto<PlanProductionSchedulResultDto>> GetPagedAsync(PlanProductionSchedulPageDto search);
        [OperateLog(LogName = "获取生产计划详情信息")]
        Task<PlanProductionSchedulDetailsResultDto> GetDetailsAsync(string schCode);

        [OperateLog(LogName = "获取生产计划详情信息NEW")]
        Task<PartSchedulePlanDto> GetDetailsNewAsync(long id);
        [OperateLog(LogName = "获取产品生产计划甘特图")]
        Task<List<PlanProductionSchedulGanttChildItem>> GetGanttByProductAsync(ProductionSchedulGanttDto dto);

        [OperateLog(LogName = "获取产品生产计划甘特图")]
        Task<PlanGanttDto> GetGanttByProductNewAsync(long id);

        [OperateLog(LogName = "获取订单产品排产计划甘特图")]
        Task<List<PlanProductionSchedulGanttNewResultDto>> GetGanttByOrderProductAsync(OrderProdcutGanttDto dto);

        [OperateLog(LogName = "获取产品订单排产计划甘特图")]
        Task<List<PlanProductionSchedulGanttForOrderDto>> GetGanttByOrderAsync(ProductionSchedulGanttDto dto);
        [OperateLog(LogName = "获取产品订单排产计划甘特图New")]
        Task<List<PlanProductionSchedulGanttForOrderDto>> GetGanttByOrderNewAsync(ProductionSchedulGanttDto dto);

        [OperateLog(LogName = "按计划编码获取订单排产计划甘特图")]
        Task<List<PlanProductionSchedulGanttNewResultForPlanDto>> GetGanttByPlanCodeAsync(string planCode);

        //[OperateLog(LogName = "获取订单生产计划甘特图")]
        //Task<List<PlanProductionSchedulGanttNewResultDto>> GetGanttForOrderProducePlanAsync(ProductionSchedulGanttDto dto);
        [OperateLog(LogName = "获取排产计划设备甘特图")]
        Task<ProductionSchedulDeviceGanttResultDto> GetGanttForDeviceAsync(ProductionSchedulGanttDto dto);
        [OperateLog(LogName = "按排产编码获取设备甘特图")]
        Task<List<ScheduleDeviceGanttItem>> GetDeviceGanttAsync(string code);

        
        /// <summary>
        /// 获取物料计划
        /// </summary>
        /// <returns></returns>
        Task<PlanMaterialListDto> GetMaterials(PlanMaterialDto search);

        [OperateLog(LogName = "物料需求计划")]
        Task<PlanMaterialListDto> GetMaterialsNew(PlanMaterialDto search);

        [OperateLog(LogName = "创建排产计划")]
        [UnitOfWork]
        Task<AppSrvResult<long>> CreateAsync(PlanProductionSchedulDto input);

        Task<AppSrvResult<long>> BatchDelAsync(List<string> codes);

        /// <summary>
        /// 获取人员计划
        /// </summary>
        /// <returns></returns>
        Task<List<PersonnelResultDto>> GetPersonnels(PersonnelDto search);
        Task<AppSrvResult<List<dynamic>>> RealoadSchedulAsync(ReloadBasProcuct input);

        //修改状态
        Task<AppSrvResult> setScheduleStatusAsync(SchedulStatus input);
        Task<AppSrvResult<long>> SaveSchedulingProduction(ReloadBasProcuct input);

        //查询排产工序方案
        Task<List<SchduleStepSolutionItem>> GetStepSolution(string code);
        [OperateLog(LogName = "物料预警")]
        Task<PageModelDto<MaterialWarningReturnDto>> ComputeMaterialWarning(MaterialWarningPagedDto search);


        [OperateLog(LogName = "物料预警Chart")]
        Task<MaterialWarningChartDto> GetMaterialWarningChart(string Mcode);

        [OperateLog(LogName = "物料预警New")]
        Task<PageModelDto<MaterialWarningReturnDto>> ComputeMaterialWarningNew(MaterialWarningPagedDto search);

    }
}
