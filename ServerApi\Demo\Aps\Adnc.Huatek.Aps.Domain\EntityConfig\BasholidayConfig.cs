﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasHolidayAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStepAggregate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class BasholidayConfig : AbstractEntityTypeConfiguration<BasHoliday>
    {
        public override void Configure(EntityTypeBuilder<BasHoliday> builder)
        {
            base.Configure(builder);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.CurrentDate).HasColumnName("currentdate");
            builder.Property(x => x.IsHoliday).HasColumnName("isholiday");
            builder.Property(x => x.IsPlaned).HasColumnName("isplaned");
            builder.Property(x => x.WeekName ).HasColumnName("weekname");

            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.CreateBy).HasColumnName("createby");
            builder.Property(x => x.CreateTime).HasColumnName("createtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
        }
    }
}
