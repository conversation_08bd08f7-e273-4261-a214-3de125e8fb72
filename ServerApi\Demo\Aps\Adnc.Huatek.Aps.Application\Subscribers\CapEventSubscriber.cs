﻿using Adnc.Huatek.Aps.Application.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Subscribers
{
    public sealed class CapEventSubscriber : ICapSubscribe
    {
        private readonly ITestAppService _testSrv;
        private readonly ILogger<CapEventSubscriber> _logger;
        private readonly IMessageTracker _tracker;

        public CapEventSubscriber(
                ITestAppService testSrv,
                ILogger<CapEventSubscriber> logger,
                MessageTrackerFactory trackerFactory)
        {
            _testSrv = testSrv;
            _logger = logger;
            _tracker = trackerFactory.Create();
        }

        /// <summary>
        /// 订阅库存锁定事件
        /// </summary>
        /// <param name="eventDto"></param>
        /// <returns></returns>
        [CapSubscribe(nameof(WarehouseQtyBlockedEvent))]
        public async Task ProcessWarehouseQtyBlockedEvent(WarehouseQtyBlockedEvent eventDto)
        {
            eventDto.EventTarget = MethodBase.GetCurrentMethod()?.GetMethodName() ?? string.Empty;
            var hasProcessed = await _tracker.HasProcessedAsync(eventDto);
         //   if (!hasProcessed)
              //  await _testSrv.MarkCreatedStatusAsync(eventDto, _tracker);
        }
    }
}
