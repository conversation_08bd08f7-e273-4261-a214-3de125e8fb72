﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Dtos
{
    public class BasClassesDto : Dto
    {
        /// <summary>
        /// ID
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
        /// <summary>
        /// 简称
        /// </summary>
        public string? ShortName { get; set; }

        /// <summary>
        /// 班次名称
        /// </summary>
        public string? Cname { get; set; }

        /// <summary>
        /// 班次编码
        /// </summary>
        public string? Ccode { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public string? BeginTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public string? EndTime { get; set; }
        /// <summary>
        /// 启用状态
        /// </summary>
        public int? Status { get; set; }

        public string? Color { get; set; }

        /// <summary>
        /// 工作时长(h)
        /// </summary>
        public int? Duration { get; set; }
        /// <summary>
        /// 人员数量
        /// </summary>
        public int? Qty { get; set; }
    }

    public class ClassesPagedDto : SearchPagedDto
    {
        /// <summary>
        /// 班次名称
        /// </summary>
        public string? Cname { get; set; }

        /// <summary>
        /// 班次编码
        /// </summary>
        public string? Ccode { get; set; }

        /// <summary>
        /// 班次简称
        /// </summary>
        public string? ShortName { get; set; }


        /// <summary>
        /// 状态
        /// </summary>
        public int Status { get; set; }
    }

    public class ClassesDto {
        /// <summary>
        /// ID
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// 班次名称
        /// </summary>
        public string? Cname { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public string? BeginTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public string? EndTime { get; set; }

        /// <summary>
        /// 班次时长(h)
        /// </summary>
        public int? Duration { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public int? Status { get; set; }
        /// <summary>
        /// 人员数量
        /// </summary>
        public int? Qty { get; set; }
    }
}
