﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasLineProductRelationAggregate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class BasLinrProductRelationConfig : AbstractEntityTypeConfiguration<BasLineProductRelation>
    {
        public override void Configure(EntityTypeBuilder<BasLineProductRelation> builder)
        {
            base.Configure(builder);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.LineCode).HasColumnName("linecode");
            builder.Property(x => x.ProductCode).HasColumnName("productcode");
            //builder.Property(x => x.Capacity).HasColumnName("capacity");
            //builder.Property(x => x.Unit).HasColumnName("unit");
            builder.Property(x => x.Remark).HasColumnName("remark");
            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.CreateBy).HasColumnName("createby");
            builder.Property(x => x.CreateTime).HasColumnName("createtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
        }
    }
}
