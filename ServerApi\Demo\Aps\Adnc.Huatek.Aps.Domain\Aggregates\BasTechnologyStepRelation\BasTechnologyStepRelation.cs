﻿using Adnc.Infra.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Aggregates.BasTechnologyStepRelationAggregate
{
    public class BasTechnologyStepRelation : EfFullAuditEntity
    {
        /// <summary>
        /// ID
        /// </summary>
        public long? Id { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 工艺路线ID
        /// </summary>
        public long? IdTechnology { get; set; }

        public string? StepCode { get; set; }

        public string? TCode { get; set; }

        /// <summary>
        /// 工序路线id
        /// </summary>
        public long? IdStep { get; set; }

        /// <summary>
        /// 是否关键工序
        /// </summary>
        public int? IsKey { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int? Sort { get; set; }

        /// <summary>
        /// 与下一道工序的关系
        /// </summary>
        public int? RelationType { get; set; }

        /// <summary>
        /// 前置工序间隔时间
        /// </summary>
        public decimal? IntervalTime { get; set; }

        /// <summary>
        /// 前置工序
        /// </summary>
        public string? _preStep;

        public string? PreStep
        { 
            get
            {
                if (_preStep == null)
                    _preStep = "";
                return _preStep;
            }
            set
            {
                _preStep = value;
            }
        }
    }
}
