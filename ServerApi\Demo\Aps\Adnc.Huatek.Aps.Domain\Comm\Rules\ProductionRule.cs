﻿using NRules.Fluent.Dsl;

namespace Adnc.Huatek.Aps.Domain.Comm.Rules
{
    /// <summary>
    /// 创建规则引擎
    /// </summary>
    public class ProductionRule : Rule
    {
        public override void Define()
        {
            // 定义规则逻辑
            ProductionOrder order = null;

            When()
                .Match<ProductionOrder>(() => order, o => o.Status == OrderStatusNew.Pending);

            Then()
                .Do(ctx => ScheduleProduction(order));
        }

        private void ScheduleProduction(ProductionOrder order)
        {
            // 在这里实现调度逻辑
            // 可以根据规则匹配的订单信息进行排产操作
        }
    }
}

public class ProductionOrder
{
    public int OrderId { get; set; }
    public OrderStatusNew Status { get; set; }
    // 其他属性
}

public enum OrderStatusNew
{
    Pending,
    Completed,
    // 其他状态
}
