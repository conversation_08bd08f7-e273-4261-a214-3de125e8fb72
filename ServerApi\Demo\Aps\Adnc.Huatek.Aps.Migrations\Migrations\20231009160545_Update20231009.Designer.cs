﻿// <auto-generated />
using System;
using Adnc.Infra.Repository.EfCore.MySql;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Adnc.Huatek.Aps.Migrations.Migrations
{
    [DbContext(typeof(MySqlDbContext))]
    [Migration("20231009160545_Update20231009")]
    partial class Update20231009
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            MySqlModelBuilderExtensions.HasCharSet(modelBuilder, "utf8mb4 ");

            modelBuilder.Entity("Adnc.Huatek.Aps.Domain.Aggregates.SysTestAggregate.SysTest", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint")
                        .HasColumnName("id")
                        .HasColumnOrder(1)
                        .HasComment("");

                    b.Property<long>("CreateBy")
                        .HasColumnType("bigint")
                        .HasColumnName("createby")
                        .HasComment("");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("createtime")
                        .HasComment("");

                    b.Property<string>("HtDescription")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("htdescription")
                        .HasComment("");

                    b.Property<string>("HtName")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ht_name")
                        .HasComment("");

                    b.Property<DateTime>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)")
                        .HasColumnName("rowversion")
                        .HasComment("");

                    b.HasKey("Id")
                        .HasName("pk_sys_test");

                    b.ToTable("sys_test", (string)null);

                    b.HasComment("");
                });

            modelBuilder.Entity("Adnc.Shared.Repository.EfEntities.EventTracker", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint")
                        .HasColumnName("id")
                        .HasColumnOrder(1)
                        .HasComment("");

                    b.Property<long>("CreateBy")
                        .HasColumnType("bigint")
                        .HasColumnName("createby")
                        .HasComment("创建人");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("createtime")
                        .HasComment("创建时间/注册时间");

                    b.Property<long>("EventId")
                        .HasColumnType("bigint")
                        .HasColumnName("eventid")
                        .HasComment("");

                    b.Property<string>("TrackerName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("trackername")
                        .HasComment("");

                    b.HasKey("Id")
                        .HasName("pk_eventtracker");

                    b.HasIndex(new[] { "EventId", "TrackerName" }, "uk_eventid_trackername")
                        .IsUnique()
                        .HasDatabaseName("ix_eventtracker_eventid_trackername");

                    b.ToTable("eventtracker", (string)null);

                    b.HasComment("事件跟踪/处理信息");
                });
#pragma warning restore 612, 618
        }
    }
}
