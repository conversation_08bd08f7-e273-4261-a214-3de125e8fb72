﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasProductStepRelationDeviceAggregate;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class BasProductStepRelationSourceConfig : AbstractEntityTypeConfiguration<BasProductStepRelationSource>
    {
        public override void Configure(EntityTypeBuilder<BasProductStepRelationSource> builder)
        {
            base.Configure(builder);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.MainId).HasColumnName("mainid");
            builder.Property(x => x.DeviceType).HasColumnName("devicetype");
            builder.Property(x => x.Capacity).HasColumnName("capacity");
            builder.Property(x => x.SourceType).HasColumnName("sourcetype");
            builder.Property(x => x.MainSource).HasColumnName("mainsource");
            builder.Property(x => x.IsCapacity).HasColumnName("iscapacity");
            builder.Property(x => x.Tat).HasColumnName("tat");

            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.CreateBy).HasColumnName("createby");
            builder.Property(x => x.CreateTime).HasColumnName("createtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
        }
    }
}
