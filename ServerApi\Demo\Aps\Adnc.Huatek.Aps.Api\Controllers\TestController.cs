﻿using Adnc.Demo.Shared.Const;
using Adnc.Huatek.Aps.Api.ApsAlgorithmHelper;
using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Application.Services;
using Adnc.Huatek.Aps.Application.Services.Implements;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Adnc.Huatek.Aps.Api.Controllers
{
    [Route($"{RouteConsts.ApsRoot}/tests")]
    [ApiController]
    public class TestController : AdncControllerBase
    {
        private readonly ITestAppService _testSrv;

        private readonly ISysUserService _sysUserSrv;

        public TestController(ITestAppService testSrv
            , ISysUserService sysUserSrv)
        {                                                       
         _testSrv = testSrv;
         _sysUserSrv= sysUserSrv;
        }


        /// <summary>
        /// 获取所有信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("GetAllAsync")]
        public async Task<ActionResult<List<SysTestDto>>> GetAllAsync() 
        {
            //    // 模拟数据
            //    List<Process> processes = new List<Process>
            //{
            //    new Process { Id = 1, WorkHours = 2, OutputQuantity = 5, PostProcessDemand = 4 },
            //    new Process { Id = 2, WorkHours = 1, OutputQuantity = 1, PostProcessDemand = 1 },
            //    new Process { Id = 3, WorkHours = 5, OutputQuantity = 1, PostProcessDemand = 0 }
            //};

            //    // 创建工序字典，用于快速查找工序
            //    Dictionary<int, Process> processDict = new Dictionary<int, Process>();
            //    foreach (Process process in processes)
            //    {
            //        processDict.Add(process.Id, process);
            //    }

            //    // 创建后继工序字典，用于存储每个工序的后继工序列表
            //    Dictionary<int, List<int>> successors = new Dictionary<int, List<int>>();
            //    foreach (Process process in processes)
            //    {
            //        if (!successors.ContainsKey(process.Id))
            //        {
            //            successors.Add(process.Id, new List<int>());
            //        }

            //        if (process.PostProcessDemand != 0)
            //        {
            //            successors[process.Id].Add(process.PostProcessDemand);
            //        }
            //    }

            //    // 执行拓扑排序
            //    List<int> sortedProcesses = TopologicalSort(successors);

            //    // 初始化工序和半成品
            //    DateTime startTime = DateTime.Now; // 给定的开始时间
            //    foreach (int processId in sortedProcesses)
            //    {
            //        Process process = processDict[processId];
            //        process.StartTime = startTime; // 将给定的开始时间赋值给起始工序的StartTime属性
            //        process.EndTime = startTime.AddHours(process.WorkHours); // 根据工序的工时设置结束时间

            //        // 输出工序的信息
            //        Console.WriteLine("工序 {0} 的开始时间：{1}，结束时间：{2}", process.Id, process.StartTime, process.EndTime);

            //        // 判断是否完成一辆车辆的生产
            //        if (process.Id == processes[processes.Count - 1].Id) // 假设工序表中的最后一个工序是结束工序
            //        {
            //            Console.WriteLine("完成一辆车辆的生产");
            //        }

            //    }

            // 创建作业列表
            //    List<Job> jobs = new List<Job>
            //{
            //    new Job { Id = 1, ProcessingTime = 2, Output = 400, Demand = 4, PreviousJobIds = new List<int>(), NextJobIds = new List<int> { 3 } },
            //    new Job { Id = 2, ProcessingTime = 1, Output = 100, Demand = 1, PreviousJobIds = new List<int>(), NextJobIds = new List<int> { 3 } },
            //    new Job { Id = 3, ProcessingTime = 5, Output = 100, Demand = 0, PreviousJobIds = new List<int> { 1, 2 }, NextJobIds = new List<int>() }
            //};

            //    // 创建调度器实例
            //    Scheduler scheduler = new Scheduler();

            //    // 计算总需求量
            //    int totalDemand = jobs[jobs.Count - 1].Output;

            //    // 指定开始日期
            //    DateTime startDate = new DateTime(2022, 1, 1);

            //    // 进行排程
            //    List<Job> scheduledJobs = scheduler.Schedule(jobs, totalDemand, startDate);

            //    if (scheduledJobs != null)
            //    {
            //        // 输出排程结果
            //        foreach (var job in scheduledJobs)
            //        {
            //            Console.WriteLine($"Job {job.Id}: Start Date = {job.StartDate}: End Date = {job.EndDate}");
            //        }
            //    }

            await _sysUserSrv.GetAllUser();
            return  await _testSrv.GetAllAsync();
        }

        // 拓扑排序算法
        static List<int> TopologicalSort(Dictionary<int, List<int>> successors)
        {
            List<int> sortedList = new List<int>();
            HashSet<int> visited = new HashSet<int>();

            foreach (int node in successors.Keys)
            {
                if (!visited.Contains(node))
                {
                    TopologicalSortUtil(node, successors, visited, sortedList);
                }
            }

            sortedList.Reverse();
            return sortedList;
        }

        static void TopologicalSortUtil(int node, Dictionary<int, List<int>> successors, HashSet<int> visited, List<int> sortedList)
        {
            visited.Add(node);

            if (successors.ContainsKey(node))
            {
                foreach (int successor in successors[node])
                {
                    if (!visited.Contains(successor))
                    {
                        TopologicalSortUtil(successor, successors, visited, sortedList);
                    }
                }
            }

            sortedList.Add(node);
        }
    
    [AllowAnonymous]
        [HttpGet("getTotalTime")]
        public async Task<ActionResult<List<SysTestDto>>> getTotalTime() 
        {
            Dictionary<char, List<char>> dependencies = new Dictionary<char, List<char>>();
            dependencies.Add('A', new List<char> { 'B', 'C' }); // A工序依赖于B和C工序
            dependencies.Add('B', new List<char> { 'D' }); // B工序依赖于D工序
            dependencies.Add('C', new List<char> { 'D', 'E' }); // C工序依赖于D和E工序
            dependencies.Add('D', new List<char> { 'F' }); // D工序依赖于F工序
            dependencies.Add('E', new List<char> { 'F' }); // E工序依赖于F工序
            dependencies.Add('F', new List<char>()); // F工序没有依赖

            Dictionary<char, int> timeMap = new Dictionary<char, int>();
            timeMap.Add('A', 2); // A工序的基础生产时间
            timeMap.Add('B', 3); // B工序的基础生产时间
            timeMap.Add('C', 4); // C工序的基础生产时间
            timeMap.Add('D', 5); // D工序的基础生产时间
            timeMap.Add('E', 6); // E工序的基础生产时间
            timeMap.Add('F', 7); // F工序的基础生产时间

            int orderQuantity = 10; // 订单数量

            int totalTime = OrderSigleProcessing.CalculateTotalTime(dependencies, timeMap, orderQuantity);

            Console.WriteLine("总时间：" + totalTime + "小时");

            return await _testSrv.GetAllAsync();
        }
    }
}
