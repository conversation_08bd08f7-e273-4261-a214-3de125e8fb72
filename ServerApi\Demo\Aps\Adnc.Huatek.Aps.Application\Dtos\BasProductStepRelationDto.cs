﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Dtos
{
    public class BasProductStepRelationDto : IDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 产品编码
        /// </summary>
        public string? ProCode { get; set; }

        /// <summary>
        /// 工艺编码
        /// </summary>
        public string? TecCode { get; set; }

        /// <summary>
        /// 工序编码
        /// </summary>
        public string? StepCode { get; set; }
        /// <summary>
        /// 工序名称
        /// </summary>
        public string? StepName { get; set; }
        /// <summary>
        /// 排序
        /// </summary>
        public int? Sort { get; set; }

        /// <summary>
        /// 前置工序
        /// </summary>
        public string? PreStep { get; set; }
        /// <summary>
        /// 前置工序名称
        /// </summary>
        public string? PreStepName { get; set; }
        /// <summary>
        /// 前置工序间隔时间
        /// </summary>
        public decimal? IntervalTime { get; set; }

        /// <summary>
        /// 单位工作时长
        /// </summary>
        public decimal? StepTime { get; set; }

        /// <summary>
        /// 单位工作时长
        /// </summary>
        public decimal? Tat { get; set; }
        /// <summary>
        /// 是否关键工序
        /// </summary>
        public int? IsKey { get; set; }
        /// <summary>
        /// 创建人ID
        /// </summary>
        public long CreateBy { get; set; }

        /// <summary>
        /// 创建时间/注册时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 最后更新人
        /// </summary>
        public long? ModifyBy { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }
        /// <summary>
        /// 输入物料
        /// </summary>
        private List<BasProductStepRelationMaterialDto> _materialsIn;

        public List<BasProductStepRelationMaterialDto> MaterialsIn
        {
            get
            {
                if (_materialsIn == null)
                    _materialsIn = new List<BasProductStepRelationMaterialDto>();
                return _materialsIn;
            }
            set
            {
                _materialsIn = value;
            }
        }
        /// <summary>
        /// 输出物料
        /// </summary>
        private List<BasProductStepRelationMaterialDto> _materialsOut;

        public List<BasProductStepRelationMaterialDto> MaterialsOut
        {
            get
            {
                if (_materialsOut == null)
                    _materialsOut = new List<BasProductStepRelationMaterialDto>();
                return _materialsOut;
            }
            set
            {
                _materialsOut = value;
            }
        }

        /// <summary>
        /// 设备
        /// </summary>
        private List<BasProductStepRelationSourceDto> _mainItems;

        public List<BasProductStepRelationSourceDto> MainItems
        {
            get
            {
                if (_mainItems == null)
                    _mainItems = new List<BasProductStepRelationSourceDto>();
                return _mainItems;
            }
            set
            {
                _mainItems = value;
            }
        }

        /// <summary>
        /// 设备
        /// </summary>
        private List<BasProductStepRelationSourceDto> _assistItems;

        public List<BasProductStepRelationSourceDto> AssistItems
        {
            get
            {
                if (_assistItems == null)
                    _assistItems = new List<BasProductStepRelationSourceDto>();
                return _assistItems;
            }
            set
            {
                _assistItems = value;
            }
        }

    }
}
