﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasBomAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasDevice;
using Adnc.Huatek.Aps.Domain.Aggregates.BasDeviceClassAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStation;
using Adnc.Huatek.Aps.Domain.Aggregates.PartDeviceStateAggregates;
using Adnc.Huatek.Aps.Domain.Aggregates.StockMaterial;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Services
{
    public class BasDeviceClassManager : IDomainService
    {
        private readonly IEfBasicRepository<BasDeviceClass> _devClassRepo;

        public BasDeviceClassManager(IEfBasicRepository<BasDeviceClass> devClassRepo)
        {
            _devClassRepo = devClassRepo;
        }

        public virtual async Task<BasDeviceClass> CreateAsync(string DevCode, string ClassCode,  DateTime WorkingDate)
        {
            var exists = await _devClassRepo.AnyAsync(x => x.DevCode == DevCode && x.WorkingDate == WorkingDate && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"设备编码:{DevCode}在{WorkingDate}已维护班次，不可重复添加！");
            return new BasDeviceClass()
            {
                Id = IdGenerater.GetNextId(),
                DevCode = DevCode,
                ClassCode = ClassCode,
                WorkingDate = WorkingDate
            };
        }

        public virtual async Task<bool> UpdateAsync(string DevCode, string ClassCode, DateTime WorkingDate, long id)
        {
            var exists = await _devClassRepo.AnyAsync(x => x.DevCode == DevCode && x.WorkingDate == WorkingDate && x.Id != id && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"设备编码:{DevCode}在{WorkingDate}已维护班次，不可重复添加！");

            return true;
        }
         
    }
}
