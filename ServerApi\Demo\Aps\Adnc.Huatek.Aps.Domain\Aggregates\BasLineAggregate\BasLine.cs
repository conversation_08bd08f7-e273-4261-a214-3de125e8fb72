﻿using Adnc.Infra.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Aggregates.BasLineAggregate
{
    public class BasLine : EfFullAuditEntity
    {
        /// <summary>
        /// ID
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 产线名称
        /// </summary>
        public string? Lname { get; set; }

        /// <summary>
        /// 产线编码
        /// </summary>
        public string? Lcode { get; set; }

        /// <summary>
        /// 车间id
        /// </summary>
        public long? IdWorkshop { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int? Status { get; set; }





    }

   
}
