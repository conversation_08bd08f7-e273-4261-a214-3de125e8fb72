﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Dtos
{


    public class ReloadBasProcuct 
    {

        /// <summary>
        /// 计划编码
        /// </summary>
        public string? PlanCode { get; set; }

        /// <summary>
        /// 排产编码
        /// </summary>
        public string? SchedulCode { get; set; }



        public List<BasProductStandardCapacityDto>? lists { get; set; }
    }

    public class BasProductStandardCapacityDto : IDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 启用状态
        /// </summary>
        public int? Status { get; set; }

        /// <summary>
        /// 产能方案
        /// </summary>
        public string? SolutionName { get; set; }

        /// <summary>
        /// 产品编码
        /// </summary>
        public string? ProCode { get; set; }

        /// <summary>
        /// 工序编码
        /// </summary>
        public string? StepCode { get; set; }

        /// <summary>
        /// 工序名称
        /// </summary>
        public string? StepName { get; set; }

        /// <summary>
        /// 工位编码
        /// </summary>
        public string? StationCode { get; set; }
        /// <summary>
        /// 工位名称
        /// </summary>
        public string? StationName { get; set; }

        /// <summary>
        /// 产线编码
        /// </summary>
        public string? LineCode { get; set; }
        /// <summary>
        /// 产线名称
        /// </summary>
        public string? LineName { get; set; }

        /// <summary>
        /// 设备编码
        /// </summary>
        public string? DeviceCode { get; set; }
        /// <summary>
        /// 设备名称
        /// </summary>
        public string? DeviceName { get; set; }

        /// <summary>
        /// 产能
        /// </summary>
        public decimal? Capacity { get; set; }

        /// <summary>
        /// 单位工作时长
        /// </summary>
        public decimal? WorkUnitTime { get; set; }

        /// <summary>
        /// 标准工作时长
        /// </summary>
        public decimal? StandardWorkTime { get; set; }

        /// <summary>
        /// 工序最短工作时长
        /// </summary>
        public decimal? MinWorkingDuration { get; set; }

        /// <summary>
        /// 创建人ID
        /// </summary>
        public long? CreateBy { get; set; }

        /// <summary>
        /// 创建时间/注册时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 最后更新人
        /// </summary>
        public long? ModifyBy { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int? Sort { internal get; set; }

    }

    public class SchedulStatus
    {

        /// <summary>
        /// 排产状态
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 排产编码
        /// </summary>
        public List<string>? SchedulCode { get; set; }


    }

    public class SchedulStatusNew
    {

        /// <summary>
        /// 排产状态
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 排产编码
        /// </summary>
        public string SchedulCode { get; set; }


    }
}
