﻿using Adnc.Infra.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Aggregates.SysUserAggregate
{
    public class SysUser : EfFullAuditEntity
    {
        public string Account { get; set; }
        public string Avatar { get; set; }
        public DateTime? Birthday { get; set; }
        public long? Deptid { get; set; }
        public string Email { get; set; }
        public string Name { get; set; }
        public string Password { get; set; }
        public string Phone { get; set; }
        public string Roleids { get; set; }
        public string Salt { get; set; }
        public int? Sex { get; set; }

        public int? Status { get; set; }
        public long Id { get; set; }

    }
}
