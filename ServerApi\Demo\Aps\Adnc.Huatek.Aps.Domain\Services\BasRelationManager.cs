﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasDevice;
using Adnc.Huatek.Aps.Domain.Aggregates.BasRelation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Services
{
    
    public class BasRelationManager : IDomainService
    {
        private readonly IEfBasicRepository<BasRelation> _relationRepo;

        public BasRelationManager(IEfBasicRepository<BasRelation> relationRepo)
        {
            _relationRepo = relationRepo;
        }


        public virtual async Task<BasRelation> CreateAsync(long? idstep,string?stepCode,string? Remark)
        {
            var exists = await _relationRepo.AnyAsync(x => x.IdStep == idstep && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"工序{stepCode}的资源已维护，不可重复添加！");

            return new BasRelation()
            {
                Id = IdGenerater.GetNextId(),
                IdStep = idstep,
                Remark = Remark
            };
        }

        public virtual async Task<bool> UpdateAsync(long? idstep, string? stepCode, long id)
        {
            var exists = await _relationRepo.AnyAsync(x => x.IdStep == idstep && x.Id != id && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"工序{stepCode}的资源已维护，不可重复添加！");

            return true;
        }
    }
}
