﻿<Project Sdk="Microsoft.NET.Sdk">
	<Import Project="..\..\..\common.props" />
	<Import Project="..\..\..\version_service.props" />
  <PropertyGroup>
	  <GenerateDocumentationFile>True</GenerateDocumentationFile>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="EPPlus" Version="7.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\ServiceShared\Adnc.Shared.Application\Adnc.Shared.Application.csproj" />
    <ProjectReference Include="..\..\Shared\Adnc.Demo.Shared.Rpc.Grpc\Adnc.Demo.Shared.Rpc.Grpc.csproj" />
    <ProjectReference Include="..\..\Shared\Adnc.Demo.Shared.Rpc.Http\Adnc.Demo.Shared.Rpc.Http.csproj" />
    <ProjectReference Include="..\Adnc.Huatek.Aps.Domain\Adnc.Huatek.Aps.Domain.csproj" />
  </ItemGroup>

</Project>
