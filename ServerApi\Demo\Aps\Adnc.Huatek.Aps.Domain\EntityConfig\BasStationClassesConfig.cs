﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasClassesAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class BasStationClassesConfig : AbstractEntityTypeConfiguration<BasStationClasses>
    {
        public override void Configure(EntityTypeBuilder<BasStationClasses> builder)
        {
            base.Configure(builder);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.StationId).HasColumnName("stationId");
            builder.Property(x => x.ClassesId).HasColumnName("classesId");
            builder.Property(x => x.Duration).HasColumnName("duration");
            builder.Property(x => x.Qty).HasColumnName("qty");
            builder.Property(x => x.Status).HasColumnName("status");
        }
    }
}
