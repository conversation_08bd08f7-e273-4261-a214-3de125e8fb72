﻿namespace Adnc.Huatek.Aps.Application.Dtos
{
    /// <summary>
    /// 生产计划
    /// </summary>
    public class PartSchedulePlanSourceDto : Dto
    {
        /// <summary>
        /// 
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 排产计划编码
        /// </summary>
        public string? ScheduleCode { get; set; }

        /// <summary>
        /// 工序编码
        /// </summary>
        public string? StepCode { get; set; }

        /// <summary>
        /// 资源大类
        /// </summary>
        public string? SourceType { get; set; }

        /// <summary>
        /// 资源大类名称
        /// </summary>
        public string? SourceTypeName { get; set; }

        /// <summary>
        /// 资源类型
        /// </summary>
        public string? DevType { get; set; }

        /// <summary>
        /// 资源类型
        /// </summary>
        public string? DevTypeName { get; set; }

        /// <summary>
        /// 资源编码
        /// </summary>
        public string? SourceCode { get; set; }

        /// <summary>
        /// 资源编码
        /// </summary>
        public string? SourceName { get; set; }

        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime BeginDate { get; set; }

        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// 总工作时长工时
        /// </summary>
        public decimal Tat { get; set; }


        /// <summary>
        /// 单位工作时长
        /// </summary>
        public decimal UnitTat { get; set; }

        /// <summary>
        /// 单位产能
        /// </summary>
        public decimal UnitCapacity { get; set; }


        /// <summary>
        /// 总产能
        /// </summary>
        public decimal TotalCapacity { get; set; }

        public int? Status { get; set; }


        public bool _isEdit;

        /// <summary>
        /// 是否人为调整
        /// </summary>
        public bool IsEdit
        {
            get
            {
                if (_isEdit == null)
                    _isEdit = false;
                return _isEdit;
            }
            set
            {
                value = _isEdit;
            }
        }
    }

    public class DevicePlanDto : Dto
    {
        public string? ScheduleCode  { get; set; }
        public string? StepCode { get; set; }
        public string? DevCode { get; set; }

        public DateTime BeginDate { get; set; }

        public DateTime EndDate { get; set; }

        public decimal Tat { get; set; }

        public int  Status { get; set; }


        public string  Notes { get; set; }

    }


    public class DeviceGanttDto
    {
        public string? DevCode { get; set; }

        public string? DevName { get; set; }

        public List<DevicePlanDto> Shifts { get; set; }
    }


    public class EnumDto
    {
        public string Label { get; set; }

        public dynamic Value { get; set; }
    }

    public class PartSchedulPlanSourcePageDto : SearchPagedDto
    {

        /// <summary>
        /// 设备编码
        /// </summary>
        public string? DevCode { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        public string? DevName { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        public string? Dtype { get; set; }

        /// 设备名称
        /// </summary>
        public DateTime BeginDate { get; set; }


        /// 设备名称
        /// </summary>
        public DateTime EndDate { get; set; }
    }

}
