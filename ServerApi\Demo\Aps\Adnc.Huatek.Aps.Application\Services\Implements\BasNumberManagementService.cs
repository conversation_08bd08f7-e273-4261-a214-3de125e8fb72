﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.BasBomAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasBomListAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasNumberManagementAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.SysUserAggregate;
using Adnc.Huatek.Aps.Domain.Services;
using Adnc.Infra.Helper;
using Adnc.Infra.IdGenerater.Yitter;
using Adnc.Shared.Application.Contracts.Dtos;
using Adnc.Shared.Application.Contracts.ResultModels;
using Microsoft.AspNetCore.Mvc;
using MongoDB.Driver.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection.Metadata;
using System.Text;
using System.Threading.Tasks;
using Z.EntityFramework.Plus;

namespace Adnc.Huatek.Aps.Application.Services.Implements
{
    public class BasNumberManagementService : AbstractAppService, IBasNumberManagementService
    {
        private readonly BasNumberMgManagement _basNumberMgBomMgr;
        private readonly IEfBasicRepository<BasNumberManagement> _basNumberManagementRepo;

        private readonly IEfBasicRepository<SysUser> _sysUserRepo;

        public BasNumberManagementService(IEfBasicRepository<BasNumberManagement> basNumberManagementRepo
            , IEfBasicRepository<SysUser> sysUserRepo,
            BasNumberMgManagement basNumberMgBomMgr)
        {
            _basNumberManagementRepo = basNumberManagementRepo;
            _sysUserRepo = sysUserRepo;
            _basNumberMgBomMgr = basNumberMgBomMgr;
        }

        public async Task<ActionResult<PageModelDto<BasNumberManagementDto>>> GetPagedAsync(BasNumberManagementPagedDto search)
        {
            search.TrimStringFields();
            var whereExpression = ExpressionCreator
                                                .New<BasNumberManagement>()
                                                .And(x => !x.IsDeleted)
                                                .AndIf(!string.IsNullOrEmpty(search.Name), x => EF.Functions.Like(x.Name!, $"%{search.Name}%"))
                                                .AndIf(!string.IsNullOrEmpty(search.SimpleName), x => EF.Functions.Like(x.SimpleName!, $"%{search.SimpleName}%"))
                                                ;

            var total = await _basNumberManagementRepo.CountAsync(whereExpression);
            if (total == 0)
                return new PageModelDto<BasNumberManagementDto>(search);
            var entities = _basNumberManagementRepo.Where(whereExpression)
                                   .OrderByDescending(x => x.CreateTime)
                                   .Skip(search.SkipRows())
                                   .Take(search.PageSize);
            var sysUsers = _sysUserRepo.Where(x => true);

            var basNumberManagementDtos = (from i in entities
                                           join j in sysUsers on i.CreateBy equals j.Id into sp
                                           from x in sp.DefaultIfEmpty()
                                           select new BasNumberManagementDto
                                           {
                                               CreateName = x.Name,
                                               IsRound = i.IsRound,
                                               Name = i.Name,
                                               Id = i.Id,
                                               CreateBy = i.CreateBy,
                                               CurrentNumber = i.CurrentNumber,
                                               CreateTime = i.CreateTime,
                                               Description = i.Description,
                                               IdentityNumber = i.IdentityNumber,
                                               IsContainerDate = i.IsContainerDate,
                                               IsDeleted = i.IsDeleted,
                                               NumberFormat = i.NumberFormat,
                                               ModifyBy = i.ModifyBy,
                                               ModifyTime = i.ModifyTime,
                                               SimpleName = i.SimpleName,
                                               StartNumber = i.StartNumber,
                                               StartTarget = i.StartTarget,
                                               TimeFormat = i.TimeFormat
                                           }).ToList();

            return new PageModelDto<BasNumberManagementDto>(search, basNumberManagementDtos, total);

        }


        public async Task<AppSrvResult<long>> CreateAsync(BasNumberManagementDto input)
        {
            input.TrimStringFields();
            input.IsDeleted = false;
            input.SimpleName = HanziConvertPinyin.HZToPYSimple(input.Name);
            var basNumberManagement = _basNumberMgBomMgr.CreateAsync(input.Name,
                input.SimpleName,
                input.NumberFormat,
                input.StartTarget,
                input.CurrentNumber,
                input.Description,
                input.IsRound,
                input.IdentityNumber,
                input.IsContainerDate,
                input.StartNumber,
                input.TimeFormat);
            await _basNumberManagementRepo.InsertAsync(basNumberManagement);
            return AppSrvResult();

        }

        public async Task<AppSrvResult> DeleteAsync(long id)
        {
            var basNumberManagement =await _basNumberManagementRepo.GetAsync(id);
            basNumberManagement.IsDeleted = true;
            await _basNumberManagementRepo.UpdateAsync(basNumberManagement);

            return AppSrvResult();
        }
        public async Task<ActionResult<BasNumberManagement>> GetByIdAsync(long id)
        {
           return await _basNumberManagementRepo.GetAsync(id);

        }
        public async Task<AppSrvResult> UpdateAsync(BasNumberManagementDto input)
        {
            var basNumberManagement = await _basNumberManagementRepo.GetAsync(input.Id);
            input.SimpleName = HanziConvertPinyin.HZToPYSimple(input.Name);
            if (basNumberManagement != null)
            {
                basNumberManagement.Id = input.Id;
                basNumberManagement.Name = input.Name;
                basNumberManagement.SimpleName = input.SimpleName;
                basNumberManagement.StartNumber = input.StartNumber;
                basNumberManagement.CurrentNumber = input.CurrentNumber;
                basNumberManagement.IdentityNumber = input.IdentityNumber;
                basNumberManagement.IsContainerDate = input.IsContainerDate;
                basNumberManagement.TimeFormat = input.TimeFormat;
                basNumberManagement.Description = input.Description;
                basNumberManagement.NumberFormat = input.NumberFormat;
                basNumberManagement.StartTarget = input.StartTarget;
                await _basNumberManagementRepo.UpdateAsync(basNumberManagement);
            }

            return AppSrvResult();
        }


        public async Task<string> GetNumberBySimpleName(string simplename) 
        {
            string currNumber = "";
            var model = await _basNumberManagementRepo.Where(o=>o.SimpleName ==simplename,true,false).FirstOrDefaultAsync();
            if (model != null)
            {
                if (!string.IsNullOrWhiteSpace(model.StartTarget))
                {
                    currNumber = model.StartTarget.ToUpper();
                }
                if (model.IsContainerDate == "1")
                {
                    currNumber += DateTime.Now.ToString($"{model.TimeFormat}");
                    // Math.
                }
                if (model.IsRound == "1")
                {
                    Random rd = new Random();
                    double db = Math.Pow(10, 3);
                    int num = rd.Next(1, Convert.ToInt32(db) - 1);
                    currNumber += num.ToString().PadLeft(Convert.ToInt32(model.NumberFormat), '0');
                    model.CurrentNumber = num.ToString();
                }
                else
                {
                    currNumber += (long.Parse(model.CurrentNumber!) + long.Parse(model.IdentityNumber!)).ToString().PadLeft(Convert.ToInt32(model.NumberFormat), '0');
                    model.CurrentNumber = (long.Parse(model.CurrentNumber!) + +long.Parse(model.IdentityNumber!)).ToString();
                }
                await _basNumberManagementRepo.UpdateAsync(model);
            }
        
            return currNumber;
        }
    }
}
