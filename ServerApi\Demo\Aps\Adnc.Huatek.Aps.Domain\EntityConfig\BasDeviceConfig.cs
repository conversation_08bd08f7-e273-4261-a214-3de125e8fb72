﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasDevice;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class BasDeviceConfig : AbstractEntityTypeConfiguration<BasDevice>
    {

        public override void Configure(EntityTypeBuilder<BasDevice> builder)
        {
            base.Configure(builder);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.Status).HasColumnName("status");
            builder.Property(x => x.Code).HasColumnName("code");
            builder.Property(x => x.Name).HasColumnName("name");
            builder.Property(x => x.Remark).HasColumnName("Remark");
            builder.Property(x => x.CreateBy).HasColumnName("createdby");
            builder.Property(x => x.CreateTime).HasColumnName("createdtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
            builder.Property(x => x.ModifyName).HasColumnName("modifyname");
            builder.Property(x => x.CreatedName).HasColumnName("createdname");
            //builder.Property(x => x.Stationid).HasColumnName("stationid");
            //builder.Property(x => x.StationName).HasColumnName("stationname");
            builder.Property(x => x.Capacity).HasColumnName("capacity");
            builder.Property(x => x.Dtype).HasColumnName("dtype");
            builder.Property(x => x.Newness).HasColumnName("newness");
        }
    }
}
