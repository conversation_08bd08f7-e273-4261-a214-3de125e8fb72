﻿using Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulAggregate;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class PlanProductionSchedulConfig : AbstractEntityTypeConfiguration<PlanProductionSchedule>
    {
        public override void Configure(EntityTypeBuilder<PlanProductionSchedule> builder)
        {
            base.Configure(builder);
            builder.<PERSON>ey(x => x.Id);
            builder.Property(x => x.SchedulCode).HasColumnName("schedulcode");
            builder.Property(x => x.PlanCode).HasColumnName("plancode");
            builder.Property(x => x.PlanStart).HasColumnName("planstart");
            builder.Property(x => x.PlanEnd).HasColumnName("planend");
            builder.Property(x => x.Status).HasColumnName("status");
            builder.Property(x => x.Sorts).HasColumnName("sorts");
            builder.Property(x => x.PreRule).HasColumnName("prerule");
            builder.Property(x => x.LCode).HasColumnName("lcode");
            builder.Property(x => x.Status).HasColumnName("status");
            builder.Property(x => x.Remark).HasColumnName("remark");
            builder.Property(x => x.PlanType).HasColumnName("plantype");
            builder.Property(x => x.SchedulTime).HasColumnName("schedultime");

            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.CreateBy).HasColumnName("createby");
            builder.Property(x => x.CreateTime).HasColumnName("createtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
        }
    }
}