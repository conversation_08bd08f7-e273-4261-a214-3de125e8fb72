﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.BasDevice;
using Adnc.Huatek.Aps.Domain.Aggregates.BasDevicePreserveAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.PartDeviceStateAggregates;
using Adnc.Huatek.Aps.Domain.Aggregates.BasProductStandardCapacityAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.BasStation;
using Adnc.Huatek.Aps.Domain.Aggregates.PlanProductionSchedulAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.SourceSchedulDeviceStationAggregate;
using Adnc.Huatek.Aps.Domain.Aggregates.SysUserAggregate;
using Adnc.Huatek.Aps.Domain.Services;
using Adnc.Infra.IdGenerater.Yitter;
using Adnc.Shared;
using Adnc.Shared.Application.Contracts.ResultModels;
using Microsoft.AspNetCore.Mvc;
using System.Net;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.ViewComponents;
using Adnc.Infra.Core.Exceptions;

namespace Adnc.Huatek.Aps.Application.Services.Implements
{

    public class BasDeviceService : AbstractAppService, IBasDeviceService
    {
        private readonly BasDeviceManager _stationMgr;
        private readonly IEfBasicRepository<BasDevice> _stationRepo;
        private readonly IEfBasicRepository<SysUser> _sysUserRepo;
        private readonly IEfBasicRepository<BasStation> _basStationRepo;
        private readonly IEfBasicRepository<BasDevicePreserve> _basDevicePreserveRepo;
        private readonly IEfBasicRepository<SourceSchedulDeviceStation> _sourceSchedulDeviceStationRepo;
        private readonly IEfBasicRepository<PartPlanPending> _basplanPendingRepo;
        private readonly IEfBasicRepository<BasProductStandardCapacity> _basProductStandardCapacityRepo;
        private readonly IEfBasicRepository<PlanProductionSchedule> _planProductionScheduleRepo;
        private readonly IEfBasicRepository<PartDeviceState> _partDevRepo;
        private readonly BasNumberManagementService _basNumberManagementService;
        public IMaintRestClient _maintRestClient;

        public BasDeviceService(
            IEfBasicRepository<BasStation> basStationRepo,
            IEfBasicRepository<BasDevicePreserve> basDevicePreserveRepo,
            IEfBasicRepository<SourceSchedulDeviceStation> sourceSchedulDeviceStationRepo,
            IEfBasicRepository<BasDevice> stationRepo,IEfBasicRepository<PartDeviceState> partDevRepo,
        IEfBasicRepository<BasProductStandardCapacity> basProductStandardCapacityRepo,
            IEfBasicRepository<PlanProductionSchedule> planProductionScheduleRepo,
            IEfBasicRepository<PartPlanPending> basplanPendingRepo, 
            BasDeviceManager stationMgr, IEfBasicRepository<SysUser> sysUserRepo,
            BasNumberManagementService basNumberManagementService,
            IMaintRestClient maintRestClient)
        {
            _stationRepo = stationRepo;
            _stationMgr = stationMgr;
            _sysUserRepo = sysUserRepo;
            _basStationRepo = basStationRepo;
            _basProductStandardCapacityRepo = basProductStandardCapacityRepo;
            _planProductionScheduleRepo = planProductionScheduleRepo;
            _sourceSchedulDeviceStationRepo = sourceSchedulDeviceStationRepo;
            _basplanPendingRepo = basplanPendingRepo;
            _basDevicePreserveRepo = basDevicePreserveRepo;
            _basNumberManagementService = basNumberManagementService;
            _maintRestClient = maintRestClient;
            _partDevRepo = partDevRepo;
        }

        /// <summary>
        /// 新增工位
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<AppSrvResult<long>> CreateAsync(BasDeviceDto input)
        {
            var menu = Mapper.Map<BasDevice>(input);
            if (string.IsNullOrWhiteSpace(menu.Code))
                menu.Code = _basNumberManagementService.GetNumberBySimpleName(CommonConst.MATERIALNUMBER).Result;
            var bom = await _stationMgr.CreateAsync(menu.Code, menu.Name, menu.Remark, menu.Capacity ?? 0,  menu.Status??0, menu.Dtype??"0", menu.Newness??0);
            bom.Id = IdGenerater.GetNextId();
            await _stationRepo.InsertAsync(bom);
            return bom.Id;
        }

        /// <summary>
        /// 删除工位
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<AppSrvResult> DeleteAsync(long id)
        {
            var userProfile = await _stationRepo.GetAsync(id);
            if (userProfile != null)
            {
                //var o = await _basStationRepo.Where(x => !x.IsDeleted && x.DeviceCode == userProfile.Code).ToListAsync();
                //if (o.Any())
                //{
                //    return Problem(HttpStatusCode.BadRequest, "该设备所属工位有效，不可以删除！");
                //}
                var menu = Mapper.Map<BasDevice>(userProfile);
                menu.IsDeleted = true;
                await _stationRepo.UpdateAsync(menu);
            }
            return AppSrvResult();
        }

        /// <summary>
        /// 获取所有工位
        /// </summary>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<List<BasDeviceDto>> GetAllAsync()
        {
            var sysTests = await _stationRepo.Where(x => !x.IsDeleted && x.Status == 1).ToListAsync();
            var sysTestsDto = Mapper.Map<List<BasDeviceDto>>(sysTests);
            return sysTestsDto;
        }

        /// <summary>
        /// 获取指定工位
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<ResultJson> GetAppointAsync(long id)
        {

            var userProfile = await _stationRepo.Where(x => !x.IsDeleted && x.Id == id).FirstOrDefaultAsync();
            if (userProfile == null)
                return new ResultJson("查询成功！无数据", null);
            var menu = Mapper.Map<BasDeviceDto>(userProfile);

            return new ResultJson("查询成功", menu, 0);
        }

        /// <summary>
        /// 修改工位
        /// </summary>
        /// <param name="id"></param>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<AppSrvResult> UpdateAsync(BasDeviceDto input)
        {
            bool isUpdete = await _stationMgr.UpdateAsync(input.Code, input.Id ?? 0);
            if (isUpdete)
            {
                input.TrimStringFields();
                var station = Mapper.Map<BasDevice>(input);
                var userProfile = await _stationRepo.GetAsync(input.Id ?? 0);
                if (userProfile != null)
                {
                    userProfile.Status = station.Status;
                    userProfile.Code = station.Code;
                    userProfile.Name = station.Name;
                    userProfile.Capacity = station.Capacity;
                    userProfile.Remark = station.Remark;
                    userProfile.Dtype = station.Dtype;
                    userProfile.Newness = station.Newness;
                    userProfile.ModifyTime = station.ModifyTime;
                    userProfile.ModifyBy = station.ModifyBy;
                    await _stationRepo.UpdateAsync(userProfile);
                }
            }
            return AppSrvResult();
        }

        public async Task<AppSrvResult> ChangeStatusAsync(long id, int status)
        {
            var userProfile = await _stationRepo.GetAsync(id);
            if (userProfile != null)
            {
                //如果数据已作废是否可以启用
                {

                }
                userProfile.Status = status;
                await _stationRepo.UpdateAsync(userProfile);
            }
            return AppSrvResult();
        }

        public async Task<AppSrvResult> ChangeStatusAsync(UpdateStatusDto input)
        {
            string[] Ids = new string[input.Ids.Length];
            for (int i = 0; i < input.Ids.Length; i++)
            {
                Ids[i] = input.Ids[i].ToString();
            }
            var userProfile = await _stationRepo.Where(x => x.Status != 2 & Ids.Contains(x.Id.ToString())).ToListAsync();
            if (userProfile != null)
            {
                //type==0为删除，type ==1为启用停用
                if (input.Type == 0)
                {
                    foreach (var item in userProfile)
                    {
                        item.IsDeleted = true;
                    }
                }
                else
                {
                    foreach (var item in userProfile)
                    {
                        item.Status = input.Status;
                    }
                }
                await _stationRepo.UpdateRangeAsync(userProfile);
            }
            return AppSrvResult();
        }


        public async Task<PageModelDto<BasDeviceDto>> GetPagedAsync(BasDevicePagedDto search)
        {
            search.TrimStringFields();
            var whereExpression = ExpressionCreator
                                                .New<BasDevice>()
                                                .AndIf(search.Code.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Code!, $"%{search.Code}%"))
                                                .AndIf(search.Name.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Name!, $"%{search.Name}%"))
                                                .AndIf(search.Status != -1, x => x.Status == search.Status)
                                                .AndIf(search.Dtype != "0", x => x.Dtype == search.Dtype)
                                                .And(x => x.IsDeleted == false);


            var sysUsers = _sysUserRepo.Where(o => true);
            var entities = _stationRepo
                                            .Where(whereExpression)
                                            .OrderByDescending(x => x.Id)
                                            .Skip(search.SkipRows())
                                            .Take(search.PageSize);

            var result = (from i in entities
                          join j in sysUsers on i.CreateBy equals j.Id into g
                          from x in g.DefaultIfEmpty()
                          join o in sysUsers on i.ModifyBy equals o.Id into g1
                          from x1 in g1.DefaultIfEmpty()
                          select new BasDeviceDto
                          {
                              CreateTime = i.CreateTime,
                              CreatedName = x.Name,
                              ModifyName = x1.Name,
                              ModifyTime = i.ModifyTime,
                              Capacity = i.Capacity,
                              Code = i.Code,
                              Id = i.Id,
                              Remark = i.Remark,
                              Status = i.Status,
                              Name = i.Name,
                              Dtype = i.Dtype,
                              Newness = i.Newness
                          }).ToList();


            var restRpcResult = await _maintRestClient.GetDictByNameAsync("设备类型");
            if (restRpcResult.IsSuccessStatusCode)
            {
                var unitEnums = restRpcResult.Content;
                if (unitEnums is not null)
                {
                    result.ForEach(x =>
                    {
                        x.DtypeDis = unitEnums.FirstOrDefault(d => d.value == x.Dtype.ToString())?.label;
                    });
                }
            }

            return new PageModelDto<BasDeviceDto>(search, result, result.Count());
        }

        public async Task<List<BasDeviceCalendarResultDto>> GetCalendarAsync(BasDeviceCalendarDto dto)
        {
            dto.TrimStringFields();
            var result = new List<BasDeviceCalendarResultDto>();

            var device = await _stationRepo.Where(x => x.Code == dto.DeviceCode && !x.IsDeleted).FirstOrDefaultAsync();
            if (device != null)
            {
                var capacitys = await _basProductStandardCapacityRepo.Where(x => !x.IsDeleted).ToListAsync();

                var planScheduleSchedule = await _planProductionScheduleRepo.Where(x => !x.IsDeleted && x.Status == SchedulStatusConst.Effect).ToListAsync();
                var planPendingEntities = await _basplanPendingRepo.Where(x => !x.IsDeleted).ToListAsync();

                var workingDevices = await _sourceSchedulDeviceStationRepo.Where(x => !x.IsDeleted && x.DiviceCode == dto.DeviceCode && x.Status == 1).ToListAsync();

                var list = (from a in workingDevices
                            join b in planScheduleSchedule on a.SchedulCode equals b.SchedulCode into t1
                            from x in t1.DefaultIfEmpty()
                            where x != null
                            join c in planPendingEntities on x.PlanCode equals c.PlanCode into t2
                            from xx in t2.DefaultIfEmpty()
                            where xx != null
                            join d in capacitys on new { xx.ProductCode, a.StepCode } equals new { ProductCode = d.ProCode, d.StepCode } into t3
                            from y in t3.DefaultIfEmpty()
                            where a.DiviceCode == dto.DeviceCode && a.UseDate >= dto.BeginTime && a.UseDate <= dto.EndTime
                            orderby a.UseDate
                            select new
                            {
                                xx.ProductCode,
                                a.SchedulCode,
                                y.StepCode,
                                y.StandardWorkTime,
                                a.OrderNumber,
                                xx.ProductName,
                                a.UseDate,
                                a.UseendDate,
                            }).ToList();


                var station = await _basStationRepo.Where(x => x.DeviceCode == dto.DeviceCode && x.IsDeleted == false).FirstOrDefaultAsync();

                var span = dto.EndTime - dto.BeginTime;

                for (var i = 0; i <= span.Days; i++)
                {
                    result.Add(new BasDeviceCalendarResultDto()
                    {
                        DeviceName = device.Name,
                        DeviceCode = dto.DeviceCode,
                        BeginTime = dto.BeginTime.AddDays(i),
                        EndTime = dto.BeginTime.AddDays(i),
                        DeviceWorkStatus = "IDLE",
                        StationName = station?.Name,
                        StationCode = station?.Code,
                    });
                }

                list.GroupBy(x => x.UseDate).ForEach(y =>
                {
                    var item = result.Find(z => z.BeginTime.Date == y.Key?.Date);
                    if (item != null)
                    {
                        item.OrderList = y.Select(x => new { x.OrderNumber, x.ProductName })
                            .GroupBy(xx => xx.OrderNumber)
                            .Select(x => new OrderInfoItem { OrderNumber = x.Key, ProductName = x.First().ProductName })
                            .ToList();
                        item.StandardWorkTime = y.First().StandardWorkTime;
                    }
                });

                //查询设备占用状态
                if (workingDevices.Any())
                {
                    workingDevices.ForEach(x =>
                    {
                        var dev = result.Find(y => y.DeviceCode == x.DiviceCode && y.BeginTime.Date == x.UseDate?.Date);
                        if (dev != null && dev.OrderList != null) { dev.DeviceWorkStatus = "RUNNING"; }
                    });
                }

                //查询设备维保状态
                var users = _sysUserRepo.Where(x => true);
                var depres = _basDevicePreserveRepo.Where(x => x.DevCode == dto.DeviceCode
                        && x.BeginTime >= dto.BeginTime
                        && x.EndTime <= dto.EndTime
                        && x.IsDeleted == false);
                var preservingDevices = await (from p in depres
                                               join u in users on p.CreateBy equals u.Id
                                               select new BasDevicePreserve
                                               {
                                                   Id = p.Id,
                                                   DevCode = p.DevCode,
                                                   DevName = p.DevName,
                                                   IsDeleted = p.IsDeleted,
                                                   Status = p.Status,
                                                   BeginTime = p.BeginTime,
                                                   EndTime = p.EndTime,
                                                   CreateBy = p.CreateBy,
                                                   CreatedName = u.Name,
                                                   CreateTime = p.CreateTime,
                                                   ModifyBy = p.ModifyBy,
                                                   ModifyName = p.ModifyName,
                                                   ModifyTime = p.ModifyTime,
                                                   Remark = p.Remark
                                               }).ToListAsync();
                //var preservingDevices = await _basDevicePreserveRepo
                //    .Where(x => x.DevCode == dto.DeviceCode
                //        && x.BeginTime >= dto.BeginTime
                //        && x.EndTime <= dto.EndTime
                //        && x.IsDeleted == false).ToListAsync();
                if (preservingDevices.Any())
                {
                    preservingDevices.ForEach(x =>
                    {
                        var dev = result.Find(y => y.BeginTime.Day == x.BeginTime.Day);
                        if (dev != null)
                        {
                            if (dev.DeviceWorkStatus == "RUNNING")
                            {
                                dev.DeviceWorkStatus = "REPAIRING";
                            }
                            else
                                dev.DeviceWorkStatus = "PRESERVE";
                            dev.Remark = x.Remark;
                            dev.CreateTime = x.CreateTime;
                            dev.Creater = x.CreatedName;
                        }
                    });
                }

                //标准工作时长
                //var capacitys = await _basProductStandardCapacityRepo.Where(x => !x.IsDeleted).ToListAsync();

                //var planScheduleSchedule = await _planProductionScheduleRepo.Where(x => !x.IsDeleted).ToListAsync();
                //var planPendingEntities = await _basplanPendingRepo.Where(x => !x.IsDeleted).ToListAsync();

                //var list = (from a in workingDevices
                //            join b in planScheduleSchedule on a.SchedulCode equals b.SchedulCode into t1
                //            from x in t1.DefaultIfEmpty()
                //            join c in planPendingEntities on x.PlanCode equals c.PlanCode into t2
                //            from xx in t2.DefaultIfEmpty()
                //            join d in capacitys on new { xx.ProductCode, a.StepCode } equals new { ProductCode = d.ProCode, d.StepCode } into t3
                //            from y in t3.DefaultIfEmpty()
                //            where a.DiviceCode == dto.DeviceCode && a.UseDate >= dto.BeginTime && a.UseDate <= dto.EndTime
                //            orderby a.UseDate
                //            select new
                //            {
                //                xx.ProductCode,
                //                a.SchedulCode,
                //                y.StepCode,
                //                y.StandardWorkTime,
                //                a.OrderNumber,
                //                xx.ProductName,
                //                a.UseDate,
                //                a.UseendDate,
                //            }).ToList();


            }
            return result;
        }

        public async Task<AppSrvResult> UpdateCalendarAsync(int t, BasDeviceCalendarDto dto)
        {
            dto.TrimStringFields();
            var device = await _stationRepo.Where(x => x.Code == dto.DeviceCode && x.IsDeleted == false).FirstOrDefaultAsync();
            if (device != null)
            {
                if (t == 0)
                {
                    var preserve = Mapper.Map<BasDevicePreserve>(dto);
                    preserve.Id = IdGenerater.GetNextId();
                    preserve.DevCode = dto.DeviceCode;
                    preserve.DevName = dto.DeviceName;
                    _basDevicePreserveRepo?.InsertAsync(preserve);
                }
                else if (t == 1)
                {
                    var preserve = await _basDevicePreserveRepo
                        .Where(x => x.DevCode == dto.DeviceCode
                            && x.BeginTime.Date == dto.BeginTime.Date
                            && !x.IsDeleted).FirstOrDefaultAsync();
                    if (preserve != null)
                        _basDevicePreserveRepo?.RemoveAsync(preserve);
                }
            }

            return AppSrvResult();
        }

        /// <summary>
        /// 更新状态
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AppSrvResult> ChangeStatusAsync(ChangeStatusDto input)
        {
            if (input.CodeList.Count == 0)
                return Problem(HttpStatusCode.BadRequest, "编码列表不能为空！");

            var steps = await _stationRepo.Where(x => input.CodeList.Contains(x.Code ?? "-1"), false, false).ToListAsync();
            if (input.Status == 0)
            {
                steps.ForEach(x => { x.Status = 0; });
            }
            else
            {
                steps.ForEach(x => { x.Status = 1; });
            }

            await _stationRepo.UpdateRangeAsync(steps);

            return AppSrvResult();
        }
        /// <summary>
        /// 获取设备保养信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ActionResult<ResultJson>> GetDevicePreserveAsync(long id)
        {
            var device = await _stationRepo.GetAsync(id);
            if (device != null)
            {
                var users = _sysUserRepo.Where(x => true);
                var depres = _basDevicePreserveRepo.Where(x => x.IsDeleted == false && x.DevCode == device.Code);
                var preservingDevices = await (from p in depres
                                               join u in users on p.CreateBy equals u.Id
                                               select new
                                               {
                                                   Id = p.Id,
                                                   DevCode = p.DevCode,
                                                   DevName = p.DevName,
                                                   IsDeleted = p.IsDeleted,
                                                   Status = p.Status,
                                                   BeginTime = p.BeginTime,
                                                   EndTime = p.EndTime,
                                                   CreateBy = p.CreateBy,
                                                   CreatedName = u.Name,
                                                   CreateTime = p.CreateTime,
                                                   Remark = p.Remark
                                               }).ToListAsync();

                return new ResultJson("查询成功", preservingDevices);
            }
            else
            {
                return new ResultJson("设备不存在");
            }

        }


        /// <summary>
        /// import
        /// </summary>
        /// <param name="dtos"></param>
        /// <returns></returns>
        public async Task<ResultJson> ImportAsync(List<BasDeviceDto> dtos)
        {
            var restRpcResult = await _maintRestClient.GetDictByNameAsync("设备类型");
            if (restRpcResult.IsSuccessStatusCode)
            {
                var unitEnums = restRpcResult.Content;
                if (unitEnums is not null)
                {
                    dtos.ForEach(x =>
                    {
                        if (string.IsNullOrWhiteSpace(x.Code))
                            x.Code = _basNumberManagementService.GetNumberBySimpleName(CommonConst.MATERIALNUMBER).Result;
                        x.Dtype = unitEnums.FirstOrDefault(d => d.label == x.DtypeDis!)?.value??"0";

                    });
                }
            }
            var entities = Mapper.Map<List<BasDevice>>(dtos);

            var resultObj = await _stationMgr.ImportAsync(entities);

            if (resultObj.insertEntities.Any())
                await _stationRepo.InsertRangeAsync(resultObj.insertEntities);

            if (resultObj.updateEntities.Any())
                await _stationRepo.UpdateRangeAsync(resultObj.updateEntities);

            return new ResultJson(resultObj.Msg);
        }

        public async Task<ResultJson> ImportDeviceStateAsync(List<PartDeviceStateDto> dtos)
        {
            var emptyCode = dtos.Where(x => string.IsNullOrWhiteSpace(x.DevCode)).Any();
            if(emptyCode)
                throw new BusinessException($"请确保您所导入的数据中设备编码都有值！");

            var emptyBegin = dtos.Where(x => x.BeginTime ==null).Any();
            if (emptyBegin)
                throw new BusinessException($"请确保您所导入的数据中开始时间都有值！");

            var emptyEnd = dtos.Where(x => x.EndTime == null).Any();
            if (emptyEnd)
                throw new BusinessException($"请确保您所导入的数据中结束时间都有值！");
            var emptyState = dtos.Where(x => string.IsNullOrWhiteSpace(x.StatusDis)).Any();
            if (emptyState)
                throw new BusinessException($"请确保您所导入的数据中状态都有值！");

            var restRpcResult = await _maintRestClient.GetDictByNameAsync("设备状态");
            if (restRpcResult.IsSuccessStatusCode)
            {
                var unitEnums = restRpcResult.Content;
                if (unitEnums is not null)
                {
                    dtos.ForEach(x =>
                    {
                        x.Status = Convert.ToInt32(unitEnums.FirstOrDefault(d => d.label == x.StatusDis!)?.value ?? "0");
                    });
                }
            }
            var entities = Mapper.Map<List<PartDeviceState>>(dtos);

            var resultObj = await _stationMgr.ImportDeviceStateAsync(entities);

            if (resultObj.Any())
                await _partDevRepo.InsertRangeAsync(resultObj);

            return new ResultJson("导入成功");
        }





        /// <summary>
        /// import
        /// </summary>
        /// <param name="dtos"></param>
        /// <returns></returns>
        public async Task<List<DeviceTypeTreeDto>> QueryDeviceAllType(BasDevicePagedDto search)
        {
            List<DeviceTypeTreeDto> results = new List<DeviceTypeTreeDto>();
            search.TrimStringFields();
            var whereExpression = ExpressionCreator
                                                .New<BasDevice>()
                                                .AndIf(search.Code.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Code!, $"%{search.Code}%"))
                                                .AndIf(search.Name.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Name!, $"%{search.Name}%"))
                                                .AndIf(search.Status != -1, x => x.Status == search.Status)
                                                .AndIf(search.Dtype != "0", x => x.Dtype == search.Dtype)
                                                .And(x => !x.IsDeleted)
                                                ;
            var entities = _stationRepo.Where(whereExpression).ToList();
            if (entities.Any())
            {
                var devicesDtos = Mapper.Map<List<DeviceTreeDtos>>(entities);

                devicesDtos.ForEach(x =>
                {
                    x.Label = x.Name + @$"({x.Code})";
                });

                results = devicesDtos.GroupBy(x => x.Dtype).Select(x => new DeviceTypeTreeDto
                {
                    Dtype = x.Key,
                    Children = x.ToList()
                }).ToList();

                var devStateRpcResult = await _maintRestClient.GetDictByNameAsync("设备类型");
                if (devStateRpcResult.IsSuccessStatusCode)
                {
                    var devStateEnums = devStateRpcResult.Content;
                    if (devStateEnums is not null)
                    {
                        results.ForEach(x =>
                        {
                            x.Label = devStateEnums.FirstOrDefault(d => d.value == x.Dtype.ToString())?.label;
                        });
                    }
                }
            }
            return results;
        }




        public async Task<List<DeviceStateDto>> QueryDeviceState(QueryDevStateDto queryData)
        {
            var results = new List<DeviceStateDto>();
            DateTime nowDate = Convert.ToDateTime(queryData.targetDate);
            // 定义起始日期和结束日期
            DateTime startDate = new DateTime(nowDate.Year, nowDate.Month, 1);
            DateTime endDate = new DateTime(nowDate.Year, (nowDate.Month + 1), 1).AddDays(-1);

            var deviceStates = await _partDevRepo.Where(x => x.DevCode == queryData.devCode && !x.IsDeleted).ToListAsync();
             for(var i = startDate; i<= endDate; i = i.AddDays(1))
            {
                var obj = new DeviceStateDto();
                obj.targetDate = i;
                //obj.status = deviceStates?.Where(x => x.TargetDate == i)?.FirstOrDefault()?.Status ?? "0";

                results.Add(obj);
            }
            return results;
        }


        public async Task<ResultJson> GetDeviceStateAsync(long id)
        {
            var userProfile = await _partDevRepo.Where(x => !x.IsDeleted && x.Id == id).FirstOrDefaultAsync();
            if (userProfile == null)
                return new ResultJson("查询成功！无数据", null);
            var menu = Mapper.Map<PartDeviceStateDto>(userProfile);

            return new ResultJson("查询成功", menu, 0);
        }


        public async Task<PageModelDto<PartDeviceStateDto>> GetDeviceStateAsync(BasDevicePagedDto search)
        {
            List<PartDeviceStateDto> results = new List<PartDeviceStateDto>();
            search.TrimStringFields();
            var whereExpression = ExpressionCreator
                                                .New<BasDevice>()
                                                .AndIf(search.Code.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Code!, $"%{search.Code}%"))
                                                .AndIf(search.Name.IsNotNullOrWhiteSpace(), x => EF.Functions.Like(x.Name!, $"%{search.Name}%"))
                                                .AndIf(search.Status != 0, x => x.Status == search.Status)
                                                .AndIf(search.Dtype != "0", x => x.Dtype == search.Dtype)
                                                .And(x => !x.IsDeleted)
                                                ;
            var devEntities = _stationRepo.Where(whereExpression).ToList();

            if (devEntities.Any())
            {
                var objs = await _partDevRepo.Where(x => !x.IsDeleted && devEntities.Select(x => x.Code).Contains(x.DevCode)).ToListAsync();

                results = Mapper.Map<List<PartDeviceStateDto>>(objs);
                var users = await _sysUserRepo.Where(m => !m.IsDeleted && m.Status == 1 && (results.Select(x => x.CreateBy).Contains(m.Id) || results.Select(x => x.ModifyBy).Contains(m.Id))).ToListAsync();
                results.ForEach(x =>
                {
                    x.Dtype = devEntities?.FirstOrDefault(p => p.Code == x.DevCode)?.Dtype??"0";
                    x.CreatedName = users?.FirstOrDefault(p => p.Id == x.CreateBy)?.Name??"";
                    x.ModifyName = users?.FirstOrDefault(p => p.Id == x.ModifyBy)?.Name ?? "";
                });

                var devStateRpcResult = await _maintRestClient.GetDictByNameAsync("设备状态");
                if (devStateRpcResult.IsSuccessStatusCode)
                {
                    var devStateEnums = devStateRpcResult.Content;
                    if (devStateEnums is not null)
                    {
                        results.ForEach(x =>
                        {
                            x.StatusDis = devStateEnums.FirstOrDefault(d => d.value == x.Status.ToString())?.label;
                        });
                    }
                }

                devStateRpcResult = await _maintRestClient.GetDictByNameAsync("设备类型");
                if (devStateRpcResult.IsSuccessStatusCode)
                {
                    var devStateEnums = devStateRpcResult.Content;
                    if (devStateEnums is not null)
                    {
                        results.ForEach(x =>
                        {
                            x.DtypeDis = devStateEnums.FirstOrDefault(d => d.value == x.Dtype.ToString())?.label;
                        });
                    }
                }


            }

            var resultDatas = results
                            .OrderByDescending(x => x.CreateTime)
                            .Skip(search.SkipRows())
                            .Take(search.PageSize)
                            .ToList();
            return new PageModelDto<PartDeviceStateDto>(search, resultDatas, results.Count());



        }



        

            public async Task<AppSrvResult> UpdateDeviceStateAsync(PartDeviceStateDto input)
        {
            bool isUpdete = await _stationMgr.UpdateDeviceStateAsync(input.DevCode,input.BeginTime,input.EndTime,input.Status, input.Id ?? 0);
            if (isUpdete)
            {
                input.TrimStringFields();
                var station = Mapper.Map<PartDeviceState>(input);
                var userProfile = await _partDevRepo.GetAsync(input.Id ?? 0);
                if (userProfile != null)
                {
                    userProfile.Status = station.Status;
                    userProfile.BeginTime = station.BeginTime;
                    userProfile.EndTime = station.EndTime;
                    userProfile.Remark = station.Remark;
                    userProfile.ModifyTime = station.ModifyTime;
                    userProfile.ModifyBy = station.ModifyBy;
                    await _partDevRepo.UpdateAsync(userProfile);
                }
            }
            return AppSrvResult();
        }


        public async Task<AppSrvResult> DeleteDeviceStateAsync(long id)
        {
            var userProfile = await _partDevRepo.GetAsync(id);
            if (userProfile != null)
            {
                //var o = await _basStationRepo.Where(x => !x.IsDeleted && x.DeviceCode == userProfile.Code).ToListAsync();
                //if (o.Any())
                //{
                //    return Problem(HttpStatusCode.BadRequest, "该设备所属工位有效，不可以删除！");
                //}
                var menu = Mapper.Map<PartDeviceState>(userProfile);
                menu.IsDeleted = true;
                await _partDevRepo.UpdateAsync(menu);
            }
            return AppSrvResult();
        }
    }
}
