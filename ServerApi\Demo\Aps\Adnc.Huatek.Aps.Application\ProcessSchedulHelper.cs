﻿namespace Adnc.Huatek.Aps.Application
{
    using System;
    using System.Collections.Generic;
    using System.Linq;

    namespace Adnc.Huatek.Aps.Application.Services.Implements
    {


        public class ProcessSchedulDto
        {
            public string? Name { get; set; } // 工序名称
            public double ProcessingTime { get; set; } // 工序所需时间（小时）
            public double RequiredQuantity { get; set; } // 工序所需数量
            public double SingleProduction { get; set; } // 单次生产数量
            public double LimitHours { get; set; }  //单日工时限制

            public double IntervalTime { get; set; } //工序间隔时间

            public string? PreStep { get; set; }  // 前置工序

            public string? DeviceCode { get; set; } //设备编码

            public string? LineCode { get; set; } //产线编码

            public string? SolutionName { get; set; } //方案名称

            public string? ProCode { get; set; } //产品编码


            public string? StationCode { get; set; } //工位编码

            public string? StepName { get; set; } //工序编码

            public int? sort { get; set; } //工序顺序

            public DateTime StartTime { get; set; }

            public DateTime EndTime { get; set; }

        }

        public class ProcessSchedul
        {
            public string? Name { get; set; } // 工序编码
            public string? StepName { get; set; } //工序名称
            public double ProcessingTime { get; set; } // 工序所需时间（小时）
            public double RequiredQuantity { get; set; } // 工序所需数量
            public double SingleProduction { get; set; } // 单次生产数量
            public double ProducedQuantity { get; set; } // 实际生产数量
            public int Day { get; set; } = 0;   //天数   
            public double TotalRequiredQuantity { get; set; } //工序总生产数量
            public DateTime StartTime { get; set; } // 开始时间
            public DateTime EndTime { get; set; } // 结束时间
            public DateTime CurrentTime { get; set; } //记录工序当前时间
            public List<string> PrecedingProcesses { get; set; } = new List<string>(); // 前置工序列表
            public Dictionary<DateTime, int> DailyExecutionCount { get; set; } = new Dictionary<DateTime, int>(); // 每日执行次数
            public string? SolutionName { get; set; } //方案名称
            public double LimitHours { get; set; }  //单日工时限制

            public int? LeaveTime { get; set; }

            public int? RemainingTime { get; set; }
            public ProcessSchedul(string? name, double processingTime, double requiredQuantity, double singleProduction, double limitHours, string? stepName, string? solutionName)
            {
                Name = name!;
                ProcessingTime = processingTime;
                RequiredQuantity = requiredQuantity;
                SingleProduction = singleProduction;
                LimitHours = limitHours;
                StepName = stepName;
                SolutionName = solutionName;
            }

            public ProcessSchedul()
            {

            }
        }

        public class ProductionScheduler
        {
            public List<ProcessSchedul> Processes { get; set; } // 工序列表
            public int TargetQuantity { get; set; } // 目标生产数量
            public DateTime StartTime { get; set; } // 开始时间
            public int DailyProduction { get; set; } // 每日生产数量
            public double TotalTime { get; set; } // 总时间
            public int Sorts { get; set; }  //排序方式 0：正排 1：倒排

            public ProductionScheduler()
            {
                Processes = new List<ProcessSchedul>();
            }

            public void AddProcess(ProcessSchedul process)
            {
                Processes.Add(process);
            }

            public void Schedule()
            {
                int remainingQuantity = TargetQuantity;
                DateTime currentTime = StartTime;

                while (remainingQuantity > 0)
                {
                    bool scheduled = false;

                    foreach (var process in Processes)
                    {
                        process.TotalRequiredQuantity = remainingQuantity * process.RequiredQuantity;
                        int hour = currentTime.AddHours(process.LimitHours).Hour;
                        if (process.ProducedQuantity <= process.TotalRequiredQuantity && process.PrecedingProcesses.Count == 0)
                        {
                            double maxProducedQuantity = process.SingleProduction;
                            double scheduledQuantity = Math.Min(process.RequiredQuantity, maxProducedQuantity);

                            process.StartTime = (process.DailyExecutionCount.Count() == 0 ? currentTime : process.CurrentTime);

                            //超过每天工作时限自动加一天
                            if (process.StartTime.Hour >= hour)
                            {
                                process.Day++;
                                process.EndTime = currentTime.AddDays(process.Day).AddHours(process.ProcessingTime);
                            }
                            else
                            {
                                process.EndTime = process.StartTime.AddHours(process.ProcessingTime);
                            }
                            process.ProducedQuantity += process.SingleProduction;

                            // remainingQuantity -= process.ProducedQuantity;
                            // DailyProduction += process.ProducedQuantity;
                            TotalTime += process.ProcessingTime;
                            UpdateDailyExecution(process, process.EndTime);
                            process.CurrentTime = process.EndTime;
                            scheduled = true;
                        }
                        else if (process.ProducedQuantity <= process.TotalRequiredQuantity && AllPrecedingProcessesScheduled(process))
                        {
                            DateTime maxEndTime = DateTime.MinValue;
                            foreach (var precedingProcessName in process.PrecedingProcesses)
                            {
                                var precedingProcess = Processes.Find(p => p.Name == precedingProcessName);
                                if (precedingProcess!.EndTime > maxEndTime)
                                {
                                    maxEndTime = precedingProcess.EndTime;
                                }
                            }

                            int maxProducedQuantity = (int)process.SingleProduction;
                            double scheduledQuantity = Math.Min(process.RequiredQuantity, maxProducedQuantity);

                            process.StartTime = maxEndTime;

                            if (process.StartTime.Hour >= hour)
                            {
                                process.Day++;
                                process.EndTime = currentTime.AddDays(process.Day).AddHours(process.ProcessingTime);
                            }
                            else
                            {
                                process.EndTime = process.StartTime.AddHours(process.ProcessingTime);
                            }

                            process.ProducedQuantity += process.SingleProduction;

                            //   remainingQuantity -= process.ProducedQuantity;
                            //  DailyProduction += process.ProducedQuantity;
                            TotalTime += process.ProcessingTime;

                            UpdateDailyExecution(process, process.EndTime);

                            process.CurrentTime = process.EndTime;
                            scheduled = true;
                        }
                    }
                    var lastProcess = Processes[Processes.Count - 1];
                    if (lastProcess.EndTime != DateTime.MinValue && lastProcess.ProducedQuantity <= lastProcess.TotalRequiredQuantity)
                    {
                        remainingQuantity -= 1;
                        DailyProduction += 1;
                        lastProcess.EndTime = DateTime.MinValue;
                    }
                    else
                    {
                        if (lastProcess.ProducedQuantity > lastProcess.TotalRequiredQuantity)
                        {
                            remainingQuantity -= 1;
                            DailyProduction += 1;
                        }
                    }
                    if (!scheduled)
                    {
                        currentTime = currentTime.AddHours(1);

                    }
                }
            }

            private bool AllPrecedingProcessesScheduled(ProcessSchedul process)
            {
                foreach (var precedingProcessName in process.PrecedingProcesses)
                {
                    var precedingProcess = Processes.Find(p => p.Name == precedingProcessName);
                    if (precedingProcess!.StartTime == DateTime.MinValue)
                    {
                        return false;
                    }
                }
                return true;
            }

            private void UpdateDailyExecution(ProcessSchedul process, DateTime currentTime)
            {
                if (!process.DailyExecutionCount.ContainsKey(currentTime.Date))
                {
                    process.DailyExecutionCount[currentTime.Date] = 0;
                }

                process.DailyExecutionCount[currentTime.Date] += 1;
            }
        }
        [Serializable]
        public class ProductionProcess
        {
            public string? StepName { get; set; } //工序名称
            public double Duration { get; set; } // 工序持续时间（小时）
            public double Quantity { get; set; } // 每次执行的产量
            public double Demand { get; set; } // 每次的需求量
            public double Interval { get; set; } // 工序间隔时间（小时）
            public string? Name { get; set; }
            public double LimitHours { get; set; }

            public int? Sorts { get; set; }

            public DateTime StartTime { get; set; }
            public string? SolutionName { get; set; }

            public int? LeaveTime { get; set; }

            public double? RemainingTime { get; set; }

            public string StationCode { get; set; }

            public DateTime EndTime { get; set; }

            public List<ProductionProcess> PreviousProcesses { get; set; } // 前置工序列表
            public Dictionary<DateTime, int> DailyExecutionCounts { get; set; } = new Dictionary<DateTime, int>();// 工序的执行日期和每天执行次数

            public ProductionProcess(string? name, double duration, double quantity, double demand, double interval, double limitHours, int? sorts, string? stepName, string? solutionName, string stationCode, DateTime startTime)
            {
                Name = name;
                Duration = duration;
                Quantity = quantity;
                Demand = demand;
                Interval = interval;
                LimitHours = limitHours;
                Sorts = sorts;
                StepName = stepName;
                PreviousProcesses = new List<ProductionProcess>();
                SolutionName = solutionName;
                StationCode = stationCode;
                StartTime = startTime;
            }

            public void CalculateExecutionDates(int productQuantity, DateTime currentDateTime, double workHoursPerDay, double expendCount)
            {
                int dayCount = (int)Math.Floor(workHoursPerDay / Duration);

                double dayProcuct = dayCount * Quantity;

                RemainingTime = (int)Math.Ceiling(workHoursPerDay % Duration);


                double totalDemand = productQuantity * Demand;

                totalDemand = totalDemand - expendCount;

                if (totalDemand < 0)
                    totalDemand = 0;

                int processDay = (int)Math.Floor(totalDemand / dayProcuct);

                int leaveCount = (int)Math.Ceiling(totalDemand % dayProcuct);
                if (expendCount > 0)
                {
                    processDay += 1;
                }

                if (leaveCount > 0)
                    processDay += 1;

                if (leaveCount == 0)
                {
                    LeaveTime = 0;
                }
                else
                {
                    LeaveTime = (int)(workHoursPerDay - (int)((int)Math.Ceiling(leaveCount / Quantity) * Duration));
                }

                for (int i = 0; i < processDay; i++)
                {
                    DateTime date = currentDateTime.AddDays(i);
                    if (!DailyExecutionCounts.ContainsKey(date.Date))
                    {
                        DailyExecutionCounts[date.Date] = 0;
                    }
                    if (i == 0 && expendCount > 0)
                    {
                        DailyExecutionCounts[date.Date] = (int)Math.Ceiling(expendCount/Quantity);
                    }
                    else if (i == (processDay - 1) && leaveCount > 0)
                    {
                        DailyExecutionCounts[date.Date] = (int)Math.Ceiling(leaveCount / Quantity);
                    }
                    else
                    {
                        DailyExecutionCounts[date.Date] = dayCount;
                    }
                    EndTime = date.Date;
                }
            }

            private DateTime GetPreviousProcessEndDate(ProductionProcess previous, int productQuantity, DateTime currentDateTime, double workHoursPerDay)
            {
                int previousProcessCount = (int)Math.Ceiling((double)(productQuantity * previous.Demand) / previous.Quantity);
                double previousProcessTime = previousProcessCount * previous.Duration;

                int previousProcessDay = (int)Math.Ceiling((double)(previousProcessTime) / workHoursPerDay);

                DateTime previousProcessStartDate = currentDateTime;

                if (previous.PreviousProcesses.Count > 0)
                {
                    foreach (var p in previous.PreviousProcesses)
                    {
                        DateTime previousEndDate = GetPreviousProcessEndDate(p, productQuantity, currentDateTime, workHoursPerDay);
                        if (previousEndDate > previousProcessStartDate)
                        {
                            previousProcessStartDate = previousEndDate;
                        }
                    }
                }
                return previousProcessStartDate.AddHours(previousProcessDay * workHoursPerDay);
            }


            public void CalculateExecutionDatesDesc(int productQuantity, DateTime lastProcessEndDate, double workHoursPerDay, double expendCount)
            {
                int dayCount = (int)Math.Floor(workHoursPerDay / Duration);

                double dayProcuct = dayCount * Quantity;

                RemainingTime = (int)Math.Ceiling(workHoursPerDay % Duration);

                double totalDemand = productQuantity * Demand;

                totalDemand = totalDemand - expendCount;

                if (totalDemand < 0)
                    totalDemand = 0;

                int processDay = (int)Math.Floor(totalDemand / dayProcuct);

                int leaveCount = (int)Math.Ceiling(totalDemand % dayProcuct);
                if (expendCount > 0)
                {
                    processDay += 1;
                }
                if (leaveCount > 0)
                    processDay += 1;
                if (leaveCount == 0)
                {
                    LeaveTime = 0;
                }
                else
                {
                    LeaveTime = (int)(workHoursPerDay - (int)Math.Ceiling(leaveCount / Quantity * Duration));
                }

                DateTime processEndDate = lastProcessEndDate;

                Dictionary<DateTime, int> sortDailyExecutionCounts = new Dictionary<DateTime, int>();

                for (int i = 0; i < processDay; i++)
                {
                    DateTime date = processEndDate.AddDays(-i);
                    if (!sortDailyExecutionCounts.ContainsKey(date.Date))
                    {
                        sortDailyExecutionCounts[date.Date] = 0;
                    }
                    if (i == 0 && expendCount > 0)
                    {
                        sortDailyExecutionCounts[date.Date] = (int)Math.Floor(expendCount / Demand);
                    }
                    else if (i == processDay - 1 && leaveCount > 0)
                    {
                        DailyExecutionCounts[date.Date] = (int)Math.Ceiling(leaveCount / Quantity);
                    }
                    else
                    {
                        sortDailyExecutionCounts[date.Date] = dayCount;
                    }
                    EndTime = date.Date;
                }
                DailyExecutionCounts = new Dictionary<DateTime, int>(sortDailyExecutionCounts.OrderByDescending(o => o.Key));
            }

        }

    }

}
