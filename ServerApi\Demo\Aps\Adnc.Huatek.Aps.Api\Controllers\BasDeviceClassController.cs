﻿using Adnc.Demo.Shared.Const;
using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Application.Services;
using Microsoft.AspNetCore.Authorization;
using Npoi.Mapper;

namespace Adnc.Huatek.Aps.Api.Controllers
{
    [Route($"{RouteConsts.ApsRoot}/deviceclass")]
    [ApiController]
    public class BasDeviceClassController : AdncControllerBase
    {
        private readonly IBasDeviceClassService _devClassSrv;

        public BasDeviceClassController(IBasDeviceClassService devClassSrv) => _devClassSrv = devClassSrv;

        /// <summary>
        /// 获取所有设备班次信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getallasync")]
        public async Task<ActionResult<List<BasDeviceClassDto>>> GetAllAsync() => await _devClassSrv.GetAllAsync();



        /// <summary>
        /// 新增设备班次
        /// </summary>
        /// <param name="input">用户信息</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("createasync")]
        public async Task<ActionResult<long>> CreateAsync([FromBody] BasDeviceClassDto input)
            => CreatedResult(await _devClassSrv.CreateAsync(input));

        /// <summary>
        /// 删除设备班次
        /// </summary>
        /// <param name="id">设备ID</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpDelete("deleteasync/{id}")]
        public async Task<ActionResult> DeleteAsync([FromRoute] long id)
            => Result(await _devClassSrv.DeleteAsync(id));


        /// <summary>
        /// 修改设备班次
        /// </summary>
        /// <param name="id">id</param>
        /// <param name="input">用户信息</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPut("updateasync")]
        public async Task<ActionResult> UpdateAsync([FromBody] BasDeviceClassDto input)
            => Result(await _devClassSrv.UpdateAsync(input));



       

        /// <summary>
        /// 获取设备班次列表分页
        /// </summary>
        /// <param name="search">查询条件</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("page")]
        public async Task<ActionResult<PageModelDto<DeviceWorkingDateDto>>> GetPagedAsync([FromBody] BasDeviceClassPagedDto search)
            => await _devClassSrv.GetPagedAsync(search);

        /// <summary>
        /// 获取单个设备班次详情信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("current/{id}")]
        public async Task<ActionResult<ResultJson>> GetCurrentUserInfoAsync(long id) => await _devClassSrv.GetByIdAsync(id);


    }
}
