﻿<Project Sdk="Microsoft.NET.Sdk">
	<Import Project="..\..\..\common.props" />
	<Import Project="..\..\..\version_service.props" />
	<PropertyGroup>
		<GenerateDocumentationFile>True</GenerateDocumentationFile>
	</PropertyGroup>
	<ItemGroup>
	  <ProjectReference Include="..\..\Shared\Adnc.Demo.Shared.Const\Adnc.Demo.Shared.Const.csproj" />
	</ItemGroup>

	<ItemGroup Condition="'$(SolutionName)'=='Adnc'">
		<ProjectReference Include="..\..\..\Infrastructures\Adnc.Infra.Repository\Adnc.Infra.Repository.csproj" />
		<ProjectReference Include="..\..\..\Infrastructures\Adnc.Infra.EfCore.MySQL\Adnc.Infra.Repository.EfCore.MySql.csproj" />
		<ProjectReference Include="..\..\..\ServiceShared\Adnc.Shared.Repository\Adnc.Shared.Repository.csproj" />
		<ProjectReference Include="..\..\..\ServiceShared\Adnc.Shared\Adnc.Shared.csproj" />
	</ItemGroup>

	<ItemGroup Condition="'$(SolutionName)'=='Adnc.Demo' ">
		<PackageReference Include="Adnc.Infra.Repository" Version="$(Infra_Version)" />
		<PackageReference Include="Adnc.Infra.Repository.EfCore.MySql" Version="$(Infra_Version)" />
		<PackageReference Include="Adnc.Shared" Version="$(Shared_Version)" />
		<PackageReference Include="Adnc.Shared.Repository" Version="$(Shared_Version)" />
	</ItemGroup>
</Project>