﻿using Adnc.Demo.Shared.Const;
using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Application.Services;
using Adnc.Shared.Application.Contracts.ResultModels;
using Adnc.Shared.WebApi.Authorization;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Adnc.Huatek.Aps.Api.Controllers
{
    [Route($"{RouteConsts.ApsRoot}/classes")]
    [ApiController]
    [AllowAnonymous]
    public class BasClassesController : AdncControllerBase
    {
        private readonly IBasClassesService _classesSrv;

        public BasClassesController(IBasClassesService classesSrv) => _classesSrv = classesSrv;


        /// <summary>
        /// 获取班次分页信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("page")]
        public async Task<ActionResult<PageModelDto<BasClassesDto>>> GetPagedAsync([FromBody] ClassesPagedDto search)
            => await _classesSrv.GetPagedAsync(search);


        /// <summary>
        /// 获取班次信息
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getall")]
        public async Task<ActionResult<List<BasClassesDto>>> GetAllAsync()
            => await _classesSrv.GetAllAsync();

        /// <summary>
        /// 新增班次
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("add")]
        public async Task<ActionResult<long>> CreateAsync([FromBody] BasClassesDto input)
           => CreatedResult(await _classesSrv.CreateAsync(input));

        /// <summary>
        /// 更新班次
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("edit")]
        public async Task<ActionResult> UpdateAsync([FromBody] BasClassesDto input)
           => Result(await _classesSrv.UpdateAsync(input));


        /// <summary>
        /// 删除班次
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
         [AllowAnonymous]
        [HttpDelete("delete/{id}")]
        public async Task<ActionResult> DeleteAsync([FromRoute] long id)
           => Result(await _classesSrv.DeleteAsync(id));



        /// <summary>
        /// 根据ID获取班次
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getbyid/{id}")]
        public async Task<ActionResult<BasClassesDto>> GetByIdAsync([FromRoute] long id)
            => await _classesSrv.GetByIdAsync(id);

        /// <summary>
        /// 获取班次信息 工位模块弹框中选择班次
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("getclasses")]
        public async Task<ActionResult<List<ClassesDto>>> GetAllClassesAsync()
            => await _classesSrv.GetAllClassesAsync();



        /// <summary>
        /// 批量启用
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPut("changestatus/{status}")]
        public async Task<ActionResult> MakeEnableAsync([FromRoute] int status, [FromBody] List<long> data)
          => Result(await _classesSrv.MakeEnableAsync(status, data));
    }
}
