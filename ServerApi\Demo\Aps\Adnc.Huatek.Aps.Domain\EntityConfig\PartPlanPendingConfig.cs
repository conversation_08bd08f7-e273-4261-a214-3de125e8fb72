﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasOrderAggregate;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class PartPlanPendingConfig : AbstractEntityTypeConfiguration<PartPlanPending>
    {
        public override void Configure(EntityTypeBuilder<PartPlanPending> builder)
        {
            base.Configure(builder);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.OrderId).HasColumnName("orderId");
            builder.Property(x => x.IdDetail).HasColumnName("idDetail");
            builder.Property(x => x.OrderNumber).HasColumnName("ordernumber");
            builder.Property(x => x.Priority).HasColumnName("priority");
            builder.Property(x => x.Status).HasColumnName("status");
            builder.Property(x => x.UserName).HasColumnName("username");
            builder.Property(x => x.DeliveryDate).HasColumnName("deliverydate");
            builder.Property(x => x.ProductCode).HasColumnName("productcode");
            builder.Property(x => x.ProductName).HasColumnName("productname");
            builder.Property(x => x.Qty).HasColumnName("qty");
            builder.Property(x => x.PlanCode).HasColumnName("plancode");

            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.CreateBy).HasColumnName("createby");
            builder.Property(x => x.CreateTime).HasColumnName("createtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
        }
    }
}