﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasLineAggregate;
using Adnc.Infra.Helper;
using Adnc.Shared.Rpc.Event;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using static StackExchange.Redis.Role;

namespace Adnc.Huatek.Aps.Domain.Services
{
    public class BasLineManagement : IDomainService
    {
        private readonly IEfBasicRepository<BasLine> _basLineRepo;

        public BasLineManagement(IEfBasicRepository<BasLine> basLineRepo) 
        {
            _basLineRepo = basLineRepo;
        }


        public virtual async Task<BasLine> CreateAsync(string Code, string Name, long IdWorkshop, string Remark,int Status)
        {
            var exists = await _basLineRepo.AnyAsync(x => x.Lcode == Code && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"产线编码:{Code}已存在，不可重复添加！");

            exists = await _basLineRepo.AnyAsync(x => x.Lname == Name && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"产线名称:{Name}已存在，不可重复添加！");

            return new BasLine()
            {
                Id= IdGenerater.GetNextId(),
                Lcode = Code,
                Lname = Name,
                IdWorkshop= IdWorkshop,
                Status = Status,
                Remark= Remark
            };
        }

        public virtual async Task<bool> UpdateAsync(string Code, string Name, long id)
        {
            var exists = await _basLineRepo.AnyAsync(x => x.Lcode == Code && x.Id != id && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"产线编码:{Code}已存在，不可重复添加！");

            exists = await _basLineRepo.AnyAsync(x => x.Lname == Name && x.Id != id && !x.IsDeleted);
            if (exists)
                throw new BusinessException($"产线名称:{Name}已存在，不可重复添加！");

            return true;
        }
    }
}
