﻿using Adnc.Huatek.Aps.Application.Dtos;
using Adnc.Huatek.Aps.Domain.Aggregates.BasTechnologyAggregate;
using Adnc.Shared.Application.Contracts.ResultModels;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Services
{
    public interface IBasTechnologyService: IAppService
    {
        [OperateLog(LogName = "获取工艺列表信息")]
        Task<PageModelDto<BasTechnologyDto>> GetPagedAsync(TechnologyPagedDto search);
        [OperateLog(LogName = "获取所有工艺信息")]
        Task<List<BasTechnologyDto>> GetAllAsync();
        [OperateLog(LogName = "创建工艺")]
        [UnitOfWork]
        Task<AppSrvResult<long>> CreateAsync(BasTechnologyDto input);
        [OperateLog(LogName = "修改工艺")]
        [UnitOfWork]
        Task<AppSrvResult> UpdateAsync(BasTechnologyDto input);
        [OperateLog(LogName = "删除工艺")]
        [UnitOfWork]
        Task<AppSrvResult> DeleteAsync(long id);
        [OperateLog(LogName = "根据ID获取工艺数据")]
        Task<BasTechnologyDto> GetByIdAsync(long id);
        [OperateLog(LogName = "根据tCode获取工艺数据")]
        Task<BasTechnologyDto> GetByTCodeAsync(string tCode);
        [OperateLog(LogName = "更新状态")]
        Task<AppSrvResult> ChangeStatusAsync(ChangeStatusDto input);
    }
}
