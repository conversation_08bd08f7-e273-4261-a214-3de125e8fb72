﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Dtos
{
    public class BasStepDto : Dto
    {
        /// <summary>
        /// ID
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        /// 
        [Display(Name = "描述")]
        public string? Remark { get; set; }

        /// <summary>
        /// 工序名称
        /// </summary>
        /// 
        [Display(Name = "工序名称")]
        public string? Name { get; set; }

        /// <summary>
        /// 工序编码S
        /// </summary>
        ///
        [Display(Name = "工序编码")]
        public string? Code { get; set; }

        /// <summary>
        /// 工序最短时长(小时)
        /// </summary>
        [Display(Name = "最短工作时长")]
        public decimal? StepTime { get; set; }


        /// <summary>
        /// 工序标准时长(小时)
        /// </summary>
        [Display(Name = "标准工作时长")]
        public decimal? Tat { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public int? Status { get; set; }
        [Display(Name = "启用/禁用")]
        public string? StatusDis { get; set; }

    }

    public class StepPagedDto : SearchPagedDto
    {
        /// <summary>
        /// 工序名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 工序编码
        /// </summary>
        public string? Code { get; set; }


        /// <summary>
        /// 状态
        /// </summary>
        public int Status { get; set; }
    }

    public class ChangeStatusDto : IDto
    {

        /// <summary>
        /// 状态
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 编码列表
        /// </summary>
        public List<string> CodeList { get; set; } = new List<string>();


    }
}
