//using Microsoft.AspNetCore.Http;
//using Microsoft.AspNetCore.Mvc;
//using Microsoft.Extensions.Configuration;
//using Microsoft.Extensions.DependencyInjection;
//using Microsoft.Extensions.Options;
//using Moq;
//using System.Text.Json;
//using Xunit;
//using Adnc.Infra.Redis.Caching;
//using Adnc.Shared;
//using Adnc.Huatek.Aps.Application.Dtos;
//using Adnc.Shared.WebApi.Authentication.JwtBearer;

//namespace Adnc.Huatek.Aps.Api.Controllers.Tests
//{
//    /// <summary>
//    /// OuterSystemController的单元测试
//    /// 主要测试token处理和Authorization header生成功能
//    /// </summary>
//    public class OuterSystemControllerTest
//    {
//        private readonly Mock<IHttpClientFactory> _mockHttpClientFactory;
//        private readonly Mock<IConfiguration> _mockConfiguration;
//        private readonly Mock<ICacheProvider> _mockCacheProvider;
//        private readonly OuterSystemController _controller;

//        public OuterSystemControllerTest()
//        {
//            _mockHttpClientFactory = new Mock<IHttpClientFactory>();
//            _mockConfiguration = new Mock<IConfiguration>();
//            _mockCacheProvider = new Mock<ICacheProvider>();

//            _controller = new OuterSystemController(
//                _mockConfiguration.Object,
//                _mockHttpClientFactory.Object,
//                _mockCacheProvider.Object
//            );

//            // 设置HttpContext
//            var httpContext = new DefaultHttpContext();
//            var serviceCollection = new ServiceCollection();
            
//            // 添加JWT配置
//            var jwtOptions = new JWTOptions
//            {
//                SymmetricSecurityKey = "alphadotnetcoresecurity2020",
//                ValidIssuer = "adnc",
//                ValidAudience = "manager",
//                Expire = 6000
//            };
//            serviceCollection.AddSingleton(Options.Create(jwtOptions));
            
//            httpContext.RequestServices = serviceCollection.BuildServiceProvider();
//            _controller.ControllerContext = new ControllerContext
//            {
//                HttpContext = httpContext
//            };
//        }

//        [Fact]
//        public void GetJsonPropertyAsString_ShouldReturnCorrectValue()
//        {
//            // Arrange
//            var json = """
//                {
//                    "userCode": "admin",
//                    "userName": "管理员",
//                    "userEmail": "<EMAIL>"
//                }
//                """;
//            var jsonElement = JsonSerializer.Deserialize<JsonElement>(json);

//            // Act & Assert
//            var userCode = OuterSystemController.GetJsonPropertyAsString(jsonElement, "userCode");
//            var userName = OuterSystemController.GetJsonPropertyAsString(jsonElement, "userName");
//            var userEmail = OuterSystemController.GetJsonPropertyAsString(jsonElement, "userEmail");
//            var nonExistent = OuterSystemController.GetJsonPropertyAsString(jsonElement, "nonExistent");

//            Assert.Equal("admin", userCode);
//            Assert.Equal("管理员", userName);
//            Assert.Equal("<EMAIL>", userEmail);
//            Assert.Equal("", nonExistent);
//        }

//        [Fact]
//        public void GetJsonPropertyAsLong_ShouldReturnCorrectValue()
//        {
//            // Arrange
//            var json = """
//                {
//                    "id": "123456789",
//                    "numericId": 987654321,
//                    "createTime": 1566334602000
//                }
//                """;
//            var jsonElement = JsonSerializer.Deserialize<JsonElement>(json);

//            // Act & Assert
//            var stringId = OuterSystemController.GetJsonPropertyAsLong(jsonElement, "id");
//            var numericId = OuterSystemController.GetJsonPropertyAsLong(jsonElement, "numericId");
//            var createTime = OuterSystemController.GetJsonPropertyAsLong(jsonElement, "createTime");
//            var nonExistent = OuterSystemController.GetJsonPropertyAsLong(jsonElement, "nonExistent");

//            Assert.Equal(123456789L, stringId);
//            Assert.Equal(987654321L, numericId);
//            Assert.Equal(1566334602000L, createTime);
//            Assert.Equal(0L, nonExistent);
//        }

//        [Fact]
//        public async Task MesCallFunction_WithValidToken_ShouldGenerateAuthorizationHeader()
//        {
//            // Arrange
//            var token = "eyJhbGciOiJIUzUxMiJ9.test.token";
//            var redisKey = $"online-token-{token}";
//            var userInfoJson = """
//                {
//                    "admin": false,
//                    "authorities": [],
//                    "comment": "131",
//                    "createTime": 1566334602000,
//                    "deleted": "0",
//                    "groupId": "1943617310996697088",
//                    "id": "f52b1f9329cb4cd68d2fc660d9c2aac5",
//                    "password": "9FC7E2FC757073DA914C2DC762A2D096",
//                    "passwordSalt": "b7016a1f88864747bbbb359527cc879b",
//                    "userCode": "admin",
//                    "userEmail": "<EMAIL>",
//                    "userMobile": "13600000001",
//                    "userName": "admin",
//                    "userPhone": "029-88888888"
//                }
//                """;

//            // 设置Redis缓存返回值
//            var cacheValue = new CacheValue<string>(userInfoJson, true);
//            _mockCacheProvider.Setup(x => x.GetAsync<string>(redisKey))
//                .ReturnsAsync(cacheValue);

//            // 设置配置
//            _mockConfiguration.Setup(x => x["OuterSystem:AppUserKey"]).Returns("testAppUserKey");
//            _mockConfiguration.Setup(x => x["ServiceAddress:BaseUrl"]).Returns("http://localhost:5000");

//            // 设置请求头
//            var headers = new HeaderDictionary
//            {
//                { "appUserKey", "testAppUserKey" },
//                { "timestamp", "**********" },
//                { "sign", "OUTER_SYSTEM_ADMIN_SIGN" },
//                { "nonce", "test123" },
//                { "token", token }
//            };

//            _controller.HttpContext.Request.Headers.Clear();
//            foreach (var header in headers)
//            {
//                _controller.HttpContext.Request.Headers.Add(header.Key, header.Value);
//            }

//            var dto = new InputParamDto
//            {
//                ServiceUrl = "/api/test",
//                Param = """{"test": "value"}""",
//                HttpMethod = "POST"
//            };

//            // 这个测试主要验证token处理逻辑，实际的HTTP调用需要mock HttpClient
//            // 由于HttpClient的复杂性，这里主要测试token处理部分的逻辑

//            // Act & Assert
//            // 验证Redis被正确调用
//            _mockCacheProvider.Verify(x => x.GetAsync<string>(redisKey), Times.Once);
//        }

//        [Theory]
//        [InlineData("")]
//        [InlineData(null)]
//        public async Task MesCallFunction_WithInvalidToken_ShouldNotCallRedis(string invalidToken)
//        {
//            // Arrange
//            var headers = new HeaderDictionary
//            {
//                { "appUserKey", "testAppUserKey" },
//                { "timestamp", "**********" },
//                { "sign", "OUTER_SYSTEM_ADMIN_SIGN" },
//                { "nonce", "test123" }
//            };

//            if (!string.IsNullOrEmpty(invalidToken))
//            {
//                headers.Add("token", invalidToken);
//            }

//            _controller.HttpContext.Request.Headers.Clear();
//            foreach (var header in headers)
//            {
//                _controller.HttpContext.Request.Headers.Add(header.Key, header.Value);
//            }

//            var dto = new InputParamDto
//            {
//                ServiceUrl = "/api/test",
//                Param = """{"test": "value"}""",
//                HttpMethod = "POST"
//            };

//            // Act & Assert
//            // 验证Redis没有被调用
//            _mockCacheProvider.Verify(x => x.GetAsync<string>(It.IsAny<string>()), Times.Never);
//        }
//    }
//}
//using Microsoft.AspNetCore.Http;
//using Microsoft.AspNetCore.Mvc;
//using Microsoft.Extensions.Configuration;
//using Microsoft.Extensions.DependencyInjection;
//using Microsoft.Extensions.Options;
//using Moq;
//using System.Text.Json;
//using Xunit;
//using Adnc.Infra.Redis.Caching;
//using Adnc.Shared;
//using Adnc.Huatek.Aps.Application.Dtos;
//using Adnc.Shared.WebApi.Authentication.JwtBearer;

//namespace Adnc.Huatek.Aps.Api.Controllers.Tests
//{
//    /// <summary>
//    /// OuterSystemController的单元测试
//    /// 主要测试token处理和Authorization header生成功能
//    /// </summary>
//    public class OuterSystemControllerTest
//    {
//        private readonly Mock<IHttpClientFactory> _mockHttpClientFactory;
//        private readonly Mock<IConfiguration> _mockConfiguration;
//        private readonly Mock<ICacheProvider> _mockCacheProvider;
//        private readonly OuterSystemController _controller;

//        public OuterSystemControllerTest()
//        {
//            _mockHttpClientFactory = new Mock<IHttpClientFactory>();
//            _mockConfiguration = new Mock<IConfiguration>();
//            _mockCacheProvider = new Mock<ICacheProvider>();

//            _controller = new OuterSystemController(
//                _mockConfiguration.Object,
//                _mockHttpClientFactory.Object,
//                _mockCacheProvider.Object
//            );

//            // 设置HttpContext
//            var httpContext = new DefaultHttpContext();
//            var serviceCollection = new ServiceCollection();
            
//            // 添加JWT配置
//            var jwtOptions = new JWTOptions
//            {
//                SymmetricSecurityKey = "alphadotnetcoresecurity2020",
//                ValidIssuer = "adnc",
//                ValidAudience = "manager",
//                Expire = 6000
//            };
//            serviceCollection.AddSingleton(Options.Create(jwtOptions));
            
//            httpContext.RequestServices = serviceCollection.BuildServiceProvider();
//            _controller.ControllerContext = new ControllerContext
//            {
//                HttpContext = httpContext
//            };
//        }

//        [Fact]
//        public void GetJsonPropertyAsString_ShouldReturnCorrectValue()
//        {
//            // Arrange
//            var json = """
//                {
//                    "userCode": "admin",
//                    "userName": "管理员",
//                    "userEmail": "<EMAIL>"
//                }
//                """;
//            var jsonElement = JsonSerializer.Deserialize<JsonElement>(json);

//            // Act & Assert
//            var userCode = OuterSystemController.GetJsonPropertyAsString(jsonElement, "userCode");
//            var userName = OuterSystemController.GetJsonPropertyAsString(jsonElement, "userName");
//            var userEmail = OuterSystemController.GetJsonPropertyAsString(jsonElement, "userEmail");
//            var nonExistent = OuterSystemController.GetJsonPropertyAsString(jsonElement, "nonExistent");

//            Assert.Equal("admin", userCode);
//            Assert.Equal("管理员", userName);
//            Assert.Equal("<EMAIL>", userEmail);
//            Assert.Equal("", nonExistent);
//        }

//        [Fact]
//        public void GetJsonPropertyAsLong_ShouldReturnCorrectValue()
//        {
//            // Arrange
//            var json = """
//                {
//                    "id": "123456789",
//                    "numericId": 987654321,
//                    "createTime": 1566334602000
//                }
//                """;
//            var jsonElement = JsonSerializer.Deserialize<JsonElement>(json);

//            // Act & Assert
//            var stringId = OuterSystemController.GetJsonPropertyAsLong(jsonElement, "id");
//            var numericId = OuterSystemController.GetJsonPropertyAsLong(jsonElement, "numericId");
//            var createTime = OuterSystemController.GetJsonPropertyAsLong(jsonElement, "createTime");
//            var nonExistent = OuterSystemController.GetJsonPropertyAsLong(jsonElement, "nonExistent");

//            Assert.Equal(123456789L, stringId);
//            Assert.Equal(987654321L, numericId);
//            Assert.Equal(1566334602000L, createTime);
//            Assert.Equal(0L, nonExistent);
//        }

//        [Fact]
//        public async Task MesCallFunction_WithValidToken_ShouldGenerateAuthorizationHeader()
//        {
//            // Arrange
//            var token = "eyJhbGciOiJIUzUxMiJ9.test.token";
//            var redisKey = $"online-token-{token}";
//            var userInfoJson = """
//                {
//                    "admin": false,
//                    "authorities": [],
//                    "comment": "131",
//                    "createTime": 1566334602000,
//                    "deleted": "0",
//                    "groupId": "1943617310996697088",
//                    "id": "f52b1f9329cb4cd68d2fc660d9c2aac5",
//                    "password": "9FC7E2FC757073DA914C2DC762A2D096",
//                    "passwordSalt": "b7016a1f88864747bbbb359527cc879b",
//                    "userCode": "admin",
//                    "userEmail": "<EMAIL>",
//                    "userMobile": "13600000001",
//                    "userName": "admin",
//                    "userPhone": "029-88888888"
//                }
//                """;

//            // 设置Redis缓存返回值
//            var cacheValue = new CacheValue<string>(userInfoJson, true);
//            _mockCacheProvider.Setup(x => x.GetAsync<string>(redisKey))
//                .ReturnsAsync(cacheValue);

//            // 设置配置
//            _mockConfiguration.Setup(x => x["OuterSystem:AppUserKey"]).Returns("testAppUserKey");
//            _mockConfiguration.Setup(x => x["ServiceAddress:BaseUrl"]).Returns("http://localhost:5000");

//            // 设置请求头
//            var headers = new HeaderDictionary
//            {
//                { "appUserKey", "testAppUserKey" },
//                { "timestamp", "**********" },
//                { "sign", "OUTER_SYSTEM_ADMIN_SIGN" },
//                { "nonce", "test123" },
//                { "token", token }
//            };

//            _controller.HttpContext.Request.Headers.Clear();
//            foreach (var header in headers)
//            {
//                _controller.HttpContext.Request.Headers.Add(header.Key, header.Value);
//            }

//            var dto = new InputParamDto
//            {
//                ServiceUrl = "/api/test",
//                Param = """{"test": "value"}""",
//                HttpMethod = "POST"
//            };

//            // 这个测试主要验证token处理逻辑，实际的HTTP调用需要mock HttpClient
//            // 由于HttpClient的复杂性，这里主要测试token处理部分的逻辑

//            // Act & Assert
//            // 验证Redis被正确调用
//            _mockCacheProvider.Verify(x => x.GetAsync<string>(redisKey), Times.Once);
//        }

//        [Theory]
//        [InlineData("")]
//        [InlineData(null)]
//        public async Task MesCallFunction_WithInvalidToken_ShouldNotCallRedis(string invalidToken)
//        {
//            // Arrange
//            var headers = new HeaderDictionary
//            {
//                { "appUserKey", "testAppUserKey" },
//                { "timestamp", "**********" },
//                { "sign", "OUTER_SYSTEM_ADMIN_SIGN" },
//                { "nonce", "test123" }
//            };

//            if (!string.IsNullOrEmpty(invalidToken))
//            {
//                headers.Add("token", invalidToken);
//            }

//            _controller.HttpContext.Request.Headers.Clear();
//            foreach (var header in headers)
//            {
//                _controller.HttpContext.Request.Headers.Add(header.Key, header.Value);
//            }

//            var dto = new InputParamDto
//            {
//                ServiceUrl = "/api/test",
//                Param = """{"test": "value"}""",
//                HttpMethod = "POST"
//            };

//            // Act & Assert
//            // 验证Redis没有被调用
//            _mockCacheProvider.Verify(x => x.GetAsync<string>(It.IsAny<string>()), Times.Never);
//        }
//    }
//}
