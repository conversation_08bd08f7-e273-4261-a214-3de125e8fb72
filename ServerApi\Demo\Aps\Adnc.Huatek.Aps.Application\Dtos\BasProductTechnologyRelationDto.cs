﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Application.Dtos
{
    public class BasProductTechnologyRelationDto : IDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 工艺路线ID
        /// </summary>
        public long? IdTechnology { get; set; }
        /// <summary>
        /// 产品id
        /// </summary>
        public long?  IdProduct { get; set; }
        /// <summary>
        /// 工艺编码
        /// </summary>
        public string? TecCode { get; set; }
        /// <summary>
        /// 工艺名称
        /// </summary>
        public string? TecName { get; set; }

        /// <summary>
        /// 创建人ID
        /// </summary>
        public long? CreateBy { get; set; }

        /// <summary>
        /// 创建时间/注册时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 最后更新人
        /// </summary>
        public long? ModifyBy { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 启用状态
        /// </summary>
        public int? Status { get; set; }

    }
}
