[{"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasBomController", "Method": "CreateAsync", "RelativePath": "aps/api/bom/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasBomDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasBomController", "Method": "DeleteAsync", "RelativePath": "aps/api/bom/delete/{productCode}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "productCode", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasBomController", "Method": "UpdateAsync", "RelativePath": "aps/api/bom/edit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasBomDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasBomController", "Method": "GetByIdAsync", "RelativePath": "aps/api/bom/getbyproductcode/{productcode}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "productcode", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Huatek.Aps.Application.Dtos.BasBomDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasBomController", "Method": "GetMaterialByProductCodeAsync", "RelativePath": "aps/api/bom/getmaterialbyproductcode/{productCode}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "productCode", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Adnc.Huatek.Aps.Application.Dtos.BasBomListDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasBomController", "Method": "GetPagedAsync", "RelativePath": "aps/api/bom/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "Adnc.Huatek.Aps.Application.Dtos.BomPagedDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.PageModelDto`1[[Adnc.Huatek.Aps.Application.Dtos.BasBomDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasClassesController", "Method": "CreateAsync", "RelativePath": "aps/api/classes/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasClassesDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasClassesController", "Method": "MakeEnableAsync", "RelativePath": "aps/api/classes/changestatus/{status}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "status", "Type": "System.Int32", "IsRequired": true}, {"Name": "data", "Type": "System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasClassesController", "Method": "DeleteAsync", "RelativePath": "aps/api/classes/delete/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasClassesController", "Method": "UpdateAsync", "RelativePath": "aps/api/classes/edit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasClassesDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasClassesController", "Method": "GetAllAsync", "RelativePath": "aps/api/classes/getall", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Adnc.Huatek.Aps.Application.Dtos.BasClassesDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasClassesController", "Method": "GetByIdAsync", "RelativePath": "aps/api/classes/getbyid/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Huatek.Aps.Application.Dtos.BasClassesDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasClassesController", "Method": "GetAllClassesAsync", "RelativePath": "aps/api/classes/getclasses", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Adnc.Huatek.Aps.Application.Dtos.ClassesDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasClassesController", "Method": "GetPagedAsync", "RelativePath": "aps/api/classes/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "Adnc.Huatek.Aps.Application.Dtos.ClassesPagedDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.PageModelDto`1[[Adnc.Huatek.Aps.Application.Dtos.BasClassesDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasDeviceController", "Method": "ChangeStatus", "RelativePath": "aps/api/device/batch/changestatus/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "status", "Type": "Adnc.Shared.Application.Contracts.Dtos.SimpleDto`1[[System.Int32, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasDeviceController", "Method": "GetCalendarAsync", "RelativePath": "aps/api/device/calendar", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasDeviceCalendarDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Adnc.Huatek.Aps.Application.Dtos.BasDeviceCalendarResultDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasDeviceController", "Method": "DelCalendarAsync", "RelativePath": "aps/api/device/calendar/delpreserve", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasDeviceCalendarDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasDeviceController", "Method": "UpdateCalendarAsync", "RelativePath": "aps/api/device/calendar/setpreserve", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasDeviceCalendarDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasDeviceController", "Method": "ChangeStatus", "RelativePath": "aps/api/device/changestatus", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.UpdateStatusDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasDeviceController", "Method": "ChangeStatus", "RelativePath": "aps/api/device/changestatus", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.ChangeStatusDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasDeviceController", "Method": "CreateAsync", "RelativePath": "aps/api/device/createasync", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasDeviceDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasDeviceController", "Method": "GetCurrentUserInfoAsync", "RelativePath": "aps/api/device/current/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.ResultJson", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasDeviceController", "Method": "GetCurrentDeviceStateInfoAsync", "RelativePath": "aps/api/device/currentdevicestate/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.ResultJson", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasDeviceController", "Method": "DeleteAsync", "RelativePath": "aps/api/device/deleteasync/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasDeviceController", "Method": "DeleteDeviceStateAsync", "RelativePath": "aps/api/device/deleteDeviceState/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasDeviceController", "Method": "UpdateDeviceStateAsync", "RelativePath": "aps/api/device/editdevicestate", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.PartDeviceStateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasDeviceController", "Method": "GetAllAsync", "RelativePath": "aps/api/device/getallasync", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Adnc.Huatek.Aps.Application.Dtos.BasDeviceDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasDeviceController", "Method": "GetTypeDeviceAsync", "RelativePath": "aps/api/device/getallType", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasDevicePagedDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.PageModelDto`1[[Adnc.Huatek.Aps.Application.Dtos.PartDeviceStateDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasDeviceController", "Method": "GetDevicePreserveAsync", "RelativePath": "aps/api/device/getdevicepreserve/{id}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.ResultJson", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasDeviceController", "Method": "GetDeviceStateAsync", "RelativePath": "aps/api/device/getdevicestate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "queryDate", "Type": "Adnc.Huatek.Aps.Application.Dtos.QueryDevStateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Adnc.Huatek.Aps.Application.Dtos.DeviceStateDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasDeviceController", "Method": "ImportAsync", "RelativePath": "aps/api/device/importasync", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "formFile", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.ResultJson", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasDeviceController", "Method": "ImportDeviceStateAsync", "RelativePath": "aps/api/device/importdevicetstateasync", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "formFile", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.ResultJson", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasDeviceController", "Method": "GetPagedAsync", "RelativePath": "aps/api/device/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasDevicePagedDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.PageModelDto`1[[Adnc.Huatek.Aps.Application.Dtos.BasDeviceDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasDeviceController", "Method": "UpdateAsync", "RelativePath": "aps/api/device/updateasync", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasDeviceDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasDeviceClassController", "Method": "CreateAsync", "RelativePath": "aps/api/deviceclass/createasync", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasDeviceClassDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasDeviceClassController", "Method": "GetCurrentUserInfoAsync", "RelativePath": "aps/api/deviceclass/current/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.ResultJson", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasDeviceClassController", "Method": "DeleteAsync", "RelativePath": "aps/api/deviceclass/deleteasync/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasDeviceClassController", "Method": "GetAllAsync", "RelativePath": "aps/api/deviceclass/getallasync", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Adnc.Huatek.Aps.Application.Dtos.BasDeviceClassDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasDeviceClassController", "Method": "GetPagedAsync", "RelativePath": "aps/api/deviceclass/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasDeviceClassPagedDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.PageModelDto`1[[Adnc.Huatek.Aps.Application.Dtos.DeviceWorkingDateDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasDeviceClassController", "Method": "UpdateAsync", "RelativePath": "aps/api/deviceclass/updateasync", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasDeviceClassDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasHolidayController", "Method": "CreateAsync", "RelativePath": "aps/api/holiday/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasHolidayDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasHolidayController", "Method": "DeleteAsync", "RelativePath": "aps/api/holiday/delete/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasHolidayController", "Method": "DeleteAllAsync", "RelativePath": "aps/api/holiday/deleteall", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasHolidayController", "Method": "UpdateAsync", "RelativePath": "aps/api/holiday/edit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasHolidayDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasHolidayController", "Method": "GetAllAsync", "RelativePath": "aps/api/holiday/getallasync", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Adnc.Huatek.Aps.Application.Dtos.BasHolidayDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasHolidayController", "Method": "GetByIdAsync", "RelativePath": "aps/api/holiday/getbyid/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Huatek.Aps.Application.Dtos.BasHolidayDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasHolidayController", "Method": "GetNonScheduleDateAsync", "RelativePath": "aps/api/holiday/getunplan", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "Adnc.Huatek.Aps.Application.Dtos.HolidayPagedDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasHolidayController", "Method": "ImportOrders", "RelativePath": "aps/api/holiday/importholidays", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "formFile", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.ResultJson", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasHolidayController", "Method": "GetPagedAsync", "RelativePath": "aps/api/holiday/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "Adnc.Huatek.Aps.Application.Dtos.HolidayPagedDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.PageModelDto`1[[Adnc.Huatek.Aps.Application.Dtos.BasHolidayDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.HomePageController", "Method": "GetDevicesStatusCount", "RelativePath": "aps/api/home/<USER>", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Adnc.Huatek.Aps.Application.Dtos.HomeDeviceNumDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.HomePageController", "Method": "GetIdleDevicesCount", "RelativePath": "aps/api/home/<USER>", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Int32", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.HomePageController", "Method": "GetOrderNumByDateAsync", "RelativePath": "aps/api/home/<USER>", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Adnc.Huatek.Aps.Application.Dtos.HomeOrderNumByDateDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.HomePageController", "Method": "GetPlanByStatusAsync", "RelativePath": "aps/api/home/<USER>", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Adnc.Huatek.Aps.Application.Dtos.PlanNumByDateDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.HomePageController", "Method": "GetOrderNumAsync", "RelativePath": "aps/api/home/<USER>/{status}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "status", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int32", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasLineController", "Method": "CreateAsync", "RelativePath": "aps/api/line/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasLineDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasLineController", "Method": "MakeEnableAsync", "RelativePath": "aps/api/line/changestatus/{status}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "status", "Type": "System.Int32", "IsRequired": true}, {"Name": "data", "Type": "System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasLineController", "Method": "DeleteAsync", "RelativePath": "aps/api/line/delete/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasLineController", "Method": "UpdateAsync", "RelativePath": "aps/api/line/edit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasLineDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasLineController", "Method": "GetAllAsync", "RelativePath": "aps/api/line/getallasync", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Adnc.Huatek.Aps.Application.Dtos.BasLineDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasLineController", "Method": "GetByIdAsync", "RelativePath": "aps/api/line/getbyid/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Huatek.Aps.Application.Dtos.BasLineDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasLineController", "Method": "GetMaterialByProductCodeAsync", "RelativePath": "aps/api/line/getlinebyproductcode/{productCode}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "productCode", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Adnc.Huatek.Aps.Application.Dtos.ProductLineDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasLineController", "Method": "GetPagedAsync", "RelativePath": "aps/api/line/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "Adnc.Huatek.Aps.Application.Dtos.LinePagedDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.PageModelDto`1[[Adnc.Huatek.Aps.Application.Dtos.BasLineDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasMaterialController", "Method": "ChangeStatus", "RelativePath": "aps/api/material/batch/changestatus/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "status", "Type": "Adnc.Shared.Application.Contracts.Dtos.SimpleDto`1[[System.Int32, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasMaterialController", "Method": "ChangeStatus", "RelativePath": "aps/api/material/changestatus", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.UpdateStatusDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasMaterialController", "Method": "MakeEnableAsync", "RelativePath": "aps/api/material/changestatus/{status}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "status", "Type": "System.Int32", "IsRequired": true}, {"Name": "data", "Type": "System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasMaterialController", "Method": "CreateAsync", "RelativePath": "aps/api/material/createasync", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasMaterialDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasMaterialController", "Method": "GetCurrentUserInfoAsync", "RelativePath": "aps/api/material/current/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.ResultJson", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasMaterialController", "Method": "DeleteAsync", "RelativePath": "aps/api/material/deleteasync/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasMaterialController", "Method": "GetAllAsync", "RelativePath": "aps/api/material/getallasync", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Adnc.Huatek.Aps.Application.Dtos.BasMaterialDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasMaterialController", "Method": "ImportAsync", "RelativePath": "aps/api/material/importasync", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "formFile", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.ResultJson", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasMaterialController", "Method": "GetPagedAsync", "RelativePath": "aps/api/material/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasMaterialPagedDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.PageModelDto`1[[Adnc.Huatek.Aps.Application.Dtos.BasMaterialDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasMaterialController", "Method": "UpdateAsync", "RelativePath": "aps/api/material/updateasync", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasMaterialDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasNumberManagementController", "Method": "CreateAsync", "RelativePath": "aps/api/number/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasNumberManagementDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasNumberManagementController", "Method": "DeleteAsync", "RelativePath": "aps/api/number/delete/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasNumberManagementController", "Method": "UpdateAsync", "RelativePath": "aps/api/number/edit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasNumberManagementDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasNumberManagementController", "Method": "GetByIdAsync", "RelativePath": "aps/api/number/getbyid/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Huatek.Aps.Domain.Aggregates.BasNumberManagementAggregate.BasNumberManagement", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasNumberManagementController", "Method": "GetNumberBySimpleName", "RelativePath": "aps/api/number/getNumberBySimpleName/{simplename}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "simplename", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasNumberManagementController", "Method": "GetPagedAsync", "RelativePath": "aps/api/number/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasNumberManagementPagedDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.PageModelDto`1[[Adnc.Huatek.Aps.Application.Dtos.BasNumberManagementDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PartOrderController", "Method": "CreateAsync", "RelativePath": "aps/api/order/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.PartOrderDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PartOrderController", "Method": "DeleteAsync", "RelativePath": "aps/api/order/delete/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PartOrderController", "Method": "UpdateAsync", "RelativePath": "aps/api/order/edit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.PartOrderDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PartOrderController", "Method": "GetByIdAsync", "RelativePath": "aps/api/order/getbyid/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Huatek.Aps.Application.Dtos.PartOrderDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PartOrderController", "Method": "GetPagedAsync", "RelativePath": "aps/api/order/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "Adnc.Huatek.Aps.Application.Dtos.PartOrderPagedDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.PageModelDto`1[[Adnc.Huatek.Aps.Application.Dtos.PartOrderDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasOrderController", "Method": "CreateAsync", "RelativePath": "aps/api/Orders/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasOrderDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasOrderController", "Method": "DeleteAsync", "RelativePath": "aps/api/Orders/delete/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasOrderController", "Method": "Download", "RelativePath": "aps/api/Orders/downloadtemplate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.ResultJson", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasOrderController", "Method": "UpdateAsync", "RelativePath": "aps/api/Orders/edit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasOrderDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasOrderController", "Method": "ExportOrdersAsync", "RelativePath": "aps/api/Orders/exportorders", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasOrderPagedDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasOrderController", "Method": "FinishedAsync", "RelativePath": "aps/api/Orders/finish", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.FinishedOrders", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasOrderController", "Method": "GetAllAsync", "RelativePath": "aps/api/Orders/getall", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasOrderPagedDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.ResultJson", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasOrderController", "Method": "GetByIdAsync", "RelativePath": "aps/api/Orders/getbyid/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.ResultJson", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasOrderController", "Method": "GetOrderDetailAsync", "RelativePath": "aps/api/Orders/getdetails/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Huatek.Aps.Application.Dtos.OrderDetailDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasOrderController", "Method": "GetByOrderIdAsync", "RelativePath": "aps/api/Orders/getdetailsbyid/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasOrderController", "Method": "ImportOrders", "RelativePath": "aps/api/Orders/importOrders", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "formFile", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.ResultJson", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasOrderController", "Method": "GetPagedAsync", "RelativePath": "aps/api/Orders/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasOrderPagedDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.PageModelDto`1[[Adnc.Huatek.Aps.Application.Dtos.BasOrderDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "OuterSystemController", "Method": "MesCallFunction", "RelativePath": "aps/api/outerSystem/mescall", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Adnc.Huatek.Aps.Application.Dtos.InputParamDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PartPlanPendingController", "Method": "CreateAsync", "RelativePath": "aps/api/PendingPlans/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PartPlanPendingController", "Method": "DeleteAsync", "RelativePath": "aps/api/PendingPlans/delete/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PartPlanPendingController", "Method": "UpdateAsync", "RelativePath": "aps/api/PendingPlans/edit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PartPlanPendingController", "Method": "GetAllAsync", "RelativePath": "aps/api/PendingPlans/getall", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.ResultJson", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PartPlanPendingController", "Method": "GetByIdAsync", "RelativePath": "aps/api/PendingPlans/getbyid/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.ResultJson", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PartPlanPendingController", "Method": "GetPlanPendingByIdAsync", "RelativePath": "aps/api/PendingPlans/getPlanPendingById/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.ResultJson", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PartPlanPendingController", "Method": "GetPagedAsync", "RelativePath": "aps/api/PendingPlans/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingPagedDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.PageModelDto`1[[Adnc.Huatek.Aps.Application.Dtos.PartPlanPendingResultDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasProductController", "Method": "CreateAsync", "RelativePath": "aps/api/product/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasProductBomDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasProductController", "Method": "ChangeStatus", "RelativePath": "aps/api/product/changestatus", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.ChangeStatusDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasProductController", "Method": "DeleteAsync", "RelativePath": "aps/api/product/delete/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasProductController", "Method": "UpdateAsync", "RelativePath": "aps/api/product/edit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasProductBomDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasProductController", "Method": "UpdateProductStdCapAsync", "RelativePath": "aps/api/product/edit/standardcapacity", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasProductDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasProductController", "Method": "UpdateStepMaterialAsync", "RelativePath": "aps/api/product/edit/stepmaterial", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasProductDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasProductController", "Method": "GetAllAsync", "RelativePath": "aps/api/product/getallasync", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Adnc.Huatek.Aps.Application.Dtos.BasProductDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasProductController", "Method": "GetByIdAsync", "RelativePath": "aps/api/product/getbyid/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Huatek.Aps.Application.Dtos.BasProductDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasProductController", "Method": "GetModuleByIdAsync", "RelativePath": "aps/api/product/getmodulebyid/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Huatek.Aps.Application.Dtos.BasProductDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasProductController", "Method": "ImportAsync", "RelativePath": "aps/api/product/importasync", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "formFile", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.ResultJson", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasProductController", "Method": "GetPagedAsync", "RelativePath": "aps/api/product/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "Adnc.Huatek.Aps.Application.Dtos.ProductPagedDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.PageModelDto`1[[Adnc.Huatek.Aps.Application.Dtos.BasProductDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasRelationController", "Method": "CreateAsync", "RelativePath": "aps/api/relationship/createasync", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasRelationDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasRelationController", "Method": "GetCurrentUserInfoAsync", "RelativePath": "aps/api/relationship/current/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Huatek.Aps.Application.Dtos.BasRelationDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasRelationController", "Method": "DeleteAsync", "RelativePath": "aps/api/relationship/deleteasync/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasRelationController", "Method": "GetPagedAsync", "RelativePath": "aps/api/relationship/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "Adnc.Huatek.Aps.Application.Dtos.RelationPagedDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.PageModelDto`1[[Adnc.Huatek.Aps.Application.Dtos.BasRelationDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasRelationController", "Method": "UpdateAsync", "RelativePath": "aps/api/relationship/updateasync", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasRelationDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PlanProductionSchedulController", "Method": "CreateAsync", "RelativePath": "aps/api/Schedul/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PlanProductionSchedulController", "Method": "DeleteAsync", "RelativePath": "aps/api/Schedul/batchDel", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "codes", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PlanProductionSchedulController", "Method": "GetDeviceGantt", "RelativePath": "aps/api/Schedul/gantt/device/{scheduleCode}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "scheduleCode", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Adnc.Huatek.Aps.Application.Dtos.ScheduleDeviceGanttItem, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PlanProductionSchedulController", "Method": "GetGanttByOrderNumber", "RelativePath": "aps/api/Schedul/gantt/orderplan", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "Adnc.Huatek.Aps.Application.Dtos.ProductionSchedulGanttDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulGanttForOrderDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PlanProductionSchedulController", "Method": "GetGanttByOrderProductNumber", "RelativePath": "aps/api/Schedul/gantt/orderproductplan", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "Adnc.Huatek.Aps.Application.Dtos.OrderProdcutGanttDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulGanttNewResultDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PlanProductionSchedulController", "Method": "GetGanttByPlanCodeNumber", "RelativePath": "aps/api/Schedul/gantt/plan", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "planCode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulGanttNewResultForPlanDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PlanProductionSchedulController", "Method": "GetGanttByProduct", "RelativePath": "aps/api/Schedul/gantt/productplan/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Huatek.Aps.Application.Dtos.PlanGanttDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PlanProductionSchedulController", "Method": "Getdetails", "RelativePath": "aps/api/Schedul/getdetails/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.ResultModels.AppSrvResult`1[[Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PlanProductionSchedulController", "Method": "GetMaterials", "RelativePath": "aps/api/Schedul/getmaterials", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "Adnc.Huatek.Aps.Application.Dtos.PlanMaterialDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Huatek.Aps.Application.Dtos.PlanMaterialListDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PlanProductionSchedulController", "Method": "GetPersonnels", "RelativePath": "aps/api/Schedul/getpersonnels", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "Adnc.Huatek.Aps.Application.Dtos.PersonnelDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Adnc.Huatek.Aps.Application.Dtos.PersonnelResultDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PlanProductionSchedulController", "Method": "GetPagedAsync", "RelativePath": "aps/api/Schedul/material", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "Adnc.Huatek.Aps.Application.Dtos.MaterialWarningPagedDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.PageModelDto`1[[Adnc.Huatek.Aps.Application.Dtos.MaterialWarningReturnDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PlanProductionSchedulController", "Method": "GetChartAsync", "RelativePath": "aps/api/Schedul/materialchart", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "Mcode", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Huatek.Aps.Application.Dtos.MaterialWarningChartDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PlanProductionSchedulController", "Method": "GetPagedAsync", "RelativePath": "aps/api/Schedul/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulPageDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.PageModelDto`1[[Adnc.Huatek.Aps.Application.Dtos.PlanProductionSchedulResultDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PlanProductionSchedulController", "Method": "RealoadSchedulAsync", "RelativePath": "aps/api/Schedul/reschedulingproduction", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.ReloadBasProcuct", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PlanProductionSchedulController", "Method": "SaveSchedulingProduction", "RelativePath": "aps/api/Schedul/saveSchedulingProduction", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.ReloadBasProcuct", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PlanProductionSchedulController", "Method": "setScheduleStatus", "RelativePath": "aps/api/Schedul/setScheduleStatus", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.SchedulStatus", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PlanProductionSchedulController", "Method": "GetStepSolution", "RelativePath": "aps/api/Schedul/stepsolution/{code}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "code", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Adnc.Huatek.Aps.Application.Dtos.SchduleStepSolutionItem, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PartSchedulePlanController", "Method": "DeleteAsync", "RelativePath": "aps/api/SchedulePlan/batchDel", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "codes", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PartSchedulePlanController", "Method": "ChangeScheduleStatusAsync", "RelativePath": "aps/api/SchedulePlan/changeplan", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.ResultJson", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PartSchedulePlanController", "Method": "ChangeStepProgressAsync", "RelativePath": "aps/api/SchedulePlan/ChangeProgress", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanStepReportDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.ResultModels.AppSrvResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PartSchedulePlanController", "Method": "ChangeScheduleStatusAsync", "RelativePath": "aps/api/SchedulePlan/changeState", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.SchedulStatusNew", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.ResultJson", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PartSchedulePlanController", "Method": "CheckSourceEffiveAsync", "RelativePath": "aps/api/SchedulePlan/CheckSource", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "System.Collections.Generic.List`1[[Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanStepDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.ResultJson", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PartSchedulePlanController", "Method": "CheckSourceConfilctAsync", "RelativePath": "aps/api/SchedulePlan/CheckSourceConfilct", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.SchedulStatusNew", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.ResultModels.AppSrvResult`1[[System.String, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PartSchedulePlanController", "Method": "CreateSchedulePlanAsync", "RelativePath": "aps/api/SchedulePlan/CreateSchedule", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.ResultJson", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PartSchedulePlanController", "Method": "GetDeviceGanttPLanAsync", "RelativePath": "aps/api/SchedulePlan/deviceGantt", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "Adnc.Huatek.Aps.Application.Dtos.PartSchedulPlanSourcePageDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Adnc.Huatek.Aps.Application.Dtos.DeviceGanttDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PartSchedulePlanController", "Method": "GetPagedAsync", "RelativePath": "aps/api/SchedulePlan/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "Adnc.Huatek.Aps.Application.Dtos.PartSchedulPlanPageDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.PageModelDto`1[[Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PartSchedulePlanController", "Method": "CheckSourceConflictAsync", "RelativePath": "aps/api/SchedulePlan/QuerySourceConflict", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.QuerySourceConflict", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.ResultJson", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.PartSchedulePlanController", "Method": "TrialSchedulePlanAsync", "RelativePath": "aps/api/SchedulePlan/TrialSchedule", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.PartSchedulePlanDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Huatek.Aps.Application.Dtos.PlanGanttDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasStationController", "Method": "ChangeStatus", "RelativePath": "aps/api/station/batch/changestatus/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "status", "Type": "Adnc.Shared.Application.Contracts.Dtos.SimpleDto`1[[System.Int32, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasStationController", "Method": "ChangeStatus", "RelativePath": "aps/api/station/changestatus", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.UpdateStatusDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasStationController", "Method": "MakeEnableAsync", "RelativePath": "aps/api/station/changestatus/{status}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "status", "Type": "System.Int32", "IsRequired": true}, {"Name": "data", "Type": "System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasStationController", "Method": "GetClassesInfoAsync", "RelativePath": "aps/api/station/classes/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Adnc.Huatek.Aps.Application.Dtos.ClassesDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasStationController", "Method": "CreateAsync", "RelativePath": "aps/api/station/createasync", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasStationDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasStationController", "Method": "GetCurrentUserInfoAsync", "RelativePath": "aps/api/station/current/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.ResultJson", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasStationController", "Method": "DeleteAsync", "RelativePath": "aps/api/station/deleteasync/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasStationController", "Method": "GetAllAsync", "RelativePath": "aps/api/station/getallasync", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Adnc.Huatek.Aps.Application.Dtos.BasStationDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasStationController", "Method": "GetListAsync", "RelativePath": "aps/api/station/list", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasStationSearchDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Adnc.Huatek.Aps.Application.Dtos.BasStationDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasStationController", "Method": "GetPagedAsync", "RelativePath": "aps/api/station/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasStationPagedDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.PageModelDto`1[[Adnc.Huatek.Aps.Application.Dtos.BasStationDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasStationController", "Method": "UpdateAsync", "RelativePath": "aps/api/station/updateasync", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasStationDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasStepController", "Method": "CreateAsync", "RelativePath": "aps/api/Steps/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasStepDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasStepController", "Method": "ChangeStatus", "RelativePath": "aps/api/Steps/changestatus", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.ChangeStatusDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasStepController", "Method": "DeleteAsync", "RelativePath": "aps/api/Steps/delete/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasStepController", "Method": "UpdateAsync", "RelativePath": "aps/api/Steps/edit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasStepDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasStepController", "Method": "GetAllAsync", "RelativePath": "aps/api/Steps/getall", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.ResultJson", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasStepController", "Method": "GetByIdAsync", "RelativePath": "aps/api/Steps/getbyid/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.ResultJson", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasStepController", "Method": "ImportAsync", "RelativePath": "aps/api/Steps/importasync", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "formFile", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.ResultJson", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasStepController", "Method": "GetPagedAsync", "RelativePath": "aps/api/Steps/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "Adnc.Huatek.Aps.Application.Dtos.StepPagedDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.PageModelDto`1[[Adnc.Huatek.Aps.Application.Dtos.BasStepDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.StockMaterialController", "Method": "CreateAsync", "RelativePath": "aps/api/stockmaterial/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.StockMaterialDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.StockMaterialController", "Method": "UpdateAsync", "RelativePath": "aps/api/stockmaterial/edit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.StockMaterialDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.StockMaterialController", "Method": "GetAllAsync", "RelativePath": "aps/api/stockmaterial/getallasync", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.ResultJson", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.StockMaterialController", "Method": "GetByIdAsync", "RelativePath": "aps/api/stockmaterial/getbyid/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Huatek.Aps.Application.Dtos.StockMaterialDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.StockMaterialController", "Method": "ImportAsync", "RelativePath": "aps/api/stockmaterial/importasync", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "formFile", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.ResultJson", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.StockMaterialController", "Method": "GetPagedAsync", "RelativePath": "aps/api/stockmaterial/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "Adnc.Huatek.Aps.Application.Dtos.StockMaterialPagedDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.PageModelDto`1[[Adnc.Huatek.Aps.Application.Dtos.StockMaterialDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasTechnologyController", "Method": "CreateAsync", "RelativePath": "aps/api/Technologys/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasTechnologyDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasTechnologyController", "Method": "ChangeStatus", "RelativePath": "aps/api/Technologys/changestatus", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.ChangeStatusDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasTechnologyController", "Method": "DeleteAsync", "RelativePath": "aps/api/Technologys/delete/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasTechnologyController", "Method": "GetByTCodeAsync", "RelativePath": "aps/api/Technologys/detailsbytcode/{tCode}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tCode", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Huatek.Aps.Application.Dtos.BasTechnologyDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasTechnologyController", "Method": "UpdateAsync", "RelativePath": "aps/api/Technologys/edit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasTechnologyDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasTechnologyController", "Method": "GetAllAsync", "RelativePath": "aps/api/Technologys/getall", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Adnc.Huatek.Aps.Application.Dtos.BasTechnologyDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasTechnologyController", "Method": "GetByIdAsync", "RelativePath": "aps/api/Technologys/getbyid/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Huatek.Aps.Application.Dtos.BasTechnologyDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasTechnologyController", "Method": "GetPagedAsync", "RelativePath": "aps/api/Technologys/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "Adnc.Huatek.Aps.Application.Dtos.TechnologyPagedDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.PageModelDto`1[[Adnc.Huatek.Aps.Application.Dtos.BasTechnologyDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.TestController", "Method": "GetAllAsync", "RelativePath": "aps/api/tests/GetAllAsync", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Adnc.Huatek.Aps.Application.Dtos.SysTestDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.TestController", "Method": "getTotalTime", "RelativePath": "aps/api/tests/getTotalTime", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Adnc.Huatek.Aps.Application.Dtos.SysTestDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasWorksjopController", "Method": "ChangeStatus", "RelativePath": "aps/api/worksjop/batch/changestatus/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "status", "Type": "Adnc.Shared.Application.Contracts.Dtos.SimpleDto`1[[System.Int32, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasWorksjopController", "Method": "ChangeStatus", "RelativePath": "aps/api/worksjop/changestatus", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.UpdateStatusDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasWorksjopController", "Method": "CreateAsync", "RelativePath": "aps/api/worksjop/createasync", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasWorksjopDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasWorksjopController", "Method": "GetCurrentUserInfoAsync", "RelativePath": "aps/api/worksjop/current/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.ResultJson", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasWorksjopController", "Method": "DeleteAsync", "RelativePath": "aps/api/worksjop/deleteasync/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasWorksjopController", "Method": "GetAllAsync", "RelativePath": "aps/api/worksjop/getallasync", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Adnc.Huatek.Aps.Application.Dtos.BasWorksjopDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasWorksjopController", "Method": "GetWorkShopAsync", "RelativePath": "aps/api/worksjop/getworkshop", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasWorksjopController", "Method": "GetPagedAsync", "RelativePath": "aps/api/worksjop/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasStationPagedDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Adnc.Shared.Application.Contracts.Dtos.PageModelDto`1[[Adnc.Huatek.Aps.Application.Dtos.BasWorksjopDto, Adnc.Huatek.Aps.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Adnc.Huatek.Aps.Api.Controllers.BasWorksjopController", "Method": "UpdateAsync", "RelativePath": "aps/api/worksjop/updateasync", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Adnc.Huatek.Aps.Application.Dtos.BasWorksjopDto", "IsRequired": true}], "ReturnTypes": []}]