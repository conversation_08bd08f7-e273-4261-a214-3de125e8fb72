﻿using Adnc.Huatek.Aps.Domain.Aggregates.SysTestAggregate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class SysTestConfig : AbstractEntityTypeConfiguration<SysTest>
    {
        public override void Configure(EntityTypeBuilder<SysTest> builder)
        {
            base.Configure(builder);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.HtName).HasColumnName("htname");
            builder.Property(x => x.HtDescription).HasColumnName("htdescription");
            builder.Property(x => x.CreateBy).HasColumnName("createby");
            builder.Property(x => x.CreateTime).HasColumnName("createtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.IsDeleted).HasColumnName("IsDeleted");
        }
    }
}
