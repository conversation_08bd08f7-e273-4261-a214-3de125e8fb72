﻿using Adnc.Infra.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.Aggregates.PartDeviceStateAggregates
{
    public class PartDeviceState : EfFullAuditEntity
    {
        /// <summary>
        /// ID
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 设备编码
        /// </summary>
        public string? DevCode { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        public string? DevName { get; set; }

       
        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime BeginTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndTime { get; set; }
    }
}
