﻿using Adnc.Huatek.Aps.Domain.Aggregates.BasLineAggregate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Adnc.Huatek.Aps.Domain.EntityConfig
{
    public class BasLineConfig : AbstractEntityTypeConfiguration<BasLine>
    {
        public override void Configure(EntityTypeBuilder<BasLine> builder)
        {
            base.Configure(builder);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.Lcode).HasColumnName("lcode");
            builder.Property(x => x.Lname).HasColumnName("lname");
            builder.Property(x => x.IdWorkshop).HasColumnName("idworkshop");
            builder.Property(x => x.Status).HasColumnName("status");
            builder.Property(x => x.Remark).HasColumnName("remark");
            builder.Property(x => x.IsDeleted).HasColumnName("isdeleted");
            builder.Property(x => x.CreateBy).HasColumnName("createby");
            builder.Property(x => x.CreateTime).HasColumnName("createtime");
            builder.Property(x => x.ModifyBy).HasColumnName("modifyby");
            builder.Property(x => x.ModifyTime).HasColumnName("modifytime");
        }
    }
}
